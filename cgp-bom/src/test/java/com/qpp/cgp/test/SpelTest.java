//package com.qpp.cgp.test;
//
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.qpp.cgp.domain.bom.Material;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.io.File;
//import java.io.IOException;
//
///**
// * <AUTHOR>
// * @version 1.0
// * @Description
// * @Date Create by 2018-08-07 18:27
// * @Modified by
// */
//
//@SpringBootTest
//@RunWith(SpringRunner.class)
//public class SpelTest {
//
//
//	@Test
//	public void test() throws IOException {
////		String materia = "{\n" + "    \"_id\" : \"193693\",\n" + "    \"code\" : \"shoppingBagNew\",\n" + "    \"name\" : \"Shopping Bag New\",\n" + "    \"childItems\" : [ \n" + "        {\n" + "            \"_id\" : \"193694\",\n" + "            \"name\" : \"shoppngBag袋子\",\n" + "            \"quantityStrategy\" : \"basic\",\n" + "            \"itemMaterial\" : {\n" + "                \"_id\" : \"193695\",\n" + "                \"idReference\" : \"Material\",\n" + "                \"clazz\" : \"com.qpp.cgp.domain.bom.MaterialType\"\n" + "            },\n" + "            \"constraints\" : [ \n" + "                {\n" + "                    \"quantity\" : 1,\n" + "                    \"clazz\" : \"com.qpp.cgp.domain.bom.constraint.FixedQuantityConstraint\"\n" + "                }\n" + "            ],\n" + "            \"clazz\" : \"com.qpp.cgp.domain.bom.bomitem.FixedBOMItem\"\n" + "        }, \n" + "        {\n" + "            \"_id\" : \"193697\",\n" + "            \"name\" : \"shoppingBag绳子\",\n" + "            \"quantityStrategy\" : \"basic\",\n" + "            \"itemMaterial\" : {\n" + "                \"_id\" : \"189024\",\n" + "                \"idReference\" : \"Material\",\n" + "                \"clazz\" : \"com.qpp.cgp.domain.bom.MaterialType\"\n" + "            },\n" + "            \"constraints\" : [ \n" + "                {\n" + "                    \"quantity\" : 1,\n" + "                    \"clazz\" : \"com.qpp.cgp.domain.bom.constraint.FixedQuantityConstraint\"\n" + "                }\n" + "            ],\n" + "            \"clazz\" : \"com.qpp.cgp.domain.bom.bomitem.FixedBOMItem\"\n" + "        }\n" + "    ],\n" + "    \"parentMaterialType\" : {\n" + "        \"_id\" : \"179411\"\n" + "    },\n" + "    \"rtType\" : {\n" + "        \"attributesToRtTypes\" : [],\n" + "        \"idReference\" : \"RtType\",\n" + "        \"clazz\" : \"com.qpp.cgp.domain.bom.attribute.RtType\"\n" + "    },\n" + "    \"rtObject\" : {\n" + "        \"_id\" : \"193698\",\n" + "        \"idReference\" : \"RtObject\",\n" + "        \"clazz\" : \"com.qpp.cgp.domain.bom.runtime.RtObject\"\n" + "    },\n" + "    \"category\" : \"138839\",\n" + "    \"clazz\" : \"com.qpp.cgp.domain.bom.MaterialSpu\"\n" + "}";
//		ObjectMapper objectMapper = new ObjectMapper();
//		Material material = objectMapper.readValue(new File("D:\\Work\\IDEA\\WorkSpace\\CGP2\\cgp-bom\\src\\test\\java\\com\\qpp\\cgp\\test\\material.json"), Material.class);
//		System.out.println(material);
//	}
//}
