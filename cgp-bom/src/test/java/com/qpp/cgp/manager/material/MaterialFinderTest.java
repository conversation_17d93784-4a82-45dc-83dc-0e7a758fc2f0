//package com.qpp.cgp.manager.material;
//
//import com.qpp.cgp.domain.bom.Material;
//import com.qpp.cgp.manager.bom.MaterialManager;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.List;
//
///**
// * <AUTHOR> Lee 2019/12/19 11:23
// */
//@SpringBootTest
//@RunWith(SpringRunner.class)
//public class MaterialFinderTest {
//
//    @Autowired
//    private MaterialFinder materialFinder;
//
//    @Autowired
//    private MaterialManager materialManager;
//
//    @Test
//    public void findMaterials() {
//
//        String materialId = "2397350";
//
//        final Material example = materialManager.findById(materialId, true);
//
//        final List<String> materials = materialFinder.findMaterials(example);
//
//        assert materials.size() == 1 && materials.get(0).equals(materialId);
//
//
//    }
//}