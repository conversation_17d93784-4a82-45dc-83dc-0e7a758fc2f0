package com.qpp.cgp.manager.bom.runtime;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/8/23
 */
@ExtendWith(MockitoExtension.class)
public class PageContentSequenceManagerTest {

    @Spy
    private PageContentSequenceManager pageContentSequenceManager;

    /**
     * numOfTeams = 0
     * numOfPlayersPerTeam = 0
     */
    @Test
    public void testGenerateTeamSequenceByNumOfTeamsAndNumOfPlayersPerTeamIsEqualZero() {
        Integer numOfTeams = 0;
        Integer numOfPlayersPerTeam = 0;

        Assertions.assertThatThrownBy(() -> pageContentSequenceManager.generateTeamSequence(numOfTeams, numOfPlayersPerTeam))
                .hasMessage("numOfTeams and numOfPlayersPerTeam must be greater than 0");
    }

    /**
     * numOfTeams > 1
     * numOfPlayersPerTeam > 1
     */
    @Test
    public void testGenerateTeamSequenceByNumOfTeamsAndNumOfPlayersPerTeamIsGreaterThanOne() {
        Integer numOfTeams = 18;
        Integer numOfPlayersPerTeam = 4;

        Map<Integer, List<Integer>> teamInfo = generateTeamInfo(numOfTeams, numOfPlayersPerTeam);

        List<Integer> teamSequences = pageContentSequenceManager.generateTeamSequence(numOfTeams, numOfPlayersPerTeam);
        List<Integer> newTeamSequences = teamSequences.stream()
                .distinct()
                .collect(Collectors.toList());

        Assertions.assertThat(teamSequences.size()).isEqualTo(numOfTeams * numOfPlayersPerTeam);
        Assertions.assertThat(newTeamSequences.size()).isEqualTo(numOfTeams * numOfPlayersPerTeam);

        /**
         * 验证每次挑选出来的球员序号都是唯一的，即没有重复挑选出同一队的球员
         */
        int num = 0, temp = 0;
        for (Map.Entry<Integer, List<Integer>> infoPerTeam : teamInfo.entrySet()) {
            for (Integer numOfTeam : infoPerTeam.getValue()) {
                for (int j = temp; j < teamSequences.size() && j < temp + numOfPlayersPerTeam - 1; j++) {
                    if (numOfTeam == newTeamSequences.get(j)) {
                        num++;
                    }
                    temp++;
                }
                /**
                 * 验证结果是否等于1，若等于1则说明是符合预期结果的
                 */
                Assertions.assertThat(num).isEqualTo(1);
            }
        }
        num++;
    }

    /**
     * numOfTeams = 1
     * numOfPlayersPerTeam = 1
     */
    @Test
    public void testGenerateTeamSequenceByNumOfTeamsIsEqualOneAndNumOfPlayersPerTeamIsGreaterThanOne() {
        Integer numOfTeams = 1;
        Integer numOfPlayersPerTeam = 1;

        generateTeamInfo(numOfTeams, numOfPlayersPerTeam);

        List<Integer> teamSequences = pageContentSequenceManager.generateTeamSequence(numOfTeams, numOfPlayersPerTeam);
        List<Integer> newTeamSequences = teamSequences.stream()
                .distinct()
                .collect(Collectors.toList());

        Assertions.assertThat(teamSequences.size()).isEqualTo(numOfTeams * numOfPlayersPerTeam);
        Assertions.assertThat(newTeamSequences.size()).isEqualTo(numOfTeams * numOfPlayersPerTeam);
    }

    /**
     * numOfTeams > 1
     * numOfPlayersPerTeam = 1
     */
    @Test
    public void testGenerateTeamSequenceByNumOfTeamsIsGreaterThanOneAndNumOfPlayersPerTeamIsEqualOne() {
        Integer numOfTeams = 30;
        Integer numOfPlayersPerTeam = 1;

        List<Integer> teamSequences = pageContentSequenceManager.generateTeamSequence(numOfTeams, numOfPlayersPerTeam);
        List<Integer> newTeamSequences = teamSequences.stream()
                .distinct()
                .collect(Collectors.toList());

        Assertions.assertThat(teamSequences.size()).isEqualTo(numOfTeams * numOfPlayersPerTeam);
        Assertions.assertThat(newTeamSequences.size()).isEqualTo(numOfTeams * numOfPlayersPerTeam);
    }

    /**
     * ruleId: 51765662
     * numOfTeams = 18
     * numOfPlayersPerTeam = 13
     */
    @Test
    public void testInsertHeadBall() {
        Integer numOfTeams = 18;
        Integer numOfPlayersPerTeam = 13;

        List<Integer> teamSequences = pageContentSequenceManager.generateTeamSequence(numOfTeams, numOfPlayersPerTeam);
        int numOfPlayer = 18 * 13;
        List<Integer> targetIndexes = pageContentSequenceManager.insertHeadBall(teamSequences, numOfPlayer);

        Assertions.assertThat(targetIndexes.size()).isEqualTo(numOfTeams * numOfPlayersPerTeam + 1);
        Assertions.assertThat(targetIndexes).contains(numOfPlayer);
    }

    private Map<Integer, List<Integer>> generateTeamInfo(Integer numOfTeams, Integer numOfPlayersPerTeam) {
        Map<Integer, List<Integer>> teams = new HashMap<>();
        int num = 0;

        for (int i = 0; i < numOfTeams; i++) {
            List<Integer> numOfPlayers = new LinkedList<>();
            for (int j = 0; j < numOfPlayersPerTeam; j++) {
                numOfPlayers.add(num);
                num++;
            }
            teams.put(i, numOfPlayers);
        }

        return teams;
    }

}
