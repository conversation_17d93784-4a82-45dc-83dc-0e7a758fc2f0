package com.qpp.cgp.domain.dto.bom;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 物料的parent和当前物料的路径table
 *
 * <AUTHOR> Lee 2021/6/15 16:44
 */
@Getter
@Setter
public class MaterialPathTable {

    private String materialId;

    private List<MaterialPathEntry> entries = new ArrayList<>();


    public String toString() {

        return entries.stream()
                .map(entry -> entry.getParentPath() + ":" + entry.getSelfPath())
                .collect(Collectors.joining("\r\n"));

    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    private class MaterialPathEntry {

        private String parentPath;

        private String selfPath;

    }

    public void addEntry(String parentPath, String selfPath) {
        this.entries.add(new MaterialPathEntry(parentPath, selfPath));
    }

    public boolean isParentPathExists(String parentPath) {
        return this.entries.stream().anyMatch(materialPathEntry -> materialPathEntry.getParentPath().equals(parentPath));
    }

    public Optional<String> getSelfPathByParentPath(String parentPath) {
        return this.entries.stream()
                .filter(materialPathEntry -> materialPathEntry.getParentPath().equals(parentPath))
                .map(MaterialPathEntry::getSelfPath)
                .findFirst();
    }

}
