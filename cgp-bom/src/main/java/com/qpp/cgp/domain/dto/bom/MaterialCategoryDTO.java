package com.qpp.cgp.domain.dto.bom;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * Created by smart on 9/2/2017.
 */
@NoArgsConstructor
@Data
@Document(collection = "materialcategories")
public class MaterialCategoryDTO extends MongoDomainDTO {

    private String name;

    private String parentId;

    private boolean isLeaf;

}
