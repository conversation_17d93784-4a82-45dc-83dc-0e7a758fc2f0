package com.qpp.cgp.domain.dto.bom;

import com.qpp.cgp.domain.bom.bomitem.BOMItem;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by smart on 9/1/2017.
 */
@NoArgsConstructor
@Data
public class BOMItemDTO extends MongoDomainDTO {

    private String name;

    private Integer quantity;

    private boolean isLeaf = false;


    public BOMItemDTO(BOMItem bomItem) {

        this.name = bomItem.getName();
        // 当BOMItem的quantity等于0时，将quantity赋值为null
        this.quantity = (0 == bomItem.getQuantity()) ? null : bomItem.getQuantity();
        this.clazz = bomItem.getClass().getName();
        this._id = bomItem.getId();

    }

}
