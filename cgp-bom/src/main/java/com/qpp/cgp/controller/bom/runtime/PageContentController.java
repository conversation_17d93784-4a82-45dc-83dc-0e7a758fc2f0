package com.qpp.cgp.controller.bom.runtime;

import com.qpp.cgp.domain.bom.runtime.PageContent;
import com.qpp.cgp.domain.dto.bom.runtime.PageContentPreviewRequestDto;
import com.qpp.cgp.domain.dto.bom.runtime.PageContentPreviewResponseDto;
import com.qpp.cgp.manager.PreviewCommonService;
import com.qpp.cgp.manager.bom.bom.PageContentBatchManager;
import com.qpp.cgp.manager.bom.runtime.PageContentManager;
import com.qpp.cgp.service.PageService;
import com.qpp.operation.log.OperationLog;
import com.qpp.operation.log.OperationLogModule;
import com.qpp.operation.log.OperationLogTag;
import com.qpp.web.business.controller.AbstractJsonRestController;
import io.swagger.annotations.ApiOperation;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Created by smart on 10/11/2017.
 */
@OperationLogModule("PageContent")
@RestController
@RequestMapping(value = "api/pageContents", produces = MediaType.APPLICATION_JSON_VALUE)
public class PageContentController extends AbstractJsonRestController<Document, String, PageContentManager> {

    @Autowired
    private PageService pageService;

    @Autowired
    private PreviewCommonService previewCommonService;

    @Autowired
    private PageContentBatchManager pageContentBatchManager;

    @Autowired
    public PageContentController(PageContentManager manager) {
        super(manager);
    }


    @PostMapping(value = "/preview")
    public Object pageContentsPreview(@RequestParam(required = false, defaultValue = "SVG") String format,
                                      @RequestParam(required = false, defaultValue = "72") Integer dpi,
                                      @RequestParam(required = false, defaultValue = "true") String convert2Base64,
                                      @RequestParam(required = false, defaultValue = "1") String scale,
                                      @RequestParam(required = false, defaultValue = "true") String isFullPath,
                                      @RequestBody List<PageContent> pageContents) {
        boolean enable = previewCommonService.getPreviewEnable();
        if (enable) {
            // true -> 调用预览服务
            return previewCommonService.preview(format, dpi, convert2Base64, scale, isFullPath, pageContents);
        }
        // false -> 走原有流程
        return pageService.preview(format, dpi, convert2Base64, scale, isFullPath, pageContents);
    }

    @ApiOperation("使用PC数据生成缩略图V2")
    @PostMapping(value = "/preview/v2")
    public Object pageContentsPreviewV2(@RequestParam(required = false, defaultValue = "SVG") String format,
                                        @RequestParam(required = false, defaultValue = "72") Integer dpi,
                                        @RequestParam(required = false, defaultValue = "true") String convert2Base64,
                                        @RequestParam(required = false, defaultValue = "1") String scale,
                                        @RequestBody List<PageContentPreviewRequestDto> pageContentPreviewRequests) {
        return null;
    }

    @ApiOperation("通过key获取PC数据生成的缩略图")
    @GetMapping(value = "/preview/result")
    public List<PageContentPreviewResponseDto> pageContentsPreviewResult(@RequestParam List<String> keys) {
        return null;
    }

    @ApiOperation("批量获取PageContent")
    @GetMapping("/batch")
    public List<PageContent> batchGet(@RequestParam List<String> pcIds) {
        return pageContentBatchManager.batchGet(pcIds);
    }

    @ApiOperation("批量修改PageContent")
    @PutMapping("/batch")
    @OperationLog(description = "Batch update pcList {#params[pageContents].![_id]}", level = OperationLog.Level.WARN,
            operator = OperationLog.Operation.UPDATE,
            tags = {
                    @OperationLogTag(key = "id", value = "{#params[pageContents].![_id]}")
            })
    public void batchUpdate(@RequestBody List<PageContent> pageContents) {
        pageContentBatchManager.update(pageContents);
    }

    @ApiOperation("批量添加PageContent")
    @PostMapping("/batch")
    @OperationLog(description = "Batch save pcList {#result.![_id]}", level = OperationLog.Level.WARN,
            operator = OperationLog.Operation.CREATE,
            tags = {
                    @OperationLogTag(key = "id", value = "{#result.![_id]}")
            })
    public List<PageContent> batchInsert(@RequestBody List<PageContent> pageContents) {
        return null;
    }
}
