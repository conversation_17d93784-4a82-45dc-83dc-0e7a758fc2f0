package com.qpp.cgp.controller.bom;

import com.qpp.web.business.controller.AbstractJsonRestController;
import com.qpp.cgp.domain.bom.PageContentSchemaGroup;
import com.qpp.cgp.manager.bom.PageContentSchemaGroupManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by smart on 9/15/2017.
 */
@RestController
@RequestMapping(value = "api/pageContentSchemaGroups", produces = MediaType.APPLICATION_JSON_VALUE)
public class PageContentSchemaGroupController extends AbstractJsonRestController<PageContentSchemaGroup, String, PageContentSchemaGroupManager> {

    @Autowired
    public PageContentSchemaGroupController(PageContentSchemaGroupManager manager) {
        super(manager);
    }
    
}
