package com.qpp.cgp.controller.bom.runtime;

import com.qpp.cgp.domain.bom.datasource.VariableDataSource;
import com.qpp.cgp.manager.bom.runtime.VariableDataSourceManager;
import com.qpp.web.business.controller.AbstractJsonRestController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2019/12/23
 */
@RequestMapping("/api/variableDataSources")
@RestController
public class VariableDataSourceController extends AbstractJsonRestController<VariableDataSource, String, VariableDataSourceManager> {

    @Autowired
    public VariableDataSourceController(VariableDataSourceManager manager) {
        super(manager);
    }

}
