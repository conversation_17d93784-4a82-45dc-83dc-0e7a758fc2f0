package com.qpp.cgp.controller.bom.runtime;

import com.qpp.web.business.controller.AbstractJsonRestController;
import com.qpp.cgp.manager.bom.runtime.VariableDataSourceObjectManager;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by smart on 10/19/2017.
 */
@RestController
@RequestMapping(value = "api/variableDataSourceObjects", produces = MediaType.APPLICATION_JSON_VALUE)
public class VariableDataSourceObjectController extends AbstractJsonRestController<Document, String, VariableDataSourceObjectManager> {

    @Autowired
    public VariableDataSourceObjectController(VariableDataSourceObjectManager manager) {
        super(manager);
    }

}
