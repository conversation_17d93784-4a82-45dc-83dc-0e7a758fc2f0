package com.qpp.cgp.controller.bom;

import com.qpp.cgp.domain.bom.Material;
import com.qpp.cgp.domain.bom.MaterialCategory;
import com.qpp.cgp.domain.dto.bom.MaterialCategoryDTO;
import com.qpp.cgp.manager.bom.MaterialCategoryManager;
import com.qpp.core.acp.ACPAnnotation;
import com.qpp.core.dto.FilterDTO;
import com.qpp.core.dto.PageDTO;
import com.qpp.core.dto.SortDTO;
import com.qpp.operation.log.OperationLogModule;
import com.qpp.web.business.controller.AbstractJsonRestController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Created by smart on 9/1/2017.
 */
@OperationLogModule("materialCategories")
@RestController
@RequestMapping(value = "api/materialCategories", produces = MediaType.APPLICATION_JSON_VALUE)
public class MaterialCategoryRestController extends AbstractJsonRestController<MaterialCategory, String, MaterialCategoryManager> {
    @Autowired
    public MaterialCategoryRestController(MaterialCategoryManager manager) {

        super(manager);
    }

    /**
     * 获取分类的子分类
     *
     * @param materialCategoryId 分类id
     * @return
     */
    @RequestMapping(value = "{materialCategoryId}/children", method = RequestMethod.GET)
    public PageDTO<MaterialCategoryDTO> getMaterialCategoriesByParentId(@PathVariable String materialCategoryId,
                                                                        @RequestParam("page") int page,
                                                                        @RequestParam("limit") int limit,
                                                                        @RequestParam(required = false) String sort,
                                                                        @RequestParam(required = false) String filter) {

        Sort sorts = this.getSort(SortDTO.getOrders(sort));
        List<FilterDTO> filters = FilterDTO.getFilters(filter);
        int pageNumber = page - 1;
        int pageSize = limit;
        PageRequest pageable = PageRequest.of(pageNumber, pageSize, sorts);

        return manager.getChildrenMaterialCategoriesByParentId(materialCategoryId, pageable, filters);

    }


    /**
     * 获得指定分类下的所有物料的分页数据
     *
     * @param page               当前页
     * @param limit              每页数量
     * @param sort               排序
     * @param filter             过滤
     * @param materialCategoryId 分类id
     * @return
     */
    @ACPAnnotation(name = "物料",resourceName = "com.qpp.cgp.domain.dto.bom.MaterialDTO")
    @RequestMapping(value = "{materialCategoryId}/materials", method = RequestMethod.GET)
    public PageDTO<Material> getMaterialsByCategoryId(@RequestParam int page, @RequestParam int limit, @RequestParam(required = false) String sort,
                                                         @RequestParam(required = false) String filter, @PathVariable String materialCategoryId) {


        Sort sorts = this.getSort(SortDTO.getOrders(sort));
        List<FilterDTO> filters = FilterDTO.getFilters(filter);
        int pageNumber = 0;
        int pageSize = 0;
        pageNumber = Integer.valueOf(page) - 1;
        pageSize = Integer.valueOf(limit);

        PageRequest pageable = PageRequest.of(pageNumber, pageSize, sorts);
        PageDTO<Material> pageDTO = this.manager.getPageMaterialsByMaterialCategoryId(pageable, filters, materialCategoryId);

        return pageDTO;

    }

    @RequestMapping(value = "/{id}/path", method = RequestMethod.GET)
    public String getMaterialCategoryPath(@PathVariable String id) {
        return manager.getMaterialCategoryPathById(id);
    }

}
