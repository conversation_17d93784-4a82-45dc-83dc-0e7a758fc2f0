package com.qpp.cgp.manager.bom.runtime;

import com.qpp.cgp.domain.bom.datasource.VariableDataSource;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.driver.HybridMongoTemplate;
import com.qpp.web.business.manager.AbstractMongoCurdManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2019/12/23
 */
@Service
public class VariableDataSourceManager extends AbstractMongoCurdManager<VariableDataSource, String> {

    @Autowired
    public VariableDataSourceManager(HybridMongoTemplate mongoTemplate, IdGenerator idgenerator) {
        super(mongoTemplate, idgenerator);
    }

}
