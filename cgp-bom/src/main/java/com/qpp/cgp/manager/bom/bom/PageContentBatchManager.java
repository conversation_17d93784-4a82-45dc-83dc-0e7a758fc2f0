package com.qpp.cgp.manager.bom.bom;

import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.bom.runtime.PageContent;
import com.qpp.cgp.manager.bom.runtime.PageContentManager;
import com.qpp.cgp.service.common.CommonPropertiesService;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
public class PageContentBatchManager {
    @Autowired
    private PageContentManager pageContentManager;

    @Autowired
    private CommonPropertiesService commonPropertiesService;

    @Autowired
    private MongoTemplate mongoTemplate;

    private String LIMIT_PROPERTY_NAME = "pc.limit";

    public List<PageContent> batchGet(List<String> ids) {
        Set<String> idSet = new HashSet<>(ids);
        String limit = commonPropertiesService.getKeyValueAndApplicationModeForNoException(LIMIT_PROPERTY_NAME);
        if (Strings.isBlank(limit)) {
            limit = "100";
        }
        Integer limitInt = Integer.parseInt(limit);

        // 防止返回的数据太多导致接口卡顿，要求前端只能分批次查询
        if (idSet.size() > limitInt) {
            throw BusinessExceptionBuilder.of(103000001, ImmutableMap.of("size", idSet.size(), "limit", limitInt));
        }

        return pageContentManager.findByIds(idSet);
    }

    public void update(List<PageContent> pageContents) {
        BulkOperations bulkOperations = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, PageContent.class);
        for (PageContent pageContent : pageContents) {
            Query query = Query.query(Criteria.where("_id").is(pageContent.getId())
                    .orOperator(Criteria.where("isDeleted").exists(false), Criteria.where("isDeleted").is(false)));
            bulkOperations.replaceOne(query, pageContent);
        }
        bulkOperations.execute();
    }
}
