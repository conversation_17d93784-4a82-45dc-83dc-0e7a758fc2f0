package com.qpp.cgp.manager.bom.runtime;

import com.qpp.cgp.domain.bom.runtime.VariableDataSourceObject;
import com.qpp.id.generator.IdGenerator;
import com.qpp.web.business.manager.AbstractDocumentCurdManager;
import com.qpp.mongo.driver.HybridMongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;


/**
 * Created by smart on 10/19/2017.
 */
@Service

public class VariableDataSourceObjectManager extends AbstractDocumentCurdManager {
    public VariableDataSourceObjectManager(HybridMongoTemplate mongoTemplate, IdGenerator idGenerator) {
        super("variabledatasourceobjects", mongoTemplate, idGenerator);
    }

    public VariableDataSourceObject findVariableDataSourceObjectById(String id){
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").is(id));
        return mongoTemplate.findOne(query, VariableDataSourceObject.class);
    }

}
