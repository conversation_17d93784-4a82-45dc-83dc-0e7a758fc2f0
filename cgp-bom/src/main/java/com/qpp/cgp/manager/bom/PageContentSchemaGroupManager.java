package com.qpp.cgp.manager.bom;

import com.qpp.id.generator.IdGenerator;
import com.qpp.cgp.domain.bom.PageContentSchemaGroup;
import com.qpp.web.business.manager.AbstractMongoCurdManager;
import com.qpp.cgp.manager.permission.NeedPermission;
import com.qpp.mongo.driver.HybridMongoTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;


import java.util.List;

/**
 * Created by smart on 9/15/2017.
 */
@Service

@NeedPermission(resource = "PageContentSchemaGroup")
public class PageContentSchemaGroupManager extends AbstractMongoCurdManager<PageContentSchemaGroup, String> {


    @Autowired
    public PageContentSchemaGroupManager(HybridMongoTemplate mongoTemplate, IdGenerator idgenerator) {
        super(mongoTemplate, idgenerator);
    }

    public List<PageContentSchemaGroup> findByProductConfigDesignId(Long productConfigDesignId) {
         return mongoTemplate.find(Query.query(Criteria.where("productConfigDesignId")
                 .is(productConfigDesignId)
         ), PageContentSchemaGroup.class);
    }

}
