package com.qpp.cgp.manager.bom.runtime;

import com.qpp.cgp.domain.bom.runtime.PageContent;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.driver.HybridMongoTemplate;
import com.qpp.web.business.manager.AbstractMongoCurdManager;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR> Lee 2021/6/2 15:07
 */
@Service
public class PageContentDomainManager extends AbstractMongoCurdManager<PageContent,String> {

    public PageContentDomainManager(HybridMongoTemplate mongoTemplate, IdGenerator idgenerator) {
        super(mongoTemplate, idgenerator);
    }

    public Optional<PageContent> queryById(String id) {
        return Optional.ofNullable(mongoTemplate.findById(id, PageContent.class));
    }

}
