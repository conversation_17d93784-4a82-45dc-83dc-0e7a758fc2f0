package com.qpp.cgp.manager.bom.runtime;

import com.qpp.cgp.domain.dto.bom.pc.sequence.CreatePageContentSequenceDto;
import com.qpp.cgp.domain.dto.bom.pc.sequence.PageContentSequence;
import com.qpp.core.exception.BusinessException;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class PageContentSequenceManager {

    public PageContentSequence create(CreatePageContentSequenceDto createPageContentSequenceDto) {
        checkCreatePageContentSequenceDto(createPageContentSequenceDto);

        // TODO 暂时只实现球星卡序列生成
        List<Integer> targetIndexes;
        if ("51765662".equals(createPageContentSequenceDto.getRuleId())) {
            // 含 HeadBall
            List<Integer> newSequences = generateTeamSequence(18, 13);
            targetIndexes = insertHeadBall(newSequences, 18 * 13);
        } else {
            // 不含 HeadBall
            targetIndexes = generateTeamSequence(18, 4);
        }

        PageContentSequence pageContentSequence = new PageContentSequence();
        pageContentSequence.setTargetIndexes(targetIndexes);
        return pageContentSequence;
    }

    private void checkCreatePageContentSequenceDto(CreatePageContentSequenceDto createPageContentSequenceDto) {
        // TODO
    }

    /**
     * 生成球星卡序列（序号从 0 开始）
     *
     * <pre> 举例
     * 输入：
     *  numOfTeams: 4           4支球队
     *  numOfPlayersPerTeam: 4  每支球队4个球员
     *
     *  球队名称   球员编号
     *  球队0    [0,  1,  2,  3 ]
     *  球队1    [4,  5,  6,  7 ]
     *  球队2    [8,  9,  10, 11]
     *  球队3    [12, 13, 14, 15]
     *
     * 过程：
     * (1) 球队乱序排序 => [ 球队2, 球队0, 球队3, 球队1 ]
     * (2) 每支球队从没选择列表中随机挑选一位球员出来 => [ 9, 0, 13, 6 ]
     * (3) 重复(1)(2)步骤 numOfPlayersPerTeam 4 遍得到 4 行序列
     *  [
     *    [ 9, 0,  13, 6  ],
     *    [ 7, 3,  8,  15 ],
     *    [ 2, 4,  10, 12 ],
     *    [ 1, 11, 5,  14 ]
     *  ]
     * (4) 摊平 4 行序列进行输出
     *
     * 输出：
     *  [
     *    9, 0,  13, 6,
     *    7, 3,  8,  15,
     *    2, 4,  10, 12,
     *    1, 11, 5,  14
     *  ]
     * </pre>
     *
     * @param numOfTeams          球队数量
     * @param numOfPlayersPerTeam 每队球员的数量
     * @return
     */
    public List<Integer> generateTeamSequence(Integer numOfTeams, Integer numOfPlayersPerTeam) {
        checkToGenerateTeamSequence(numOfTeams, numOfPlayersPerTeam);

        Map<Integer, List<Integer>> teams = new HashMap<>();
        List<Integer> nameOfTeams = new LinkedList<>();
        int num = 0;

        // 初始化球队名称及球队内的球员编号
        for (int i = 0; i < numOfTeams; i++) {
            List<Integer> numOfPlayers = new LinkedList<>();
            for (int j = 0; j < numOfPlayersPerTeam; j++) {
                numOfPlayers.add(num);
                num++;
            }
            teams.put(i, numOfPlayers);
            nameOfTeams.add(i);
        }

        List<Integer> newSequences = new LinkedList<>();
        // 球队乱序排序
        Collections.shuffle(nameOfTeams);
        // 生成新的球队序列
        for (int z = 0; z < numOfPlayersPerTeam; z++) {
            List<Integer> sequences = nameOfTeams.stream()
                    .map(nameOfTeam -> {
                        List<Integer> originalSequences = teams.get(nameOfTeam);

                        int randomIndex;
                        Integer sequence;
                        // 从球队中随机挑选一位球员出来
                        randomIndex = (int) (Math.random() * originalSequences.size());
                        sequence = originalSequences.remove(randomIndex);

                        return sequence;
                    })
                    .collect(Collectors.toList());

            newSequences.addAll(sequences);
        }

        return newSequences;
    }

    public List<Integer> insertHeadBall(List<Integer> sequences, Integer numOfPlayer) {
        // 将 HeadBall 随机插入到打乱后的球队序列中
        sequences.add((int) (Math.random() * sequences.size()), numOfPlayer);
        return sequences;
    }

    private void checkToGenerateTeamSequence(Integer numOfTeams, Integer numOfPlayersPerTeam) {
        if (numOfTeams <= 0 || numOfPlayersPerTeam <= 0) {
            throw new BusinessException("numOfTeams and numOfPlayersPerTeam must be greater than 0");
        }
    }

}
