package com.qpp.cgp.manager.bom.attribute.checker;

import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.bom.Material;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

@Service
public class MaterialRtTypeChecker implements RtTypeChecker {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public void checkBeforeDelete(String id) {

        Criteria criteria = new Criteria().orOperator(
                Criteria.where("rtType._id").is(id),
                Criteria.where("spuRtType._id").is(id));


        Query query = Query.query(criteria);

        Material material = mongoTemplate.findOne(query, Material.class);

        if (null != material) {
            throw BusinessExceptionBuilder.of(1400010, ImmutableMap.of("rtTypeId", id, "materialId", material.getId()));
        }
    }


}
