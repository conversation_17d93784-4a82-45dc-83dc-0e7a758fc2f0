package com.qpp.cgp.manager.bom;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.qpp.mongo.domain.MongoDomain;
import org.reflections.Reflections;
import org.reflections.scanners.ResourcesScanner;
import org.reflections.scanners.SubTypesScanner;
import org.reflections.util.ClasspathHelper;
import org.reflections.util.ConfigurationBuilder;
import org.reflections.util.FilterBuilder;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Bom数据处理器
 * Created by smart on 8/26/2017.
 */
@Service
public class ClassFullNameMapManager {


    /**
     * 替换bom数据中的clazz为完整的类名（包含包名）
     *
     * @param bomData
     * @return
     */
    public String replaceClazzByFullName(String bomData) {

        DocumentContext context = JsonPath.parse(bomData);
        List<String> classes = context.read("$..clazz");

        Map<String, String> classMap = this.getClassFullNameMap("com.qpp.cgp.domain.bom");
        String jsonPath = "$..[?(@.clazz=='%s')]";
        for (String clazz : classes) {

            if (classMap.containsKey(clazz)) {
                context = context.put(String.format(jsonPath, clazz), "clazz", classMap.get(clazz));
            }
        }

        return context.jsonString();
    }


    /**
     * 获得类名和带包名的类名的映射关系
     *
     * @return
     */
    public Map<String, String> getClassFullNameMap(String basePackage) {

        List<ClassLoader> classLoadersList = new LinkedList<ClassLoader>();
        classLoadersList.add(ClasspathHelper.contextClassLoader());
        classLoadersList.add(ClasspathHelper.staticClassLoader());

        Reflections reflections = new Reflections(new ConfigurationBuilder()
                .setScanners(new SubTypesScanner(false /* don't exclude Object.class */), new ResourcesScanner())
                .setUrls(ClasspathHelper.forClassLoader(classLoadersList.toArray(new ClassLoader[0])))
                .filterInputsBy(new FilterBuilder().include(FilterBuilder.prefix(basePackage))));

        Set<Class<? extends MongoDomain>> classes = reflections.getSubTypesOf(MongoDomain.class);
        Map<String, String> classMap = classes.stream()
                .collect(Collectors.toMap(Class::getSimpleName, Class::getName));

        return classMap;
    }

    public static void main(String[] args) throws Exception {

        Map<String, String> clazzMap = new ClassFullNameMapManager().getClassFullNameMap("com.qpp.cgp.domain.product.attribute.constraint");
        String json = new ObjectMapper().writeValueAsString(clazzMap);
        System.out.println(json);

    }


}
