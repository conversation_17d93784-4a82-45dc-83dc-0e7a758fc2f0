package com.qpp.cgp.manager;

import com.qpp.cgp.domain.bom.runtime.PageContent;
import com.qpp.cgp.manager.application.ApplicationConfigService;
import com.qpp.cgp.manager.application.ApplicationMode;
import com.qpp.cgp.service.PagePreviewRemoteService;
import com.qpp.cgp.service.common.CommonPropertiesService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/24
 */
@Service
public class PreviewCommonService {

    private static final String PREVIEW_SERVICE_URL = "preview.service.url";
    private static final String PREVIEW_SERVICE_ENABLE = "preview.service.enable";

    @Autowired
    private ApplicationConfigService applicationConfigService;

    @Autowired
    private CommonPropertiesService commonPropertiesService;

    @Autowired
    private PagePreviewRemoteService pagePreviewRemoteService;

    public boolean getPreviewEnable() {
        ApplicationMode applicationMode = applicationConfigService.getApplicationMode();
        try {
            String value = commonPropertiesService.getKeyValueAndApplicationMode(PREVIEW_SERVICE_ENABLE, applicationMode);
            return Boolean.TRUE.toString().equalsIgnoreCase(value);
        } catch (Exception e) {
            // 不抛异常
            return false;
        }
    }

    public String getPreviewUrl() {
        ApplicationMode applicationMode = applicationConfigService.getApplicationMode();
        return commonPropertiesService.getKeyValueAndApplicationModeForNoException(PREVIEW_SERVICE_URL, applicationMode);
    }

    public String getPreviewUrl(String apiPath, String defaultUrl) {
        String previewUrl;
        ApplicationMode applicationMode = applicationConfigService.getApplicationMode();
        boolean enable = getPreviewEnable();
        // 是否启用
        if (enable) {
            // 配置
            previewUrl = commonPropertiesService.getKeyValueAndApplicationModeForNoException(PREVIEW_SERVICE_URL, applicationMode);
            if (StringUtils.isNotBlank(apiPath)) {
                previewUrl = previewUrl + apiPath;
            }
        } else {
            // 默认
            previewUrl = defaultUrl;
        }
        return previewUrl;
    }

    /**
     * 调用预览服务返回 PC 的绘制
     */
    public Object preview(String format, Integer dpi, String convert2Base64, String scale, String isFullPath,
                          List<PageContent> pageContents) {
        ApplicationMode applicationMode = applicationConfigService.getApplicationMode();
        String pagePreviewUrl = commonPropertiesService.getKeyValueAndApplicationModeForNoException(PREVIEW_SERVICE_URL, applicationMode);
        URI uri = URI.create(pagePreviewUrl);
        return pagePreviewRemoteService.preview(uri, format, dpi, convert2Base64, scale, isFullPath, pageContents);
    }

    public String previewSinglePc(String format, Integer dpi,
                                  String convert2Base64, String upload2FileServer,
                                  String scale, String isFullPath, PageContent pageContent) {
        ApplicationMode applicationMode = applicationConfigService.getApplicationMode();
        String pagePreviewUrl = commonPropertiesService.getKeyValueAndApplicationModeForNoException(PREVIEW_SERVICE_URL, applicationMode);
        URI uri = URI.create(pagePreviewUrl);
        return pagePreviewRemoteService.previewSinglePC(uri, format, dpi, convert2Base64, upload2FileServer, scale, isFullPath, pageContent);
    }

}
