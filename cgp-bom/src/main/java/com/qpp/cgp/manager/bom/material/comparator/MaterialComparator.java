package com.qpp.cgp.manager.bom.material.comparator;

import com.qpp.cgp.domain.bom.Material;
import com.qpp.cgp.domain.bom.bomitem.BOMItem;
import com.qpp.cgp.manager.bom.RtObjectComparator;

import javax.swing.text.html.Option;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * 物料比较器
 * Created by smart on 2018/3/13
 */
public class MaterialComparator implements Comparator<Material> {


    @Override
    public int compare(Material spu1, Material spu2) {

        //先检查RtObject是否相等
        boolean rtObjectEqual = false;
        if (spu1.getRtObject() == null && spu2.getRtObject() == null) {
            rtObjectEqual = true;
        } else if (spu1.getRtObject() == null || spu2.getRtObject() == null) {
            rtObjectEqual = false;
        } else {
            rtObjectEqual = new RtObjectComparator().compare(spu1.getRtObject(), spu2.getRtObject()) == 0;
        }

        //package qty是否相等
        boolean packageQtyEqual = false;
        Boolean spu1IsPackage = Optional.ofNullable(spu1.getIsPackage()).orElse(false);
        Boolean spu2IsPackage = Optional.ofNullable(spu2.getIsPackage()).orElse(false);

        if(!spu1IsPackage && !spu2IsPackage) {
            packageQtyEqual = true;
        } else if((spu1IsPackage && !spu2IsPackage) || (!spu1IsPackage && spu2IsPackage) ) {
            packageQtyEqual = false;
        } else {
            packageQtyEqual = spu1.getPackageQty() == spu2.getPackageQty();
        }



        //spuRtObject是否相等
        boolean spuRtTypeRtObjectEquals = false;
        if (spu1.getSpuRtTypeRtObject() == null && spu2.getSpuRtTypeRtObject() == null) {
            spuRtTypeRtObjectEquals = true;
        } else if (spu1.getSpuRtTypeRtObject() == null || spu2.getSpuRtTypeRtObject() == null) {
            spuRtTypeRtObjectEquals = false;
        } else {
            spuRtTypeRtObjectEquals = new RtObjectComparator().compare(spu1.getSpuRtTypeRtObject(), spu2.getSpuRtTypeRtObject()) == 0;
        }


        //再检查BOMItem是否相同
        List<String> checkIds = new ArrayList<>();

        List<BOMItem> bomItems = spu1.getChildItems();

        BOMItemComparator bomItemComparator = new BOMItemComparator();


        boolean bomItemMatched = true;

        //先检查BomItem的数量是否匹配
        final List<BOMItem> childItems1 = spu1.getChildItems();
        final List<BOMItem> childItems2 = spu2.getChildItems();

        //bomItem都为空
        if ((childItems1 == null || childItems1.size() == 0) && (childItems2 == null || childItems2.size() == 0)) {
            bomItemMatched = true;
        } else {

            if (childItems1 != null && childItems1.size() > 0) {

                if ((childItems2 == null || childItems2.size() != childItems1.size())) {
                    bomItemMatched = false;
                } else {

                    for (BOMItem bomItem : childItems1) {

                        boolean currentBOMItemMatched = false;

                        for (BOMItem bomItem1 : spu2.getChildItems()) {
                            if (checkIds.contains(bomItem1.getId())) {
                                continue;
                            }
                            currentBOMItemMatched = bomItemComparator.compare(bomItem1, bomItem) == 0;
                            if (currentBOMItemMatched) {
                                checkIds.add(bomItem1.getId());
                                break;
                            }
                        }

                        if (!currentBOMItemMatched) {
                            bomItemMatched = false;
                            break;
                        }
                    }
                }
            } else {
                if (spu2.getChildItems() != null && spu2.getChildItems().size() > 0) {
                    bomItemMatched = false;
                }
            }
        }


        return packageQtyEqual && rtObjectEqual && spuRtTypeRtObjectEquals && bomItemMatched ? 0 : 1;
    }
}
