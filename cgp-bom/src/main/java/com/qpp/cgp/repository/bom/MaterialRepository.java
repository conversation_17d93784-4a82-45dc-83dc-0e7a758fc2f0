package com.qpp.cgp.repository.bom;

import com.qpp.cgp.domain.bom.Material;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.driver.HybridMongoTemplate;
import com.qpp.web.business.manager.AbstractMongoCurdManager;
import org.springframework.stereotype.Service;

/**
 * Created by smart on 8/30/2017.
 */
@Service
public class MaterialRepository extends AbstractMongoCurdManager<Material, String> {

    public MaterialRepository(HybridMongoTemplate mongoTemplate, IdGenerator idgenerator) {
        super(mongoTemplate, idgenerator);
    }
}
