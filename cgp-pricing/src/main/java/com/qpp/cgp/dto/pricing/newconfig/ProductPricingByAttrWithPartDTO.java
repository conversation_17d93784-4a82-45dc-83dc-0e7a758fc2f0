package com.qpp.cgp.dto.pricing.newconfig;

import com.qpp.cgp.domain.pricing.newconfig.table.QtyPricingTableItemInterval;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import java.util.List;

@Data
public class ProductPricingByAttrWithPartDTO {

    private Long productId;

    private Long versionedAttributeId;

    private String propertyModelId;

    // 计价输入数据

    /**
     * 固定购买数量
     */
    @Min(1)
    private Integer qty;

    /**
     * 自定义数量区间表
     */
    @Valid
    private List<QtyPricingTableItemInterval> intervals;

}
