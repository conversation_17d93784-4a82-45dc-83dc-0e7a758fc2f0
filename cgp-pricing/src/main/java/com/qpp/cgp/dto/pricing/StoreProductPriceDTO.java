package com.qpp.cgp.dto.pricing;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class StoreProductPriceDTO {

    private String currencyCode;

    @ApiModelProperty(value = "金额数值", required = true)
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "显示金额", required = true)
    private String unitPriceString;

    private BigDecimal price;

    /**
     * qty * unitPrice
     */
    private String priceString;

}
