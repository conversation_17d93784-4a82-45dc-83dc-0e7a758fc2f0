package com.qpp.cgp.domain.pricing.entity;

import com.qpp.cgp.common.pricing.EntityEmptyException;
import com.qpp.cgp.common.pricing.EntityNotCorrectException;
import com.qpp.cgp.domain.pricing.CalculationExpression;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class ExpressionPricingFormulaEntity extends BaseEntity {

    private Integer index;

    private CalculationExpression formula;

    private List<ExpressionGeneratorEntity> factorGenerators;

    @Override
    public void check(Long pid, Class pclass) {
        super.check(pid, pclass);

        if (this.getIndex() == null) {
            throw new EntityEmptyException(this.getClass().getSimpleName(), this.get_id(), "index");
        }

        if (this.getFormula() == null) {
            throw new EntityEmptyException(this.getClass().getSimpleName(), this.get_id(), "formula");
        }

        if (this.getFormula().getValue() == null) {
            if (this.checkEmpty(this.getFormula().getType()) || this.checkEmpty(this.getFormula().getExpression())) {
                throw new EntityNotCorrectException(this.getClass().getSimpleName(), this.get_id(), "formula", pclass.getSimpleName(), pid);
            }
        }

        if (this.getFactorGenerators() != null && this.getFactorGenerators().size() > 0) {
            for (ExpressionGeneratorEntity generator : this.getFactorGenerators()) {
                generator.check(this.get_id(), this.getClass());
            }
        }
    }
}
