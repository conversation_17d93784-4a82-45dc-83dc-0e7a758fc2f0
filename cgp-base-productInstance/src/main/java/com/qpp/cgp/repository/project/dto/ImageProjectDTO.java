package com.qpp.cgp.repository.project.dto;

import com.qpp.cgp.domain.design.source.ViewDesignSource;
import com.qpp.cgp.repository.project.dto.v3.MaterialComponent;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by smart on 11/22/2017.
 */
@NoArgsConstructor
@Data
public class ImageProjectDTO {

    private Long productId;

    private Long versionedAttributeId;

    /**
     * 店铺产品Id
     */
    private String storeProductId;

    /**
     * 运行是通过店铺可配置产品生成的sku店铺产品Id
     */
    private String configurableSkuStoreProductId;

    private String comparisonThumbnail;

    private List<ImageProjectSideDTO> content = new ArrayList<>();

    private List<ImageProjectDesignDTO> designs = new ArrayList<>();

    private List<MaterialComponent> components = new ArrayList<>();

    /**
     * 定制模板来源<可选>
     */
    private List<ViewDesignSource> viewDesignSources;

    List<Map<String, Object>> photos;

    Map<String, Object> params;

}
