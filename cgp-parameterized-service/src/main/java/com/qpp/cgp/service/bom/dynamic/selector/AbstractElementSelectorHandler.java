package com.qpp.cgp.service.bom.dynamic.selector;

import com.qpp.cgp.domain.bom.dynamic.ElementSelector;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/9
 */
public abstract class AbstractElementSelectorHandler<T extends ElementSelector> {

    public abstract String select(String pageContentSchemaId, String pageContentSchemaJson,
                                  T elementSelector, Map<String, Object> attributes);

    public abstract boolean isMatch(ElementSelector elementSelector);
}
