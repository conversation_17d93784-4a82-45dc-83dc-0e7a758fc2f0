package com.qpp.cgp.service.bom.dynamic;

import com.qpp.cgp.domain.bom.dynamic.DynamicAttribute;
import com.qpp.cgp.domain.bom.dynamic.IAttributeValue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/7
 */
@Service
public class DynamicAttributeService {

    @Autowired
    private AttributeValueCalculateService attributeValueCalculateService;

    public Map<String, Object> calc(List<DynamicAttribute> dynamicAttributes, Map<String, Object> parameters) {
        Map<String, Object> attributes = new HashMap<>();
        dynamicAttributes.forEach(attribute -> {
            String name = attribute.getName();
            IAttributeValue attributeValue = attribute.getValue();
            Object value = attributeValueCalculateService.calc(attributeValue, parameters);
            attributes.put(name, value);
        });
        return attributes;
    }
}
