package com.qpp.cgp.service.parameterized;

import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.bom.dynamic.DynamicParameter;
import com.qpp.cgp.domain.parameterized.ArgumentToParameter;
import com.qpp.cgp.value.ValueEx;
import com.qpp.cgp.value.calculator.ValueExCalculateService;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class DynamicParametersCalculateService {

    @Autowired
    private ValueExCalculateService valueExCalculateService;


    /**
     * @param parameters 动态参数
     * @param arguments  实参值
     * @param context    计算实参值的上下文
     * com.qpp.cgp.service.bom.dynamic.PageContentSchemaDynamicSizeParameterService.calc
     */
    public Map<String, Object> calculate(List<DynamicParameter> parameters,
                                         List<ArgumentToParameter> arguments,
                                         Map<String, Object> context) {
        Map<String, Object> parametersCalculate = new HashMap<>();
        arguments.stream()
                .map(parameterConfig -> calcParameter(parameterConfig, context))
                .forEach(pair -> parametersCalculate.put(pair.getKey(), pair.getValue()));
        // 校验参数
        validateParameter(parameters, parametersCalculate);

        return parametersCalculate;
    }


    public ImmutablePair<String, Object> calcParameter(ArgumentToParameter argumentToParameter,
                                                       Map<String, Object> context) {
        String name = argumentToParameter.getName();
        ValueEx valueExpression = argumentToParameter.getValueExpression();
        Object value = valueExCalculateService.calculateV2(valueExpression, context, context);
        return new ImmutablePair<>(name, value);
    }


    /**
     * 校验计算结果parametersCalculate
     * @param dynamicParameters 计算参数
     * @param parametersCalculate 计算结果
     */
    private void validateParameter(List<DynamicParameter> dynamicParameters,
                                   Map<String, Object> parametersCalculate) {
        dynamicParameters.forEach(parameter -> {
            String parameterName = parameter.getName();
            if (!parametersCalculate.containsKey(parameterName)) {
                throw BusinessExceptionBuilder.of(2800005,
                        ImmutableMap.of("parameterName", parameterName));
            }
        });
    }

}
