package com.qpp.cgp.manager.parameterized;

import com.qpp.cgp.config.mongo.MongoTemplateBeanNames;
import com.qpp.cgp.domain.parameterized.ParameterizedConfig;
import com.qpp.cgp.domain.parameterized.ParameterizedEventType;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.driver.HybridMongoTemplate;
import com.qpp.web.business.manager.AbstractLongMongoCurdManager;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ParameterizedConfigManager extends AbstractLongMongoCurdManager<ParameterizedConfig, Long> {

    public ParameterizedConfigManager(@Qualifier(MongoTemplateBeanNames.CONFIG) HybridMongoTemplate mongoTemplate, IdGenerator idgenerator) {
        super(mongoTemplate, idgenerator);
    }

    @Override
    public ParameterizedConfig saveNew(ParameterizedConfig parameterizedConfig) {
        check(Collections.singletonList(parameterizedConfig));
        return super.saveNew(parameterizedConfig);
    }

    @Override
    public ParameterizedConfig saveUpdate(ParameterizedConfig parameterizedConfig, Long id) {
        parameterizedConfig.setId(id);
        check(Collections.singletonList(parameterizedConfig));
        return super.saveUpdate(parameterizedConfig, id);
    }


    public Optional<ParameterizedConfig> findByTargetIdAndEventType(Object targetId, ParameterizedEventType eventType) {
        Query query = Query.query(Criteria.where("target._id").is(targetId).and("eventType").is(eventType));
        return Optional.ofNullable(mongoTemplate.findOne(query, entityType));
    }

    public boolean existsByUniqueKey(Long selfId, Object targetId, ParameterizedEventType eventType) {
        // common
        Query query = Query.query(Criteria.where("target._id").is(targetId)
                .and("eventType").is(eventType)
        );
        if (Objects.nonNull(selfId)) {
            // update
            query.addCriteria(Criteria.where("_id").ne(selfId));
        }

        return mongoTemplate.exists(query, ParameterizedConfig.class);
    }

    public void check(List<ParameterizedConfig> configs) {
        configs.forEach(config -> {
            Long selfId = config.getId();
            Object targetId = config.getTarget().getId();
            ParameterizedEventType eventType = config.getEventType();
            boolean exists = this.existsByUniqueKey(selfId, targetId, eventType);
            if (exists) {
                // 抛异常，materialViewTypeId + name 不唯一
                Map<String, Object> errorParams = new HashMap<>();
                errorParams.put("targetId", targetId);
                errorParams.put("eventType", eventType);
                throw BusinessExceptionBuilder.of(3300009, errorParams);
            }
        });
    }


    /**
     * 获取ParameterizedConfig对象的id集合
     * @param pageContentSchemaIds
     * @param pcsPreprocessOperationConfigIds
     * @return
     */
    public Set<Long> findParameterizedConfigIds(Set<String> pageContentSchemaIds,
                                                Set<String> pcsPreprocessOperationConfigIds) {
        Set<Long> ids = new HashSet<>();

        List<ParameterizedConfig> parameterizedConfigs = mongoTemplate.find(Query.query(Criteria.where("target._id").in(pageContentSchemaIds)), ParameterizedConfig.class);
        parameterizedConfigs.addAll(mongoTemplate.find(Query.query(Criteria.where("target._id").in(pcsPreprocessOperationConfigIds)), ParameterizedConfig.class));

        if (!parameterizedConfigs.isEmpty()) {
            ids = parameterizedConfigs.stream()
                    .map(ParameterizedConfig :: get_id)
                    .collect(Collectors.toSet());
        }
        return ids;
    }
}
