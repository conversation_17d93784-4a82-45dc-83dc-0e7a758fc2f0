package com.qpp.cgp.controller.bom.dynamic;

import com.qpp.cgp.domain.bom.dynamic.DynamicAttributeGroup;
import com.qpp.cgp.manager.bom.dynamic.DynamicAttributeGroupManager;
import com.qpp.web.business.controller.AbstractJsonRestController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/1/9
 */
@RestController
@RequestMapping("/api/dynamicAttributeGroups")
public class DynamicAttributeGroupController
        extends AbstractJsonRestController<DynamicAttributeGroup, Long, DynamicAttributeGroupManager> {

    public DynamicAttributeGroupController(DynamicAttributeGroupManager manager) {
        super(manager);
    }
}
