package com.qpp.cgp.controller.parameterized;

import com.qpp.cgp.domain.parameterized.*;
import com.qpp.cgp.manager.parameterized.ParameterizedConfigManager;
import com.qpp.web.business.controller.AbstractJsonRestController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/api/parameterizedConfigs")
@RestController
@Validated
public class ParameterizedConfigController extends AbstractJsonRestController<ParameterizedConfig, Long, ParameterizedConfigManager> {

    public ParameterizedConfigController(ParameterizedConfigManager manager) {
        super(manager);
    }

}
