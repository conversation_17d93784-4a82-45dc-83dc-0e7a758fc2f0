topic.business.exception=business-exception
topic.system.error=system-error
topic.runtime.exception=runtime-exception
topic.unchecked.exception=unchecked-exception
topic.preprocess.exception=pre-process-exception
#project name
project.name=cgp-rest
#oauth server
oauth.server=http://*************:8888/cgp2-oauth/
oauth.tokenservice.clientid=cgpadmin
oauth.tokenservice.password=password
oauth.resource.id=CGP
#kafka
kafka.bootstrap.servers=*************:9092,*************:9093,*************:9094

#cgp database connection setting
jdbc.driverClassName=com.mysql.jdbc.Driver
jdbc.url=jdbc:mysql://*************:3306/cgp2_dev?useUnicode=true&characterEncoding=utf-8&useSSL=false
#jdbc.url=jdbc:mysql://*************:3306/cgp2_test?useUnicode=true&characterEncoding=utf-8&useSSL=false
jdbc.user=developer
jdbc.passwd=Dev!123a

hibernate.hbm2ddl.auto=validate
hibernate.show_sql=false
hibernate.dialect=org.hibernate.dialect.MySQL5Dialect

#mongodb database connection setting
mongodb.host=*************
mongodb.port=27017
mongodb.database=cgp2_dev
#mongodb.database=cgp2_test
mongodb.user=developer
mongodb.passwd=Dev!123a

##file-server
server.file=http://*************:8080/file/file
server.id.generator=http://*************:8888/cgp-rest/common/keyGenerate


cgp.admin.website.id=5


#reports director
reports.directory=classpath:reports/

##composing
printing.normal_notify_url=api/orders/ORDERID/printNotification
printing.order_url=api/orders/ORDERID/composing

cgp.frontend.server=http://*************:8888/cgp-rest/
printing.notify_url=api/orders/ORDERID/printNotification?statusId=STATUSID



##composing order item
order_item_printing.normal_notify_url=api/orderItems/ORDERITEMID/printNotification
order_item_printing.order_url=api/orderItems/ORDERITEMID/composing
order_item_printing.notify_url=api/orders/ORDERITEMID/printNotification?statusId=STATUSID
order_item_composing_url=api/composing/orderItem