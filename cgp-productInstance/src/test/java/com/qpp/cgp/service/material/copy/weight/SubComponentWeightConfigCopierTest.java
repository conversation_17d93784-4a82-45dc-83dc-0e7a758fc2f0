package com.qpp.cgp.service.material.copy.weight;

import com.qpp.cgp.domain.product.config.ProductConfigBom;
import com.qpp.cgp.domain.product.newconfig.v2.NewProductWeightConfig;
import com.qpp.cgp.domain.product.newconfig.v2.SubComponentProductWeightConfig;
import com.qpp.cgp.domain.product.newconfig.v2.SubComponentWeightConfigService;
import com.qpp.cgp.domain.product.weight.ProductWeightConfig;
import com.qpp.cgp.expression.Expression;
import com.qpp.cgp.manager.product.copier.product.NewProductWeightConfigCopier;
import com.qpp.cgp.manager.product.copier.product.OldProductWeightConfigCopier;
import com.qpp.cgp.manager.product.weight.ProductWeightConfigManager;
import com.qpp.cgp.manager.product.weight.v2.NewProductWeightConfigManager;
import com.qpp.cgp.service.material.copy.context.ProductSubMaterialCopyConfigContext;
import com.qpp.core.utils.ReplaceIdUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(MockitoJUnitRunner.class)
public class SubComponentWeightConfigCopierTest {
    @Spy
    @InjectMocks
    private SubComponentWeightConfigCopier subComponentWeightConfigCopier;

    @Mock
    private ReplaceIdUtil replaceIdUtil;

    @Mock
    private NewProductWeightConfigManager newProductWeightConfigManager;

    @Mock
    private NewProductWeightConfigCopier newProductWeightConfigCopier;

    @Mock
    private ProductWeightConfigManager productWeightConfigManager;

    @Mock
    private OldProductWeightConfigCopier oldProductWeightConfigCopier;

    @Mock
    private SubComponentWeightConfigService subComponentWeightConfigService;

    /**
     * 方法 ： copy
     * 验证 ： 验证调用指定次数的copyOne方法
     * */
    @Test
    public void testCopy(){
        List<SubComponentProductWeightConfig> subWeightConfigs = new ArrayList<>();
        subWeightConfigs.add(new SubComponentProductWeightConfig());
        subWeightConfigs.add(new SubComponentProductWeightConfig());
        Mockito.doReturn(new SubComponentProductWeightConfig()).when(subComponentWeightConfigCopier).copyOne(Mockito.any(), Mockito.any());

        subComponentWeightConfigCopier.copy(subWeightConfigs, new ProductSubMaterialCopyConfigContext());
        //验证
        Mockito.verify(subComponentWeightConfigCopier, Mockito.times(2)).copyOne(Mockito.any(), Mockito.any());
    }

    /**
     * 方法 ： copyOne
     * 验证 ： 当Condition存在时，验证调用了两次replaceId和一次replaceCode方法
     * */
    @Test
    public void testCopyOneByExistsCondition(){
        SubComponentProductWeightConfig subComponentProductWeightConfig = new SubComponentProductWeightConfig();
        subComponentProductWeightConfig.setSubBom(new ProductConfigBom(){{setId(2L);}});
        Expression condition = new Expression();
        subComponentProductWeightConfig.setCondition(condition);
        HashMap<String, Object> conditionDTO = new HashMap<>();
        subComponentProductWeightConfig.setConditionDTO(conditionDTO);

        ProductSubMaterialCopyConfigContext context = new ProductSubMaterialCopyConfigContext();
        context.setTargetBomId(1L);
        context.setStringIdMap(new HashMap<>());

        Mockito.when(replaceIdUtil.replaceId(Mockito.eq(condition),Mockito.anyMap())).thenReturn(condition);
        Mockito.when(replaceIdUtil.replaceId(Mockito.eq(conditionDTO),Mockito.anyMap())).thenReturn(conditionDTO);

        subComponentWeightConfigCopier.copyOne(subComponentProductWeightConfig, context);
        //验证
        Mockito.verify(replaceIdUtil, Mockito.times(2)).replaceId(Mockito.any(),Mockito.anyMap());
        Mockito.verify(subComponentWeightConfigCopier).replaceCode(Mockito.any(), Mockito.anyMap(), Mockito.anyMap());
    }

    /**
     * 方法 ： copyOne
     * 验证 ： 当Condition不存在时，验证未调用replaceId和replaceCode方法
     * */
    @Test
    public void testCopyOneByNotExistsCondition(){
        SubComponentProductWeightConfig subComponentProductWeightConfig = new SubComponentProductWeightConfig();
        subComponentProductWeightConfig.setSubBom(new ProductConfigBom(){{setId(2L);}});

        ProductSubMaterialCopyConfigContext context = new ProductSubMaterialCopyConfigContext();
        context.setTargetBomId(1L);
        context.setStringIdMap(new HashMap<>());

        subComponentWeightConfigCopier.copyOne(subComponentProductWeightConfig, context);
        //验证
        Mockito.verify(replaceIdUtil, Mockito.never()).replaceId(Mockito.any(),Mockito.anyMap());
        Mockito.verify(subComponentWeightConfigCopier, Mockito.never()).replaceCode(Mockito.any(), Mockito.anyMap(), Mockito.anyMap());
    }

    /**
     * 方法 ： copyOne
     * 验证 ： 当productWeightConfig不存在时，验证未调用新旧计重配置拷贝方法
     * */
    @Test
    public void testCopyOneByNotExistsProductWeightConfig(){
        SubComponentProductWeightConfig subComponentProductWeightConfig = new SubComponentProductWeightConfig();
        subComponentProductWeightConfig.setSubBom(new ProductConfigBom(){{setId(2L);}});
        subComponentProductWeightConfig.setFixWeight(2.0);

        ProductSubMaterialCopyConfigContext context = new ProductSubMaterialCopyConfigContext();
        context.setTargetBomId(1L);
        context.setStringIdMap(new HashMap<>());

        subComponentWeightConfigCopier.copyOne(subComponentProductWeightConfig, context);
        //验证
        Mockito.verify(newProductWeightConfigCopier,Mockito.never()).copy(Mockito.any(), Mockito.anyMap());
        Mockito.verify(oldProductWeightConfigCopier, Mockito.never()).copy(Mockito.any(), Mockito.anyMap());
    }

    /**
     * 方法 ： copyOne
     * 验证 ： 当productWeightConfig是新计重时，验证调用新计重配置拷贝方法
     * */
    @Test
    public void testCopyOneByExistsNewProductWeightConfig(){
        SubComponentProductWeightConfig subComponentProductWeightConfig = new SubComponentProductWeightConfig();
        subComponentProductWeightConfig.setSubBom(new ProductConfigBom(){{setId(2L);}});
        subComponentProductWeightConfig.setWeightConfig(Collections.singletonList(new NewProductWeightConfig(){{setId(3L);}}));
        subComponentProductWeightConfig.setFixWeight(2.0);

        ProductSubMaterialCopyConfigContext context = new ProductSubMaterialCopyConfigContext();
        context.setTargetBomId(1L);
        context.setStringIdMap(new HashMap<>());

        Mockito.when(newProductWeightConfigManager.findById(Long.valueOf(3))).thenReturn(new NewProductWeightConfig());

        subComponentWeightConfigCopier.copyOne(subComponentProductWeightConfig, context);
        //验证
        Mockito.verify(newProductWeightConfigCopier).copy(Mockito.any(), Mockito.anyMap());
        Mockito.verify(oldProductWeightConfigCopier, Mockito.never()).copy(Mockito.any(), Mockito.anyMap());
    }

    /**
     * 方法 ： copyOne
     * 验证 ： 当productWeightConfig是旧计重时，验证调用新计重配置拷贝方法
     * */
    @Test
    public void testCopyOneByExistsOldProductWeightConfig(){
        SubComponentProductWeightConfig subComponentProductWeightConfig = new SubComponentProductWeightConfig();
        subComponentProductWeightConfig.setSubBom(new ProductConfigBom(){{setId(2L);}});
        subComponentProductWeightConfig.setWeightConfig(Collections.singletonList(new ProductWeightConfig(){{setId(3L);}}));
        subComponentProductWeightConfig.setFixWeight(2.0);

        ProductSubMaterialCopyConfigContext context = new ProductSubMaterialCopyConfigContext();
        context.setTargetBomId(1L);
        context.setStringIdMap(new HashMap<>());

        Mockito.when(productWeightConfigManager.findById(Long.valueOf(3))).thenReturn(new ProductWeightConfig());

        subComponentWeightConfigCopier.copyOne(subComponentProductWeightConfig, context);
        //验证
        Mockito.verify(newProductWeightConfigCopier, Mockito.never()).copy(Mockito.any(), Mockito.anyMap());
        Mockito.verify(oldProductWeightConfigCopier).copy(Mockito.any(), Mockito.anyMap());
    }
}