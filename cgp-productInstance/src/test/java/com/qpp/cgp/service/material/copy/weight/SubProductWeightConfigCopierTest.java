package com.qpp.cgp.service.material.copy.weight;

import com.qpp.cgp.domain.dto.product.ProductDTO;
import com.qpp.cgp.domain.product.newconfig.v2.NewProductWeightConfig;
import com.qpp.cgp.domain.product.newconfig.v2.SubComponentWeightConfigService;
import com.qpp.cgp.domain.product.weight.ProductWeightConfig;
import com.qpp.cgp.manager.product.ProductManager;
import com.qpp.cgp.manager.product.copier.product.NewProductWeightConfigCopier;
import com.qpp.cgp.manager.product.copier.product.OldProductWeightConfigCopier;
import com.qpp.cgp.manager.product.weight.ProductWeightConfigManager;
import com.qpp.cgp.manager.product.weight.v2.NewProductWeightConfigManager;
import com.qpp.cgp.manager.product.weight.v2.SubComponentProductWeightConfigManager;
import com.qpp.cgp.service.material.copy.context.ProductSubMaterialCopyConfigContext;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(MockitoJUnitRunner.class)
public class SubProductWeightConfigCopierTest {

    @Spy
    @InjectMocks
    private SubProductWeightConfigCopier subProductWeightConfigCopier;

    @Mock
    private SubComponentProductWeightConfigManager subComponentProductWeightConfigManager;

    @Mock
    private NewProductWeightConfigManager newProductWeightConfigManager;

    @Mock
    private NewProductWeightConfigCopier newProductWeightConfigCopier;

    @Mock
    private SubComponentWeightConfigService subComponentWeightConfigService;

    @Mock
    private ProductWeightConfigManager productWeightConfigManager;

    @Mock
    private OldProductWeightConfigCopier oldProductWeightConfigCopier;

    @Mock
    private ProductManager productManager;

    @Mock
    private SubComponentWeightConfigCopier subComponentWeightConfigCopier;

    @Before
    public void setUp(){
        subProductWeightConfigCopier = new SubProductWeightConfigCopier();
        subProductWeightConfigCopier.setSubComponentProductWeightConfigManager(subComponentProductWeightConfigManager);
        subProductWeightConfigCopier.setNewProductWeightConfigManager(newProductWeightConfigManager);
        subProductWeightConfigCopier.setNewProductWeightConfigCopier(newProductWeightConfigCopier);
        subProductWeightConfigCopier.setSubComponentWeightConfigService(subComponentWeightConfigService);
        subProductWeightConfigCopier.setProductWeightConfigManager(productWeightConfigManager);
        subProductWeightConfigCopier.setOldProductWeightConfigCopier(oldProductWeightConfigCopier);
        subProductWeightConfigCopier.setProductManager(productManager);
        subProductWeightConfigCopier.setSubComponentWeightConfigCopier(subComponentWeightConfigCopier);
    }

    /**
     * 方法 ： copy
     * 测试 ： subBom不存在旧计重和新计重时，拷贝固定重量
     * */
    @Test
    public void testCopyBySubComponentHasNoOldWeightConfigAndNoNewWeightConfig(){
        SubProductWeightConfigCopier spy = Mockito.spy(subProductWeightConfigCopier);

        Mockito.when(newProductWeightConfigManager.findByProductIdAndVersionedProductAttributeId(11L,22L))
                .thenReturn(Optional.empty());
        Mockito.when(productWeightConfigManager.findByProductIdAndVersionedAttributeId(11L,22L))
                .thenReturn(new ArrayList<>());
        Mockito.when(productWeightConfigManager.findByProductId(11L))
                .thenReturn(new ArrayList<>());

        ProductDTO productDTO = new ProductDTO();
        productDTO.setWeight(20.0);

        Mockito.when(productManager.findById(11L)).thenReturn(productDTO);

        Mockito.doNothing().when(spy).markWeightConfig(Mockito.anyLong(),Mockito.anyLong());

        ProductSubMaterialCopyConfigContext context = new ProductSubMaterialCopyConfigContext();
        context.setSourceProductId(11L);
        context.setSourceVersionedAttributeId(22L);
        context.setTargetBomId(1L);
        context.setSourceBomId(2L);
        context.setTargetProductId(111L);
        context.setTargetVersionedAttributeId(222L);
        context.setMaterialPath("11,22,33");
        spy.copy(2L,1L, context);

        //验证调用
        Mockito.verify(subComponentWeightConfigService).createSubComponentProductWeightConfig(1L,2L,
                "11,22,33", null, null, null, 20.0);
        Mockito.verify(newProductWeightConfigCopier,Mockito.never()).copy(Mockito.any(), Mockito.anyMap());
        Mockito.verify(oldProductWeightConfigCopier, Mockito.never()).copy(Mockito.any(), Mockito.anyMap());
        Mockito.verify(subComponentWeightConfigCopier).copy(Mockito.any(), Mockito.any());
        Mockito.verify(spy).markWeightConfig(Mockito.anyLong(), Mockito.anyLong());
    }

    /**
     * 方法 ： copy
     * 测试 ： subBom存在旧计重，不存在新计重时，拷贝旧计重
     * */
    @Test
    public void testCopyBySubComponentHasOldWeightConfigButNoNewWeightConfig(){
        SubProductWeightConfigCopier spy = Mockito.spy(subProductWeightConfigCopier);

        Mockito.when(newProductWeightConfigManager.findByProductIdAndVersionedProductAttributeId(11L,22L))
                .thenReturn(Optional.empty());

        ProductWeightConfig productWeightConfig = new ProductWeightConfig();

        Mockito.when(productWeightConfigManager.findByProductIdAndVersionedAttributeId(11L,22L))
                .thenReturn(Collections.singletonList(productWeightConfig));
        Mockito.when(productWeightConfigManager.findByProductId(11L))
                .thenReturn(Collections.singletonList(productWeightConfig));
        Mockito.when(oldProductWeightConfigCopier.copy(Mockito.any(), Mockito.anyMap()))
                        .thenReturn(productWeightConfig);

        Mockito.doNothing().when(spy).markWeightConfig(Mockito.anyLong(),Mockito.anyLong());

        ProductSubMaterialCopyConfigContext context = new ProductSubMaterialCopyConfigContext();
        context.setSourceProductId(11L);
        context.setSourceVersionedAttributeId(22L);
        context.setTargetBomId(1L);
        context.setSourceBomId(2L);
        context.setTargetProductId(111L);
        context.setTargetVersionedAttributeId(222L);
        context.setMaterialPath("11,22,33");
        context.setStringIdMap(new HashMap<>());
        spy.copy(2L,1L, context);

        //验证调用
        Mockito.verify(subComponentWeightConfigService).createSubComponentProductWeightConfig(1L,2L,
                "11,22,33", null, Collections.singletonList(productWeightConfig),null, null);
        Mockito.verify(newProductWeightConfigCopier,Mockito.never()).copy(Mockito.any(), Mockito.anyMap());
        Mockito.verify(oldProductWeightConfigCopier).copy(Mockito.any(), Mockito.anyMap());
        Mockito.verify(productManager, Mockito.never()).findById(Mockito.anyLong());
        Mockito.verify(subComponentWeightConfigCopier).copy(Mockito.any(), Mockito.any());
        Mockito.verify(spy).markWeightConfig(Mockito.anyLong(), Mockito.anyLong());
    }

    /**
     * 方法 ： copy
     * 测试 ： subBom存在旧计重，同时存在新计重时，拷贝新计重
     * */
    @Test
    public void testCopyBySubComponentHasOldWeightConfigAndNewWeightConfig(){
        SubProductWeightConfigCopier spy = Mockito.spy(subProductWeightConfigCopier);

        NewProductWeightConfig newProductWeightConfig = new NewProductWeightConfig();
        Mockito.when(newProductWeightConfigManager.findByProductIdAndVersionedProductAttributeId(11L,22L))
                .thenReturn(Optional.of(newProductWeightConfig));
        Mockito.when(newProductWeightConfigCopier.copy(Mockito.any(),Mockito.anyMap()))
                .thenReturn(newProductWeightConfig);

        Mockito.doNothing().when(spy).markWeightConfig(Mockito.anyLong(),Mockito.anyLong());

        ProductSubMaterialCopyConfigContext context = new ProductSubMaterialCopyConfigContext();
        context.setSourceProductId(11L);
        context.setSourceVersionedAttributeId(22L);
        context.setTargetBomId(1L);
        context.setSourceBomId(2L);
        context.setTargetProductId(111L);
        context.setTargetVersionedAttributeId(222L);
        context.setMaterialPath("11,22,33");
        context.setStringIdMap(new HashMap<>());
        spy.copy(2L,1L, context);

        //验证调用
        Mockito.verify(subComponentWeightConfigService).createSubComponentProductWeightConfig(1L,2L,
                "11,22,33", null, Collections.singletonList(newProductWeightConfig),null, null);
        Mockito.verify(newProductWeightConfigCopier).copy(Mockito.any(), Mockito.anyMap());
        Mockito.verify(oldProductWeightConfigCopier, Mockito.never()).copy(Mockito.any(), Mockito.anyMap());
        Mockito.verify(productManager, Mockito.never()).findById(Mockito.anyLong());
        Mockito.verify(subComponentWeightConfigCopier).copy(Mockito.any(), Mockito.any());
        Mockito.verify(spy).markWeightConfig(Mockito.anyLong(), Mockito.anyLong());
    }

    /**
     * 方法 ： markWeightConfig
     * 测试 ： 当不存在计重配置
     * */
    @Test
    public void testMarkWeightConfigByHasNoOldWeightAndNoNewWeightConfig(){
        SubProductWeightConfigCopier spy = Mockito.spy(subProductWeightConfigCopier);

        Mockito.when(newProductWeightConfigManager.findByProductIdAndVersionedProductAttributeId(1L,2L))
                        .thenReturn(Optional.empty());
        Mockito.when(productWeightConfigManager.findByProductIdAndVersionedAttributeId(1L,2L))
                        .thenReturn(new ArrayList<>());
        Mockito.when(productWeightConfigManager.findByProductId(1L))
                        .thenReturn(new ArrayList<>());

        spy.markWeightConfig(1L,2L);
        //验证
        Mockito.verify(newProductWeightConfigManager, Mockito.never()).markSubComponentById(Mockito.anyLong(),Mockito.anyBoolean());
        Mockito.verify(productWeightConfigManager, Mockito.never()).markSubComponentById(Mockito.anyLong(), Mockito.anyBoolean());
    }

    /**
     * 方法 ： markWeightConfig
     * 测试 ： 当只存在旧计重配置
     * */
    @Test
    public void testMarkWeightConfigByHasOldWeightButNoNewWeightConfig(){
        SubProductWeightConfigCopier spy = Mockito.spy(subProductWeightConfigCopier);

        Mockito.when(newProductWeightConfigManager.findByProductIdAndVersionedProductAttributeId(1L,2L))
                        .thenReturn(Optional.empty());

        ProductWeightConfig productWeightConfig = new ProductWeightConfig();
        productWeightConfig.setId(11L);
        Mockito.when(productWeightConfigManager.findByProductIdAndVersionedAttributeId(1L,2L))
                        .thenReturn(Collections.singletonList(productWeightConfig));
        Mockito.when(productWeightConfigManager.findByProductId(1L))
                        .thenReturn(Collections.singletonList(productWeightConfig));
        Mockito.doNothing().when(productWeightConfigManager).markSubComponentById(Mockito.anyLong(), Mockito.anyBoolean());

        spy.markWeightConfig(1L,2L);
        //验证
        Mockito.verify(newProductWeightConfigManager, Mockito.never()).markSubComponentById(Mockito.anyLong(),Mockito.anyBoolean());
        Mockito.verify(productWeightConfigManager).markSubComponentById(Mockito.anyLong(), Mockito.anyBoolean());
    }

    /**
     * 方法 ： markWeightConfig
     * 测试 ： 同时存在新计重配置和旧计重配置
     * */
    @Test
    public void testMarkWeightConfigByHasOldWeightAndNewWeightConfig(){
        SubProductWeightConfigCopier spy = Mockito.spy(subProductWeightConfigCopier);

        NewProductWeightConfig newProductWeightConfig = new NewProductWeightConfig();
        newProductWeightConfig.setId(22L);
        Mockito.when(newProductWeightConfigManager.findByProductIdAndVersionedProductAttributeId(1L,2L))
                        .thenReturn(Optional.of(newProductWeightConfig));

        ProductWeightConfig productWeightConfig = new ProductWeightConfig();
        productWeightConfig.setId(11L);
        Mockito.when(productWeightConfigManager.findByProductIdAndVersionedAttributeId(Mockito.anyLong(),Mockito.anyLong()))
                        .thenReturn(Collections.singletonList(productWeightConfig));
        Mockito.when(productWeightConfigManager.findByProductId(1L))
                        .thenReturn(Collections.singletonList(productWeightConfig));

        spy.markWeightConfig(1L,2L);
        //验证
        Mockito.verify(newProductWeightConfigManager).markSubComponentById(Mockito.anyLong(),Mockito.anyBoolean());
        Mockito.verify(productWeightConfigManager).markSubComponentById(Mockito.anyLong(), Mockito.anyBoolean());
    }

}