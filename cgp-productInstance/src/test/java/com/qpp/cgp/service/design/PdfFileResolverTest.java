package com.qpp.cgp.service.design;

import com.qpp.cgp.domain.design.ImageInfo;
import com.qpp.core.exception.BusinessException;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @date 2024/5/27
 */
@ExtendWith(MockitoExtension.class)
public class PdfFileResolverTest {

    @Spy
    @InjectMocks
    private PdfFileResolver pdfFileResolver;

    @Test
    public void testResolveByImageFile() throws UnsupportedEncodingException {
        String pdfFile = this.getClass().getClassLoader().getResource("design/design_pdf.pdf").getPath();
        pdfFile = URLDecoder.decode(pdfFile, StandardCharsets.UTF_8.name()).substring(1);

        ImageInfo result = pdfFileResolver.resolve(new File(pdfFile),
                "http://test/file/file/design_pdf.pdf", "front");

        Assertions.assertThat(result.getWidth()).isEqualTo(220.82f);
        Assertions.assertThat(result.getHeight()).isEqualTo(350.79f);
        Assertions.assertThat(result.getFormat()).isEqualTo("pdf");
    }

    @Test
    public void testResolveByNoPdfFile() throws UnsupportedEncodingException {
        BusinessException businessException = new BusinessException();
        businessException.setMessage("Side front file http://test/file/file/no_pdf.pdf is not a valid pdf!");

        MockedStatic<BusinessExceptionBuilder> businessExceptionBuilderMocked
                = Mockito.mockStatic(BusinessExceptionBuilder.class);
        businessExceptionBuilderMocked.when(() -> BusinessExceptionBuilder.of(ArgumentMatchers.eq(5300002), any()))
                .thenReturn(businessException);

        String pdfFile = this.getClass().getClassLoader().getResource("design/no_pdf.pdf").getPath();
        pdfFile = URLDecoder.decode(pdfFile, StandardCharsets.UTF_8.name()).substring(1);

        String finalPdfFile = pdfFile;
        Assertions.assertThatThrownBy(() -> pdfFileResolver.resolve(new File(finalPdfFile),
                        "http://test/file/file/no_pdf.pdf", "front"))
                .hasMessage("Side front file http://test/file/file/no_pdf.pdf is not a valid pdf!");

        businessExceptionBuilderMocked.close();
    }

    @Test
    public void testResolveByPng() {
        Assertions.assertThat(pdfFileResolver.isMatch("png")).isFalse();
    }

    @Test
    public void testResolveByEmpty() {
        Assertions.assertThat(pdfFileResolver.isMatch("")).isFalse();
    }

    @Test
    public void testResolveByPdf() {
        Assertions.assertThat(pdfFileResolver.isMatch("pdf")).isTrue();
    }
}
