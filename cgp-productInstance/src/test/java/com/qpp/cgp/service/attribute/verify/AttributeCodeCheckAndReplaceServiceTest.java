package com.qpp.cgp.service.attribute.verify;

import com.qpp.cgp.dto.attribute.verify.AttrsCodeConflictInfo;
import com.qpp.cgp.dto.attribute.verify.AttrsCodeConflictInfoDTO;
import com.qpp.cgp.dto.attribute.verify.AttrsCodeConflictType;
import com.qpp.cgp.dto.bom.ProductConfigMaterialDTO;
import com.qpp.cgp.dto.attribute.verify.AttrsConflictCodeReplaceInfo;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Package com.qpp.cgp.service.attribute.verify
 * @Date 2024/6/21 10:50
 */
@ExtendWith(MockitoExtension.class)
public class AttributeCodeCheckAndReplaceServiceTest {

    @Spy
    @InjectMocks
    private AttributeCodeCheckAndReplaceService attributeCodeCheckAndReplaceService;

    /**
     * 根据子产品的冲突信息判断是否需要进行Code替换 : 此用例不存在冲突信息，不需要替换Code
     */
    @Test
    public void testGenerateReplaceInfoByNotNeedReplaceCode() {
        ProductConfigMaterialDTO dto = new ProductConfigMaterialDTO();
        AttrsConflictCodeReplaceInfo attrsConflictCodeReplaceInfo =
                attributeCodeCheckAndReplaceService.generateReplaceInfo(dto);

        Assertions.assertThat(attrsConflictCodeReplaceInfo.isNeedReplaceConfigAttrsCode()).isEqualTo(false);
        Assertions.assertThat(attrsConflictCodeReplaceInfo.isNeedReplaceDescAttrsCode()).isEqualTo(false);
        Assertions.assertThat(attrsConflictCodeReplaceInfo.isNeedReplaceProfileCode()).isEqualTo(false);
        Assertions.assertThat(attrsConflictCodeReplaceInfo.isNeedReplaceAMVTCode()).isEqualTo(false);
    }


    /**
     * 根据子产品的冲突信息判断是否需要进行Code替换 : 此用例存在冲突信息，需要替换Code
     */
    @Test
    public void testGenerateReplaceInfoByNeedReplaceCode() {
        ProductConfigMaterialDTO dto = generateDTO();
        AttrsConflictCodeReplaceInfo attrsConflictCodeReplaceInfo =
                attributeCodeCheckAndReplaceService.generateReplaceInfo(dto);

        Assertions.assertThat(attrsConflictCodeReplaceInfo.isNeedReplaceConfigAttrsCode()).isEqualTo(true);
        Assertions.assertThat(attrsConflictCodeReplaceInfo.isNeedReplaceDescAttrsCode()).isEqualTo(false);
        Assertions.assertThat(attrsConflictCodeReplaceInfo.isNeedReplaceProfileCode()).isEqualTo(true);
        Assertions.assertThat(attrsConflictCodeReplaceInfo.isNeedReplaceAMVTCode()).isEqualTo(false);
    }


    /**
     * 检验Code冲突是否已已经解决 ：该用例中Code冲突已解决
     */
    @Test
    public void testCheckCodeByNotExistsCodeConflict() {
        List<ProductConfigMaterialDTO> sourceConfigDTOs = new ArrayList<>();
        ProductConfigMaterialDTO dto = generateDTO();
        sourceConfigDTOs.add(dto);

        Assertions.assertThat(attributeCodeCheckAndReplaceService.checkCode(sourceConfigDTOs)).isEqualTo(true);
    }


    /**
     * 检验Code冲突是否已已经解决 ：该用例中仍存在Code冲突
     */
    @Test
    public void testCheckCodeByExistsCodeConflict() {
        List<ProductConfigMaterialDTO> sourceConfigDTOs = new ArrayList<>();
        ProductConfigMaterialDTO dto = generateDTOExistsCodeConflict();
        sourceConfigDTOs.add(dto);

        Assertions.assertThat(attributeCodeCheckAndReplaceService.checkCode(sourceConfigDTOs)).isEqualTo(false);
    }


    /**
     * 获取指定子产品Profile的Code替换信息
     * 用例2：不存在描述属性Code冲突时
     */
    @Test
    public void testGenerateReplaceInfoByTypeByNotExistsCodeConflict() {
        ProductConfigMaterialDTO dto = generateDTO();

        Map<String, String> replaceInfo = attributeCodeCheckAndReplaceService
                .generateReplaceInfo(dto, AttrsCodeConflictType.DESC);

        Assertions.assertThat(replaceInfo.size()).isEqualTo(0);
    }


    /**
     * 获取指定子产品Profile的Code替换信息
     * 用例1：存在Profile冲突Code时，可以成功生成替换信息
     */
    @Test
    public void testGenerateReplaceInfoByTypeByExistsCodeConflict() {
        ProductConfigMaterialDTO dto = generateDTO();

        Map<String, String> replaceInfo = attributeCodeCheckAndReplaceService
                .generateReplaceInfo(dto, AttrsCodeConflictType.PROFILE);

        Assertions.assertThat(replaceInfo.size()).isEqualTo(4);
        Assertions.assertThat(replaceInfo.get("D")).isEqualTo("H");
        Assertions.assertThat(replaceInfo.get("E")).isEqualTo("M");
        Assertions.assertThat(replaceInfo.get("F")).isEqualTo("N");
        Assertions.assertThat(replaceInfo.get("G")).isEqualTo("O");
    }


    //子产品,Code冲突已解决
    private ProductConfigMaterialDTO generateDTO() {
        //可配置属性的Code信息
        Map<String, AttrsCodeConflictInfo> configAttrsCodes = new HashMap<>();

        AttrsCodeConflictInfo infoOne = new AttrsCodeConflictInfo();
        infoOne.setNeedModify(false);
        infoOne.setCode("X");
        configAttrsCodes.put("A", infoOne);
        AttrsCodeConflictInfo infoTwo = new AttrsCodeConflictInfo();
        infoTwo.setNeedModify(false);
        infoTwo.setCode("Y");
        configAttrsCodes.put("B", infoTwo);
        AttrsCodeConflictInfo infoThree = new AttrsCodeConflictInfo();
        infoThree.setNeedModify(false);
        infoThree.setCode("Z");
        configAttrsCodes.put("C", infoThree);

        AttrsCodeConflictInfoDTO configAttrsCodeConflictInfoDTO = new AttrsCodeConflictInfoDTO();
        configAttrsCodeConflictInfoDTO.setAttrsCodeConflictType(AttrsCodeConflictType.CONFIG);
        configAttrsCodeConflictInfoDTO.setAttrsCodes(configAttrsCodes);

        //profile的Code信息
        Map<String, AttrsCodeConflictInfo> profileAttrsCodes = new HashMap<>();

        AttrsCodeConflictInfo infoFour = new AttrsCodeConflictInfo();
        infoFour.setNeedModify(false);
        infoFour.setCode("H");
        profileAttrsCodes.put("D", infoFour);
        AttrsCodeConflictInfo infoFive = new AttrsCodeConflictInfo();
        infoFive.setNeedModify(false);
        infoFive.setCode("M");
        profileAttrsCodes.put("E", infoFive);
        AttrsCodeConflictInfo infoSix = new AttrsCodeConflictInfo();
        infoSix.setNeedModify(false);
        infoSix.setCode("N");
        profileAttrsCodes.put("F", infoSix);
        AttrsCodeConflictInfo infoSeven = new AttrsCodeConflictInfo();
        infoSeven.setNeedModify(false);
        infoSeven.setCode("O");
        profileAttrsCodes.put("G", infoSeven);

        AttrsCodeConflictInfoDTO profileAttrsCodeConflictInfoDTO = new AttrsCodeConflictInfoDTO();
        profileAttrsCodeConflictInfoDTO.setAttrsCodeConflictType(AttrsCodeConflictType.PROFILE);
        profileAttrsCodeConflictInfoDTO.setAttrsCodes(profileAttrsCodes);

        List<AttrsCodeConflictInfoDTO> attrsCodeConflictInfoDTOS = new ArrayList<>();
        attrsCodeConflictInfoDTOS.add(configAttrsCodeConflictInfoDTO);
        attrsCodeConflictInfoDTOS.add(profileAttrsCodeConflictInfoDTO);

        ProductConfigMaterialDTO dto = new ProductConfigMaterialDTO();
        dto.setAttrsCodeConflictInfoDTOS(attrsCodeConflictInfoDTOS);

        return dto;
    }

    //子产品，code冲突未解决
    private ProductConfigMaterialDTO generateDTOExistsCodeConflict() {
        //可配置属性的Code信息
        Map<String, AttrsCodeConflictInfo> configAttrsCodes = new HashMap<>();

        AttrsCodeConflictInfo infoOne = new AttrsCodeConflictInfo();
        infoOne.setNeedModify(false);
        infoOne.setCode("X");
        configAttrsCodes.put("A", infoOne);
        AttrsCodeConflictInfo infoTwo = new AttrsCodeConflictInfo();
        infoTwo.setNeedModify(false);
        infoTwo.setCode("Y");
        configAttrsCodes.put("B", infoTwo);
        AttrsCodeConflictInfo infoThree = new AttrsCodeConflictInfo();
        infoThree.setNeedModify(true);
        infoThree.setCode("A");
        configAttrsCodes.put("C", infoThree);

        AttrsCodeConflictInfoDTO configAttrsCodeConflictInfoDTO = new AttrsCodeConflictInfoDTO();
        configAttrsCodeConflictInfoDTO.setAttrsCodeConflictType(AttrsCodeConflictType.CONFIG);
        configAttrsCodeConflictInfoDTO.setAttrsCodes(configAttrsCodes);

        //profile的Code信息
        Map<String, AttrsCodeConflictInfo> profileAttrsCodes = new HashMap<>();

        AttrsCodeConflictInfo infoFour = new AttrsCodeConflictInfo();
        infoFour.setNeedModify(false);
        infoFour.setCode("H");
        profileAttrsCodes.put("D", infoFour);
        AttrsCodeConflictInfo infoFive = new AttrsCodeConflictInfo();
        infoFive.setNeedModify(false);
        infoFive.setCode("M");
        profileAttrsCodes.put("E", infoFive);
        AttrsCodeConflictInfo infoSix = new AttrsCodeConflictInfo();
        infoSix.setNeedModify(false);
        infoSix.setCode("N");
        profileAttrsCodes.put("F", infoSix);
        AttrsCodeConflictInfo infoSeven = new AttrsCodeConflictInfo();
        infoSeven.setNeedModify(false);
        infoSeven.setCode("O");
        profileAttrsCodes.put("G", infoSeven);

        AttrsCodeConflictInfoDTO profileAttrsCodeConflictInfoDTO = new AttrsCodeConflictInfoDTO();
        profileAttrsCodeConflictInfoDTO.setAttrsCodeConflictType(AttrsCodeConflictType.PROFILE);
        profileAttrsCodeConflictInfoDTO.setAttrsCodes(profileAttrsCodes);

        List<AttrsCodeConflictInfoDTO> attrsCodeConflictInfoDTOS = new ArrayList<>();
        attrsCodeConflictInfoDTOS.add(configAttrsCodeConflictInfoDTO);
        attrsCodeConflictInfoDTOS.add(profileAttrsCodeConflictInfoDTO);

        ProductConfigMaterialDTO dto = new ProductConfigMaterialDTO();
        dto.setAttrsCodeConflictInfoDTOS(attrsCodeConflictInfoDTOS);

        return dto;
    }

}
