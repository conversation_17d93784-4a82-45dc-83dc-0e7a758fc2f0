package com.qpp.cgp.service.attribute.verify.amvt;

import com.qpp.cgp.domain.pcspreprocess.config.PageContentSchemaPreprocessConfig;
import com.qpp.cgp.domain.pcspreprocess.config.PageContentSchemaTemplatePreprocessConfig;
import com.qpp.cgp.service.attribute.verify.handle.amvt.AbstractReplaceAMVTCodeHandler;
import com.qpp.cgp.service.attribute.verify.handle.amvt.PageContentSchemaPreprocessConfigReplaceAMVTCodeHandler;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Package com.qpp.cgp.service.attribute.verify.amvt
 * @Date 2024/6/28 14:26
 */
@ExtendWith(MockitoExtension.class)
public class PageContentSchemaPreprocessConfigReplaceAMVTCodeHandlerTest {

    private List<AbstractReplaceAMVTCodeHandler> handlers;

    @BeforeEach
    public void init() {
        handlers = new ArrayList<>();
        PageContentSchemaPreprocessConfigReplaceAMVTCodeHandler handler =
                new PageContentSchemaPreprocessConfigReplaceAMVTCodeHandler();
        handlers.add(handler);
    }

    @Test
    public void handleTest() {
        PageContentSchemaPreprocessConfig config = generate();

        Map<String, String> replaceInfo = new HashMap<>();
        replaceInfo.put("card", "newCard");

        handlers.stream()
                .filter(handler -> handler.isMatch(config))
                .findFirst()
                .ifPresent(handler -> handler.handle(config, replaceInfo));

        Assertions.assertThat(config.getSourceMvtCode()).isEqualTo("newCard");
    }

    private PageContentSchemaPreprocessConfig generate() {
        PageContentSchemaPreprocessConfig config = new PageContentSchemaTemplatePreprocessConfig();
        config.setSourceMvtCode("card");
        return config;
    }

}
