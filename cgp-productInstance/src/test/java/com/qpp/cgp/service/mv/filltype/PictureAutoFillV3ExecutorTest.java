package com.qpp.cgp.service.mv.filltype;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jayway.jsonpath.JsonPath;
import com.qpp.cgp.dto.PictureAutoFillIndexRelationDTO;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

@RunWith(MockitoJUnitRunner.class)
public class PictureAutoFillV3ExecutorTest {

    @InjectMocks
    @Spy
    private PictureAutoFillV3Executor pictureAutoFillV3Executor;

    private String pcJson = "{\n" +
            "  \"_id\": \"42531712\",\n" +
            "  \"idReference\": \"PageContent\",\n" +
            "  \"clazz\": \"com.qpp.cgp.domain.bom.runtime.PageContent\",\n" +
            "  \"createdDate\": 1684980065684,\n" +
            "  \"createdBy\": \"856766\",\n" +
            "  \"modifiedDate\": 1684980065686,\n" +
            "  \"index\": \"0\",\n" +
            "  \"code\": \"Polyhedral Dice D4_V2-1\",\n" +
            "  \"name\": \"Polyhedral Dice D4_V2-1\",\n" +
            "  \"width\": 328.0,\n" +
            "  \"height\": 99.0,\n" +
            "  \"layers\": [\n" +
            "    {\n" +
            "      \"readOnly\": false,\n" +
            "      \"_id\": 14631895,\n" +
            "      \"clazz\": \"Layer\",\n" +
            "      \"items\": [\n" +
            "        {\n" +
            "          \"clazz\": \"Container\",\n" +
            "          \"_id\": 38899450,\n" +
            "          \"readOnly\": false,\n" +
            "          \"tags\": [\n" +
            "            \"customElement\"\n" +
            "          ],\n" +
            "          \"x\": 181,\n" +
            "          \"y\": 38,\n" +
            "          \"width\": 43,\n" +
            "          \"height\": 37,\n" +
            "          \"scale\": 1,\n" +
            "          \"items\": [\n" +
            "            {\n" +
            "              \"clazz\": \"Container\",\n" +
            "              \"_id\": 39023094,\n" +
            "              \"readOnly\": false,\n" +
            "              \"tags\": [\n" +
            "                \"customElement\"\n" +
            "              ],\n" +
            "              \"x\": 0,\n" +
            "              \"y\": 0,\n" +
            "              \"width\": 43,\n" +
            "              \"height\": 37,\n" +
            "              \"scale\": 1,\n" +
            "              \"items\": []\n" +
            "            },\n" +
            "            {\n" +
            "              \"clazz\": \"Picture\",\n" +
            "              \"_id\": 14631924,\n" +
            "              \"readOnly\": false,\n" +
            "              \"tags\": [],\n" +
            "              \"x\": 0,\n" +
            "              \"y\": 0,\n" +
            "              \"width\": 43,\n" +
            "              \"height\": 37,\n" +
            "              \"rotation\": 0,\n" +
            "              \"originalWidth\": 43,\n" +
            "              \"originalHeight\": 37,\n" +
            "              \"printFile\": \"\",\n" +
            "              \"imageName\": \"\"\n" +
            "            }\n" +
            "          ]\n" +
            "        },\n" +
            "      ],\n" +
            "      \"tags\": []\n" +
            "    }\n" +
            "  ],\n" +
            "  \"pageContentSchemaId\": \"38899414\",\n" +
            "  \"sortIndex\": 0,\n" +
            "  \"pageContentSchemaRuntimeId\": \"42531708\",\n" +
            "  \"isNeedPreset\": false,\n" +
            "  \"multilingualKey\": \"com.qpp.cgp.domain.bom.runtime.PageContent\"\n" +
            "}";

    private String pcJson2 = "{\n" +
            "  \"_id\": \"42531712\",\n" +
            "  \"idReference\": \"PageContent\",\n" +
            "  \"clazz\": \"com.qpp.cgp.domain.bom.runtime.PageContent\",\n" +
            "  \"createdDate\": 1684980065684,\n" +
            "  \"createdBy\": \"856766\",\n" +
            "  \"modifiedDate\": 1684980065686,\n" +
            "  \"index\": \"0\",\n" +
            "  \"code\": \"Polyhedral Dice D4_V2-1\",\n" +
            "  \"name\": \"Polyhedral Dice D4_V2-1\",\n" +
            "  \"width\": 328.0,\n" +
            "  \"height\": 99.0,\n" +
            "  \"layers\": [\n" +
            "    {\n" +
            "      \"readOnly\": false,\n" +
            "      \"_id\": 14631895,\n" +
            "      \"clazz\": \"Layer\",\n" +
            "      \"items\": [\n" +
            "        {\n" +
            "          \"clazz\": \"Container\",\n" +
            "          \"_id\": 38899450,\n" +
            "          \"readOnly\": false,\n" +
            "          \"tags\": [\n" +
            "            \"customElement\"\n" +
            "          ],\n" +
            "          \"x\": 181,\n" +
            "          \"y\": 38,\n" +
            "          \"width\": 43,\n" +
            "          \"height\": 37,\n" +
            "          \"scale\": 1,\n" +
            "          \"items\": [\n" +
            "              \n" +
            "            {\n" +
            "              \"clazz\": \"Container\",\n" +
            "              \"_id\": 39023094,\n" +
            "              \"readOnly\": false,\n" +
            "              \"tags\": [],\n" +
            "              \"x\": 0,\n" +
            "              \"y\": 0,\n" +
            "              \"width\": 43,\n" +
            "              \"height\": 37,\n" +
            "              \"scale\": 1,\n" +
            "              \"items\": [\n" +
            "                {\n" +
            "                  \"clazz\": \"Picture\",\n" +
            "                  \"_id\": 14631924,\n" +
            "                  \"readOnly\": false,\n" +
            "                  \"tags\": [],\n" +
            "                  \"x\": 0,\n" +
            "                  \"y\": 0,\n" +
            "                  \"width\": 43,\n" +
            "                  \"height\": 37,\n" +
            "                  \"rotation\": 0,\n" +
            "                  \"originalWidth\": 43,\n" +
            "                  \"originalHeight\": 37,\n" +
            "                  \"printFile\": \"\",\n" +
            "                  \"imageName\": \"\"\n" +
            "                },\n" +
            "                {\n" +
            "                  \"clazz\": \"Picture\",\n" +
            "                  \"_id\": 14631925,\n" +
            "                  \"readOnly\": false,\n" +
            "                  \"tags\": [],\n" +
            "                  \"x\": 0,\n" +
            "                  \"y\": 0,\n" +
            "                  \"width\": 43,\n" +
            "                  \"height\": 37,\n" +
            "                  \"rotation\": 0,\n" +
            "                  \"originalWidth\": 43,\n" +
            "                  \"originalHeight\": 37,\n" +
            "                  \"printFile\": \"\",\n" +
            "                  \"imageName\": \"\"\n" +
            "                }\n" +
            "              ]\n" +
            "            },\n" +
            "            {\n" +
            "              \"clazz\": \"Picture\",\n" +
            "              \"_id\": 14631926,\n" +
            "              \"readOnly\": false,\n" +
            "              \"tags\": [],\n" +
            "              \"x\": 0,\n" +
            "              \"y\": 0,\n" +
            "              \"width\": 43,\n" +
            "              \"height\": 37,\n" +
            "              \"rotation\": 0,\n" +
            "              \"originalWidth\": 43,\n" +
            "              \"originalHeight\": 37,\n" +
            "              \"printFile\": \"\",\n" +
            "              \"imageName\": \"\"\n" +
            "            }\n" +
            "            \n" +
            " \n" +
            "          ]\n" +
            "        }\n" +
            "      ],\n" +
            "      \"tags\": []\n" +
            "    }\n" +
            "  ],\n" +
            "  \"pageContentSchemaId\": \"38899414\",\n" +
            "  \"sortIndex\": 0,\n" +
            "  \"pageContentSchemaRuntimeId\": \"42531708\",\n" +
            "  \"isNeedPreset\": false,\n" +
            "  \"multilingualKey\": \"com.qpp.cgp.domain.bom.runtime.PageContent\"\n" +
            "}";


    private String pcJson3 = "{\n" +
            "  \"_id\": \"42531712\",\n" +
            "  \"idReference\": \"PageContent\",\n" +
            "  \"clazz\": \"com.qpp.cgp.domain.bom.runtime.PageContent\",\n" +
            "  \"createdDate\": 1684980065684,\n" +
            "  \"createdBy\": \"856766\",\n" +
            "  \"modifiedDate\": 1684980065686,\n" +
            "  \"index\": \"0\",\n" +
            "  \"code\": \"Polyhedral Dice D4_V2-1\",\n" +
            "  \"name\": \"Polyhedral Dice D4_V2-1\",\n" +
            "  \"width\": 328.0,\n" +
            "  \"height\": 99.0,\n" +
            "  \"layers\": [\n" +
            "    {\n" +
            "      \"readOnly\": false,\n" +
            "      \"_id\": 14631895,\n" +
            "      \"clazz\": \"Layer\",\n" +
            "      \"items\": [\n" +
            "        {\n" +
            "          \"clazz\": \"Container\",\n" +
            "          \"_id\": 38899450,\n" +
            "          \"readOnly\": false,\n" +
            "          \"tags\": [\n" +
            "            \"customElement\"\n" +
            "          ],\n" +
            "          \"x\": 181,\n" +
            "          \"y\": 38,\n" +
            "          \"width\": 43,\n" +
            "          \"height\": 37,\n" +
            "          \"scale\": 1,\n" +
            "          \"items\": [\n" +
            "              \n" +
            "            {\n" +
            "              \"clazz\": \"Container\",\n" +
            "              \"_id\": 39023094,\n" +
            "              \"readOnly\": false,\n" +
            "              \"tags\": [],\n" +
            "              \"x\": 0,\n" +
            "              \"y\": 0,\n" +
            "              \"width\": 43,\n" +
            "              \"height\": 37,\n" +
            "              \"scale\": 1,\n" +
            "              \"items\": [\n" +
            "                {\n" +
            "                  \"clazz\": \"Picture\",\n" +
            "                  \"_id\": 14631924,\n" +
            "                  \"readOnly\": false,\n" +
            "                  \"tags\": [],\n" +
            "                  \"x\": 0,\n" +
            "                  \"y\": 0,\n" +
            "                  \"width\": 43,\n" +
            "                  \"height\": 37,\n" +
            "                  \"rotation\": 0,\n" +
            "                  \"originalWidth\": 43,\n" +
            "                  \"originalHeight\": 37,\n" +
            "                  \"printFile\": \"\",\n" +
            "                  \"correct_index\": 0,\n" +
            "                  \"imageName\": \"\"\n" +
            "                },\n" +
            "                {\n" +
            "                  \"clazz\": \"Picture\",\n" +
            "                  \"_id\": 14631925,\n" +
            "                  \"readOnly\": false,\n" +
            "                  \"tags\": [],\n" +
            "                  \"x\": 0,\n" +
            "                  \"y\": 0,\n" +
            "                  \"width\": 43,\n" +
            "                  \"height\": 37,\n" +
            "                  \"rotation\": 0,\n" +
            "                  \"originalWidth\": 43,\n" +
            "                  \"originalHeight\": 37,\n" +
            "                  \"printFile\": \"\",\n" +
            "                  \"correct_index\": 1,\n" +
            "                  \"imageName\": \"\"\n" +
            "                }\n" +
            "              ]\n" +
            "            },\n" +
            "            {\n" +
            "              \"clazz\": \"Picture\",\n" +
            "              \"_id\": 14631926,\n" +
            "              \"readOnly\": false,\n" +
            "              \"tags\": [],\n" +
            "              \"x\": 0,\n" +
            "              \"y\": 0,\n" +
            "              \"width\": 43,\n" +
            "              \"height\": 37,\n" +
            "              \"rotation\": 0,\n" +
            "              \"originalWidth\": 43,\n" +
            "              \"originalHeight\": 37,\n" +
            "              \"printFile\": \"\",\n" +
            "                  \"correct_index\":2,\n" +
            "              \"imageName\": \"\"\n" +
            "            }\n" +
            "            \n" +
            " \n" +
            "          ]\n" +
            "        }\n" +
            "      ],\n" +
            "      \"tags\": []\n" +
            "    }\n" +
            "  ],\n" +
            "  \"pageContentSchemaId\": \"38899414\",\n" +
            "  \"sortIndex\": 0,\n" +
            "  \"pageContentSchemaRuntimeId\": \"42531708\",\n" +
            "  \"isNeedPreset\": false,\n" +
            "  \"multilingualKey\": \"com.qpp.cgp.domain.bom.runtime.PageContent\"\n" +
            "}";
    @Mock
    ObjectMapper objectMapper;

//    @Test
//    public void testAutoFillByFileOne() throws JsonProcessingException {
//        AtomicInteger index = new AtomicInteger(0);
//        String path = "$..[?(@.tags && 'customElement' in @.tags)]..[?(@.clazz==\"Picture\")]";
//        PageContent pageContent = new PageContent();
//        CycleAutoFillStrategyFileGenerator cycleAutoFillStrategyFileGenerator = new CycleAutoFillStrategyFileGenerator();
//        SequenceFile sequenceFile = new SequenceFile();
//        sequenceFile.setFileName("test.jpg");
//        sequenceFile.setWidth(22);
//        sequenceFile.setHeight(1);
//        cycleAutoFillStrategyFileGenerator.init(com.google.common.collect.Lists.newArrayList(sequenceFile), 1);
//
//        Mockito.when(objectMapper.writeValueAsString(refEq(pageContent)))
//                .thenReturn(pcJson);
//        ObjectMapper objectMapperIns = new ObjectMapper();
//
//        Mockito.when(objectMapper.readValue(anyString(), eq(PageContent.class)))
//                .thenAnswer((Answer<PageContent>) invocation ->
//                        objectMapperIns.readValue((String) invocation.getArguments()[0], PageContent.class));
//
//        PageContent result = pictureAutoFillV3Executor.autoFill(index, Sets.newHashSet(path), pageContent,
//                cycleAutoFillStrategyFileGenerator, "Picture");
//
//        String s = objectMapperIns.writeValueAsString(result);
//        Object read = JsonPath.parse(s).read(path);
//        Assertions.assertThat(read).isInstanceOf(List.class);
//        List list = (List) read;
//        Assertions.assertThat(list.size()).isEqualTo(1);
//        Map map = (Map) list.get(0);
//        Assertions.assertThat(map.get("imageName")).isEqualTo("test.jpg");
//    }
//
//    @Test
//    public void testAutoFillResultBetter2() throws JsonProcessingException {
//        AtomicInteger index = new AtomicInteger(0);
//        String path = "$..[?(@.tags && 'customElement' in @.tags)]..[?(@.clazz==\"Picture\")]";
//        PageContent pageContent = new PageContent();
//        CycleAutoFillStrategyFileGenerator cycleAutoFillStrategyFileGenerator = new CycleAutoFillStrategyFileGenerator();
//        SequenceFile sequenceFile = new SequenceFile();
//        sequenceFile.setFileName("test.jpg");
//        sequenceFile.setWidth(22);
//        sequenceFile.setHeight(1);
//        SequenceFile sequenceFile2 = new SequenceFile();
//        sequenceFile2.setFileName("test12.jpg");
//        sequenceFile2.setWidth(22);
//        sequenceFile2.setHeight(1);
//        cycleAutoFillStrategyFileGenerator.init(com.google.common.collect.Lists.newArrayList(sequenceFile, sequenceFile2), 2);
//
//        Mockito.when(objectMapper.writeValueAsString(refEq(pageContent)))
//                .thenReturn(pcJson2);
//        ObjectMapper objectMapperIns = new ObjectMapper();
//
//        Mockito.when(objectMapper.readValue(anyString(), eq(PageContent.class)))
//                .thenAnswer((Answer<PageContent>) invocation ->
//                        objectMapperIns.readValue((String) invocation.getArguments()[0], PageContent.class));
//
//        PageContent result =
//                pictureAutoFillV3Executor.autoFill(index, Sets.newHashSet(path), pageContent,
//                        cycleAutoFillStrategyFileGenerator, "Picture");
//
//        String s = objectMapperIns.writeValueAsString(result);
//        Object read = JsonPath.parse(s).read(path);
//        Assertions.assertThat(read).isInstanceOf(List.class);
//        List list = (List) read;
//        Assertions.assertThat(list.size()).isEqualTo(3);
//        Map map1 = (Map) list.get(0);
//        Map map2 = (Map) list.get(1);
//        Map map3 = (Map) list.get(2);
//        Assertions.assertThat(map1.get("imageName")).isIn("test.jpg");
//        Assertions.assertThat(map2.get("imageName")).isIn("test12.jpg");
//        Assertions.assertThat(map3.get("imageName")).isIn("test.jpg");
//    }

    @Test
    public void testAddFlagToPcAndCreateRelation() {
        String path = "$..[?(@.tags && 'customElement' in @.tags)]..[?(@.clazz==\"Picture\")]";
        PictureAutoFillIndexRelationDTO pictureAutoFillIndexRelationDTO =
                pictureAutoFillV3Executor.addFlagToPcAndCreateRelation(new AtomicInteger(0), path, pcJson3);
        Map<Integer, Integer> flagIndexToIndexMap = pictureAutoFillIndexRelationDTO.getFlagIndexToIndexMap();
        String pageContentJson = pictureAutoFillIndexRelationDTO.getPageContentJson();
        List<Map> readList = JsonPath.parse(pageContentJson).read(path);
        Assertions.assertThat(readList.size()).isEqualTo(3);
        for (Map map : readList) {
            Object correct_index = map.get("correct_index");
            Object flag_index = map.get("flag_index");
            Assertions.assertThat(correct_index).isEqualTo(flagIndexToIndexMap.get(flag_index));
        }
    }
}