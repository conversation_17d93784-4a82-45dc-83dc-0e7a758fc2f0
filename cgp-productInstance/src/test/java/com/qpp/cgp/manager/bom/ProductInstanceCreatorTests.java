package com.qpp.cgp.manager.bom;

import com.qpp.cgp.domain.productssuit.SkuProductSet;
import com.qpp.cgp.domain.suit.SkuProductSetInstance;
import com.qpp.core.exception.BusinessException;
import com.qpp.id.generator.IdGenerator;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
//@RunWith(SpringRunner.class)
//@SpringBootTest
public class ProductInstanceCreatorTests {

    @Mock
    private IdGenerator idGenerator;

    @InjectMocks
    private ProductInstanceCreator productInstanceCreator;

    @Mock
    private MongoTemplate mongoTemplate;

    @Rule
    public ExpectedException expectedException = ExpectedException.none();

    @Test
    public void testCreateSaleStuffInstanceByNull() {

        // invoke & assert
//        expectedException.expect(BusinessException.class);
//        expectedException.expectMessage("productSetId cannot is null");
        try {
            productInstanceCreator.createSaleStuffInstance(null, null, null, false, Optional.empty());
        } catch (BusinessException e) {
            assertEquals("productSetId cannot is null", e.getErrorParams().get("message"));
        }

    }

    @Test
    public void testCreateSaleStuffInstanceNotExists() {
        // mock behavior
        Mockito.when(mongoTemplate.findById(Mockito.any(), Mockito.any())).thenReturn(null);

        // invoke & assert
//        expectedException.expect(BusinessException.class);
//        expectedException.expectMessage("productSet entity is not exists");
        try {
            productInstanceCreator.createSaleStuffInstance(123L, null, null, false, Optional.empty());
        } catch (BusinessException e) {
            assertEquals("productSet entity is not exists", e.getErrorParams().get("message"));
        }
    }

    @Test
    public void testCreateSaleStuffInstanceRelateSkuProductSet() {

        Mockito.when(mongoTemplate.findById(Mockito.any(), Mockito.any())).thenReturn(new SkuProductSet());
        Mockito.doReturn(123L).when(idGenerator).generateId();

        // 使用@InjectMock注入的实例并不是mock对象，实例方法无法被mock，故新建一个ProductInstanceCreator的mock实例
        ProductInstanceCreator productInstanceCreator = Mockito.mock(ProductInstanceCreator.class);
        ReflectionTestUtils.setField(productInstanceCreator, "mongoTemplate", mongoTemplate);
        ReflectionTestUtils.setField(productInstanceCreator, "idGenerator", idGenerator);
//        Mockito.doReturn(new ProductInstance()).when(productInstanceCreator).createProductInstance(Mockito.anyLong(), Mockito.anyLong(), Mockito.anyList(), Mockito.anyBoolean(), Mockito.any(), Mockito.());
        Mockito.doCallRealMethod().when(productInstanceCreator).createSaleStuffInstance(Mockito.anyLong(), Mockito.anyLong(), Mockito.anyMap(), Mockito.anyBoolean(), Optional.empty());

        // 验证该产品套件实例关联的产品 - 实例关系字段是否存在
        Map<Long, List<Map<String, Object>>> photos = new HashMap<>();
        photos.put(1L, new ArrayList<>());
        SkuProductSetInstance saleStuffInstance = (SkuProductSetInstance) productInstanceCreator.createSaleStuffInstance(123L, 123L, photos, false, Optional.empty());
        assertNotNull(saleStuffInstance.getSaleStuffProductInstanceRefs());
        assertTrue(saleStuffInstance.getSaleStuffProductInstanceRefs().size() > 0);

    }

    @Test
    public void testCreateWhiteLabelProductInstance() {

        // 验证产品实例是否正确创建，包含套件实例的idRef
        // 无需验证，仅在原代码中加了一行set字段的代码

    }

}