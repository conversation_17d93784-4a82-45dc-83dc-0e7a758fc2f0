package com.qpp.cgp.manager.bom.image.integration;

import com.qpp.cgp.CgpProductInstanceTestApplication;
import com.qpp.cgp.manager.bom.pmv.ProductMaterialViewBuildContext;
import com.qpp.cgp.manager.bom.pmv.ProductMaterialViewBuildService;
import com.qpp.cgp.manager.bom.pmv.ProductMaterialViewBuilder;
import com.qpp.core.exception.BusinessException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.mock;

/**
 * <AUTHOR>
 * @Date 2021/4/22 14:02
 * @Version 1.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CgpProductInstanceTestApplication.class)
public class ProductMaterialViewBuildServiceTest {

    @Autowired
    private ProductMaterialViewBuildService productMaterialViewBuildService;

    @Test
    public void testBuildByContextNotMatching(){
        List<ProductMaterialViewBuilder> list = new ArrayList<>();
        ProductMaterialViewBuildContext context = mock(ProductMaterialViewBuildContext.class);
        // 置换
        ReflectionTestUtils.setField(productMaterialViewBuildService,"productMaterialViewBuilders",list);
        BusinessException ex = null;
        try{
            // 调用测试方法
            productMaterialViewBuildService.build(context);
        }catch (BusinessException e){
            // 断言
            Assert.assertEquals(400022,e.getCode());
            ex = e;
        }
        Assert.assertNotNull(ex);
    }
    @Test
    public void testBuilByContextMatching(){
        List<ProductMaterialViewBuilder> list = new ArrayList<>();
        ImageProjectDesignProductMaterialViewBuilder imageProjectDesignProductMaterialViewBuilder = new ImageProjectDesignProductMaterialViewBuilder();
        ImageProjectSideProductMaterialViewBuilder imageProjectSideProductMaterialViewBuilder = new ImageProjectSideProductMaterialViewBuilder();
        list.add(imageProjectDesignProductMaterialViewBuilder);
        list.add(imageProjectSideProductMaterialViewBuilder);
        ImageProjectDesignProductMaterialViewBuildContext context
                = new ImageProjectDesignProductMaterialViewBuildContext(null,
                null,null,null, "1,2,3");
        // 置换
        ReflectionTestUtils.setField(productMaterialViewBuildService,"productMaterialViewBuilders",list);
        BusinessException ex = null;
        try{
            // 调用测试方法
            productMaterialViewBuildService.build(context);
        }catch (BusinessException e){
            // 断言
            Assert.assertEquals(400023,e.getCode());
            ex = e;
        }
        Assert.assertNotNull(ex);
    }

}
