package com.qpp.cgp.manager.preprocess;

import com.qpp.cgp.config.mongo.MongoTemplateFactory;
import com.qpp.cgp.domain.preprocess.config.AbstractPreprocessConfig;
import com.qpp.cgp.domain.preprocess.config.MaterialViewTypePreprocessConfig;
import com.qpp.cgp.domain.preprocess.config.PageContentSourceConfig;
import com.qpp.cgp.domain.preprocess.config.SourceConfig;
import com.qpp.cgp.domain.simplifyBom.SimplifyMaterialViewType;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;


/**
 * mv预处理的测试用例
 * 测试点，测试是将MV数据组成PageContentPre
 */
public class MaterialViewPreprocessServiceTest {

    MaterialViewPreprocessService materialViewPreprocessService = new MaterialViewPreprocessService();
    //代理对应的私有方法
    Method filterSourceSimplifyMaterialViewPreprocess = PowerMockito.method(MaterialViewPreprocessService.class, "filterSourceSimplifyMaterialViewPreprocess", AbstractPreprocessConfig.class);

    @Test
    public void preprocessSuccessTest(){
        //校验是否
    }

    @Test
    public void convertPreprocessDTOTest() {
        //校验是否成功转化为对应的DTO

    }

    @Test
    public void filterSourceSimplifyMaterialViewPreprocessByExistsSimplifyMaterialViewTypeTrueTest() throws InvocationTargetException, IllegalAccessException {
        //测试是否过滤对应的数据源是SMV的预处理配置
        MaterialViewTypePreprocessConfig materialViewTypePreprocessConfig = new MaterialViewTypePreprocessConfig();
        List<SourceConfig> sourceConfigs = new ArrayList<>();
        PageContentSourceConfig pageContentSourceConfig = new PageContentSourceConfig();
        pageContentSourceConfig.setMaterialViewTypeId("123");
        sourceConfigs.add(pageContentSourceConfig);
        materialViewTypePreprocessConfig.setSourceMaterialViewTypes(sourceConfigs);
        //mock数据
        MongoTemplateFactory mongoTemplateFactory = PowerMockito.mock(MongoTemplateFactory.class);
        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
        Mockito.when(mongoTemplateFactory.getMongoTemplate(SimplifyMaterialViewType.class)).thenReturn(mongoTemplate);
        Mockito.when(mongoTemplate.exists(Query.query(Criteria.where("_id").is("123")), SimplifyMaterialViewType.class)).thenReturn(true);
        Object result = filterSourceSimplifyMaterialViewPreprocess.invoke(materialViewPreprocessService, materialViewTypePreprocessConfig);
        //验证是否调用mock
        Mockito.verify(mongoTemplateFactory, Mockito.times(1)).getMongoTemplate(SimplifyMaterialViewType.class);
        Mockito.verify(mongoTemplate, Mockito.times(1)).exists(Query.query(Criteria.where("_id").is("123")), SimplifyMaterialViewType.class);
        Assert.assertTrue(result instanceof Boolean);
        Assert.assertTrue((Boolean) result);

    }

}