//package com.qpp.cgp.manager.builder;
//
//import com.qpp.cgp.config.mongo.MongoTemplateFactory;
//import com.qpp.cgp.domain.attributecalculate.PropertyModel;
//import com.qpp.cgp.domain.bom.runtime.PageContent;
//import com.qpp.cgp.domain.bom.runtime.ProductInstance;
//import com.qpp.cgp.domain.buildercache.BuilderCache;
//import com.qpp.cgp.domain.dto.product.SkuAttributeValueDTO;
//import com.qpp.cgp.domain.product.Product;
//import com.qpp.cgp.domain.product.SkuProduct;
//import com.qpp.cgp.domain.simplifyBom.SBNodeRuntime;
//import com.qpp.cgp.domain.simplifyBom.SimplifyMaterialView;
//import com.qpp.cgp.dto.bom.InitProductInstanceDTO;
//import com.qpp.cgp.manager.bom.ProductInstanceManager;
//import com.qpp.cgp.manager.bom.ProductInstanceV3Generator;
//import com.qpp.cgp.manager.product.ProductManager;
//import com.qpp.cgp.manager.product.search.SkuProductSearchService;
//import com.qpp.cgp.repository.product.SkuProductRepository;
//import com.qpp.core.exception.BusinessException;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.Mockito;
//import org.powermock.api.mockito.PowerMockito;
//import org.powermock.core.classloader.annotations.PowerMockIgnore;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.modules.junit4.PowerMockRunner;
//import org.powermock.reflect.Whitebox;
//import org.springframework.beans.BeanUtils;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.data.mongodb.core.query.Criteria;
//import org.springframework.data.mongodb.core.query.Query;
//import org.springframework.test.util.ReflectionTestUtils;
//
//import java.lang.reflect.InvocationTargetException;
//import java.lang.reflect.Method;
//import java.util.*;
//
//import static org.mockito.ArgumentMatchers.any;
//
//@RunWith(PowerMockRunner.class)
//@PrepareForTest(InitProductInstanceManager.class)
//@PowerMockIgnore( {"javax.management.*","org.w3c.dom.*","org.apache.log4j.*","org.xml.sax.*",  "javax.xml.*"})
//public class InitProductInstanceManagerTest {
//
//    InitProductInstanceManager initProductInstanceManager = new InitProductInstanceManager();
//    Method replacePCBySimplifyMaterialViewMethod = PowerMockito.method(InitProductInstanceManager.class, "replacePCBySimplifyMaterialView", List.class, List.class);
//
//
//    @Test
//    public void replacePCBySimplifyMaterialViewTest() throws InvocationTargetException, IllegalAccessException {
//        MongoTemplateFactory mongoTemplateFactory = Mockito.mock(MongoTemplateFactory.class);
//        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
//        Whitebox.setInternalState(initProductInstanceManager, "mongoTemplateFactory", mongoTemplateFactory);
//        List<SimplifyMaterialView> oldSimplifyMaterialViews = new ArrayList<>();
//        List<PageContent> oldPCList = new ArrayList<>();
//        PageContent oldPageContent = new PageContent();
//        oldPageContent.setId("11");
//        oldPageContent.setName("old");
//        oldPCList.add(oldPageContent);
//        SimplifyMaterialView oldSimplifyMaterialView = new SimplifyMaterialView();
//        oldSimplifyMaterialView.setSimplifySBOMMaterialViewTypeId("123");
//        oldSimplifyMaterialView.setPageContents(oldPCList);
//        oldSimplifyMaterialViews.add(oldSimplifyMaterialView);
//        PageContent newPageContent = new PageContent();
//        newPageContent.setId("12");
//        newPageContent.setName("new");
//        List<PageContent> newPCList = new ArrayList<>();
//        newPCList.add(newPageContent);
//        List<SimplifyMaterialView> newSimplifyMaterialViews = new ArrayList<>();
//        SimplifyMaterialView newSimplifyMaterialView = new SimplifyMaterialView();
//        newSimplifyMaterialView.setPageContents(newPCList);
//        newSimplifyMaterialView.setSimplifySBOMMaterialViewTypeId("123");
//        newSimplifyMaterialViews.add(newSimplifyMaterialView);
//        //mock
//        Mockito.when(mongoTemplateFactory.getMongoTemplate(PageContent.class)).thenReturn(mongoTemplate);
//        PageContent replacePageContent = new PageContent();
//        Mockito.doAnswer(e -> {
//            PageContent argument = e.getArgument(0);
//            BeanUtils.copyProperties(argument, replacePageContent);
//            return null;
//        }).when(mongoTemplate).save(any());
//        Mockito.when(mongoTemplate.findById(oldPageContent.getId(), PageContent.class)).thenReturn(oldPageContent);
//        replacePCBySimplifyMaterialViewMethod.invoke(initProductInstanceManager, oldSimplifyMaterialViews, newSimplifyMaterialViews);
//        //verify
//        Mockito.verify(mongoTemplate, Mockito.times(1)).findById(oldPageContent.getId(), PageContent.class);
//        Mockito.verify(mongoTemplateFactory, Mockito.times(1)).getMongoTemplate(PageContent.class);
//        Mockito.verify(mongoTemplate, Mockito.times(1)).save(any());
//        //Test Verify
//        Assert.assertEquals(newPageContent.getId(), replacePageContent.getId());
//        Assert.assertEquals(oldPageContent.getName(), replacePageContent.getName());
//    }
//    Method checkOldSimplifyMaterialViewsAndNewSimplifyMaterialViewsMethod = PowerMockito.method(InitProductInstanceManager.class, "checkOldSimplifyMaterialViewsAndNewSimplifyMaterialViews", List.class, List.class);
//    List<SimplifyMaterialView> oldSimplifyMaterialViewsByCheck = new ArrayList<>();
//    List<SimplifyMaterialView> newSimplifyMaterialViewsByCheck = new ArrayList<>();
//
//    @Test
//    public void checkOldSimplifyMaterialViewsAndNewSimplifyMaterialViewsByCountExceptionTest(){
//        try {
//            SimplifyMaterialView oldSimplifyMaterialView = new SimplifyMaterialView();
//            SimplifyMaterialView oldSimplifyMaterialView1 = new SimplifyMaterialView();
//            SimplifyMaterialView newSimplifyMaterialView = new SimplifyMaterialView();
//            oldSimplifyMaterialViewsByCheck.add(oldSimplifyMaterialView);
//            oldSimplifyMaterialViewsByCheck.add(oldSimplifyMaterialView1);
//            newSimplifyMaterialViewsByCheck.add(newSimplifyMaterialView);
//            checkOldSimplifyMaterialViewsAndNewSimplifyMaterialViewsMethod.invoke(initProductInstanceManager, oldSimplifyMaterialViewsByCheck, newSimplifyMaterialViewsByCheck);
//        } catch (Exception e) {
//            Assert.assertNotNull(e);
//            if (e instanceof InvocationTargetException) {
//                Throwable targetException = ((InvocationTargetException) e).getTargetException();
//                Assert.assertTrue(targetException instanceof BusinessException);
//            }
//        }
//    }
//
//    @Test
//    public void checkOldSimplifyMaterialViewsAndNewSimplifyMaterialViewsByCheckMVTIdExceptionTest(){
//        try {
//            SimplifyMaterialView oldSimplifyMaterialView = new SimplifyMaterialView();
//            oldSimplifyMaterialView.setSimplifySBOMMaterialViewTypeId("123");
//            SimplifyMaterialView newSimplifyMaterialView = new SimplifyMaterialView();
//            newSimplifyMaterialView.setSimplifySBOMMaterialViewTypeId("111");
//            oldSimplifyMaterialViewsByCheck.add(oldSimplifyMaterialView);
//            newSimplifyMaterialViewsByCheck.add(newSimplifyMaterialView);
//            checkOldSimplifyMaterialViewsAndNewSimplifyMaterialViewsMethod.invoke(initProductInstanceManager, oldSimplifyMaterialViewsByCheck, newSimplifyMaterialViewsByCheck);
//        } catch (Exception e) {
//            Assert.assertNotNull(e);
//            if (e instanceof InvocationTargetException) {
//                Throwable targetException = ((InvocationTargetException) e).getTargetException();
//                Assert.assertTrue(targetException instanceof BusinessException);
//            }
//        }
//    }
//    Method getSimplifyMaterialViewsBySBNodeRuntimeMethod = PowerMockito.method(InitProductInstanceManager.class, "getSimplifyMaterialViewsBySBNodeRuntime", SBNodeRuntime.class);
//
//    @Test
//    public void getSimplifyMaterialViewsBySBNodeRuntimeTest() throws InvocationTargetException, IllegalAccessException {
//        SBNodeRuntime nodeRuntime = new SBNodeRuntime();
//        List<SimplifyMaterialView> rootSimplifyMaterialViews = new ArrayList<>();
//        SimplifyMaterialView simplifyMaterialViewRoot = new SimplifyMaterialView();
//        rootSimplifyMaterialViews.add(simplifyMaterialViewRoot);
//        nodeRuntime.setSimplifyMaterialViews(rootSimplifyMaterialViews);
//        List<SBNodeRuntime> child = new ArrayList<>();
//        SBNodeRuntime childNode = new SBNodeRuntime();
//        List<SimplifyMaterialView> childSimplifyMaterialViews = new ArrayList<>();
//        SimplifyMaterialView simplifyMaterialViewChildNode = new SimplifyMaterialView();
//        childSimplifyMaterialViews.add(simplifyMaterialViewChildNode);
//        childNode.setSimplifyMaterialViews(childSimplifyMaterialViews);
//        child.add(childNode);
//        nodeRuntime.setChild(child);
//        Object simplifyMaterialViews = getSimplifyMaterialViewsBySBNodeRuntimeMethod.invoke(initProductInstanceManager, nodeRuntime);
//        Assert.assertTrue(simplifyMaterialViews instanceof List);
//        ((List<?>) simplifyMaterialViews).forEach(simplifyMaterialView->{
//            Assert.assertNotNull(simplifyMaterialView);
//            Assert.assertTrue(simplifyMaterialView instanceof SimplifyMaterialView);
//        });
//        Assert.assertEquals(((List<?>) simplifyMaterialViews).size(), 2);
//    }
//
//    Method findSBNodeRuntimeByIdMethod = PowerMockito.method(InitProductInstanceManager.class, "findSBNodeRuntimeById", String.class);
//    @Test
//    public void findSBNodeRuntimeByIdTest() throws InvocationTargetException, IllegalAccessException {
//        MongoTemplateFactory mongoTemplateFactory = Mockito.mock(MongoTemplateFactory.class);
//        Whitebox.setInternalState(initProductInstanceManager, "mongoTemplateFactory", mongoTemplateFactory);
//        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
//        Mockito.when(mongoTemplateFactory.getMongoTemplate(SBNodeRuntime.class)).thenReturn(mongoTemplate);
//        SBNodeRuntime sbNodeRuntime = new SBNodeRuntime();
//        Mockito.when(mongoTemplate.findById("123", SBNodeRuntime.class)).thenReturn(sbNodeRuntime);
//        Object result = findSBNodeRuntimeByIdMethod.invoke(initProductInstanceManager, "123");
//        //verify
//        Mockito.verify(mongoTemplateFactory, Mockito.times(1)).getMongoTemplate(SBNodeRuntime.class);
//        Mockito.verify(mongoTemplate, Mockito.times(1)).findById("123", SBNodeRuntime.class);
//        Assert.assertEquals(sbNodeRuntime, result);
//    }
//
//    @Test
//    public void findSBNodeRuntimeByIdByNotFindExceptionTest() {
//        MongoTemplateFactory mongoTemplateFactory = Mockito.mock(MongoTemplateFactory.class);
//        Whitebox.setInternalState(initProductInstanceManager, "mongoTemplateFactory", mongoTemplateFactory);
//        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
//        Mockito.when(mongoTemplateFactory.getMongoTemplate(SBNodeRuntime.class)).thenReturn(mongoTemplate);
//        Mockito.when(mongoTemplate.findById("123", SBNodeRuntime.class)).thenReturn(null);
//        try {
//            findSBNodeRuntimeByIdMethod.invoke(initProductInstanceManager, "123");
//        } catch (Exception e) {
//            if (e instanceof InvocationTargetException) {
//                Throwable targetException = ((InvocationTargetException) e).getTargetException();
//                Assert.assertTrue(targetException instanceof BusinessException);
//            }
//        }
//        //verify
//        Mockito.verify(mongoTemplateFactory, Mockito.times(1)).getMongoTemplate(SBNodeRuntime.class);
//        Mockito.verify(mongoTemplate, Mockito.times(1)).findById("123", SBNodeRuntime.class);
//    }
//
//    @Test
//    public void getProductInstanceByIdTest() throws Exception {
//        //验证这次加的四个字段是否添加上去 photos name productId thumbnail
//        Method method = PowerMockito.method(InitProductInstanceManager.class, "getProductInstanceById", String.class, String.class, String.class, boolean.class);
//        ProductInstanceManager productInstanceManager = Mockito.mock(ProductInstanceManager.class);
//        InitProductInstanceManager spy = PowerMockito.spy(new InitProductInstanceManager());
//        Whitebox.setInternalState(spy, "productInstanceManager", productInstanceManager);
//        ProductInstance productInstance = new ProductInstance();
//        productInstance.setProductId(123L);
//        productInstance.setThumbnail("192.168.41.161");
//        productInstance.setName("name");
//        productInstance.setPropertyModelId("123");
//        BuilderCache builderCache = new BuilderCache();
//        builderCache.setRegion("Regin");
//        productInstance.setBuilderCache(builderCache);
//        List<Map<String, Object>> photos = new ArrayList<>();
//        Map<String, Object> photo = new HashMap<>();
//        photo.put("123", "test");
//        photos.add(photo);
//        productInstance.setPhotos(photos);
//        Mockito.when(productInstanceManager.findById("1233")).thenReturn(productInstance);
//        InitProductInstanceDTO initProductInstanceDTO = new InitProductInstanceDTO();
//        PowerMockito.doReturn(initProductInstanceDTO).when(spy, "getInitProductInstance", "123", "en", "zh", false);
//        PowerMockito.doReturn("123" ).when(spy, "copyPropertyModel", "123");
//        PowerMockito.doNothing().when(spy, "replacePCBySBNodeRunTime", null, null);
//        PowerMockito.doReturn(null).when(spy, "findSBNodeRuntimeById", null);
//        Object result = method.invoke(spy, "1233", "en", "zh", false);
//        Assert.assertNotNull(result);
//        Assert.assertTrue(result instanceof InitProductInstanceDTO);
//        InitProductInstanceDTO resultDTO = (InitProductInstanceDTO) result;
//        Assert.assertNotNull(resultDTO.getProductId());
//        Assert.assertNotNull(resultDTO.getThumbnail());
//        Assert.assertNotNull(resultDTO.getBuilderCache());
//        Assert.assertNotNull(resultDTO.getName());
//        Assert.assertNotNull(resultDTO.getPhotos());
//        Assert.assertEquals(initProductInstanceDTO.getProductId(),resultDTO.getProductId());
//        Assert.assertEquals(initProductInstanceDTO.getName(), resultDTO.getName());
//        Assert.assertEquals(initProductInstanceDTO.getPhotos(), resultDTO.getPhotos());
//        Assert.assertEquals(initProductInstanceDTO.getThumbnail(), resultDTO.getThumbnail());
//        Assert.assertEquals(initProductInstanceDTO.getBuilderCache(), resultDTO.getBuilderCache());
//        PowerMockito.verifyPrivate(spy, Mockito.times(1)).invoke("getInitProductInstance", "123", "en", "zh", false);
//        PowerMockito.verifyPrivate(spy, Mockito.times(1)).invoke("copyPropertyModel", "123");
//        PowerMockito.verifyPrivate(spy, Mockito.times(1)).invoke("replacePCBySBNodeRunTime", null, null);
//        PowerMockito.verifyPrivate(spy, Mockito.times(2)).invoke("findSBNodeRuntimeById", null);
//    }
//
//    @Test
//    public void setNameTest() throws InvocationTargetException, IllegalAccessException {
//        Method method = PowerMockito.method(ProductInstanceV3Generator.class, "setName", ProductInstance.class, String.class);
//        ProductInstanceV3Generator productInstanceV3Generator = new ProductInstanceV3Generator();
//        ProductInstance productInstance = new ProductInstance();
//        method.invoke(productInstanceV3Generator, productInstance, "张三");
//        Assert.assertEquals("张三", productInstance.getName());
//        Exception exception = null;
//        try {
//            method.invoke(productInstanceV3Generator, null, "张三");
//        } catch (Exception e) {
//            exception = e;
//        }
//        Assert.assertNotNull(exception);
//        Assert.assertTrue(exception instanceof InvocationTargetException);
//        Assert.assertNotNull(((InvocationTargetException) exception).getTargetException());
//        Assert.assertTrue(((InvocationTargetException) exception).getTargetException() instanceof BusinessException);
//        BusinessException businessExceptionError =(BusinessException) ((InvocationTargetException) exception).getTargetException();
//        Assert.assertNotNull(businessExceptionError);
//        Assert.assertEquals(2700801, businessExceptionError.getCode());
//    }
//
//    @Test
//    public void checkPropertyExistsTest(){
//        String propertyModelId = "123";
//        String propertyModelId2 = "111";
//        MongoTemplateFactory mongoTemplateFactory = Mockito.mock(MongoTemplateFactory.class);
//        Whitebox.setInternalState(initProductInstanceManager, "mongoTemplateFactory", mongoTemplateFactory);
//        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
//        Mockito.doReturn(mongoTemplate).when(mongoTemplateFactory).getMongoTemplate(PropertyModel.class);
//        Mockito.doReturn(false).when(mongoTemplate).exists(Query.query(Criteria.where("_id").is(propertyModelId)), PropertyModel.class);
//        Mockito.doReturn(true).when(mongoTemplate).exists(Query.query(Criteria.where("_id").is(propertyModelId2)), PropertyModel.class);
//        Exception exception = null;
//        try {
//            initProductInstanceManager.checkPropertyExists(propertyModelId);
//        } catch (Exception e) {
//            exception = e;
//        }
//        Exception exception1 = null;
//        try {
//            initProductInstanceManager.checkPropertyExists(propertyModelId2);
//        } catch (Exception e) {
//            exception1 = e;
//        }
//        Assert.assertNotNull(exception);
//        Assert.assertNull(exception1);
//        Mockito.verify(mongoTemplateFactory, Mockito.times(2)).getMongoTemplate(PropertyModel.class);
//        Mockito.verify(mongoTemplate, Mockito.times(1)).exists(Query.query(Criteria.where("_id").is(propertyModelId)), PropertyModel.class);
//        Mockito.verify(mongoTemplate, Mockito.times(1)).exists(Query.query(Criteria.where("_id").is(propertyModelId2)), PropertyModel.class);
//    }
//
//    /**
//     * 测试传入可配置产品id
//     */
//    @Test
//    public void calculateMaterialConfigurableProductTest(){
//        InitProductInstanceManager initProductInstanceManager = new InitProductInstanceManager();
//        // mock
//        ProductManager productManager = Mockito.mock(ProductManager.class);
//        ReflectionTestUtils.setField(initProductInstanceManager,"productManager",productManager);
//        MongoTemplate mongoTemplateConfig = Mockito.mock(MongoTemplate.class);
//        ReflectionTestUtils.setField(initProductInstanceManager,"mongoTemplateConfig",mongoTemplateConfig);
//        SkuProductSearchService skuProductSearchService = Mockito.mock(SkuProductSearchService.class);
//        ReflectionTestUtils.setField(initProductInstanceManager,"skuProductSearchService",skuProductSearchService);
//        // 参数
//        List<SkuAttributeValueDTO> attributeValues=new ArrayList<>();
//        Long productId=100L;
//        Long productConfigDesignId=101L;
//        Long configVersion=102l;
//        boolean isTest=true;
//
//        // when
//        Mockito.when(productManager.isPureSkuProductOrConfigProduct(productId)).thenReturn(true);
//        Mockito.when(mongoTemplateConfig.exists(Query.query(Criteria.where("_id").is(productId).and("clazz").is(SkuProduct.class.getName())), Product.class)).thenReturn(false);
//        SkuProduct skuProduct = new SkuProduct();
//        Optional<SkuProduct> skuProductOptional=Optional.of(skuProduct);
//        Mockito.when(skuProductSearchService.findByAttributeValueV3(productId, attributeValues, Optional.empty())).thenReturn(skuProductOptional);
//
//        try {
//            initProductInstanceManager.calculateMaterial(attributeValues,productId,productConfigDesignId,configVersion,isTest, Optional.empty());
//        }catch (Exception e){
//            Assert.assertNotNull(e);
//            Mockito.verify(productManager, Mockito.times(1)).isPureSkuProductOrConfigProduct(productId);
//            Mockito.verify(mongoTemplateConfig, Mockito.times(1)).exists(Query.query(Criteria.where("_id").is(productId).and("clazz").is(SkuProduct.class.getName())), Product.class);
//            Mockito.verify(skuProductSearchService, Mockito.times(1)).findByAttributeValueV3(productId, attributeValues, Optional.empty());
//
//        }
//    }
//
//    /**
//     * 测试传入纯skuProductId
//     */
//    @Test
//    public void calculateMaterialSkuProductIdTest(){
//        InitProductInstanceManager initProductInstanceManager = new InitProductInstanceManager();
//        // mock
//        ProductManager productManager = Mockito.mock(ProductManager.class);
//        ReflectionTestUtils.setField(initProductInstanceManager,"productManager",productManager);
//        MongoTemplate mongoTemplateConfig = Mockito.mock(MongoTemplate.class);
//        ReflectionTestUtils.setField(initProductInstanceManager,"mongoTemplateConfig",mongoTemplateConfig);
//        SkuProductSearchService skuProductSearchService = Mockito.mock(SkuProductSearchService.class);
//        ReflectionTestUtils.setField(initProductInstanceManager,"skuProductSearchService",skuProductSearchService);
//        SkuProductRepository skuProductRepository = Mockito.mock(SkuProductRepository.class);
//        ReflectionTestUtils.setField(initProductInstanceManager,"skuProductRepository",skuProductRepository);
//        // 参数
//        List<SkuAttributeValueDTO> attributeValues=new ArrayList<>();
//        Long productId=100L;
//        Long productConfigDesignId=101L;
//        Long configVersion=102l;
//        boolean isTest=true;
//
//        // when
//        Mockito.when(productManager.isPureSkuProductOrConfigProduct(productId)).thenReturn(true);
//        Mockito.when(mongoTemplateConfig.exists(Query.query(Criteria.where("_id").is(productId).and("clazz").is(SkuProduct.class.getName())), Product.class)).thenReturn(true);
//        SkuProduct skuProduct = new SkuProduct();
//        Optional<SkuProduct> skuProductOptional=Optional.of(skuProduct);
//        Mockito.when(skuProductSearchService.findByAttributeValueV3(productId, attributeValues, Optional.empty())).thenReturn(skuProductOptional);
//        SkuProduct skuProduct1 =new SkuProduct();
//        Mockito.when(skuProductRepository.getOne(productId)).thenReturn(skuProduct1);
//
//        try {
//            initProductInstanceManager.calculateMaterial(attributeValues,productId,productConfigDesignId,configVersion,isTest, Optional.empty());
//        }catch (Exception e){
//            Assert.assertNotNull(e);
//            Mockito.verify(productManager, Mockito.times(1)).isPureSkuProductOrConfigProduct(productId);
//            Mockito.verify(mongoTemplateConfig, Mockito.times(1)).exists(Query.query(Criteria.where("_id").is(productId).and("clazz").is(SkuProduct.class.getName())), Product.class);
//            Mockito.verify(skuProductSearchService, Mockito.times(0)).findByAttributeValueV3(productId, attributeValues, Optional.empty());
//            Mockito.verify(skuProductRepository, Mockito.times(1)).getOne(productId);
//
//        }
//    }
//
//
//}