package com.qpp.cgp.service.file.library;

import com.qpp.cgp.domain.file.library.FileLibrary;
import com.qpp.cgp.domain.file.library.FileLibraryElement;
import com.qpp.cgp.domain.file.library.FileObject;
import com.qpp.cgp.manager.file.library.FileLibraryElementManager;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;

@RunWith(MockitoJUnitRunner.class)
public class FileLibraryServiceV2Test {
    @Spy
    @InjectMocks
    private FileLibraryServiceV2 fileLibraryServiceV2;

    @Mock
    private FileLibraryElementManager fileLibraryElementManager;

    @Test
    public void testFindFileLibraryByCatalogId() {
        FileLibraryElement fileLibraryElement = new FileObject();
        fileLibraryElement.setParentPath("/123/333");

        Mockito.when(fileLibraryElementManager.findById(any()))
                .thenReturn(fileLibraryElement);
        Mockito.when(fileLibraryElementManager.getLibraryById(any()))
                .thenReturn(new FileLibrary());

        ArgumentCaptor<String> stringArgumentCaptor = ArgumentCaptor.forClass(String.class);


        fileLibraryServiceV2.findFileLibraryByCatalogId("11");
        Mockito.verify(fileLibraryElementManager).getLibraryById(stringArgumentCaptor.capture());

        Assertions.assertThat(stringArgumentCaptor.getValue()).isEqualTo("123");
    }

    @Test
    public void testFindFileLibraryByCatalogId2() {
        FileLibraryElement fileLibraryElement = new FileObject();
        fileLibraryElement.setParentPath("123/333");

        Mockito.when(fileLibraryElementManager.findById(any()))
                .thenReturn(fileLibraryElement);
        Mockito.when(fileLibraryElementManager.getLibraryById(any()))
                .thenReturn(new FileLibrary());

        ArgumentCaptor<String> stringArgumentCaptor = ArgumentCaptor.forClass(String.class);


        fileLibraryServiceV2.findFileLibraryByCatalogId("11");
        Mockito.verify(fileLibraryElementManager).getLibraryById(stringArgumentCaptor.capture());

        Assertions.assertThat(stringArgumentCaptor.getValue()).isEqualTo("123");
    }


    @Test
    public void testFindFileLibraryByCatalogId3() {
        FileLibraryElement fileLibraryElement = new FileObject();
        fileLibraryElement.setParentPath("/");

        Mockito.when(fileLibraryElementManager.findById(any()))
                .thenReturn(fileLibraryElement);
        Mockito.when(fileLibraryElementManager.getLibraryById(any()))
                .thenReturn(new FileLibrary());

        ArgumentCaptor<String> stringArgumentCaptor = ArgumentCaptor.forClass(String.class);


        fileLibraryServiceV2.findFileLibraryByCatalogId("11");
        Mockito.verify(fileLibraryElementManager).getLibraryById(stringArgumentCaptor.capture());

        Assertions.assertThat(stringArgumentCaptor.getValue()).isEqualTo("11");
    }

    @Test
    public void testFindFileLibraryByCatalogId4() {
        FileLibraryElement fileLibraryElement = new FileObject();
        fileLibraryElement.setParentPath("");

        Mockito.when(fileLibraryElementManager.findById(any()))
                .thenReturn(fileLibraryElement);
        Mockito.when(fileLibraryElementManager.getLibraryById(any()))
                .thenReturn(new FileLibrary());

        ArgumentCaptor<String> stringArgumentCaptor = ArgumentCaptor.forClass(String.class);


        fileLibraryServiceV2.findFileLibraryByCatalogId("11");
        Mockito.verify(fileLibraryElementManager).getLibraryById(stringArgumentCaptor.capture());

        Assertions.assertThat(stringArgumentCaptor.getValue()).isEqualTo("11");
    }
}