//package com.qpp.cgp.service.file;
//
//import com.qpp.cgp.domain.file.library.FileCatalog;
//import com.qpp.cgp.dto.file.library.FileOperator;
//import org.assertj.core.api.Assertions;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.*;
//import org.mockito.junit.MockitoJUnitRunner;
//import org.springframework.util.ReflectionUtils;
//
//import java.lang.reflect.Field;
//import java.util.LinkedList;
//import java.util.List;
//import java.util.Optional;
//
//import static org.mockito.ArgumentMatchers.any;
//
//@RunWith(MockitoJUnitRunner.class)
//public class FileLibraryCreateServiceTest {
//    @Spy
//    @InjectMocks
//    public FileLibraryCreateService fileLibraryCreateService;
//
//
//    private List<FileLibraryCreator> fileLibraryCreators = new LinkedList<>();
//
//    @Test
//    public void testGetOrCreateFileLibrary(){
//        FileOperator fileOperator = new FileOperator();
//        fileOperator.setOperatorId("123");
//
//        FileLibraryCreator mock = Mockito.mock(FileLibraryCreator.class);
//        fileLibraryCreators.add(mock);
//        Field filed = ReflectionUtils.findField(FileLibraryCreateService.class, "fileLibraryCreators");
//        ReflectionUtils.makeAccessible(filed);
//        ReflectionUtils.setField(filed, fileLibraryCreateService, fileLibraryCreators);
//
//        Mockito.when(mock.match(any())).thenReturn(true);
//        ArgumentCaptor<FileOperator> argumentCaptor = ArgumentCaptor.forClass(FileOperator.class);
//
//        fileLibraryCreateService.getOrCreateFileLibrary(fileOperator, "123");
//        Mockito.verify(mock).getOrCreateFileLibrary(argumentCaptor.capture());
//        FileOperator value = argumentCaptor.getValue();
//        Assertions.assertThat(value.getOperatorId()).isEqualTo("123");
//    }
//
//    @Test
//    public void testConverCatalogId() {
//        FileOperator fileOperator = new FileOperator();
//        fileOperator.setOperatorId("123");
//
//        FileLibraryCreator mock = Mockito.mock(FileLibraryCreator.class);
//        fileLibraryCreators.add(mock);
//        Field filed = ReflectionUtils.findField(FileLibraryCreateService.class, "fileLibraryCreators");
//        ReflectionUtils.makeAccessible(filed);
//        ReflectionUtils.setField(filed, fileLibraryCreateService, fileLibraryCreators);
//
//        FileCatalog fileCatalog = new FileCatalog();
//        fileCatalog.setId("321");
//
//        Mockito.when(mock.match(any())).thenReturn(true);
//        Mockito.when(mock.findLibraryCatalog(any())).thenReturn(Optional.of(fileCatalog));
//
//        String s = fileLibraryCreateService.convertCatalogId("123", fileOperator);
//
//        Assertions.assertThat(s).isEqualTo("321");
//
//    }
//}