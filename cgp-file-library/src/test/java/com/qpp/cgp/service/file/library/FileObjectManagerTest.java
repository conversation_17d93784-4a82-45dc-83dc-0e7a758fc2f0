package com.qpp.cgp.service.file.library;

import com.qpp.cgp.manager.file.library.FileObjectManager;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.driver.HybridMongoTemplate;
import org.assertj.core.api.Assertions;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/13
 */
@ExtendWith(MockitoExtension.class)
public class FileObjectManagerTest {

    @InjectMocks
    private FileObjectManager fileObjectManager;

    @Mock
    private HybridMongoTemplate mongoTemplate;

    @Mock
    private IdGenerator idGenerator;

    @Before
    public void init() {
        fileObjectManager = new FileObjectManager(mongoTemplate, idGenerator);
    }

    /**
     * baseName 含特殊字符
     */
    @Test
    public void testBuildRegex1() {
        List<String> disPlayNames = Arrays.asList("$()*+.[]?\\^{}|.jpg", "$()*+.[]?\\^{}|(1).jpg", "test1.jpg", "test2.jpg",
                "$()*+.[]?\\^{}|(2).jpg");

        String fileName = "$()*+.[]?\\^{}|.jpg";

        String regex = fileObjectManager.buildRegex(fileName);

        List<String> results = disPlayNames.stream()
                .filter(disPlayName -> disPlayName.matches(regex))
                .collect(Collectors.toList());

        Assertions.assertThat(results.size()).isEqualTo(3);
        Assertions.assertThat(results).contains("$()*+.[]?\\^{}|.jpg", "$()*+.[]?\\^{}|(1).jpg", "$()*+.[]?\\^{}|(2).jpg");
    }

    /**
     * baseName and extension 含特殊字符
     */
    @Test
    public void testBuildRegex2() {
        List<String> disPlayNames = Arrays.asList("$()*+.[]?\\^{}|.$()*+[]?\\^{}|", "$()*+.[]?\\^{}|(1).$()*+[]?\\^{}|",
                "test1.jpg", "test2.jpg", "$()*+.[]?\\^{}|(2).$()*+[]?\\^{}|");

        String fileName = "$()*+.[]?\\^{}|.$()*+[]?\\^{}|";

        String regex = fileObjectManager.buildRegex(fileName);

        List<String> results = disPlayNames.stream()
                .filter(disPlayName -> disPlayName.matches(regex))
                .collect(Collectors.toList());

        Assertions.assertThat(results.size()).isEqualTo(3);
        Assertions.assertThat(results).contains("$()*+.[]?\\^{}|.$()*+[]?\\^{}|", "$()*+.[]?\\^{}|(1).$()*+[]?\\^{}|",
                "$()*+.[]?\\^{}|(2).$()*+[]?\\^{}|");
    }

    /**
     * 不含特殊字符
     */
    @Test
    public void testBuildRegex3() {
        List<String> disPlayNames = Arrays.asList("test.jpg", "test(1).jpg", "test(2).jpg", "test(3).jpg");

        String fileName = "test.jpg";

        String regex = fileObjectManager.buildRegex(fileName);

        List<String> results = disPlayNames.stream()
                .filter(disPlayName -> disPlayName.matches(regex))
                .collect(Collectors.toList());

        Assertions.assertThat(results.size()).isEqualTo(4);
        Assertions.assertThat(results).contains("test.jpg", "test(1).jpg", "test(2).jpg", "test(3).jpg");
    }

    /**
     * 不含扩展名的文件名
     */
    @Test
    public void testBuildRegex4() {
        List<String> disPlayNames = Arrays.asList("$()*+[]?\\^{}|", "$()*+[]?\\^{}|(1)", "$()*+[]?\\^{}|(2)", "test()(3)");

        String fileName = "$()*+[]?\\^{}|";

        String regex = fileObjectManager.buildRegex(fileName);

        List<String> results = disPlayNames.stream()
                .filter(disPlayName -> disPlayName.matches(regex))
                .collect(Collectors.toList());

        Assertions.assertThat(results.size()).isEqualTo(3);
        Assertions.assertThat(results).contains("$()*+[]?\\^{}|", "$()*+[]?\\^{}|(1)", "$()*+[]?\\^{}|(2)");
    }

}
