package com.qpp.cgp.service.file.library.handler;

import com.qpp.cgp.domain.file.library.FileLibraryElement;
import com.qpp.cgp.service.file.library.core.FileLibraryCoreOperationHandler;
import com.qpp.cgp.service.file.library.operation.FindOneFileLibraryElementOperation;
import org.springframework.stereotype.Component;

@Component
public class FindOneFileLibraryElementOperationHandler
        implements FileLibraryCoreOperationHandler<FindOneFileLibraryElementOperation<FileLibraryElement>, FileLibraryElement> {

    @Override
    public FileLibraryElement handle(FindOneFileLibraryElementOperation<FileLibraryElement> operation) {
        // TODO
        return null;
    }

}
