package com.qpp.cgp.service.file.library;

import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.file.library.FileCatalog;
import com.qpp.cgp.domain.file.library.FileLibraryElement;
import com.qpp.cgp.domain.file.library.FileLibraryElementType;
import com.qpp.cgp.domain.file.library.FileObject;
import com.qpp.cgp.domain.file.library.*;
import com.qpp.cgp.dto.file.library.FileObjectDTO;
import com.qpp.cgp.dto.file.library.FileOperator;
import com.qpp.cgp.manager.file.library.FileLibraryElementManager;
import com.qpp.cgp.manager.file.library.FileObjectManager;
import com.qpp.cgp.service.file.event.FileLibraryElementCreateEvent;
import com.qpp.cgp.service.file.event.FileLibraryElementDeleteEvent;
import com.qpp.cgp.service.file.event.FileLibraryElementRetrieveEvent;
import com.qpp.cgp.service.file.event.FileLibraryElementUpdateEvent;
import com.qpp.cgp.utils.file.library.*;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <pre>
 * 功能列表：
 * （1）新建文件
 * （2）删除文件
 * （3）获取文件信息
 * （4）修改文件信息
 * </pre>
 */
@Service
public class FileObjectService {

    @Autowired
    private FileObjectManager fileObjectManager;

    @Autowired
    private CatalogUpdateUtils catalogUpdateUtils;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private FileCatalogLockService fileCatalogLockService;

    @Autowired
    private FileSizeInfoUpdater fileSizeInfoUpdater;
    @Autowired
    private FileLibraryElementManager fileLibraryElementManager;
    @Autowired
    private FileParentIdFinder parentIdFinder;

    @Autowired
    private FileLibraryElementDisplayNameRenameService fileLibraryElementDisplayNameRenameService;

    /**
     * 新建文件
     *
     * @param fileObjectDTO
     * @param catalogId
     * @param fileOperator
     * @return
     */
    public FileObjectDTO createFileObject(FileObjectDTO fileObjectDTO, String catalogId, FileOperator fileOperator) {
        AbstractCatalog parentCatalog = Optional.ofNullable(fileLibraryElementManager.getFileCatalogById(catalogId))
                .orElseThrow(() -> BusinessExceptionBuilder.of(97110011, ImmutableMap.of("id", catalogId != null ? catalogId : "")));

        // 检查文件名是否含非法字符
        check(fileObjectDTO.getDisplayName());

        fileObjectDTO.setParentId(catalogId);
        fileObjectDTO.setParentPath(parentCatalog.getParentPath() + "/" + catalogId);
        fileObjectDTO.setOwnerId(fileOperator.getOperatorId());
        FileLibraryElement fileObject = fileLibraryElementManager.create(fileObjectDTO, fileOperator);
        boolean success = Objects.nonNull(fileObject) && Objects.equals(fileObjectDTO.get_id(), fileObject.getId());
        if (!success) {
            fileObject = fileLibraryElementDisplayNameRenameService.rename(fileObjectDTO, fileOperator);
            fileObjectDTO.setIsConflicted(true);
        }

        fileObjectDTO.setLastModifierId(fileObject.getLastModifierId());
        fileObjectDTO.setLastModifierId(fileOperator.getOperatorId());
        fileObjectDTO.setCreationTime(fileObject.getCreationTime());
        fileObjectDTO.setLastModifiedTime(fileObject.getLastModifiedTime());

        // 伪造一个文件夹,来更新total
        FileCatalog fileCatalog = new FileCatalog();
        fileCatalog.setTotalCatalogQty(-1);
        fileCatalog.setTotalFileQty(1);
        fileCatalog.setTotalFileSize(fileObjectDTO.getSize());
        fileCatalog.setId(fileObjectDTO.get_id());

        Optional<FileLibraryElement> fileLibraryElementOpt =
                fileLibraryElementManager.findByIdNoException(fileObject.getParentId());


        fileLibraryElementOpt.ifPresent(fileLibraryElement -> fileSizeInfoUpdater.createOrDeleteUpdate(fileCatalog,
                parentIdFinder.find(fileLibraryElement, true),
                FileSizeInfoUpdater.Operate.Create));

        // 发布事件
        FileLibraryElementCreateEvent event = new FileLibraryElementCreateEvent(fileOperator, fileObject);
        applicationEventPublisher.publishEvent(event);

        return fileObjectDTO;
    }

    /**
     * 删除文件
     *
     * @param fileObjectId
     * @param fileOperator
     * @return
     */
    public boolean deleteFileObject(String fileObjectId, FileOperator fileOperator) {
        FileObject fileObject = fileObjectManager.deleteByFileObjectIdAndOwnerId(fileObjectId, fileOperator.getOperatorId());
        // 更新total
        FileCatalog fileCatalog = new FileCatalog();
        fileCatalog.setTotalCatalogQty(-1);
        fileCatalog.setTotalFileQty(1);
        fileCatalog.setTotalFileSize(fileObject.getSize());

        Optional<FileLibraryElement> fileLibraryElementOpt =
                fileLibraryElementManager.findByIdNoException(fileObject.getParentId());
        fileLibraryElementOpt.ifPresent(fileLibraryElement -> fileSizeInfoUpdater.createOrDeleteUpdate(fileCatalog,
                parentIdFinder.find(fileLibraryElement, true),
                FileSizeInfoUpdater.Operate.DELETE));

        // 发布事件
        FileLibraryElementDeleteEvent event = new FileLibraryElementDeleteEvent(fileOperator, fileObject);
        applicationEventPublisher.publishEvent(event);
        return true;
    }

    /**
     * 修改文件信息
     *
     * @param fileObjectDTO
     * @param fileOperator
     * @return
     */
    public FileObjectDTO updateFileObject(FileObjectDTO fileObjectDTO, FileOperator fileOperator) {
        // 检查文件名是否含非法字符
        check(fileObjectDTO.getDisplayName());

        FileLibraryElement fileObject = fileObjectManager.findById(fileObjectDTO.get_id());
        fileObject.setDisplayName(fileObjectDTO.getDisplayName());

        boolean isConflicted = false;
        FileLibraryElement updatedFileObject = fileLibraryElementManager.update(fileObject, fileOperator);
        if (Objects.isNull(updatedFileObject)) {
            updatedFileObject = fileLibraryElementDisplayNameRenameService.rename(fileObject, fileOperator);
            isConflicted = true;
        }

        FileLibraryElementUpdateEvent event = new FileLibraryElementUpdateEvent(fileOperator, fileObjectManager.findById(fileObjectDTO.get_id()));
        applicationEventPublisher.publishEvent(event);

        FileObjectDTO result = new FileObjectDTO();
        BeanUtils.copyProperties(updatedFileObject, result);
        result.setIsConflicted(isConflicted);

        return result;
    }

    /**
     * 获取文件信息
     *
     * @param fileOperator
     * @return
     */
    public FileObjectDTO getFileObject(FileObject fileObject, FileOperator fileOperator) {
        FileObjectDTO fileObjectDTO = new FileObjectDTO();
        BeanUtils.copyProperties(fileObject, fileObjectDTO);
        fileObjectDTO.setType(FileLibraryElementType.FileObject.toString());

        // 发布事件
        FileLibraryElementRetrieveEvent event = new FileLibraryElementRetrieveEvent(fileOperator, fileObject);
        applicationEventPublisher.publishEvent(event);

        return fileObjectDTO;
    }

    /**
     * 通过已有 displayName 重命名同名文件
     *
     * @param existsDisplayNames
     * @param displayName
     * @return
     */
    public String getDisplayName(List<String> existsDisplayNames, String displayName) {
        if (!existsDisplayNames.contains(displayName)) {
            return displayName;
        }

        List<Integer> sequenceNums = existsDisplayNames.stream()
                .filter(existsDisplayName -> existsDisplayName.length() > displayName.length())
                .map(name -> {
                    String sequenceNum = name.substring(name.lastIndexOf("(") + 1, name.lastIndexOf(")"));
                    return Integer.parseInt(sequenceNum);
                })
                .sorted()
                .collect(Collectors.toList());

        String newDisplayName = null;
        String baseName = FilenameUtils.getBaseName(displayName);

        for (int i = 0; i < sequenceNums.size(); i++) {
            if (sequenceNums.get(i) != (i + 1)) {
                newDisplayName = displayName.replace(baseName, baseName + "(" + (i + 1) + ")");
                break;
            }
        }

        if (StringUtils.isEmpty(newDisplayName)) {
            newDisplayName = displayName.replace(baseName, baseName + "(" + existsDisplayNames.size() + ")");
        }

        return newDisplayName;
    }

    /**
     * 新建、修改文件时，检查文件名是否含非法字符
     *
     * @param displayName
     */
    private void check(String displayName) {
        boolean isValid = FileNameUtils.check(displayName);

        // 判断文件名是否含非法字符（\/:*?"<>|），是则抛出异常
        if (!isValid) {
            String message = String.format("%s is an invalid file name, because it cannot contain any of the following characters: \\/:*?\" <>|",
                    displayName);
            throw BusinessExceptionBuilder.of(15800101, ImmutableMap.of("message", message));
        }
    }

}
