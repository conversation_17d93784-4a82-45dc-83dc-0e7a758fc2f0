package com.qpp.cgp.service.file;

import com.qpp.cgp.domain.file.library.AbstractCatalog;
import com.qpp.cgp.domain.file.library.FileLibrary;
import com.qpp.cgp.dto.file.library.FileCatalogDTO;
import com.qpp.cgp.dto.file.library.FileOperator;
import com.qpp.cgp.manager.file.library.FileLibraryElementManager;
import com.qpp.cgp.service.file.library.FileCatalogService;
import com.qpp.cgp.service.file.library.FileLibraryServiceV2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class ShareCatalogFinder implements CatalogFinder {

    @Autowired
    private FileLibraryServiceV2 fileLibraryService;

    @Autowired
    private FileLibraryElementManager fileLibraryElementManager;

    @Autowired
    private FileCatalogService fileCatalogService;

    @Override
    public boolean match(String catalogType) {
        return "share".equalsIgnoreCase(catalogType);
    }

    public Optional<AbstractCatalog> findLibraryCatalog(FileOperator operator) {
        return fileLibraryService.findFileLibrary(operator)
                .map(FileLibrary::getShareRootCatalogId)
                .map(fileLibraryElementManager::getFileCatalogById);
    }

    public AbstractCatalog createLibraryCatalog(FileOperator operator) {
        FileLibrary fileLibrary = fileLibraryService.findFileLibrary(operator)
                .orElseGet(() -> fileLibraryService.createFileLibrary(operator));

        FileCatalogDTO shareRootCatalog = new FileCatalogDTO();
        shareRootCatalog.setDisplayName("share files");
        shareRootCatalog = fileCatalogService.createCatalog(fileLibrary.getId(), shareRootCatalog, operator);

        // 保存 share root catalog
        fileLibraryElementManager.saveShareRootCatalog(fileLibrary.getId(), shareRootCatalog.get_id());

        return fileLibraryElementManager.getFileCatalogById(shareRootCatalog.get_id());
    }
}
