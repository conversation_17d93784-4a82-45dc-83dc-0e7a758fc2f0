package com.qpp.cgp.service.file.library.handler;

import com.qpp.cgp.domain.file.library.FileLibrary;
import com.qpp.cgp.service.file.library.core.FileLibraryCoreOperationHandler;
import com.qpp.cgp.service.file.library.operation.CreateFileLibraryOperation;
import org.springframework.stereotype.Component;

@Component
public class CreateFileLibraryOperationHandler
        implements FileLibraryCoreOperationHandler<CreateFileLibraryOperation, FileLibrary> {

    @Override
    public FileLibrary handle(CreateFileLibraryOperation operation) {
        // TODO
        return null;
    }

}
