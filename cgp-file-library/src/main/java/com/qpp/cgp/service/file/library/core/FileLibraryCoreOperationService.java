package com.qpp.cgp.service.file.library.core;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@SuppressWarnings("all")
public class FileLibraryCoreOperationService {

    private List<FileLibraryCoreOperationHandler> handlers = new ArrayList<>();

    private List<FileLibraryCoreOperationInterceptor> interceptors = new ArrayList<>();

    @Autowired(required = false)
    public void setHandlers(List<FileLibraryCoreOperationHandler> handlers) {
        if (CollectionUtils.isNotEmpty(handlers)) {
            this.handlers.addAll(handlers);
        }
    }

    @Autowired(required = false)
    public void setInterceptors(List<FileLibraryCoreOperationInterceptor> interceptors) {
        if (CollectionUtils.isNotEmpty(interceptors)) {
            this.interceptors.addAll(interceptors);
        }

    }

    public <R> R execute(FileLibraryCoreOperation<R> operation) {
        FileLibraryCoreOperationHandler matchedHandler
                = matchHandler(operation).orElseThrow(UnsupportedOperationException::new);

        List<FileLibraryCoreOperationInterceptor> matchInterceptors = matchInterceptors(operation);

        ExecutionChain executionChain = new ExecutionChain(matchedHandler, matchInterceptors);

        return (R) executionChain.proceed(operation);
    }

    private Optional<FileLibraryCoreOperationHandler> matchHandler(FileLibraryCoreOperation operation) {
        if (CollectionUtils.isEmpty(handlers)) {
            return Optional.empty();
        }

        return handlers.stream()
                .filter(handler -> handler.match(operation))
                .findFirst();
    }

    private List<FileLibraryCoreOperationInterceptor> matchInterceptors(FileLibraryCoreOperation operation) {
        if (CollectionUtils.isEmpty(interceptors)) {
            return new ArrayList<>();
        }

        return interceptors.stream()
                .filter(interceptor -> interceptor.match(operation))
                .collect(Collectors.toList());
    }

    static class ExecutionChain implements FileLibraryCoreOperationChain {

        private FileLibraryCoreOperationHandler handler;

        private List<FileLibraryCoreOperationInterceptor> interceptors;

        public ExecutionChain(FileLibraryCoreOperationHandler handler, List<FileLibraryCoreOperationInterceptor> interceptors) {
            this.handler = handler;
            this.interceptors = interceptors;
        }

        @Override
        public Object proceed(FileLibraryCoreOperation operation) {

            FileLibraryCoreOperationChain chain = handler::handle;

            for (int idx = interceptors.size() - 1; idx >= 0; idx--) {

                FileLibraryCoreOperationInterceptor interceptor = interceptors.get(idx);

                FileLibraryCoreOperationChain lastChain = chain;

                chain = op -> interceptor.intercept(op, lastChain);

            }

            return chain.proceed(operation);
        }

    }

}
