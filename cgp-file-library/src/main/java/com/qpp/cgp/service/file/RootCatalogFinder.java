package com.qpp.cgp.service.file;

import com.qpp.cgp.domain.file.library.AbstractCatalog;
import com.qpp.cgp.domain.file.library.FileLibrary;
import com.qpp.cgp.domain.file.library.owner.FileOwner;
import com.qpp.cgp.domain.partner.client.Client;
import com.qpp.cgp.domain.partner.store.Store;
import com.qpp.cgp.dto.file.library.FileCatalogDTO;
import com.qpp.cgp.dto.file.library.FileOperator;
import com.qpp.cgp.dto.file.library.FileOperatorType;
import com.qpp.cgp.manager.file.library.FileLibraryElementManager;
import com.qpp.cgp.manager.file.library.FileOwnerManager;
import com.qpp.cgp.manager.partner.client.ClientManager;
import com.qpp.cgp.manager.partner.store.StoreManager;
import com.qpp.cgp.service.file.library.FileCatalogService;
import com.qpp.cgp.service.file.library.FileLibraryServiceV2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class RootCatalogFinder implements CatalogFinder {
    @Autowired
    private FileOwnerManager fileOwnerManager;

    @Autowired
    private FileLibraryElementManager fileLibraryElementManager;

    @Autowired
    private FileLibraryServiceV2 fileLibraryService;

    @Autowired
    private FileCatalogService fileCatalogService;

    @Autowired
    private ClientManager clientManager;

    @Autowired
    private StoreManager storeManager;

    @Override
    public boolean match(String catalogType) {
        return "root".equalsIgnoreCase(catalogType);
    }

    public Optional<AbstractCatalog> findLibraryCatalog(FileOperator operator) {
        // FileOwner#rootCatalogId
        if (operator != null) {
            String operatorId = operator.getOperatorId();

            FileOwner fileOwner = fileOwnerManager.findById(operatorId);

            String rootCatalogId = fileOwner.getRootCatalogId();

            if (StringUtils.isNotBlank(rootCatalogId)) {
                return Optional.ofNullable(fileLibraryElementManager.getFileCatalogById(rootCatalogId));
            } else {
                return Optional.empty();
            }
        }

        return Optional.empty();
    }

    public AbstractCatalog createLibraryCatalog(FileOperator operator) {
        // 获取 FileLibrary
        FileLibrary fileLibrary = fileLibraryService.findFileLibrary(operator)
                .orElseGet(() -> fileLibraryService.createFileLibrary(operator));

        // 创建 rootCatalog
        AbstractCatalog rootCatalog;
        if (FileOperatorType.User.equals(operator.getType())) {
            rootCatalog = fileLibrary;
        } else if (FileOperatorType.ShopOwner.equals(operator.getType())) {
            rootCatalog = fileLibrary;
        } else if (FileOperatorType.Client.equals(operator.getType())) {
            rootCatalog = createClientRootCatalog(fileLibrary, operator);
        } else {
            throw new UnsupportedOperationException();
        }

        // 保存 rootCatalog
        fileOwnerManager.saveRootCatalog(operator.getOperatorId(), rootCatalog.getId());

        return rootCatalog;
    }

    private AbstractCatalog createClientRootCatalog(FileLibrary fileLibrary, FileOperator operator) {
        AbstractCatalog fileLibraryClientRootCatalog = fileLibraryService.getClientCatalog(fileLibrary, operator);

        FileOwner fileOwner = fileOwnerManager.findById(operator.getOperatorId());
        Client client = clientManager.findById(fileOwner.getRelatedAccountId());
        String displayName;
        String clientId = client.getClientId();
        // clientName 为空 ? clientId : (store 为空 ? (clientId + " " + clientName) : (store name + " " + clientName))
        String clientName = client.getClientName();
        if (StringUtils.isBlank(clientName)) {
            displayName = clientId;
        } else {
            Store storeIdRef = client.getStore();
            if (storeIdRef != null) {
                Store existingStore = storeManager.findStoreById(storeIdRef.getId());
                if (existingStore != null) {
                    displayName = String.join(" ", existingStore.getName(), clientName);
                } else {
                    displayName = String.join(" ", clientId, clientName);
                }
            } else {
                displayName = String.join(" ", clientId, clientName);
            }
        }

        FileCatalogDTO clientRootCatalog = new FileCatalogDTO();
        clientRootCatalog.setDisplayName(displayName);
        clientRootCatalog = fileCatalogService.createCatalog(fileLibraryClientRootCatalog.getId(), clientRootCatalog, operator);
        return fileLibraryElementManager.getFileCatalogById(clientRootCatalog.get_id());
    }
}
