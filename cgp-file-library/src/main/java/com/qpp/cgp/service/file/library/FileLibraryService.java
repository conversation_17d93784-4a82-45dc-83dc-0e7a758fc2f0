package com.qpp.cgp.service.file.library;

import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.file.library.AbstractCatalog;
import com.qpp.cgp.domain.file.library.FileLibrary;
import com.qpp.cgp.domain.file.library.FileLibraryElement;
import com.qpp.cgp.domain.file.library.FileLibraryElementType;
import com.qpp.cgp.domain.file.library.owner.FileLibraryOwner;
import com.qpp.cgp.domain.file.library.owner.FileLibraryOwnerType;
import com.qpp.cgp.domain.file.library.owner.FileOwner;
import com.qpp.cgp.dto.file.library.*;
import com.qpp.cgp.manager.file.library.FileLibraryElementManager;
import com.qpp.cgp.manager.file.library.FileLibraryOwnerManager;
import com.qpp.cgp.manager.file.library.FileOwnerManager;
import com.qpp.cgp.service.file.CatalogFinderService;
import com.qpp.cgp.service.file.library.operator.FileOperatorService;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <pre>
 * 功能列表：
 * （1）新建图库
 * （2）获取图库信息
 * （3）修改图库信息
 * </pre>
 */
@Service
public class FileLibraryService {

    @Autowired
    private FileLibraryOwnerManager fileLibraryOwnerManager;

    @Autowired
    private FileLibraryElementManager fileLibraryElementManager;

    @Autowired
    private FileOwnerManager fileOwnerManager;

    @Autowired
    private FileCatalogService fileCatalogService;

    @Autowired
    private FileLibraryServiceV2 fileLibraryService;

    @Autowired
    private MoveFileService moveFileService;

    @Autowired
    private FileOperatorService fileOperatorService;

    @Autowired
    private CatalogFinderService catalogFinderService;

    /**
     * 创建图库
     */
    public FileCatalogDTO createLibrary(FileOperator fileOperator, String catalogType) {
        if (fileOperator.getType().equals(FileOperatorType.User) && "root".equals(catalogType)) {
            // 如果已经有rootCatalogId
            FileOwner fileOwnerRoot = fileOwnerManager.findById(fileOperator.getOperatorId());

            if (fileOwnerRoot != null && Strings.isNotBlank(fileOwnerRoot.getRootCatalogId())) {
                return fileCatalogService.getCatalog(fileOwnerRoot.getRootCatalogId(), fileOperator);
            }

            FileLibraryOwner fileLibraryOwner =
                    fileLibraryOwnerManager.getByRelatedAccountId(fileOwnerRoot.getRelatedAccountId(), FileLibraryOwnerType.User);

            FileLibrary library;
            // 如果已经有图库
            if (fileLibraryOwner != null) {
                library = fileLibraryElementManager.getLibraryByOwner(fileLibraryOwner.getId());
                // 如果图库不存在
                if (library == null) {
                    // 创建一个fileLibrary
                    library = (FileLibrary) createFileLibrary(fileLibraryOwner, fileOwnerRoot);
                }
            } else {
                // 创建一个新的LibraryOwner
                FileLibraryOwner newOwner = new FileLibraryOwner();
                newOwner.setRelatedAccountId(fileOwnerRoot.getRelatedAccountId());
                newOwner.setType(FileLibraryOwnerType.User);
                fileLibraryOwner = fileLibraryOwnerManager.saveNew(newOwner);

                library = (FileLibrary) createFileLibrary(fileLibraryOwner, fileOwnerRoot);
            }

            // 更新fileOwner
            fileOwnerRoot.setRootCatalogId(library.getId());
            fileOwnerManager.saveUpdate(fileOwnerRoot, fileOwnerRoot.getId());

            FileCatalogDTO fileCatalogDTO = new FileCatalogDTO();
            BeanUtils.copyProperties(library, fileCatalogDTO);
            return fileCatalogDTO;
        } else {
            throw new UnsupportedOperationException();
        }
    }

    /**
     * 查询根目录
     */
    public String findLibrary(FileOperator fileOperator) {
        FileOwner fileOwnerRoot = fileOwnerManager.findById(fileOperator.getOperatorId());
        if (fileOwnerRoot == null || Strings.isBlank(fileOwnerRoot.getRootCatalogId())) {
            throw BusinessExceptionBuilder.of(97110010);
        }
        return fileOwnerRoot.getRootCatalogId();
    }

    private FileLibraryElement createFileLibrary(FileLibraryOwner saveOwner, FileOwner saveFileOwner) {
        // 创建一个新的FileLibrary
        FileLibrary fileLibrary = new FileLibrary();
        fileLibrary.setTotalFileQty(0);
        fileLibrary.setTotalFileSize(0L);
        fileLibrary.setTotalCatalogQty(0);
        fileLibrary.setLibraryOwnerId(saveOwner.getId());
        fileLibrary.setOwnerId(saveFileOwner.getId());
        fileLibrary.setType(FileLibraryElementType.FileLibrary);
        fileLibrary.setParentPath("");
        return fileLibraryElementManager.saveNew(fileLibrary);
    }

    /**
     * 更新图库 clientRootCatalog
     *
     * @param updateDTO
     * @param fileOperator
     */
    public void updateFileLibraryClientRootCatalog(UpdateClientRootCatalogDTO updateDTO, FileOperator fileOperator) {
        if (!FileOperatorType.ShopOwner.equals(fileOperator.getType())) {
            throw new UnsupportedOperationException();
        }

        FileLibrary fileLibrary = fileLibraryService.findFileLibrary(fileOperator).orElseThrow(() ->
                BusinessExceptionBuilder.of(97110017, ImmutableMap.of("fileOwnerId", fileOperator.getOperatorId()))
        );

        // 检查 clientRootCatalogId 是否是当前用户图库 fileLibraryId 下的目录
        check(updateDTO.getClientRootCatalogId(), fileLibrary.getId());

        // 判断是否需要把旧目录下数据迁移到新目录下
        if (updateDTO.getNeedMigrate()) {
            // 将原 clientRootCatalog 目录下数据迁移到新的 clientRootCatalog 目录下
            moveToCatalog(fileLibrary.getClientRootCatalogId(), updateDTO.getClientRootCatalogId(), fileOperator);
        }

        // 更新图库
        fileLibraryElementManager.saveClientRootCatalog(fileLibrary.getId(), updateDTO.getClientRootCatalogId());
    }

    /**
     * 更新图库 shareRootCatalog
     *
     * @param updateDTO
     * @param fileOperator
     */
    public void updateFileLibraryShareRootCatalog(UpdateShareRootCatalogDTO updateDTO, FileOperator fileOperator) {
        if (!(FileOperatorType.ShopOwner.equals(fileOperator.getType()) || FileOperatorType.User.equals(fileOperator.getType()))) {
            throw new UnsupportedOperationException();
        }

        FileLibrary fileLibrary = fileLibraryService.findFileLibrary(fileOperator).orElseThrow(() ->
                BusinessExceptionBuilder.of(97110017, ImmutableMap.of("fileOwnerId", fileOperator.getOperatorId()))
        );

        // 检查 shareRootCatalogId 是否是当前用户图库 fileLibraryId 下的目录
        check(updateDTO.getShareRootCatalogId(), fileLibrary.getId());

        if (updateDTO.getNeedMigrate()) {
            // 将原 shareRootCatalog 目录下数据迁移到新的 shareRootCatalog 目录下
            moveToCatalog(fileLibrary.getShareRootCatalogId(), updateDTO.getShareRootCatalogId(), fileOperator);
        }

        fileLibraryElementManager.saveShareRootCatalog(fileLibrary.getId(), updateDTO.getShareRootCatalogId());
    }

    /**
     * 更新图库 clientRootCatalog/shareRootCatalog 时检查 catalogId 是否是当前用户图库 fileLibraryId 下的目录，不是则抛出异常
     *
     * @param catalogId
     * @param fileLibraryId
     */
    private void check(String catalogId, String fileLibraryId) {
        boolean existed = Objects.equals(fileLibraryId, getFileLibrary(catalogId));

        // 只能更新当前用户（ShopOwner/User）图库下的目录（clientRootCatalog/shareRootCatalog）
        if (!existed) {
            throw BusinessExceptionBuilder.of(97110016,
                    ImmutableMap.of("fileCatalogId", catalogId, "fileLibraryId", fileLibraryId));
        }
    }

    /**
     * 移动指定目录下的所有文件/目录到目标目录中
     *
     * @param sourceCatalogId 源目录
     * @param targetCatalogId 目标目录
     * @param fileOperator
     */
    private void moveToCatalog(String sourceCatalogId, String targetCatalogId, FileOperator fileOperator) {
        List<FileLibraryElement> elements = fileLibraryElementManager.getElementsByParentId(sourceCatalogId);
        List<String> elementIds = elements.stream()
                .map(FileLibraryElement::getId)
                .collect(Collectors.toList());

        // 将指定目录 sourceCatalogId 下数据迁移到新的目录 targetCatalogId 下
        moveFileService.moveToCatalog(elementIds, targetCatalogId, fileOperator);
    }

    /**
     * 获取目录 catalogId 的所属图库 fileLibraryId
     *
     * @param catalogId
     * @return
     */
    public String getFileLibrary(String catalogId) {
        AbstractCatalog catalog = fileLibraryElementManager.getFileCatalogById(catalogId);

        if (catalog == null) {
            return null;
        }

        String parentId = catalog.getParentId();

        if (StringUtils.isEmpty(parentId)) {
            return catalogId;
        }

        return getFileLibrary(parentId);
    }

    public void initFileLibrary(Long userId) {
        //TODO 幂等控制
        //创建 ShopOwner 的 FileLibrary
        FileOperator operator = fileOperatorService.getFileOperator(FileOperatorType.ShopOwner.name(), userId);
        catalogFinderService.getOrCreateFileLibrary(operator, "root");
    }
}
