package com.qpp.cgp.service.file.library;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.qpp.cgp.domain.file.library.*;
import com.qpp.cgp.domain.file.library.owner.FileOwner;
import com.qpp.cgp.dto.file.library.*;
import com.qpp.cgp.manager.file.library.FileLibraryElementManager;
import com.qpp.cgp.manager.file.library.FileOwnerManager;
import com.qpp.cgp.service.file.CatalogFinderService;
import com.qpp.cgp.service.file.event.FileLibraryElementCreateEvent;
import com.qpp.cgp.service.file.event.FileLibraryElementDeleteEvent;
import com.qpp.cgp.service.file.event.FileLibraryElementRetrieveEvent;
import com.qpp.cgp.service.file.event.FileLibraryElementUpdateEvent;
import com.qpp.cgp.service.file.library.operator.FileOperatorService;
import com.qpp.cgp.utils.file.library.*;
import com.qpp.core.dto.FilterDTO;
import com.qpp.core.utils.SecurityUtils;
import com.qpp.mongo.domain.MongoDomain;
import com.qpp.id.generator.IdGenerator;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class FileCatalogService {

    @Autowired
    private FileLibraryElementManager fileLibraryElementManager;

    @Autowired
    private CatalogUpdateUtils catalogUpdateUtils;

    @Autowired
    private FileOwnerManager fileOwnerManager;

    @Autowired
    private FileCatalogLockService fileCatalogLockService;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private FileLibraryServiceV2 fileLibraryServiceV2;

    @Autowired
    private FileOperatorService fileOperatorService;

    @Autowired
    private CatalogFinderService catalogFinderService;

    @Autowired
    private FileSizeInfoUpdater fileSizeInfoUpdater;

    @Autowired
    private FileParentIdFinder fileParentIdFinder;
    @Autowired
    private IdGenerator idGenerator;

    @Autowired
    private FileLibraryElementDisplayNameRenameService fileLibraryElementDisplayNameRenameService;

    /**
     * 创建一个目录
     */
    public FileCatalogDTO createCatalog(String parentCatalogId, FileCatalogDTO newCatalog, FileOperator operator) {
        // 检查文件名是否含非法字符
        check(newCatalog.getDisplayName());

        // 找到父目录
        AbstractCatalog parentCatalog = fileLibraryElementManager.getFileCatalogById(parentCatalogId);
        if (parentCatalog == null) {
            throw BusinessExceptionBuilder.of(97110011, ImmutableMap.of("id", parentCatalogId));
        }

        newCatalog.set_id(idGenerator.generateId().toString());
        newCatalog.setTotalCatalogQty(0);
        newCatalog.setTotalFileQty(0);
        newCatalog.setTotalFileSize(0L);
        newCatalog.setParentId(parentCatalogId);
        newCatalog.setParentPath(parentCatalog.getParentPath() + "/" + parentCatalog.getId());
        newCatalog.setOwnerId(operator.getOperatorId());

        FileLibraryElement fileCatalog = fileLibraryElementManager.create(newCatalog, operator);
        boolean success = Objects.nonNull(fileCatalog) && Objects.equals(newCatalog.get_id(), fileCatalog.getId());
        if (!success) {
            fileCatalog = fileLibraryElementDisplayNameRenameService.rename(newCatalog, operator);
            newCatalog.setIsConflicted(true);
        }

        newCatalog.setCreationTime(fileCatalog.getCreationTime());
        newCatalog.setLastModifiedTime(fileCatalog.getLastModifiedTime());

        // 更新父目录的信息
        Set<String> idset = fileParentIdFinder.find(fileCatalog, false);
        fileSizeInfoUpdater.createOrDeleteUpdate((AbstractCatalog) fileCatalog, idset, FileSizeInfoUpdater.Operate.Create);

        //发送事件
        FileLibraryElementCreateEvent event = new FileLibraryElementCreateEvent(operator, fileCatalog);
        applicationEventPublisher.publishEvent(event);

        FileCatalogDTO res = new FileCatalogDTO();
        // 组装结果
        BeanUtils.copyProperties(fileCatalog, res);
        return res;
    }


    /**
     * 删除目录
     */
    public boolean deleteCatalog(String catalogId, FileOperator fileOperator) {
        AbstractCatalog abstractCatalog = (AbstractCatalog) fileLibraryElementManager.deleteCatalogById(catalogId);
        FileOwner fileOwner = fileOwnerManager.findById(fileOperator.getOperatorId());
        // user的root目录不能被删除
        if (FileOperatorType.User.equals(fileOperator.getType()) &&
                fileOwner.getRootCatalogId().equals(catalogId) &&
                abstractCatalog.getType().equals(FileLibraryElementType.FileLibrary)) {
            throw BusinessExceptionBuilder.of(97110014, ImmutableMap.of("ownerId", fileOwner.getId()));
        }

        // 进行删除后的更新
        if (abstractCatalog != null) {
            Set<String> idSet = fileParentIdFinder.find(abstractCatalog, false);
            fileSizeInfoUpdater.createOrDeleteUpdate(abstractCatalog, idSet, FileSizeInfoUpdater.Operate.DELETE);
        }

        // 发布事件
        FileLibraryElementDeleteEvent event = new FileLibraryElementDeleteEvent(fileOperator, abstractCatalog);
        applicationEventPublisher.publishEvent(event);
        return abstractCatalog != null;
    }

    /**
     * 创建特殊目录（root / share）
     *
     * @param role
     * @param catalogType
     * @return
     */
    public FileCatalogDTO createSpecialCatalog(String role, String catalogType) {
        FileOperator operator = fileOperatorService.getFileOperator(role, SecurityUtils.getLoginedInUserId());
        AbstractCatalog catalog = catalogFinderService.getOrCreateFileLibrary(operator, catalogType);
        return getCatalog(catalog.getId(), operator);
    }

    /**
     * 获取目录信息
     *
     * @param catalogId
     * @param fileOperator
     * @return
     */
    public FileCatalogDTO getCatalog(String catalogId, FileOperator fileOperator) {
        AbstractCatalog fileCatalog = fileLibraryElementManager.getFileCatalogById(catalogId);
        FileCatalogDTO fileCatalogDTO = new FileCatalogDTO();
        BeanUtils.copyProperties(fileCatalog, fileCatalogDTO);

        // 发布事件
        FileLibraryElementRetrieveEvent event = new FileLibraryElementRetrieveEvent(fileOperator, fileCatalog);
        applicationEventPublisher.publishEvent(event);

        return fileCatalogDTO;
    }

    public FileCatalogDTO getCatalog(AbstractCatalog catalog, FileOperator fileOperator) {
        FileCatalogDTO fileCatalogDTO = new FileCatalogDTO();
        BeanUtils.copyProperties(catalog, fileCatalogDTO);

        // 发布事件
        FileLibraryElementRetrieveEvent event = new FileLibraryElementRetrieveEvent(fileOperator, catalog);
        applicationEventPublisher.publishEvent(event);

        return fileCatalogDTO;
    }

    /**
     * 更新目录
     */
    public FileCatalogDTO updateCatalog(FileCatalogDTO catalogDTO, FileOperator fileOperator) {
        // 检查文件名是否含非法字符
        check(catalogDTO.getDisplayName());

        FileCatalog fileCatalog = (FileCatalog) fileLibraryElementManager.getFileCatalogById(catalogDTO.get_id());
        fileCatalog.setColor(catalogDTO.getColor());
        fileCatalog.setDisplayName(catalogDTO.getDisplayName());
        fileCatalog.setIcon(catalogDTO.getIcon());

        boolean isConflicted = false;
        FileLibraryElement updatedFileCatalog = fileLibraryElementManager.update(fileCatalog, fileOperator);
        if (Objects.isNull(updatedFileCatalog)) {
            updatedFileCatalog = fileLibraryElementDisplayNameRenameService.rename(fileCatalog, fileOperator);
            isConflicted = true;
        }

        // 发布事件
        FileLibraryElementUpdateEvent event = new FileLibraryElementUpdateEvent(fileOperator, fileCatalog);
        applicationEventPublisher.publishEvent(event);

        FileCatalogDTO result = new FileCatalogDTO();
        BeanUtils.copyProperties(updatedFileCatalog, result);
        result.setIsConflicted(isConflicted);

        return result;
    }

    /**
     * 分页获取目录
     */
    public FileCatalogPage getCatalogPage(String catalogId, FileOperator fileOperator,
                                          PageRequest pageable, List<FilterDTO> filterDTOList) {

        AbstractCatalog parentCatalog = fileLibraryElementManager.getFileCatalogById(catalogId);
        if (parentCatalog == null) {
            throw BusinessExceptionBuilder.of(97110011, ImmutableMap.of("id", catalogId));
        }
        Page<FileLibraryElement> fileLibraryElementPage = getElementPage(catalogId, fileOperator, pageable, filterDTOList);
        processDirty(parentCatalog, fileLibraryElementPage.getContent());

        List<FileLibraryElementDTO> contentDto = fileLibraryElementPage.getContent().stream().map(fileLibraryElement -> {
            FileLibraryElementDTO fileLibraryElementDTO;
            if (fileLibraryElement instanceof FileObject) {
                fileLibraryElementDTO = new FileObjectDTO();
                BeanUtils.copyProperties(fileLibraryElement, fileLibraryElementDTO);
            } else {
                fileLibraryElementDTO = new FileCatalogDTO();
                BeanUtils.copyProperties(fileLibraryElement, fileLibraryElementDTO);
            }
            return fileLibraryElementDTO;
        }).collect(Collectors.toList());

        AbstractCatalog fileCatalog = fileLibraryElementManager.getFileCatalogById(catalogId);

        FileCatalogDTO fileCatalogDTO = new FileCatalogDTO();
        BeanUtils.copyProperties(fileCatalog, fileCatalogDTO);

        FileCatalogPage fileCatalogPage = new FileCatalogPage();
        fileCatalogPage.setCatalog(fileCatalogDTO);
        fileCatalogPage.setPageNumber(fileLibraryElementPage.getNumber());
        fileCatalogPage.setPageSize(fileLibraryElementPage.getSize());
        fileCatalogPage.setTotalPages(fileLibraryElementPage.getTotalPages());
        fileCatalogPage.setTotalCount(fileLibraryElementPage.getTotalElements());
        fileCatalogPage.setElements(contentDto);

        // 发布事件
        FileLibraryElementRetrieveEvent event = new FileLibraryElementRetrieveEvent(fileOperator, parentCatalog);
        applicationEventPublisher.publishEvent(event);

        return fileCatalogPage;
    }

    private void processDirty(AbstractCatalog parentCatalog, List<FileLibraryElement> content) {
        // 如果父目录的路径是正确的
        if (!parentCatalog.getIsDirty()) {
            String parentPath = parentCatalog.getParentPath() + "/" + parentCatalog.getId();
            Set<String> updateParentPath = new HashSet<>();
            for (FileLibraryElement fileLibraryElement : content) {
                Boolean isDirty = fileLibraryElement.getIsDirty();
                if (isDirty) {
                    // 如果子节点是错误的路径
                    fileLibraryElement.setIsDirty(false);
                    fileLibraryElement.setParentPath(parentPath);
                    updateParentPath.add(fileLibraryElement.getId());
                }
                fileLibraryElementManager.updateParentPath(updateParentPath, false, parentPath);
            }
        }else{
            // 父目录的parentPath也是错误的，需要更新父目录的路径，并且更新子目录的路径
            String parentPath = fileParentIdFinder.getParentPath(parentCatalog);
            parentCatalog.setParentPath(parentPath);
            parentCatalog.setIsDirty(false);
            fileLibraryElementManager.updateParentPath(Sets.newHashSet(parentCatalog.getId()), false, parentPath);

            Set<String> idSet = content.stream()
                    .filter(FileLibraryElement::getIsDirty)
                    .map(MongoDomain::get_id)
                    .collect(Collectors.toSet());
            fileLibraryElementManager.updateParentPath(idSet, false, parentPath + "/" + parentCatalog.getId());
        }
    }

    /**
     * 作用：用于生成client目录和share目录放在前面的情况的Page类（先client后share）
     * 1、 判断是否有过滤或者排序条件
     * 2、如果有，判断是否是第一页
     * 3、 如果是第一页，判断长度是否小于等于特殊目录的长度
     * 4、 如果小于等于，直接返回特殊目录的目录page
     * 5、如果大于，把page的size-2，并且目录放在最前面
     * 6、如果不是第一页，就去判断前面的长度加起来小于特殊目录的长度
     * 7、 如果小于，放回client目录page
     * 8、 重新计算启始位置
     * 9、 如果没有，就直接按照默认的查找目录page
     */
    public Page<FileLibraryElement> getElementPage(String catalogId, FileOperator fileOperator,
                                                   PageRequest pageable, List<FilterDTO> filterDTOList) {
        Page<FileLibraryElement> fileLibraryElementPage;
        // 没有排序顺序并且没有过滤
        if (filterDTOList.isEmpty() && pageable.getSort().isEmpty()) {
            // 获取total
            long total = this.fileLibraryElementManager.getCountByParentId(catalogId);

            // 获取特殊目录
            FileLibrary fileLibrary = fileLibraryServiceV2.findFileLibraryByCatalogId(catalogId).get();
            LinkedList<FileLibraryElement> specialCatalogs = new LinkedList<>();

            if (Strings.isNotBlank(fileLibrary.getClientRootCatalogId())) {
                FileLibraryElement clientCatalog = fileLibraryElementManager
                        .getFileCatalogByParentIdAndId(catalogId, fileLibrary.getClientRootCatalogId());
                if (clientCatalog != null) {
                    specialCatalogs.add(clientCatalog);
                }
            }
            if (Strings.isNotBlank(fileLibrary.getShareRootCatalogId())) {
                FileLibraryElement shareCatalog = fileLibraryElementManager
                        .getFileCatalogByParentIdAndId(catalogId, fileLibrary.getShareRootCatalogId());
                if (shareCatalog != null) {
                    specialCatalogs.add(shareCatalog);
                }
            }

            // 判断是否是第一页
            if (pageable.getPageNumber() == 0) {
                // 判断size是否小于特殊目录
                if (pageable.getPageSize() <= specialCatalogs.size()) {
                    if (pageable.getPageSize() < specialCatalogs.size()) {
                        specialCatalogs.removeLast();
                    }
                    fileLibraryElementPage = new PageImpl<>(specialCatalogs, pageable, total);
                } else {
                    // 获取特殊目录id
                    List<String> specialCatalogIds = specialCatalogs.stream()
                            .map(FileLibraryElement::getId)
                            .collect(Collectors.toList());

                    // 获取除去特殊目录的page
                    PageRequest pageRequest = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize() - specialCatalogIds.size());
                    fileLibraryElementPage =
                            fileLibraryElementManager.getElementsByParentId(catalogId, pageRequest, filterDTOList, specialCatalogIds);
                    List<FileLibraryElement> content = fileLibraryElementPage.getContent();
                    specialCatalogs.addAll(content);

                    // 生成page
                    fileLibraryElementPage = new PageImpl<>(specialCatalogs, pageable, total);
                }
            } else {
                // 获取特殊目录id
                List<String> specialCatalogIds = specialCatalogs.stream()
                        .map(FileLibraryElement::getId)
                        .collect(Collectors.toList());

                // 计算前面的长度
                int pageSize = pageable.getPageSize();
                int pageNumber = pageable.getPageNumber();

                if (pageSize * pageNumber < specialCatalogs.size()) {
                    specialCatalogs.removeFirst();
                    fileLibraryElementPage = new PageImpl<>(specialCatalogs, pageable, total);
                } else {
                    List<FileLibraryElement> content = fileLibraryElementManager
                            .getElementsByLimitAndSkip(catalogId, pageSize,
                                    pageNumber * pageSize - specialCatalogs.size(), specialCatalogIds);
                    fileLibraryElementPage = new PageImpl<>(content, pageable, total);
                }
            }
        } else {
            fileLibraryElementPage = fileLibraryElementManager
                    .getElementsByParentId(catalogId, pageable, filterDTOList, new ArrayList<>());
        }
        return fileLibraryElementPage;
    }

    public FileCatalogTree getTree(String catalogId, FileOperator operator) {
        AbstractCatalog headCatalog = fileLibraryElementManager.getFileCatalogById(catalogId);
        FileCatalogTree fileCatalogTree = new FileCatalogTree();
        BeanUtils.copyProperties(headCatalog, fileCatalogTree);

        // 发布事件
        FileLibraryElementRetrieveEvent event = new FileLibraryElementRetrieveEvent(operator, headCatalog);
        applicationEventPublisher.publishEvent(event);

        buildTree(fileCatalogTree);

        return fileCatalogTree;
    }

    public List<FileCatalogDTO> getSubCatalogList(String catalogId, FileOperator operator) {
        AbstractCatalog parentCatalog = Optional.ofNullable(fileLibraryElementManager.getFileCatalogById(catalogId))
                .orElseThrow(() -> BusinessExceptionBuilder.of(97110011, ImmutableMap.of("id", catalogId)));

        // 发布事件
        FileLibraryElementRetrieveEvent event = new FileLibraryElementRetrieveEvent(operator, parentCatalog);
        applicationEventPublisher.publishEvent(event);

        List<FileLibraryElement> children = fileLibraryElementManager.getElementsByParentIdAndType(parentCatalog.getId(), FileLibraryElementType.FileCatalog);
        return children.stream()
                .map(element -> (FileCatalog) element)
                .map(catalog -> {
                    FileCatalogDTO catalogDTO = new FileCatalogDTO();
                    BeanUtils.copyProperties(catalog, catalogDTO);
                    catalogDTO.setIsLeaf(Objects.equals(catalog.getTotalCatalogQty(), 0));
                    return catalogDTO;
                })
                .collect(Collectors.toList());
    }

    public void buildTree(FileCatalogTree fileCatalogTree) {
        String id = fileCatalogTree.get_id();
        List<FileLibraryElement> children = fileLibraryElementManager.getElementsByParentId(id);
        LinkedList<FileCatalogTree> fileCatalogTrees = new LinkedList<>();
        for (FileLibraryElement child : children) {
            if (child instanceof AbstractCatalog) {
                FileCatalogTree childFileCatalogTree = new FileCatalogTree();
                BeanUtils.copyProperties(child, childFileCatalogTree);
                fileCatalogTrees.add(childFileCatalogTree);

                buildTree(childFileCatalogTree);

                fileCatalogTree.setChildren(fileCatalogTrees);
            }
        }
    }

    /**
     * 新建、修改目录时，检查文件名是否含非法字符
     *
     * @param displayName
     */
    private void check(String displayName) {
        boolean isValid = FileNameUtils.check(displayName);

        // 判断文件名是否含非法字符（\/:*?"<>|），是则抛出异常
        if (!isValid) {
            String message = String.format("%s is an invalid file name, because it cannot contain any of the following characters: \\/:*?\" <>|",
                    displayName);
            throw BusinessExceptionBuilder.of(15800101, ImmutableMap.of("message", message));
        }
    }

}
