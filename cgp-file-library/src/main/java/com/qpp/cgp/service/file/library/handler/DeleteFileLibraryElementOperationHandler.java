package com.qpp.cgp.service.file.library.handler;

import com.qpp.cgp.service.file.library.core.FileLibraryCoreOperationHandler;
import com.qpp.cgp.service.file.library.operation.DeleteFileLibraryElementOperation;
import org.springframework.stereotype.Component;

@Component
public class DeleteFileLibraryElementOperationHandler
        implements FileLibrary<PERSON>oreOperationHandler<DeleteFileLibraryElementOperation, Boolean> {

    @Override
    public Boolean handle(DeleteFileLibraryElementOperation operation) {
        // TODO
        return null;
    }

}
