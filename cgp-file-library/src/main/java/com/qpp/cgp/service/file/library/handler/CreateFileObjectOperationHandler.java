package com.qpp.cgp.service.file.library.handler;

import com.qpp.cgp.domain.file.library.FileObject;
import com.qpp.cgp.service.file.library.core.FileLibraryCoreOperationHandler;
import com.qpp.cgp.service.file.library.operation.CreateFileObjectOperation;
import org.springframework.stereotype.Component;

@Component
public class CreateFileObjectOperationHandler
        implements FileLibraryCoreOperationHandler<CreateFileObjectOperation, FileObject> {

    @Override
    public FileObject handle(CreateFileObjectOperation operation) {
        // TODO
        return null;
    }

}
