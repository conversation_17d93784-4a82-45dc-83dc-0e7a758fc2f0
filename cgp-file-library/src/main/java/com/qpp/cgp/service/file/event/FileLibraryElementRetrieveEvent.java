package com.qpp.cgp.service.file.event;

import com.qpp.cgp.domain.file.library.FileLibraryElement;
import com.qpp.cgp.domain.file.library.record.OperationTypes;
import com.qpp.cgp.dto.file.library.FileOperator;

public class FileLibraryElementRetrieveEvent extends FileLibraryElementEvent<FileLibraryElement>{
    public FileLibraryElementRetrieveEvent(FileOperator operator, FileLibraryElement source) {
        super(operator, OperationTypes.RETRIEVE, source);
    }
}
