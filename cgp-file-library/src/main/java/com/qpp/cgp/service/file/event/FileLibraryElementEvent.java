package com.qpp.cgp.service.file.event;

import com.qpp.cgp.dto.file.library.FileOperator;
import org.springframework.context.ApplicationEvent;

public abstract class FileLibraryElementEvent<FileLibraryElement> extends ApplicationEvent {

    protected FileOperator operator;

    protected String operation;

    protected String message;

    public FileLibraryElementEvent(FileOperator operator, String operation, FileLibraryElement source) {
        super(source);
        this.operator = operator;
        this.operation = operation;
    }

    public FileOperator getOperator() {
        return operator;
    }

    public String getOperation() {
        return operation;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

}
