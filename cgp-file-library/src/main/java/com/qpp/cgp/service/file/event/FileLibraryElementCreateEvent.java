package com.qpp.cgp.service.file.event;

import com.qpp.cgp.domain.file.library.FileLibraryElement;
import com.qpp.cgp.domain.file.library.record.OperationTypes;
import com.qpp.cgp.dto.file.library.FileOperator;

public class FileLibraryElementCreateEvent extends FileLibraryElementEvent<FileLibraryElement>{
    public FileLibraryElementCreateEvent(FileOperator operator, FileLibraryElement source) {
        super(operator, OperationTypes.CREATE, source);
    }
}
