package com.qpp.cgp.service.file.library;

import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.file.library.FileLibraryElement;
import com.qpp.cgp.domain.file.library.FileLibraryElementNameSequence;
import com.qpp.cgp.domain.file.library.FileObject;
import com.qpp.cgp.dto.file.library.FileCatalogDTO;
import com.qpp.cgp.dto.file.library.FileLibraryElementDTO;
import com.qpp.cgp.dto.file.library.FileObjectDTO;
import com.qpp.cgp.dto.file.library.FileOperator;
import com.qpp.cgp.manager.file.library.FileLibraryElementManager;
import com.qpp.cgp.manager.file.library.FileLibraryElementNameSequenceManager;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.UUID;

@Service
@Log4j2
public class FileLibraryElementDisplayNameRenameService {

    @Autowired
    private FileLibraryElementNameSequenceManager fileLibraryElementNameSequenceManager;

    @Autowired
    private FileLibraryElementManager fileLibraryElementManager;

    /**
     * 目录或文件显示名称重复时进行重命名
     *
     * @return
     */
    public FileLibraryElement rename(FileLibraryElementDTO fileLibraryElementDTO, FileOperator fileOperator) {
        if (!(fileLibraryElementDTO instanceof FileObjectDTO || fileLibraryElementDTO instanceof FileCatalogDTO)) {
            throw new UnsupportedOperationException();
        }

        FileLibraryElementNameSequence fileLibraryElementNameSequence =
                fileLibraryElementNameSequenceManager.create(fileLibraryElementDTO.getParentId(), fileLibraryElementDTO.getDisplayName());

        String displayName = fileLibraryElementDTO.getDisplayName();
        String baseName = fileLibraryElementDTO instanceof FileObjectDTO ? FilenameUtils.getBaseName(displayName) : displayName;

        String parentId = fileLibraryElementDTO.getParentId();
        // 以 baseName + sortOrder 重命名
        String newDisplayName = StringUtils.replaceOnce(displayName, baseName, baseName + "(" + fileLibraryElementNameSequence.getSortOrder() + ")");
        fileLibraryElementDTO.setDisplayName(newDisplayName);

        log.info("Filename {} is conflicted in the FileLibraryElement {}, try to set {}", displayName, parentId, newDisplayName);

        FileLibraryElement fileLibraryElement = fileLibraryElementManager.create(fileLibraryElementDTO, fileOperator);
        if (Objects.isNull(fileLibraryElement) || !Objects.equals(fileLibraryElementDTO.get_id(), fileLibraryElement.getId())) {
            // 以 baseName + UUID 重命名
            newDisplayName = StringUtils.replaceOnce(displayName, baseName, baseName + "-" + UUID.randomUUID().toString().replace("-", ""));
            fileLibraryElementDTO.setDisplayName(newDisplayName);

            log.info("Filename {} is conflicted in the FileLibraryElement {}, try to set {}", displayName, parentId, newDisplayName);

            fileLibraryElement = fileLibraryElementManager.create(fileLibraryElementDTO, fileOperator);
            if (Objects.isNull(fileLibraryElement) || !Objects.equals(fileLibraryElementDTO.get_id(), fileLibraryElement.getId())) {
                String errorMsg = String.format("Filename %s is still conflicted in the FileLibraryElement %s", displayName, parentId);
                throw BusinessExceptionBuilder.of(97110022, ImmutableMap.of("message", errorMsg));
            }
        }

        return fileLibraryElement;
    }

    public FileLibraryElement rename(FileLibraryElement fileLibraryElement, FileOperator fileOperator) {
        FileLibraryElementNameSequence fileLibraryElementNameSequence =
                fileLibraryElementNameSequenceManager.create(fileLibraryElement.getParentId(), fileLibraryElement.getDisplayName());

        String displayName = fileLibraryElement.getDisplayName();
        String baseName = fileLibraryElement instanceof FileObject ? FilenameUtils.getBaseName(displayName) : displayName;

        String parentId = fileLibraryElement.getParentId();
        String newDisplayName = StringUtils.replaceOnce(displayName, baseName, baseName + "(" + fileLibraryElementNameSequence.getSortOrder() + ")");
        fileLibraryElement.setDisplayName(newDisplayName);

        log.info("Filename {} is conflicted in the FileLibraryElement {}, try to set {}", displayName, parentId, newDisplayName);

        FileLibraryElement result = fileLibraryElementManager.update(fileLibraryElement, fileOperator);
        if (Objects.isNull(result)) {
            newDisplayName = StringUtils.replaceOnce(displayName, baseName, baseName + "-" + UUID.randomUUID().toString().replace("-", ""));
            fileLibraryElement.setDisplayName(newDisplayName);

            log.info("Filename {} is conflicted in the FileLibraryElement {}, try to set {}", displayName, parentId, newDisplayName);

            result = fileLibraryElementManager.update(fileLibraryElement, fileOperator);
            if (Objects.isNull(result)) {
                String errorMsg = String.format("Filename %s is still conflicted in the FileLibraryElement %s", displayName, parentId);
                throw BusinessExceptionBuilder.of(97110022, ImmutableMap.of("message", errorMsg));
            }
        }

        return result;
    }

}
