package com.qpp.cgp.service.file;

import com.qpp.cgp.domain.file.library.AbstractCatalog;
import com.qpp.cgp.dto.file.library.FileOperator;

import java.util.Optional;

public interface CatalogFinder {

    boolean match(String catalogType);

    default AbstractCatalog getOrCreateFileLibrary(FileOperator operator) {
        return findLibraryCatalog(operator).orElseGet(() -> createLibraryCatalog(operator));
    }

    Optional<AbstractCatalog> findLibraryCatalog(FileOperator fileOperator);

    AbstractCatalog createLibraryCatalog(FileOperator fileOperator);
}
