package com.qpp.cgp.service.file.library.handler;

import com.qpp.cgp.service.file.library.core.FileLibraryCoreOperationHandler;
import com.qpp.cgp.service.file.library.operation.UpdateFileCatalogOperation;
import org.springframework.stereotype.Component;

@Component
public class UpdateFileCatalogOperationHandler
        implements FileLibraryCoreOperationHandler<UpdateFileCatalogOperation, Boolean> {

    @Override
    public Boolean handle(UpdateFileCatalogOperation operation) {
        // TODO
        return null;
    }

}
