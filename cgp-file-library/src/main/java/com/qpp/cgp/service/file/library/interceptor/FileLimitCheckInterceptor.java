package com.qpp.cgp.service.file.library.interceptor;

import com.qpp.cgp.service.file.library.core.FileLibraryCoreOperation;
import com.qpp.cgp.service.file.library.core.FileLibraryCoreOperationChain;
import com.qpp.cgp.service.file.library.core.FileLibraryCoreOperationInterceptor;
import org.springframework.stereotype.Component;

@Component
public class FileLimitCheckInterceptor implements FileLibraryCoreOperationInterceptor {

    @Override
    public Object intercept(FileLibraryCoreOperation operation, FileLibraryCoreOperationChain chain) {
        // TODO
        return chain.proceed(operation);
    }

}
