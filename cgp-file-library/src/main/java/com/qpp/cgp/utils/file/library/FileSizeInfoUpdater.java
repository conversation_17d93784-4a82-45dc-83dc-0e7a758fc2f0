package com.qpp.cgp.utils.file.library;

import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.file.library.AbstractCatalog;
import com.qpp.cgp.manager.file.library.FileLibraryElementManager;
import com.qpp.cgp.manager.file.library.FileObjectManager;
import com.qpp.core.utils.exception.ExceptionUtils;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import io.sentry.Sentry;
import io.sentry.event.Event;
import io.sentry.event.EventBuilder;
import io.sentry.event.interfaces.ExceptionInterface;
import lombok.Data;
import lombok.Getter;
import lombok.extern.log4j.Log4j2;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 对文件/文件目录进行新增，移动，删除后，进行对父目录的size等信息修改的类
 */
@Service
@Log4j2
public class FileSizeInfoUpdater {
    @Autowired
    private FileLibraryElementManager fileLibraryElementManager;
    @Autowired
    private FileObjectManager fileObjectManager;


    /**
     * 新建或者删除后对父目录的size信息的更新
     *
     * @param childCatalog 新增或者删除的目标目录
     * @param parentIds   父id
     * @param operate      操作，新增为1，删除为-1
     */
    public void createOrDeleteUpdate(AbstractCatalog childCatalog, Set<String> parentIds, Operate operate) {
        if (childCatalog == null || CollectionUtils.isEmpty(parentIds)) {
            return;
        }
        String parentPath = parentIds.stream().collect(Collectors.joining(","));

        // 因为他本身也是目录，需要加1
        Integer totalCatalogQty = (Optional.ofNullable(childCatalog.getTotalCatalogQty()).orElse(0) + 1) * operate.getValue();
        Integer totalFileQty = Optional.ofNullable(childCatalog.getTotalFileQty()).orElse(0) * operate.getValue();
        Long totalFileSize = Optional.ofNullable(childCatalog.getTotalFileSize()).orElse(0L) * operate.getValue();

        try {
            if (isNeedUpdate(totalCatalogQty, totalFileQty, totalFileSize)) {
                log.info("FileLibrary update element {} to {}: Start update fileCatalog size info," +
                                " totalCatalogQty:{}, totalFileQty:{}, totalFileSize:{}", childCatalog.getId(), parentPath,
                        totalCatalogQty, totalFileQty, totalFileSize);
                Long start = System.currentTimeMillis();
                fileLibraryElementManager.updateSize(parentIds, totalCatalogQty, totalFileQty, totalFileSize);
                log.info("FileLibrary updateSize time is {}", System.currentTimeMillis() - start);
            }
            // 更新
        } catch (Exception e) {
            ImmutableMap<String, String> errorData =
                    ImmutableMap.of("childCatalog", childCatalog.getId(), "parentCatalog", parentPath,
                            "operate", Operate.DELETE.equals(operate) ? "DELETE" : "CREATE");

            EventBuilder eventBuilder = new EventBuilder()
                    .withMessage(Optional.ofNullable(e.getMessage()).orElse(" ") +
                            ExceptionUtils.resolveStack(e.getStackTrace()))
                    .withLevel(Event.Level.ERROR)
                    .withTag("category", "FileLibrary")
                    .withTag("owner", childCatalog.getOwnerId())
                    .withExtra("ErrorData", errorData)
                    .withSentryInterface(new ExceptionInterface(e));
            Sentry.capture(eventBuilder);
            // TODO 异常码
            throw BusinessExceptionBuilder.of(97110022, ImmutableMap.of("id", childCatalog.getId()));
        }
    }


    /**
     * 移动后更新, 先更新源目录的信息，再更新新目录的信息
     * @param childCatalog      移动的目录
     */
    public void moveUpdate(Set<String> sourceCatalogIdSet, Set<String> targetCatalogIdSet, AbstractCatalog childCatalog) {
        if (CollectionUtils.isEmpty(sourceCatalogIdSet)|| CollectionUtils.isEmpty(targetCatalogIdSet)) {
            return;
        }

        Integer totalCatalogQty = Optional.ofNullable(childCatalog.getTotalCatalogQty()).orElse(0) + 1;
        Integer totalFileQty = Optional.ofNullable(childCatalog.getTotalFileQty()).orElse(0);
        Long totalFileSize = Optional.ofNullable(childCatalog.getTotalFileSize()).orElse(0L);

        String sourceCatalogPath = sourceCatalogIdSet.stream().collect(Collectors.joining(","));
        String targetCatalogPath = targetCatalogIdSet.stream().collect(Collectors.joining(","));

        try {
            if (isNeedUpdate(totalCatalogQty, totalFileQty, totalFileSize)) {
                log.info("FileLibrary move element {} from {} to {}: Start update fileCatalog size info," +
                                " totalCatalogQty:{}, totalFileQty:{}, totalFileSize:{}", childCatalog.getId(),
                        sourceCatalogPath, targetCatalogPath, totalCatalogQty, totalFileQty,
                        totalFileSize);

                fileLibraryElementManager.updateSize(sourceCatalogIdSet, -totalCatalogQty, -totalFileQty, -totalFileSize);
                log.info("Success move catalog {} success update source catalogs {}!",
                        childCatalog.getId(), sourceCatalogPath);

                fileLibraryElementManager.updateSize(targetCatalogIdSet, totalCatalogQty, totalFileQty, totalFileSize);
                log.info("Success move catalog {} success update target catalogs {}!",
                        childCatalog.getId(), targetCatalogPath);
            }
        } catch (Exception e) {
            ImmutableMap<String, String> errorData =
                    ImmutableMap.of("childCatalog", childCatalog.getId(), "sourceCatalogPath", sourceCatalogPath,
                            "targetCatalogPath", targetCatalogPath, "operate", "MOVE");

            EventBuilder eventBuilder = new EventBuilder()
                    .withMessage(Optional.ofNullable(e.getMessage()).orElse(" ") +
                            ExceptionUtils.resolveStack(e.getStackTrace()))
                    .withLevel(Event.Level.ERROR)
                    .withTag("category", "FileLibrary")
                    .withTag("owner", childCatalog.getOwnerId())
                    .withExtra("ErrorData", errorData)
                    .withSentryInterface(new ExceptionInterface(e));
            Sentry.capture(eventBuilder);
            // TODO 异常码
            throw BusinessExceptionBuilder.of(97110022, ImmutableMap.of("id", childCatalog.getId()));
        }
    }

    /**
     * 是否需要更新信息
     * @param totalCatalogQty
     * @param totalFileQty
     * @param totalFileSize
     * @return
     */
    private boolean isNeedUpdate(Integer totalCatalogQty, Integer totalFileQty, Long totalFileSize) {
        return !Objects.equals(totalCatalogQty, 0) || !Objects.equals(totalFileQty, 0)
                || !Objects.equals(totalFileSize, 0L);
    }



    @Getter
    public enum Operate {
        DELETE(-1),
        Create(1);

        private Integer value;

        Operate(Integer value) {
            this.value = value;
        }

    }
}
