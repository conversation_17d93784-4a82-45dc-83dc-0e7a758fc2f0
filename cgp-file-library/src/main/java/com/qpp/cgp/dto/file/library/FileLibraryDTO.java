package com.qpp.cgp.dto.file.library;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("图库信息")
@Data
public class FileLibraryDTO {

    @ApiModelProperty("图库名称")
    private String name;

    @ApiModelProperty("Root目录ID")
    private String rootCatalogId;

    @ApiModelProperty("图库文件数量")
    private Integer totalFileQty;

    @ApiModelProperty("图库目录数量")
    private Integer totalCatalogQty;

    @ApiModelProperty("已使用容量")
    private Long used;

    @ApiModelProperty("剩余容量")
    private Long remain;

    @ApiModelProperty("最大容量")
    private Long total;

}
