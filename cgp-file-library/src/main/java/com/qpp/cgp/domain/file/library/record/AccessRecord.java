package com.qpp.cgp.domain.file.library.record;

import com.qpp.cgp.config.domaintype.RuntimeDomain;
import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

@Data
@RuntimeDomain
@Document(collection = "filelibraryaccessrecords")
public class AccessRecord extends MongoDomain {

    private String elementId;

    private String operatorId;

    private String operation;

    private Date timestamp;

    private String message;

}
