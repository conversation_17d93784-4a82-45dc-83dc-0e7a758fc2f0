package com.qpp.cgp.domain.file.library.owner;

import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.mongo.domain.MongoDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

@ApiModel("文件拥有者")
@Data
@ConfigDomain
@Document(collection = "fileowners")
public class FileOwner extends MongoDomain {

    @ApiModelProperty(value = "类型", required = true)
    private FileOwnerType type;

    @ApiModelProperty(value = "关联账户ID", required = true)
    private String relatedAccountId;

    @ApiModelProperty(value = "Root目录")
    private String rootCatalogId;

}
