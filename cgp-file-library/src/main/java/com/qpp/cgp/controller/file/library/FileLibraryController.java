package com.qpp.cgp.controller.file.library;

import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.file.library.FileLibrary;
import com.qpp.cgp.domain.file.library.FileLibraryElement;
import com.qpp.cgp.dto.file.library.*;
import com.qpp.cgp.dto.file.server.FileUploadFailureResultDTO;
import com.qpp.cgp.dto.file.server.FileUploadSuccessResultDTO;
import com.qpp.cgp.manager.file.library.FileLibraryElementManager;
import com.qpp.cgp.service.file.CatalogFinderService;
import com.qpp.cgp.service.file.FileMigrateService;
import com.qpp.cgp.service.file.library.*;
import com.qpp.cgp.service.file.library.operator.FileOperatorService;
import com.qpp.core.dto.FilterDTO;
import com.qpp.core.dto.SortDTO;
import com.qpp.core.utils.SecurityUtils;
import com.qpp.operation.log.OperationLogModule;
import com.qpp.web.business.sort.SortProcessor;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

@Log4j2(topic = "FileLibrary")
@OperationLogModule("file-library")
@Api(tags = "图库管理接口")
@RequestMapping("/api/file/library")
@RestController
public class FileLibraryController {

    @Autowired
    private FileCatalogService fileCatalogService;

    @Autowired
    private FileObjectService fileObjectService;

    @Autowired
    private UploadFileService uploadFileService;

    @Autowired
    private FileOperatorService fileOperatorService;

    @Autowired
    private CatalogFinderService catalogFinderService;

    @Autowired
    private FileLibraryService fileLibraryService;

    @Autowired
    private FileMigrateService fileMigrateService;

    @Autowired
    private FileLibraryServiceV2 fileLibraryServiceV2;

    @Autowired
    private FileLibraryElementManager fileLibraryElementManager;


    @ApiOperation("创建特殊目录（root / share）")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "角色标识符，[ user | clientId | shopOwner]", name = "role", required = true, paramType = "path"),
            @ApiImplicitParam(value = "目录类型，[ root | share ]", name = "catalogType", required = true, paramType = "path")
    })
    @PostMapping("/role/{role}/catalogs/{catalogType}")
    public FileCatalogDTO createSpecialCatalog(@PathVariable("role") String role, @PathVariable("catalogType") String catalogType) {
        return fileCatalogService.createSpecialCatalog(role, catalogType);
    }

    @ApiOperation("获取指定角色指定目录的子文件/文件夹分页信息")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "角色标识符，[ user | clientId | shopOwner]", name = "role", required = true, paramType = "path"),
            @ApiImplicitParam(value = "目录标识符，[ root | share | catalogId ]", name = "catalog", required = true, paramType = "path"),
            @ApiImplicitParam(value = "分页参数-第几页（从1开始）", name = "page", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(value = "分页参数-一页包含几条记录", name = "limit", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(value = "分页参数-排序参数", name = "sort", dataType = "String", paramType = "query"),
            @ApiImplicitParam(value = "分页参数-过滤参数", name = "filter", dataType = "String", paramType = "query")
    })
    @GetMapping("/role/{role}/catalogs/{catalog}/elements")
    public FileCatalogPage getCatalogChildren(@PathVariable("role") String role,
                                              @PathVariable("catalog") String catalog,
                                              @RequestParam("page") int page,
                                              @RequestParam("limit") int limit,
                                              @RequestParam(value = "sort", required = false) String sort,
                                              @RequestParam(value = "filter", required = false) String filter) {
        Sort sorts = SortProcessor.getSort(SortDTO.getOrders(sort));
        List<FilterDTO> filters = FilterDTO.getFilters(filter);
        PageRequest pageable = PageRequest.of(page - 1, limit, sorts);

        FileOperator operator = fileOperatorService.getFileOperator(role, SecurityUtils.getLoginedInUserId());
        catalog = catalogFinderService.convertCatalogId(catalog, operator);

        check(operator, catalog);

        return fileCatalogService.getCatalogPage(catalog, operator, pageable, filters);
    }

    @ApiOperation("在指定角色指定目录创建子文件/目录")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "角色标识符，[ user | clientId | shopOwner]", name = "role", required = true, paramType = "path"),
            @ApiImplicitParam(value = "目录标识符，[ root | share | catalogId ]", name = "catalog", required = true, paramType = "path")
    })
    @PostMapping("/role/{role}/catalogs/{catalog}/elements")
    public FileLibraryElementDTO createCatalogChildren(@PathVariable("role") String role,
                                                       @PathVariable("catalog") String catalog,
                                                       @RequestBody FileLibraryElementDTO element) {
        FileOperator operator = fileOperatorService.getFileOperator(role, SecurityUtils.getLoginedInUserId());
        catalog = catalogFinderService.convertCatalogId(catalog, operator);

        check(operator, catalog);

        FileLibraryElementDTO fileLibraryElementDto;
        if ("FileObject".equals(element.getType())) {
            fileLibraryElementDto = fileObjectService.createFileObject((FileObjectDTO) element, catalog, operator);
        } else {
            fileLibraryElementDto = fileCatalogService.createCatalog(catalog, (FileCatalogDTO) element, operator);
        }
        return fileLibraryElementDto;
    }

    @ApiOperation("在指定角色指定目录上传文件")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "角色标识符，[ user | clientId | shopOwner]", name = "role", required = true, paramType = "path"),
            @ApiImplicitParam(value = "目录标识符，[ root | share | catalogId ]", name = "catalog", required = true, paramType = "path")
    })
    @PostMapping("/role/{role}/catalogs/{catalog}/upload")
    public List<Object> createCatalogChildren(HttpServletRequest request,
                                              @PathVariable("role") String role,
                                              @PathVariable("catalog") String catalog,
                                              @RequestParam("file") MultipartFile[] files) {
        long start = System.currentTimeMillis();

        FileOperator operator = fileOperatorService.getFileOperator(role, SecurityUtils.getLoginedInUserId());

        // 当目录是 root 或 share 时忽略校验
        if (!("root".equalsIgnoreCase(catalog) || "share".equalsIgnoreCase(catalog))) {
            String catalogId = catalogFinderService.convertCatalogId(catalog, operator);
            check(operator, catalogId);
        }

        List<Object> fileUploadResults = uploadFileService.uploadFiles(request, role, catalog, files, operator);

        // 统计
        int successCount = 0;
        int failureCount = 0;
        long uploadedFileTotalSize = 0;
        for (Object result : fileUploadResults) {
            if (result instanceof FileUploadSuccessResultDTO) {
                successCount++;
                uploadedFileTotalSize += ((FileUploadSuccessResultDTO) result).getLength();
            } else if (result instanceof FileUploadFailureResultDTO) {
                failureCount++;
            }
        }

        log.info("FileLibrary Images Upload Completed, Success: {}, Failure: {}, TotalLength: {}byte, Cost: {}ms.",
                successCount, failureCount, uploadedFileTotalSize, System.currentTimeMillis() - start);

        return fileUploadResults;
    }

    @ApiOperation("在指定角色指定目录创建文件")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "角色标识符，[ user | clientId | shopOwner]", name = "role", required = true, paramType = "path"),
            @ApiImplicitParam(value = "目录标识符，[ root | share | catalogId ]", name = "catalog", required = true, paramType = "path")
    })
    @PostMapping("/role/{role}/catalogs/{catalog}/files")
    public List<Object> createCatalogChildrenFiles(HttpServletRequest request,
                                              @PathVariable("role") String role,
                                              @PathVariable("catalog") String catalog,
                                              @RequestBody CreateFileObjectDTO[] files) {
        long start = System.currentTimeMillis();

        FileOperator operator = fileOperatorService.getFileOperator(role, SecurityUtils.getLoginedInUserId());

        // 当目录是 root 或 share 时忽略校验
        if (!("root".equalsIgnoreCase(catalog) || "share".equalsIgnoreCase(catalog))) {
            String catalogId = catalogFinderService.convertCatalogId(catalog, operator);
            check(operator, catalogId);
        }

        List<Object> fileUploadResults = uploadFileService.uploadFiles(request, role, catalog, files, operator);

        // 统计
        int successCount = 0;
        int failureCount = 0;
        long uploadedFileTotalSize = 0;
        for (Object result : fileUploadResults) {
            if (result instanceof FileUploadSuccessResultDTO) {
                successCount++;
                uploadedFileTotalSize += ((FileUploadSuccessResultDTO) result).getLength();
            } else if (result instanceof FileUploadFailureResultDTO) {
                failureCount++;
            }
        }

        log.info("FileLibrary Images Upload Completed, Success: {}, Failure: {}, TotalLength: {}byte, Cost: {}ms.",
                successCount, failureCount, uploadedFileTotalSize, System.currentTimeMillis() - start);

        return fileUploadResults;
    }

    @ApiOperation("获取指定角色指定目录的目录树信息")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "角色标识符，[ user | clientId | shopOwner]", name = "role", required = true, paramType = "path"),
            @ApiImplicitParam(value = "目录标识符，[ root | share | catalogId ]", name = "catalog", required = true, paramType = "path")
    })
    @GetMapping("/role/{role}/catalogs/{catalog}/tree")
    public FileCatalogTree getCatalogTree(@PathVariable("role") String role, @PathVariable("catalog") String catalog) {
        FileOperator operator = fileOperatorService.getFileOperator(role, SecurityUtils.getLoginedInUserId());
        catalog = catalogFinderService.convertCatalogId(catalog, operator);

        check(operator, catalog);

        return fileCatalogService.getTree(catalog, operator);
    }

    @ApiOperation("获取指定角色指定目录下的目录列表信息")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "角色标识符，[ user | clientId | shopOwner]", name = "role", required = true, paramType = "path"),
            @ApiImplicitParam(value = "目录标识符，[ root | share | catalogId ]", name = "catalog", required = true, paramType = "path")
    })
    @GetMapping("/role/{role}/catalogs/{catalog}/subCatalogs")
    public List<FileCatalogDTO> getSubCatalogList(@PathVariable("role") String role, @PathVariable("catalog") String catalog) {
        FileOperator operator = fileOperatorService.getFileOperator(role, SecurityUtils.getLoginedInUserId());
        catalog = catalogFinderService.convertCatalogId(catalog, operator);

        check(operator, catalog);

        return fileCatalogService.getSubCatalogList(catalog, operator);
    }

    @ApiOperation("更新图库clientRootCatalog")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "角色标识符，[shopOwner]", name = "role", required = true, paramType = "path")
    })
    @PutMapping("/role/{role}/clientRootCatalog")
    public void updateFileLibraryClientRootCatalog(@PathVariable("role") String role,
                                                   @RequestBody UpdateClientRootCatalogDTO updateDTO) {
        FileOperator operator = fileOperatorService.getFileOperator(role, SecurityUtils.getLoginedInUserId());
        fileLibraryService.updateFileLibraryClientRootCatalog(updateDTO, operator);
    }

    @ApiOperation("更新图库shareRootCatalog")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "角色标识符，[user | shopOwner]", name = "role", required = true, paramType = "path")
    })
    @PutMapping("/role/{role}/shareRootCatalog")
    public void updateFileLibraryShareRootCatalog(@PathVariable("role") String role,
                                                  @RequestBody UpdateShareRootCatalogDTO updateDTO) {
        FileOperator operator = fileOperatorService.getFileOperator(role, SecurityUtils.getLoginedInUserId());
        fileLibraryService.updateFileLibraryShareRootCatalog(updateDTO, operator);
    }

    @ApiOperation("迁移文件")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "迁移源的用户的角色标识符，当前只支持 client", name = "srcRole", required = true, paramType = "path"),
            @ApiImplicitParam(value = "迁移源的用户的ID，当前只支持 clientId", name = "srcUserId", required = true, paramType = "path"),
            @ApiImplicitParam(value = "迁移目标的用户的角色标识符，当前只支持 client", name = "tgtRole", required = true, paramType = "path"),
            @ApiImplicitParam(value = "迁移目标的用户的ID，当前只支持 clientId", name = "tgtUserId", required = true, paramType = "path"),
    })
    @PostMapping("/migrate/{srcRole}/{srcUserId}/to/{tgtRole}/{tgtUserId}")
    public void migrate(@PathVariable("srcRole") String srcRole,
                        @PathVariable("srcUserId") String srcUserId,
                        @PathVariable("tgtRole") String tgtRole,
                        @PathVariable("tgtUserId") String tgtUserId) {
        // 剪切 srcUser 根目录上的文件到 tgtUser 的根目录。（是剪切根目录下的文件列表，不是根目录）
        // PS:
        // 1. 剪切的文件需要修改 ownerId
        // 2. 剪切后需要清空 srcUser 的根目录，但根目录这个文件夹不用删除
        // 3. 接口加上操作日志
        // 4. src client 只能是匿名 client
        fileMigrateService.migrate(srcRole, srcUserId, tgtRole, tgtUserId);
    }

    public void check(FileOperator fileOperator, String libraryObjectId) {
        FileLibrary library = fileLibraryServiceV2.findFileLibrary(fileOperator).get();
        String libraryId = library.getId();
        if (libraryId.equals(libraryObjectId)) {
            return;
        }

        if (!Objects.equals(libraryId, getFileLibrary(libraryObjectId))) {
            throw BusinessExceptionBuilder.of(97110019, ImmutableMap.of("fileElementId", libraryObjectId,
                    "fileLibraryId", libraryId));
        }
    }

    @PostMapping("initFileLibrary")
    public void initFileLibrary(@RequestParam("userId") Long userId) {
        fileLibraryService.initFileLibrary(userId);
    }

    /**
     * 获取 elementId 的所属图库 fileLibraryId
     *
     * @param elementId
     * @return
     */
    public String getFileLibrary(String elementId) {
        FileLibraryElement element = fileLibraryElementManager.findById(elementId);

        if (element == null) {
            return null;
        }

        String parentId = element.getParentId();

        if (StringUtils.isEmpty(parentId)) {
            return elementId;
        }

        return getFileLibrary(parentId);
    }

}
