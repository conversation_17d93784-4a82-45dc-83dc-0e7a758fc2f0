package com.qpp.cgp.controller.file.library;

import com.qpp.cgp.dto.file.library.FileLibraryImageUploadDTO;
import com.qpp.cgp.dto.file.library.FileObjectDTO;
import com.qpp.cgp.dto.file.library.FileOperator;
import com.qpp.cgp.dto.file.server.FileUploadFailureResultDTO;
import com.qpp.cgp.dto.file.server.FileUploadResultDTO;
import com.qpp.cgp.dto.file.server.FileUploadSuccessResultDTO;
import com.qpp.cgp.service.file.library.UploadFileService;
import com.qpp.cgp.service.file.library.operator.FileOperatorService;
import com.qpp.core.utils.SecurityUtils;
import com.qpp.operation.log.OperationLogModule;
import io.swagger.annotations.Api;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

@Log4j2(topic = "FileLibrary")
@OperationLogModule("file-library")
@Api(tags = "图库图片上传接口")
@RequestMapping("api/fileLibrary/images")
@RestController
public class FileLibraryImageUploadController {

    @Autowired
    private FileOperatorService fileOperatorService;

    @Autowired
    private UploadFileService uploadFileService;

    /**
     * role : ShopOwner
     * catalog : root
     *
     * @param files
     * @return
     */
    @PostMapping
    public List<FileLibraryImageUploadDTO> uploadImages(@RequestParam("file") MultipartFile[] files, HttpServletRequest request) {

        long start = System.currentTimeMillis();

        String role = "shopOwner";
        FileOperator fileOperator = fileOperatorService.getFileOperator(role, SecurityUtils.getLoginedInUserId());
        List<Object> fileUploadResults = uploadFileService.uploadFiles(request, role, "root", files, fileOperator);
        List<FileLibraryImageUploadDTO> imageUploadDTOS = fileUploadResults.stream()
                .filter(fileUploadResult -> fileUploadResult instanceof FileObjectDTO)
                .map(fileUploadSuccessResultDTO -> {
                    FileLibraryImageUploadDTO fileLibraryImageUploadDTO = new FileLibraryImageUploadDTO();
                    BeanUtils.copyProperties(fileUploadSuccessResultDTO, fileLibraryImageUploadDTO);
                    return fileLibraryImageUploadDTO;
                })
                .collect(Collectors.toList());

        // 统计
        int successCount = 0;
        int failureCount = 0;
        long uploadedFileTotalSize = 0;
        for (Object result : fileUploadResults) {
            if (result instanceof FileUploadSuccessResultDTO) {
                successCount++;
                uploadedFileTotalSize += ((FileUploadSuccessResultDTO) result).getLength();
            } else if (result instanceof FileUploadFailureResultDTO) {
                failureCount++;
            }
        }

        log.info("FileLibrary Images Upload Completed, Success: {}, Failure: {}, TotalLength: {}byte, Cost: {}ms.",
                successCount, failureCount, uploadedFileTotalSize, System.currentTimeMillis() - start);

        return imageUploadDTOS;
    }

}
