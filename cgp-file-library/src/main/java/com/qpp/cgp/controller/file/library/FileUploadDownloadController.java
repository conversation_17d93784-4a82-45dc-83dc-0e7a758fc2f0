package com.qpp.cgp.controller.file.library;

import com.qpp.cgp.service.file.server.FileDownloadService;
import com.qpp.operation.log.OperationLogModule;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@OperationLogModule("file-library")
@Api(tags = "文件上传/下载接口")
@RequestMapping("/api/file")
@RestController
public class FileUploadDownloadController {

    @Autowired
    private FileDownloadService fileDownloadService;

    // 下载文件

    @ApiOperation("文件下载")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "宽度，width/height 必须同时有值或为空", name = "width", paramType = "query"),
            @ApiImplicitParam(value = "高度，width/height 必须同时有值或为空", name = "height", paramType = "query"),
            @ApiImplicitParam(value = "目标格式", name = "format", paramType = "query")
    })
    @GetMapping(value = "/{fileName:.*}")
    public void downloadFile(HttpServletRequest request,
                             HttpServletResponse response,
                             @PathVariable("fileName") String fileName,
                             @RequestParam(value = "width", required = false) Integer width,
                             @RequestParam(value = "height", required = false) Integer height,
                             @RequestParam(value = "format", required = false) String format) {
        fileDownloadService.downloadFile(response, fileName, width, height, format);
    }

    @GetMapping(value = "/{fileName}/{format}")
    public void downloadFileByFormat(HttpServletRequest request,
                                     HttpServletResponse response,
                                     @PathVariable("fileName") String fileName,
                                     @PathVariable("format") String format) {
        fileDownloadService.downloadFileByFormat(response, fileName, format);
    }

    @GetMapping(value = "/{fileName}/{width}/{height}")
    public void downloadFileByWidthAndHeight(HttpServletRequest request,
                                             HttpServletResponse response,
                                             @PathVariable("fileName") String fileName,
                                             @PathVariable("width") int width,
                                             @PathVariable("height") int height) {
        fileDownloadService.downloadFileByWidthAndHeight(response, fileName, width, height);
    }

    @GetMapping(value = "/{fileName}/{width}/{height}/{format}")
    public void downloadFileByWidthAndHeightAndFormat(HttpServletRequest request,
                                                      HttpServletResponse response,
                                                      @PathVariable("fileName") String fileName,
                                                      @PathVariable("width") int width,
                                                      @PathVariable("height") int height,
                                                      @PathVariable("format") String format) {
        fileDownloadService.downloadFileByWidthAndHeightAndFormat(response, fileName, width, height, format);
    }

}
