package com.qpp.cgp.manager.file.library;

import com.qpp.cgp.config.mongo.MongoTemplateBeanNames;
import com.qpp.cgp.domain.file.library.record.AccessRecord;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.driver.HybridMongoTemplate;
import com.qpp.web.business.manager.AbstractMongoCurdManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Service
public class AccessRecordManager extends AbstractMongoCurdManager<AccessRecord, String> {

    public AccessRecordManager(@Autowired @Qualifier(MongoTemplateBeanNames.RUNTIME) HybridMongoTemplate mongoTemplate,
                               IdGenerator idgenerator) {
        super(mongoTemplate, idgenerator);
    }

}
