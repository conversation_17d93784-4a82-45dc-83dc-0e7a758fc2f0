package com.qpp.cgp.manager.file.library;

import com.qpp.cgp.config.mongo.MongoTemplateBeanNames;
import com.qpp.cgp.domain.file.library.owner.FileOwner;
import com.qpp.cgp.domain.file.library.owner.FileOwnerType;
import com.qpp.core.utils.SecurityUtils;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.driver.HybridMongoTemplate;
import com.qpp.web.business.manager.AbstractMongoCurdManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class FileOwnerManager extends AbstractMongoCurdManager<FileOwner,String> {

    public FileOwnerManager(@Autowired @Qualifier(MongoTemplateBeanNames.CONFIG) HybridMongoTemplate mongoTemplate,
                            IdGenerator idgenerator) {
        super(mongoTemplate, idgenerator);
    }

    public FileOwner getByRelatedAccountId(String relatedAccountId, FileOwnerType type) {
        return this.mongoTemplate.findOne(Query.query(new Criteria().andOperator(
                Criteria.where("relatedAccountId").is(relatedAccountId),
                Criteria.where("type").is(type.name())
        )), FileOwner.class);
    }

    public boolean saveRootCatalog(String id, String rootCatalogId) {
        return mongoTemplate.updateFirst(
                Query.query(Criteria.where(FileOwner.idProperty).is(id)),
                new Update().set("rootCatalogId", rootCatalogId),
                FileOwner.class
        ).getModifiedCount() > 0;
    }

    public FileOwner findAndCreateIfNotExists(String relatedAccountId, FileOwnerType type) {
        Criteria criteria = Criteria.where("relatedAccountId").is(relatedAccountId)
                .and("type").is(type.name());

        Query query = Query.query(criteria);

        Update update = new Update().setOnInsert(FileOwner.idProperty, idgenerator.generateId().toString())
                .setOnInsert("type", type)
                .setOnInsert("relatedAccountId", relatedAccountId)
                .setOnInsert("createdDate", new Date())
                .setOnInsert("createdBy", SecurityUtils.getLoginedInUserId())
                .setOnInsert("modifiedDate", new Date())
                .setOnInsert("clazz", FileOwner.class.getName());

        FindAndModifyOptions findAndModifyOptions = FindAndModifyOptions.options().upsert(true).returnNew(true);

        return mongoTemplate.findAndModify(query, update, findAndModifyOptions, FileOwner.class);
    }

}
