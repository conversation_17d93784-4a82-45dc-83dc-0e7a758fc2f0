package com.qpp.cgp.domain.bom.dynamic;

import com.qpp.cgp.value.ValueEx;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/7
 */
@Getter
@Setter
public class PathDataValue extends IAttributeValue {

    @NotNull
    private ValueEx template;

    @NotEmpty
    private List<String> layers = new ArrayList<>();

    @Valid
    private List<DsVariable> variables = new ArrayList<>();

    /**
     * 前端VaueEx组件数据
     */
    private Map<String, Object> templateDto;

    /**
     * 前端VaueEx组件数据
     */
    private Map<String, Object> layersDto;
}
