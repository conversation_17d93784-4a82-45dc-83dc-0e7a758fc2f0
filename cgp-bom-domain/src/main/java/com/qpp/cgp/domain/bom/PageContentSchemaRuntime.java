package com.qpp.cgp.domain.bom;

import com.qpp.cgp.config.domaintype.RuntimeDomain;
import com.qpp.cgp.domain.bom.attribute.RtType;
import com.qpp.cgp.domain.bom.datasource.VariableDataSource;
import com.qpp.cgp.domain.bom.runtime.RoundingMode;
import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RuntimeDomain
@Document(collection = "pagecontentschemaruntimes")
@NoArgsConstructor
@Data
public class PageContentSchemaRuntime extends MongoDomain {
    private String code;

    private String name;

    private String description;

    private double width;

    private double height;

    private List<Map<String, Object>> layers;

    private Map<String, Object> clipPath;

    private List<Map<String, Object>> priceAreas;

    private List<Map<String, Object>> displayObjectConstraints;

    private List<Map<String, Object>> canvases;

    private RtType rtType;

    private List<PageContentItemPlaceholder> pageContentItemPlaceholders;

    private String templateId;

    private PageContentSchemaGroup pageContentSchemaGroup;

    private VariableDataSource mainVariableDataSource;

    private List<Double> bound;

    /**
     * 是否经过PCS预处理
     */
    private boolean preprocessed = false;

    /**
     * 结构版本
     */
    private int structVersion = 2;

    /**
     * 取整方式
     */
    private RoundingMode roundingMode;
}
