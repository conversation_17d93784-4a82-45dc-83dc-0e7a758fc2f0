package com.qpp.cgp.domain.bom;

import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.mongo.domain.LongMongoDomain;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 物料复写主组件的路径
 */
@Document(collection = "materialcomponentinfos")
@Data
@ConfigDomain
public class MaterialComponentInfo extends LongMongoDomain {


    /**
     * 关联的顶层物料
     */
    private Material topMaterial;

    /**
     * 主组件的物料路径
     */
    private String mainComponentMaterialPath;
}
