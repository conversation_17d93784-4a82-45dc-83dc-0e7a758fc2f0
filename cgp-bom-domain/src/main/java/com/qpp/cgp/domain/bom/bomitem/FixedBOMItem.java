package com.qpp.cgp.domain.bom.bomitem;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.qpp.cgp.domain.bom.Material;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by smart on 8/17/2017.
 */
@NoArgsConstructor
@Data
public class FixedBOMItem extends BOMItem {

    private Boolean isCompleted = false;

    @JsonIgnore
    @Override
    public List<Material> getChildMaterials() {
        List<Material> materials = new ArrayList<>();

        if (null != getItemMaterial()) {
            materials.add(getItemMaterial());
        }

        return materials;
    }
}
