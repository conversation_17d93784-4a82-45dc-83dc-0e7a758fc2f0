package com.qpp.cgp.domain.bom;

import com.qpp.mongo.domain.MongoDomain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * Created by smart on 8/19/2017.
 */
@NoArgsConstructor
@Data
@AllArgsConstructor
@Document(collection = "materialcategories")
public class MaterialCategory extends MongoDomain {


    private String name;

    private String parentId;

}
