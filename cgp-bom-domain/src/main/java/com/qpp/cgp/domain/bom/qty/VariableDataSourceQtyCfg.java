package com.qpp.cgp.domain.bom.qty;

import com.qpp.cgp.domain.bom.QuantityRange;
import com.qpp.cgp.domain.bom.datasource.VariableDataSource;
import com.qpp.cgp.value.ValueEx;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> jerry
 * @version : 1.0
 * Description : variableDataSource数量配置
 * Create Date : 2019-11-13 14:39
 **/
@NoArgsConstructor
@Data
public class VariableDataSourceQtyCfg {

    private String _id;

    /**
     * 数量
     */
    private ValueEx vdQtyCfg;

    /**
     * 范围
     */
    private QuantityRange qtyRange;

    /**
     * VariableDataSource的Id引用
     */
    private VariableDataSource variableDataSource;

    /**
     * 描述
     */
    private String description;
}
