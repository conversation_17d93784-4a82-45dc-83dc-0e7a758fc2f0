package com.qpp.cgp.domain.bom;

import com.qpp.cgp.domain.bom.datasource.DsDataSource;
import com.qpp.cgp.domain.bom.datasource.VariableDataSource;
import com.qpp.cgp.domain.bom.qty.PlaceHolderVdCfg;
import com.qpp.cgp.domain.bom.qty.VariableDataSourceQtyCfg;
import com.qpp.cgp.domain.bom.runtime.RtObject;
import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@NoArgsConstructor
@Data
@Document(collection = "materialviewtypes")
public class MaterialViewType extends MongoDomain {

    private String name;

    private String designTypeId;

    private RtObject preDesignObject;

    private Integer sequenceNumber;

    private String description;

    private String pageContentStrategy;

    private String pageContentFetchStrategy;

    private Integer pageContentQuantity;

    private Integer viewQuantity;

    private String pageType;

    private PageContentSchema pageContentSchema;

    private String pageContentIndexExpression;

    private QuantityRange pageContentInstanceRange;

    private QuantityRange pageContentRange;

    private String userAssign;

    private VariableDataSource mainVariableDataSource;

    private DsDataSource dsDataSource;

    private TemplatePlaceholder[] pcsPlaceholders;

    private MaterialViewTypeTemplateType templateType;

    /**
     * 上传数量配置
     */
    private List<VariableDataSourceQtyCfg> variableDataSourceQtyCfgs;

    /**
     * 模板数量配置
     */
    private List<PlaceHolderVdCfg> placeHolderVdCfgs;

    /**
     * 实际高
     */
    private Double actualHeight;

    /**
     * 实际宽
     */
    private Double actualWidth;

    /**
     * 实际尺寸单位
     */
    private String unit;

    private String code;
}
