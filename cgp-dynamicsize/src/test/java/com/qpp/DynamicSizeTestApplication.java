package com.qpp;

/**
 * Created by admin on 2018/8/31.
 */

import com.qpp.boot.autoconfigure.web.security.QppWebSecurityConfiguration;
import com.qpp.cgp.DocumentToMapConverter;
import com.qpp.mongo.driver.HybridMongoTemplate;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.gson.GsonAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.embedded.EmbeddedMongoAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.PropertySource;
import org.springframework.data.mongodb.MongoDbFactory;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.core.convert.MongoConverter;
import org.springframework.data.mongodb.core.convert.MongoCustomConversions;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 2018/8/2
 */
@EnableCaching
@SpringBootApplication(exclude = {
        QppWebSecurityConfiguration.class,
        GsonAutoConfiguration.class
        , EmbeddedMongoAutoConfiguration.class
}, scanBasePackages = {"com.qpp.cgp", "com.qpp.value", "com.qpp.expression"})
@PropertySource("classpath:application.yml")
@PropertySource("classpath:mail.service.properties")
public class DynamicSizeTestApplication {
    public static void main(String[] args) {
        SpringApplication.run(DynamicSizeTestApplication.class, args);
    }

    @Primary
    @Bean
    public HybridMongoTemplate hybirdMongoTemplate(MongoDbFactory mongoDbFactory,
                                                   MongoConverter converter) {

        if (converter instanceof MappingMongoConverter) {
            MappingMongoConverter mappingMongoConverter = (MappingMongoConverter) converter;


            final MongoCustomConversions mongoCustomConversions = new MongoCustomConversions(Arrays.asList(new DocumentToMapConverter()));
            ((MappingMongoConverter) converter).afterPropertiesSet();
            mappingMongoConverter.setCustomConversions(mongoCustomConversions);
        }

        return new HybridMongoTemplate(mongoDbFactory, converter);
    }
}
