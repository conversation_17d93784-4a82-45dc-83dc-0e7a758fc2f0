package com.qpp.cgp;

import com.qpp.mongo.driver.HybridMongoTemplate;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.mongodb.MongoDbFactory;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.core.convert.MongoConverter;
import org.springframework.data.mongodb.core.convert.MongoCustomConversions;

import java.util.Arrays;

/**
 * <AUTHOR> Lee 2020/3/24 14:24
 */
@Configuration
public class CustomMongoConfiguration {

    @Primary
    @Bean
    public HybridMongoTemplate hybirdMongoTemplate(MongoDbFactory mongoDbFactory,
                                                   MongoConverter converter) {

        if (converter instanceof MappingMongoConverter) {
            MappingMongoConverter mappingMongoConverter = (MappingMongoConverter) converter;


            final MongoCustomConversions mongoCustomConversions = new MongoCustomConversions(Arrays.asList(new DocumentToMapConverter()));
            ((MappingMongoConverter) converter).afterPropertiesSet();
            mappingMongoConverter.setCustomConversions(mongoCustomConversions);
        }

        return new HybridMongoTemplate(mongoDbFactory, converter);
    }

}
