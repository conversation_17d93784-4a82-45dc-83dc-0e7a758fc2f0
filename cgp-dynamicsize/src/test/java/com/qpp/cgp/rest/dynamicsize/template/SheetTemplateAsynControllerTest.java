//package com.qpp.cgp.rest.dynamicsize.template;
//
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.http.MediaType;
//import org.springframework.test.context.junit4.SpringRunner;
//import org.springframework.test.web.servlet.MockMvc;
//
//import static org.junit.Assert.*;
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
//import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
//
//@RunWith(SpringRunner.class)
//@SpringBootTest
//@AutoConfigureMockMvc(addFilters = false)
//public class SheetTemplateAsynControllerTest {
//
//    @Autowired
//    private MockMvc mock;
//
//    @Test
//    public void generateSheetBySkuAndPageType() {
//    }
//
//    @Test
//    public void generateSheetTemplate() {
//    }
//
//    @Test
//    public void generateProductSheetTemplate() throws Exception {
//        mock.perform(post("/api/dynamicsize/producttype/signature_TB_pdf/impression/sheet/asyn").content("{\"productId\":\"signature_TB_pdf\",\"W1\":64,\"H1\":67,\"W2\":60,\"H2\":60,\"depth\":0,\"type\":\"signature\"}").contentType(MediaType.APPLICATION_JSON)).andDo(print());
//    }
//}