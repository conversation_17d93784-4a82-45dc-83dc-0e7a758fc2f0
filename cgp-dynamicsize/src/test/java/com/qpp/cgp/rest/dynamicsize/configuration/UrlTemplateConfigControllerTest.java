//package com.qpp.cgp.rest.dynamicsize.configuration;
//
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//import org.springframework.test.web.servlet.MockMvc;
//
//import static org.junit.Assert.*;
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
//import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
//
///**
// * Created by admin on 2019/4/8.
// */
//@RunWith(SpringRunner.class)
//@SpringBootTest
//@AutoConfigureMockMvc(addFilters = false)
//public class UrlTemplateConfigControllerTest {
//
//    @Autowired
//    private MockMvc mock;
//
//    @Test
//    public void getOne() throws Exception {
//        mock.perform(get("/api/dynamicsize/urltemplates/1")).andDo(print());
//    }
//}