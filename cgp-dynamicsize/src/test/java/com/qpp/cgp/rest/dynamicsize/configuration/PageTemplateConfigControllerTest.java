//package com.qpp.cgp.rest.dynamicsize.configuration;
//
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.http.MediaType;
//import org.springframework.test.context.junit4.SpringRunner;
//import org.springframework.test.web.servlet.MockMvc;
//
//import static org.junit.Assert.*;
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
//import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
//
///**
// * Created by admin on 2019/4/8.
// */
//@RunWith(SpringRunner.class)
//@SpringBootTest
//@AutoConfigureMockMvc(addFilters = false)
//public class PageTemplateConfigControllerTest {
//
//    @Autowired
//    private MockMvc mock;
//
//    @Test
//    public void getOne() throws Exception {
//        mock.perform(get("/api/dynamicsize/pagetemplates/1")).andDo(print());
//    }
//
//    @Test
//    public void creat() throws Exception {
//        mock.perform(post("/api/dynamicsize/pagetemplates").content("{\"templateFileName\":\"PS008026001_front.svg\",\"dataSourceId\":12345,\"pageType\":\"test04\"}").contentType(MediaType.APPLICATION_JSON)).andDo(print());
//    }
//
//    @Test
//    public void update() throws Exception {
//        mock.perform(put("/api/dynamicsize/pagetemplates/1560922").content("{\"_id\":1560922,\"templateFileName\":\"PS008026001_front_1.svg\",\"dataSourceId\":12345,\"pageType\":\"test04\"}").contentType(MediaType.APPLICATION_JSON)).andDo(print());
//    }
//
//    @Test
//    public void copy() throws Exception {
//        mock.perform(post("/api/dynamicsize/pagetemplates/1/duplicate").contentType(MediaType.APPLICATION_JSON)).andDo(print());
//    }
//}