package com.qpp.cgp.service.dynamicsize.configuration;

import com.qpp.cgp.manager.dynamicsize.configuration.SheetTemplateConfigManager;
import org.assertj.core.api.Assertions;
import org.bson.Document;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @Date 2024/4/23 14:38
 */
@ExtendWith(MockitoExtension.class)
public class SheetTemplateConfigCopyServiceTest {

    @Spy
    @InjectMocks
    private SheetTemplateConfigCopyService sheetTemplateConfigCopyService;

    @Mock
    private TemplateConfigCopyService templateConfigCopyService;

    @Mock
    private SheetTemplateConfigManager sheetTemplateConfigManager;

    /**
     * 关联了DsDataSource的模板
     */
    @Test
    public void testCopySheetTemplateConfigByRelateDsDataSource() {
        Document sheetConfigDoc = generateDocument();
        sheetConfigDoc.put("dataSourceId", 60278779);
        Mockito.when(sheetTemplateConfigManager.findById(any())).thenReturn(sheetConfigDoc);

        Mockito.when(templateConfigCopyService.copyDsDataSource(any())).thenReturn(70027740L);
        Mockito.when(sheetTemplateConfigManager.saveNew(any())).thenReturn(sheetConfigDoc);

        Document document = sheetTemplateConfigCopyService.copySheetTemplateConfig(3978978L);
        Assertions.assertThat(Long.valueOf(document.get("dataSourceId").toString())).isEqualTo(70027740);
    }


    /**
     * 没有关联DsDataSource的模板
     */
    @Test
    public void testCopySheetTemplateConfigByNotRelateDsDataSource() {
        Document sheetConfigDoc = generateDocument();
        Mockito.when(sheetTemplateConfigManager.findById(any())).thenReturn(sheetConfigDoc);

        Document copySheetConfigDoc = generateDocument();
        copySheetConfigDoc.put("_id", 1111111);

        Mockito.when(sheetTemplateConfigManager.saveNew(any())).thenReturn(copySheetConfigDoc);

        Document document = sheetTemplateConfigCopyService.copySheetTemplateConfig(587909L);
        Assertions.assertThat(Long.valueOf(document.get("_id").toString())).isEqualTo(1111111);
        Assertions.assertThat(document.get("templateFileName")).isEqualTo("PS014028002_EPS9908_x1_back_gray.svg");
        Assertions.assertThat(document.get("productType")).isEqualTo("PS014028002Empty");
        Assertions.assertThat(((List<Document>)document.get("impressionPlaceholders")).get(0).size()).isEqualTo(4);
    }


    private Document generateDocument() {
        Document document = new Document();
        document.put("_id", 587909);
        document.put("templateFileName", "PS014028002_EPS9908_x1_back_gray.svg");
        document.put("productType", "PS014028002Empty");
        document.put("sheetType", "backGray");
        document.put("index", 3);
        document.put("strategy", "manyToOne");
        document.put("condition", "function expression(p){ return p.width<= 4262 && p.height <= 2986; }");
        document.put("fileNameSuffix", "_地盒灰板刀線_EPS9908");

        List<Document> list = new ArrayList<>();
        Document list1 = new Document();
        list1.put("id", 1);
        list1.put("index", 1);
        list1.put("contentType", "PS014028002EmptybackGray");
        list1.put("contentSortOrder", 1);
        list.add(list1);

        document.put("impressionPlaceholders", list);

        return document;
    }
}
