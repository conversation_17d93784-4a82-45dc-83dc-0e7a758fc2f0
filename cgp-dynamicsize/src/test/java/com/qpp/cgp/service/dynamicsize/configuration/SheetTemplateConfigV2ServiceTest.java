package com.qpp.cgp.service.dynamicsize.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.dynamicsize.configuration.v2.SheetTemplateConfigV2;
import com.qpp.cgp.domain.dynamicsize.dto.PlaceholderV2DTO;
import com.qpp.cgp.domain.dynamicsize.dto.SheetTemplateConfigV2InputDTO;
import com.qpp.cgp.domain.dynamicsize.dto.SheetTemplateConfigV2OutputDTO;
import com.qpp.cgp.manager.dynamicsize.configuration.v2.SheetTemplateConfigV2Manager;
import com.qpp.cgp.model.dynamicsize.common.DataResultDTO;
import com.qpp.core.exception.BusinessException;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.mockStatic;

/**
 * <AUTHOR>
 * @Package com.qpp.cgp.service.dynamicsize.configuration
 * @Date 2025/1/15 13:57
 */
@ExtendWith(MockitoExtension.class)
public class SheetTemplateConfigV2ServiceTest {

    @Spy
    @InjectMocks
    private SheetTemplateConfigV2Service service;

    @Mock
    private SheetTemplateConfigV2Manager sheetTemplateConfigV2Manager;


    /**
     * 模板的元素为空时抛出异常 109000013
     */
    @Test
    public void testGenerateCombinationSheetByElementsIsEmpty() {
        Long templateId = 244498845l;
        BusinessException businessException = new BusinessException();
        businessException.setMessage("244498845 : Template's elements can not be empty ! ");

        MockedStatic<BusinessExceptionBuilder> businessExceptionBuilderMocked = mockStatic(BusinessExceptionBuilder.class);
        businessExceptionBuilderMocked.when(() -> BusinessExceptionBuilder.of(
                        ArgumentMatchers.eq(109000013),
                        ArgumentMatchers.eq(ImmutableMap.of("templateId", templateId))))
                .thenReturn(businessException);

        SheetTemplateConfigV2 template = generateTemplate();
        template.setElements(null);
        List<SheetTemplateConfigV2InputDTO> inputDTOS = generateInputDtoList();
        Mockito.when(sheetTemplateConfigV2Manager.findById(templateId)).thenReturn(template);
        Assertions.assertThatThrownBy(() -> service.generateCombinationSheet(inputDTOS, templateId))
                .hasMessage("244498845 : Template's elements can not be empty ! ");
    }

    /**
     * Layout的Sequences为null时抛出异常   109000012
     */
    @Test
    public void testGenerateCombinationSheetBySequencesIsEmpty() {
        Long templateId = 244498845l;
        BusinessException businessException = new BusinessException();
        businessException.setMessage("244498845 : PlaceholderLayout's sequences can not be empty ! ");

        MockedStatic<BusinessExceptionBuilder> businessExceptionBuilderMocked = mockStatic(BusinessExceptionBuilder.class);
        businessExceptionBuilderMocked.when(() -> BusinessExceptionBuilder.of(
                        ArgumentMatchers.eq(109000012),
                        ArgumentMatchers.eq(ImmutableMap.of("templateId", templateId))))
                .thenReturn(businessException);

        SheetTemplateConfigV2 template = generateTemplate();
        template.getLayout().setSequences(null);
        List<SheetTemplateConfigV2InputDTO> inputDTOS = generateInputDtoList();
        Mockito.when(sheetTemplateConfigV2Manager.findById(templateId)).thenReturn(template);
        Assertions.assertThatThrownBy(() -> service.generateCombinationSheet(inputDTOS, templateId))
                .hasMessage("244498845 : PlaceholderLayout's sequences can not be empty ! ");
    }

    /**
     * 模板的Layout为null时抛出异常  109000011
     */
    @Test
    public void testGenerateCombinationSheetByLayoutIsEmpty() {
        Long templateId = 244498845l;
        BusinessException businessException = new BusinessException();
        businessException.setMessage("244498845 : Template's layout can not be empty ! ");

        MockedStatic<BusinessExceptionBuilder> businessExceptionBuilderMocked = mockStatic(BusinessExceptionBuilder.class);
        businessExceptionBuilderMocked.when(() -> BusinessExceptionBuilder.of(
                        ArgumentMatchers.eq(109000011),
                        ArgumentMatchers.eq(ImmutableMap.of("templateId", templateId))))
                .thenReturn(businessException);

        SheetTemplateConfigV2 template = generateTemplate();
        template.setLayout(null);
        List<SheetTemplateConfigV2InputDTO> inputDTOS = generateInputDtoList();
        Mockito.when(sheetTemplateConfigV2Manager.findById(templateId)).thenReturn(template);
        Assertions.assertThatThrownBy(() -> service.generateCombinationSheet(inputDTOS, templateId))
                .hasMessage("244498845 : Template's layout can not be empty ! ");
    }

    /**
     * 正确生成一个模板信息
     */
    @Test
    public void testGenerateCombinationSheet() {
        Long templateId = 244498845l;
        SheetTemplateConfigV2 template = generateTemplate();
        List<SheetTemplateConfigV2InputDTO> inputDTOS = generateInputDtoList();
        Mockito.when(sheetTemplateConfigV2Manager.findById(templateId)).thenReturn(template);

        DataResultDTO resultDTO = service.generateCombinationSheet(inputDTOS, templateId);
        List<SheetTemplateConfigV2OutputDTO> results = resultDTO.getResults();

        Assertions.assertThat(results.size()).isEqualTo(1);

        //生成Svg图形信息
        for (SheetTemplateConfigV2OutputDTO result : results) {
            String s = generateSvgStr(result.getWidth(), result.getHeight(), result.getPlaceholders());
            System.out.println(s);
        }
    }

    private SheetTemplateConfigV2 generateTemplate() {
        String jsonStr = "{\"_id\":244498845,\"idReference\":null,\"clazz\":\"com.qpp.cgp.domain.dynamicsize.configuration.v2.SheetTemplateConfigV2\",\"createdDate\":null,\"createdBy\":null,\"modifiedDate\":null,\"modifiedBy\":null,\"dataSourceId\":null,\"templateFileName\":null,\"templateFileNameV2\":null,\"textTemplateFileName\":null,\"placeholders\":null,\"condition\":null,\"layout\":{\"arrange\":\"ROW\",\"margin\":null,\"sequences\":[{\"align\":\"TOP\",\"margin\":{\"top\":5.0,\"bottom\":5.0,\"left\":5.0,\"right\":5.0},\"sortOrder\":1,\"placeholders\":[{\"margin\":{\"top\":5.0,\"bottom\":5.0,\"left\":5.0,\"right\":5.0},\"rotate\":0.0,\"sortOrder\":1},{\"margin\":{\"top\":10.0,\"bottom\":10.0,\"left\":10.0,\"right\":10.0},\"rotate\":0.0,\"sortOrder\":2}]},{\"align\":\"CENTER\",\"margin\":{\"top\":0.0,\"bottom\":0.0,\"left\":0.0,\"right\":0.0},\"sortOrder\":2,\"placeholders\":[{\"margin\":{\"top\":0.0,\"bottom\":0.0,\"left\":0.0,\"right\":0.0},\"rotate\":0.0,\"sortOrder\":3},{\"margin\":{\"top\":5.0,\"bottom\":5.0,\"left\":5.0,\"right\":5.0},\"rotate\":0.0,\"sortOrder\":4}]},{\"align\":\"BOTTOM\",\"margin\":{\"top\":20.0,\"bottom\":20.0,\"left\":20.0,\"right\":20.0},\"sortOrder\":3,\"placeholders\":[{\"margin\":{\"top\":5.0,\"bottom\":5.0,\"left\":5.0,\"right\":5.0},\"rotate\":0.0,\"sortOrder\":5},{\"margin\":{\"top\":5.0,\"bottom\":5.0,\"left\":5.0,\"right\":5.0},\"rotate\":0.0,\"sortOrder\":6}]}]},\"elements\":[{\"isRotateCombination\":false,\"contentTypeId\":244498806,\"contentSortOrder\":1,\"sortOrder\":1,\"combinationRotate\":null},{\"isRotateCombination\":false,\"contentTypeId\":244498807,\"contentSortOrder\":2,\"sortOrder\":2,\"combinationRotate\":null},{\"isRotateCombination\":false,\"contentTypeId\":244498815,\"contentSortOrder\":3,\"sortOrder\":3,\"combinationRotate\":null},{\"isRotateCombination\":false,\"contentTypeId\":244498816,\"contentSortOrder\":4,\"sortOrder\":4,\"combinationRotate\":null},{\"isRotateCombination\":false,\"contentTypeId\":244498806,\"contentSortOrder\":5,\"sortOrder\":5,\"combinationRotate\":null},{\"isRotateCombination\":false,\"contentTypeId\":244498822,\"contentSortOrder\":6,\"sortOrder\":6,\"combinationRotate\":null}]}";
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            SheetTemplateConfigV2 template = objectMapper.readValue(jsonStr, SheetTemplateConfigV2.class);
            return template;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return new SheetTemplateConfigV2();
    }

    private String generateSvgStr(Double finalWidth, Double finalHeight, List<PlaceholderV2DTO> placeholder) {
        StringBuilder svgBuilder = new StringBuilder();
        svgBuilder.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");
        svgBuilder.append("<svg id=\"dynamicSizeSvg\" data-name=\"dynamicSizeSvg\" xmlns=\"http://www.w3.org/2000/svg\" ")
                .append("width=\"").append(finalWidth).append("\" height=\"").append(finalHeight).append("\" ")
                .append("xmlns:xlink=\"http://www.w3.org/1999/xlink\" ")
                .append("viewBox=\"0 0 ").append(finalWidth).append(" ").append(finalHeight).append("\">\n");

        for (PlaceholderV2DTO placeholderV2DTO : placeholder) {
            String placeholderRect = createRectElement(placeholderV2DTO);
            svgBuilder.append(placeholderRect);
        }

        svgBuilder.append("</svg>");
        return svgBuilder.toString();
    }

    private String createRectElement(PlaceholderV2DTO placeholder) {
        double x = placeholder.getX() != null ? placeholder.getX() : 0.0;
        double y = placeholder.getY() != null ? placeholder.getY() : 0.0;
        double width = placeholder.getWidth() != null ? placeholder.getWidth() : 0.0;
        double height = placeholder.getHeight() != null ? placeholder.getHeight() : 0.0;

        StringBuilder rectBuilder = new StringBuilder();
        rectBuilder.append("    <rect x=\"").append(x).append("\" y=\"").append(y)
                .append("\" width=\"").append(width).append("\" height=\"").append(height)
                .append("\" fill=\"none\" stroke=\"red\" />\n");

        return rectBuilder.toString();
    }

    private List<SheetTemplateConfigV2InputDTO> generateInputDtoList() {
        List<SheetTemplateConfigV2InputDTO> inputDTOS = new ArrayList<>();
        inputDTOS.add(new SheetTemplateConfigV2InputDTO(244498806l, 1, 40.0, 40.0));
        inputDTOS.add(new SheetTemplateConfigV2InputDTO(244498807l, 2, 80.0, 80.0));
        inputDTOS.add(new SheetTemplateConfigV2InputDTO(244498815l, 3, 40.0, 40.0));
        inputDTOS.add(new SheetTemplateConfigV2InputDTO(244498816l, 4, 80.0, 80.0));
        inputDTOS.add(new SheetTemplateConfigV2InputDTO(244498806l, 5, 40.0, 40.0));
        inputDTOS.add(new SheetTemplateConfigV2InputDTO(244498822l, 6, 100.0, 100.0));
        return inputDTOS;
    }

}
