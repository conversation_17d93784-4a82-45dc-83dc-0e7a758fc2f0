package com.qpp.cgp.service.dynamicsize.configuration;

import com.qpp.cgp.manager.dynamicsize.configuration.DsDataSourceManager;
import com.qpp.cgp.manager.dynamicsize.configuration.UrlTemplateManager;
import org.assertj.core.api.Assertions;
import org.bson.Document;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @Date 2024/4/23 15:32
 */
@ExtendWith(MockitoExtension.class)
public class TemplateConfigCopyServiceTest {

    @Spy
    @InjectMocks
    private TemplateConfigCopyService templateConfigCopyService;

    @Mock
    private DsDataSourceManager dataSourceManager;

    @Mock
    private UrlTemplateManager urlTemplateManager;

    @Test
    public void testCopyDsDataSource(){

        Document dataSource = generateDsDataSourceDocument();
        Mockito.when(dataSourceManager.findById(any())).thenReturn(dataSource);
        Mockito.when(urlTemplateManager.copy(any())).thenReturn(generateUrlTemplateDocument());
        Document newDataSource = generateDsDataSourceDocument();
        newDataSource.put("_id", 133771);
        Mockito.when(dataSourceManager.saveNew(any())).thenReturn(newDataSource);

        templateConfigCopyService.copyDsDataSource(133770L);
        Assertions.assertThat(((List<Document>) dataSource.get("selectors")).get(0).get("urlTemplateId"))
                .isEqualTo(212529L);
    }


    private Document generateDsDataSourceDocument() {
        Document document = new Document();
        document.put("_id", 133770);
        document.put("type", "ImpactSvg");
        document.put("description", "PS008026005-無折遊戲板數據配置--Builder");
        document.put("author", "<EMAIL>");

        List<Document> list = new ArrayList<>();
        Document list1 = new Document();
        list1.put("key", "backWidth");
        list1.put("selector", "svg");
        list1.put("attr", "width");
        list1.put("urlTemplateId", 212525);
        list.add(list1);

        Document list2 = new Document();
        list2.put("key", "backHeight");
        list2.put("selector", "svg");
        list2.put("attr", "height");
        list2.put("urlTemplateId", 212525);
        list.add(list2);

        document.put("selectors", list);

        return document;
    }


    private Document generateUrlTemplateDocument() {
        Document document = new Document();
        document.put("_id", 212529);

        return document;
    }


}
