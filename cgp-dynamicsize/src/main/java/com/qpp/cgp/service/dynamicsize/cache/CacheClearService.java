package com.qpp.cgp.service.dynamicsize.cache;

import com.qpp.cgp.manager.dynamicsize.result.DynamicSizeDieLineResultManager;
import com.qpp.cgp.model.dynamicsize.impact.ImpactParameterDTO;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * 缓存清理服务
 *
 * <AUTHOR>
 * @since 2020/12/18
 */
@Service
public class CacheClearService {

    private final static String dieLineCacheKeyPrefix = "ds";

    private final static String KafkaRequestCacheKeyPrefix = "ds.kafka";

    @Autowired
    RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private RedisKeyGenerator redisKeyGenerator;

    @Autowired
    private DynamicSizeDieLineResultManager dynamicSizeDieLineResultManager;

    public void clearAllCache() {
        clearAllDieLineCache();
        clearAllKafkaRequestCache();
    }

    public void clearAllDieLineCache() {
        redisTemplate.delete(getAllDieLineCacheKeys());
    }

    public void clearAllKafkaRequestCache() {
        redisTemplate.delete(getAllKafkaRequestCacheKeys());
    }

    public void clearDieLineCache(ImpactParameterDTO parameter) {
        String key = getDieLineCacheKey(parameter);
        if (StringUtils.isNotBlank(key) && redisTemplate.hasKey(key) ) {
            redisTemplate.delete(key);
        }
    }

    public void clearKafkaRequestCache(ImpactParameterDTO parameter) {
        String key = getKafkaRequestCacheKey(parameter);
        if (StringUtils.isNotBlank(key) && redisTemplate.hasKey(key) ) {
            redisTemplate.delete(key);
        }
    }

    public Set<String> getAllDieLineCacheKeys() {
        return redisTemplate.keys(dieLineCacheKeyPrefix+":*");
    }

    public Set<String> getAllKafkaRequestCacheKeys() {
        return redisTemplate.keys(KafkaRequestCacheKeyPrefix+":*");
    }

    public String getDieLineCacheKey(ImpactParameterDTO parameter) {
        return redisKeyGenerator.getDieLineCacheKey(parameter);
    }

    public String getKafkaRequestCacheKey(ImpactParameterDTO parameter) {
        return redisKeyGenerator.getKafkaRequestCacheKey(parameter);
    }

    public void clearAllRedisCachesAndMongoCaches() {
        dynamicSizeDieLineResultManager.removeAll();
        clearAllCache();
    }

    public void clearCacheByKey(String key) {
        dynamicSizeDieLineResultManager.removeById(key);
        redisTemplate.delete(key);
    }

    public void clearCacheByKeys(List<String> keys) {
        keys.forEach(this::clearCacheByKey);
    }

    public void clearCacheByImpactParameter(ImpactParameterDTO impactParameterDTO) {
        String key = getDieLineCacheKey(impactParameterDTO);
        clearCacheByKey(key);
        String kafkaCacheKey = getKafkaRequestCacheKey(impactParameterDTO);
        clearCacheByKey(kafkaCacheKey);
    }

    public void clearCacheByImpactParameters(List<ImpactParameterDTO> impactParameters) {
        impactParameters.forEach(this::clearCacheByImpactParameter);
    }
}
