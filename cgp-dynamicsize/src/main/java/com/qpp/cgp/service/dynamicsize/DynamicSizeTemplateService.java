package com.qpp.cgp.service.dynamicsize;

import com.qpp.cgp.configuration.dynamicsize.DynamicSizeProperties;
import com.qpp.cgp.domain.dynamicsize.configuration.DsTemplateGenerateConfig;
import com.qpp.cgp.manager.dynamicsize.configuration.DsTemplateGenerateConfigManager;
import com.qpp.cgp.manager.dynamicsize.impact.KafkaImpactRequestService;
import com.qpp.cgp.manager.dynamicsize.template.DynamicSizeDieLineFileUrlProcessor;
import com.qpp.cgp.model.dynamicsize.impact.DynamicDieLineResultDTO;
import com.qpp.cgp.model.dynamicsize.impact.ImpactParameterDTO;
import com.qpp.cgp.service.dynamicsize.v2.QppDynamicSizeRemoteService;
import com.qpp.cgp.service.dynamicsize.v2.QppDynamicSizeTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/6/6
 */
@Service
public class DynamicSizeTemplateService {

    @Autowired
    private DsTemplateGenerateConfigManager dsTemplateGenerateConfigManager;

    @Autowired
    private DynamicSizeDieLineFileUrlProcessor dynamicSizeDieLineFileUrlProcessor;

    @Autowired
    private QppDynamicSizeTemplateService qppDynamicSizeTemplateService;

    @Autowired
    private KafkaImpactRequestService impactRequestService;

    @Autowired
    private DynamicSizeProperties appSettings;

    @Autowired
    private QppDynamicSizeRemoteService qppDynamicSizeRemoteService;

    @Autowired
    private DynamicSizeProperties dynamicSizeProperties;

    public DynamicDieLineResultDTO generateV2(ImpactParameterDTO parameterDTO) {
        //生成緩存Key keyGenerator.getKafkaRequestCacheKey(parameterDTO)
        if (parameterDTO.getPalettes() != null) {
            //包含刀線列表
            List<String> includes = new ArrayList<>();

            //排除刀線列表
            List<String> excludes = new ArrayList<>();

            parameterDTO.getPalettes().stream().forEach(
                    p -> {

                        //兼容V1版本Palette的All配置，新版本All傳空
                        if (appSettings.getImpact().getRequest().getIncludePalettes().containsKey(p)) {
                            List<String> includesPalettes = appSettings.getImpact().getRequest().getIncludePalettes().get(p);
                            if (includesPalettes == null || includesPalettes.isEmpty()) {
                                includes.clear();
                            } else {
                                includes.addAll(includesPalettes);
                            }
                        }
                        //兼容V1版本Palette的Other配置
                        else if (appSettings.getImpact().getRequest().getExcludePalettes().containsKey(p)) {
                            List<String> excludesPalettes = appSettings.getImpact().getRequest().getExcludePalettes().get(p);
                            if (excludesPalettes == null || excludesPalettes.isEmpty()) {
                                excludes.clear();
                            } else {
                                excludes.addAll(excludesPalettes);
                            }
                        }
                        else {
                            includes.add(p);
                        }
                    }
            );

            parameterDTO.setPalettes(includes);
            parameterDTO.setExcludePalettes(excludes);
        }

        DynamicDieLineResultDTO result = qppDynamicSizeTemplateService.generate(parameterDTO, false);
        // 替换文件的url
        if (null != result) {
            dynamicSizeDieLineFileUrlProcessor.processFileUrl(result);
        }

        return result;
    }

    public DynamicDieLineResultDTO generate(ImpactParameterDTO parameterDTO) {
        if (!dynamicSizeProperties.isNewLogic()) {
            return impactRequestService.generate(parameterDTO);
        }

        String standard = parameterDTO.getStandard();
        Optional<DsTemplateGenerateConfig> configOptional
                = dsTemplateGenerateConfigManager.findByTemplateNameAndEnable(standard, true);
        if (!configOptional.isPresent()) {
            configOptional = dsTemplateGenerateConfigManager.findDefault();
        }

        DynamicDieLineResultDTO result;
        if (!configOptional.isPresent()) {
            result = impactRequestService.generate(parameterDTO);
        } else {
            DsTemplateGenerateConfig config = configOptional.get();
            if (config.getVersion() <= 1) {
                result = impactRequestService.generate(parameterDTO);
            } else {
                result = qppDynamicSizeTemplateService.generate(parameterDTO, true);
            }
        }

        // 替换文件的url
        if (result != null) {
            dynamicSizeDieLineFileUrlProcessor.processFileUrl(result);
        }

        return result;
    }
}
