package com.qpp.cgp.service.dynamicsize.impact;

import com.google.common.collect.ImmutableMap;
import com.jayway.jsonpath.JsonPath;
import com.qpp.cgp.configuration.dynamicsize.DynamicSizeProperties;
import com.qpp.cgp.domain.dynamicsize.configuration.DsKeyValue;
import com.qpp.cgp.domain.dynamicsize.configuration.ImpactKeyValue;
import com.qpp.cgp.manager.dynamicsize.configuration.RequestTemplateManager;
import com.qpp.cgp.manager.dynamicsize.configuration.UrlTemplateManager;
import com.qpp.cgp.manager.dynamicsize.impact.ImpactContentGenerator;
import com.qpp.cgp.service.dynamicsize.cache.CacheService;
import com.qpp.core.dto.ObjectDTO;
import com.qpp.core.utils.http.RestTemplateBuildUtils;
import com.qpp.service.script.NashornScriptService;
import com.qpp.service.script.ScriptService;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.FileSystemResource;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.io.IOException;
import java.util.Base64;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by admin on 2019/3/28.
 */
@Service("ImpactPdf")
public class ImpactPdfService implements ImpactService {

    @Autowired
    UrlTemplateManager utManager;

    @Autowired
    RequestTemplateManager reqManager;

    @Autowired
    @Qualifier("dynamicsizeCacheService")
    CacheService cacheService;

    @Autowired
    DynamicSizeProperties appSetting;

    @Autowired
    ImpactContentGenerator impactContentGenerator;

    ScriptService jsService = new NashornScriptService();


    /****************** ImpactService接口成員 ************************/

    /**
     *初始化
     * @param isAsyn
     */
    @Override
    public void init(boolean isAsyn) {
        impactContentGenerator.init(isAsyn);
    }

    /**
     * 獲取ImpactPDF數據
     * @param keyValue
     * @param data
     * @return
     * @throws IOException
     */
    @Override
    public Object selectValue(DsKeyValue keyValue, ObjectDTO data, AtomicInteger dsVersion) throws IOException {
        return this.selectPDFDataValue((ImpactKeyValue) keyValue, data);
    }

    /****************** ImpactService接口成員 ************************/

    /****************** ImpactPdfService方法 ************************/

    /**
     * 獲取PDF文件，并進行後續處理
     *
     * @param keyValue
     * @param data
     * @return
     */
    protected Object selectPDFDataValue(ImpactKeyValue keyValue, ObjectDTO data) throws IOException {

        //獲取文件
        byte[] sourceObj = (byte[])impactContentGenerator.getImpactContent(keyValue, data);

        String sourceData = null;

        //進行後續處理
        if (keyValue.getHandler() == null || keyValue.getHandler().isEmpty() || keyValue.getHandler().equalsIgnoreCase("fileservice")) {
            String filePath = appSetting.getTempFolder() + System.currentTimeMillis() + "-" + sourceObj.hashCode() + ".pdf";

            File newFile = new File(filePath);

            if (newFile.exists()) {
                newFile.deleteOnExit();
            }

            FileUtils.writeByteArrayToFile(newFile, sourceObj);

            //上傳到圖片服務器
            RestTemplate fileService = RestTemplateBuildUtils.buildRestTemplate();
            FileSystemResource resource = new FileSystemResource(new File(filePath));
            MultiValueMap<String, Object> param = new LinkedMultiValueMap<>();
            param.add("file", resource);
            //param.add("fileName", svgData.hashCode() + ".svg");

            ObjectDTO upFile = fileService.postForObject(appSetting.getFileServiceUrl() + "/file/upload", param, ObjectDTO.class);

            if (upFile.getBoolean("Success")) {
                sourceData = ObjectDTO.getInstance(upFile.getMap("data")).toJsonString();
            } else {
                throw BusinessExceptionBuilder.of(2700006, ImmutableMap.of("key", keyValue.getKey()));
            }
        } else if (keyValue.getHandler().equalsIgnoreCase("encoder")) {
            sourceData = Base64.getEncoder().encodeToString(sourceObj).replace("\r", "").replace("\n", "");
        }

        //查詢數據并返回
        String selectValue = null;

        if (keyValue.getSelector() != null && !keyValue.getSelector().isEmpty()) {
            Object value = JsonPath.read(sourceData, keyValue.getSelector());
            selectValue = (null == value) ? null : value.toString();
        } else {
            selectValue = sourceData;
        }

        Object value = null;
        //類型轉換
        if (keyValue.getConverter() != null && !keyValue.getConverter().isEmpty()) {
            value = this.convertValue(selectValue, keyValue.getConverter());
        } else {
            value = selectValue;
        }

        return value;
    }

    /**
     * 類型轉換器
     *
     * @param value
     * @param typeName
     * @return
     */
    protected Object convertValue(String value, String typeName) {

        switch (typeName.toLowerCase()) {
            case "int":
                return new Integer(value);
            case "long":
                return new Long(value);
            case "float":
                return new Float(value);
            case "double":
                return new Double(value);
            case "string":
                return value;
            default:
                throw BusinessExceptionBuilder.of(2700015, ImmutableMap.of("typeName", typeName));
        }
    }

    /****************** ImpactPdfService方法 ************************/
}
