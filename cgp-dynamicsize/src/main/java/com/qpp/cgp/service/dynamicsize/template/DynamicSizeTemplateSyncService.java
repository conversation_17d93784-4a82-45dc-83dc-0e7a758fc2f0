package com.qpp.cgp.service.dynamicsize.template;

import com.qpp.cgp.configuration.dynamicsize.DynamicSizeProperties;
import com.qpp.cgp.domain.dynamicsize.configuration.ImpactUrlTemplate;
import com.qpp.cgp.domain.dynamicsize.configuration.ImpactUrlTemplateVariable;
import com.qpp.cgp.domain.dynamicsize.file.DynamicSizeFileType;
import com.qpp.cgp.domain.product.sync.ProjectDeployEnv;
import com.qpp.cgp.manager.dynamicsize.configuration.PageTemplateConfigManager;
import com.qpp.cgp.manager.dynamicsize.configuration.SheetTemplateConfigManager;
import com.qpp.cgp.manager.dynamicsize.configuration.UrlTemplateManager;
import com.qpp.cgp.service.common.CommonPropertiesService;
import com.qpp.cgp.service.dynamicsize.file.DynamicSizeFileSyncService;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;
import org.apache.commons.fileupload.disk.DiskFileItem;

import java.io.*;
import java.net.URI;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/4/15 13:35
 */
@Service
public class DynamicSizeTemplateSyncService {

    @Autowired
    private UrlTemplateManager urlTemplateManager;

    @Autowired
    private PageTemplateConfigManager pageTemplateConfigManager;

    @Autowired
    private SheetTemplateConfigManager sheetTemplateConfigManager;

    @Autowired
    private DynamicSizeProperties appSetting;

    @Autowired
    private CommonPropertiesService commonPropertiesService;

    @Autowired
    private QppDynamicSizeSyncRemoteService qppDynamicSizeSyncRemoteService;

    @Autowired
    private DynamicSizeFileSyncService dynamicSizeFileSyncService;

    /**
     * 同步Qpp的DS模板信息
     * @param env
     * @param dsUrlTemplateIds
     */
    @Async
    public void syncQppDSTemplateInfo(ProjectDeployEnv env, Set<Long> dsUrlTemplateIds) {

        String isSyncQppDS = commonPropertiesService.getKeyValueAndApplicationMode("com.qpp.cgp.ds.sync.enable");

        if ("true".equalsIgnoreCase(isSyncQppDS)) {
            // DS模板名
            Set<String> templateNames = urlTemplateManager.findImpactUrlTemplateByIds(new ArrayList<>(dsUrlTemplateIds))
                    .stream()
                    .map(ImpactUrlTemplate::getVariables)
                    .flatMap(List::stream)
                    .filter(variable -> "standard".equals(variable.getName()))
                    .map(ImpactUrlTemplateVariable::getValue)
                    .map(value -> String.valueOf(value))
                    .collect(Collectors.toSet());

            if (templateNames.size() > 0) {
                qppDynamicSizeSyncRemoteService.sync(generateQppDSUrl(), env, null, null, templateNames);
            }
        }
    }


    /**
     * 同步Cgp的DS文件信息
     * @param env
     * @param dsPageTemplateConfigIds
     * @param dsSheetTemplateConfigIds
     */
    @Async
    public void syncCgpDSTemplateInfo(ProjectDeployEnv env, Set<Long> dsPageTemplateConfigIds,
                                      Set<Long> dsSheetTemplateConfigIds) {

        Set<File> pageFiles = new HashSet<>();
        pageTemplateConfigManager.findPageTemplateConfigByIds(new ArrayList<>(dsPageTemplateConfigIds))
                .stream()
                .forEach(pageConfig -> {

                    File file = new File(appSetting.getPageTemplatesFolder() + pageConfig.getTemplateFileName());
                    if (file.exists() && file.isFile()) {
                        pageFiles.add(file);
                    }

                    File fileV2 = new File(appSetting.getPageTemplatesFolder() + pageConfig.getTemplateFileNameV2());
                    if (fileV2.exists() && fileV2.isFile()){
                        pageFiles.add(fileV2);
                    }

                });
        syncCgpDSFile(env, pageFiles, DynamicSizeFileType.PAGE_TEMPLATE);

        Set<File> sheetFiles = new HashSet<>();
        sheetTemplateConfigManager.findSheetTemplateConfigByIds(new ArrayList<>(dsSheetTemplateConfigIds))
                .stream()
                .forEach(sheetConfig -> {

                    File file = new File(appSetting.getSheetTemplatesFolder() + sheetConfig.getTemplateFileName());
                    if (file.exists() && file.isFile()) {
                        sheetFiles.add(file);
                    }

                    File fileV2 = new File(appSetting.getSheetTemplatesFolder() + sheetConfig.getTemplateFileNameV2());
                    if (fileV2.exists() && fileV2.isFile()) {
                        sheetFiles.add(fileV2);
                    }

                });
        syncCgpDSFile(env, sheetFiles, DynamicSizeFileType.SHEET_TEMPLATE);
    }


    private void syncCgpDSFile(ProjectDeployEnv env, Set<File> files, DynamicSizeFileType type) {
        if (files == null || files.isEmpty()) {
            return;
        }

        //同步文件到同一环境的不同服务器
        for (URI uri : generateCgpDSUrl(env)) {
            MultipartFile[] multipartFiles = files.stream()
                    .map(file -> createMultipartFile(file))
                    .toArray(MultipartFile[]::new);

            dynamicSizeFileSyncService.syncCgpDSFile(uri, env, multipartFiles, type);
        }
    }


    /**
     * File文件转换为MultipartFile文件
     *
     * @param file
     * @return
     */
    private MultipartFile createMultipartFile(File file) {
        // 创建FileItem对象
        FileItem fileItem;
        try {
            fileItem = new DiskFileItem("files", Files.probeContentType(file.toPath()),
                    true, file.getName(), (int) file.length(), file.getParentFile());
        } catch (IOException ex) {
            throw new RuntimeException(ex);
        }

        // 将文件数据拷贝到FileItem对象
        try(InputStream in = Files.newInputStream(file.toPath());
            OutputStream out = fileItem.getOutputStream()) {
            IOUtils.copy(in, out);
            out.flush();
        } catch (IOException ex) {
            throw new RuntimeException(ex);
        }

        // 创建MultipartFile对象
        return new CommonsMultipartFile(fileItem);
    }


    /**
     * 获取需要同步cgp-ds文件的服务器url
     *
     * @param env
     * @return
     */
    private List<URI> generateCgpDSUrl(ProjectDeployEnv env) {
        String envKey = String.format("com.qpp.cgp.ds.sync.file.%s.url", env);
        String url = commonPropertiesService.getKeyValueAndApplicationMode(envKey);
        String prefix = commonPropertiesService.getKeyValueAndApplicationMode("com.qpp.cgp.ds.sync.file.prefix");

        List<URI> uris = new ArrayList<>();
        for (String urlInfo : url.split(",")) {
            String dsUrl = urlInfo.concat(prefix);
            URI uri = URI.create(dsUrl);
            uris.add(uri);
        }

        return uris;
    }


    /**
     * 获取当前服务器url，调用DS服务进行模板数据同步
     * @return
     */
    private URI generateQppDSUrl() {
        String url = commonPropertiesService.getKeyValueAndApplicationMode("com.qpp.cgp.ds.sync.url");
        String prefix = commonPropertiesService.getKeyValueAndApplicationMode("com.qpp.cgp.ds.sync.prefix");
        String dsUrl = url.concat(prefix);
        return URI.create(dsUrl);
    }

}
