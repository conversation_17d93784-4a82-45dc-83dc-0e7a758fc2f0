package com.qpp.cgp.service.dynamicsize.cache;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.qpp.cgp.cache.RedisComponent;
import com.qpp.cgp.configuration.dynamicsize.DynamicSizeProperties;
import com.qpp.cgp.domain.dynamicsize.result.DynamicSizeDieLineResult;
import com.qpp.cgp.manager.dynamicsize.impact.KafkaImpactRequestService;
import com.qpp.cgp.manager.dynamicsize.record.DynamicSizeDieLineRecorder;
import com.qpp.cgp.manager.dynamicsize.result.DynamicSizeDieLineResultManager;
import com.qpp.cgp.model.dynamicsize.impact.*;
import com.qpp.cgp.service.dynamicsize.DynamicSizeTemplateService;
import com.qpp.cgp.utils.ObjectMapperBeanCreateUtils;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

@Service
public class ImpactCacheService {

    @Autowired
    RedisKeyGenerator keyGenerator;

    @Autowired
    DynamicSizeProperties appSettings;

    @Getter
    @Setter
    KafkaImpactRequestService impactRequestService;

    @Autowired
    private RedisComponent<String,Object> redisComponent;

    ObjectMapper mapper = ObjectMapperBeanCreateUtils.getObjectMapper();;

    Log logger = LogFactory.getLog(this.getClass());

    @Autowired
    private DynamicSizeDieLineResultManager dynamicSizeDieLineResultManager;

    @Autowired
    private DynamicSizeDieLineRecorder recorder;

    @Autowired
    private DynamicSizeTemplateService dynamicSizeTemplateService;

    @Autowired
    private DsKeyLock dsKeyLock;

    /**
     * 生成Impact刀線
     *
     * @param parameterDTO
     * @return
     */
    public DynamicDieLineResultDTO generateDieLine(ImpactParameterDTO parameterDTO) {
        //生成緩存Key keyGenerator.getKafkaRequestCacheKey(parameterDTO)
        if (parameterDTO.getPalettes() != null) {
            //包含刀線列表
            List<String> includes = new ArrayList<>();

            //排除刀線列表
            List<String> excludes = new ArrayList<>();

            parameterDTO.getPalettes().stream().forEach(
                    p -> {

                        //兼容V1版本Palette的All配置，新版本All傳空
                        if (appSettings.getImpact().getRequest().getIncludePalettes().containsKey(p)) {
                            List<String> includesPalettes = appSettings.getImpact().getRequest().getIncludePalettes().get(p);
                            if (includesPalettes == null || includesPalettes.isEmpty()) {
                                includes.clear();
                            } else {
                                includes.addAll(includesPalettes);
                            }
                        }
                        //兼容V1版本Palette的Other配置
                        else if (appSettings.getImpact().getRequest().getExcludePalettes().containsKey(p)) {
                            List<String> excludesPalettes = appSettings.getImpact().getRequest().getExcludePalettes().get(p);
                            if (excludesPalettes == null || excludesPalettes.isEmpty()) {
                                excludes.clear();
                            } else {
                                excludes.addAll(excludesPalettes);
                            }
                        }
                        else {
                            includes.add(p);
                        }
                    }
            );

            parameterDTO.setPalettes(includes);
            parameterDTO.setExcludePalettes(excludes);
        }

        String cacheKey = keyGenerator.getDieLineCacheKey(parameterDTO);

        recorder.recordDsKey(cacheKey);
        recorder.recordProcessedParameter(parameterDTO);

        return generateDieLineSync(parameterDTO, cacheKey);
    }

    private DynamicDieLineResultDTO generateDieLineSync(ImpactParameterDTO parameterDTO, String cacheKey) {
        Optional<DynamicDieLineResultDTO> optionalCache = findExistsCache(cacheKey);

        if (!optionalCache.isPresent()) {
            Lock lock = null;
            try {
                lock = dsKeyLock.dsCacheKeyLock(cacheKey);
                lock.lock();

                optionalCache = findExistsCache(cacheKey);
                if (optionalCache.isPresent()) {
                    // 击中缓存
                    DynamicDieLineResultDTO cache = optionalCache.get();
                    recorder.recordCachedDieLineResult(cache);
                    return cache;
                } else {
                    // 未击中缓存
                    //            return impactRequestService.generate(parameterDTO);
                    return dynamicSizeTemplateService.generate(parameterDTO);
                }
            } finally {
                if (null != lock) {
                    lock.unlock();
                }
            }
        } else {
            // 击中缓存
            DynamicDieLineResultDTO cache = optionalCache.get();
            recorder.recordCachedDieLineResult(cache);
            return cache;
        }
    }

    /**
     * 查找刀线生成结果，先从 redis 查找，再从 mongo 中查找
     *
     * @param cacheKey
     * @return
     */
    private Optional<DynamicDieLineResultDTO> findExistsCache(String cacheKey) {
        // 查询 redis 是否有缓存
        if (Objects.equals(redisComponent.hasKey(cacheKey), true)) {
            logger.info(String.format("get redis cache data successfully, key: %s", cacheKey));
            return Optional.of(mapper.convertValue(redisComponent.entries(cacheKey), DynamicDieLineResultDTO.class));
        }

        // 查询 mongo 是否有缓存
        return dynamicSizeDieLineResultManager.findById(cacheKey)
                .map(dynamicSizeDieLineResult -> {
                    logger.info(String.format("get mongo cache data successfully, key: %s", cacheKey));

                    DynamicDieLineResultDTO dynamicDieLineResultDTO = new DynamicDieLineResultDTO();

                    BeanUtils.copyProperties(dynamicSizeDieLineResult, dynamicDieLineResultDTO);

                    dynamicDieLineResultDTO.setImpactParameterDTO(null);

                    return dynamicDieLineResultDTO;
                });
    }

    /**
     * 保存刀線生成信息
     *
     * @param key
     * @param resultDTO
     */
    public void saveImpactResult(String key, DynamicDieLineResultDTO resultDTO) {

        logger.debug(String.format("get impact return successfully, request key: %s , status %d", key, resultDTO.getStatusCode()));

        if(redisComponent.hasKey(key)) {

            //獲取已緩存參數
            ImpactParameterDTO parameterDTO = mapper.convertValue(redisComponent.entries(key), ImpactParameterDTO.class);

            //保存到緩存
            this.saveToCache(key, resultDTO, parameterDTO);

            //刪除請求緩存
            redisComponent.delete(key);
        }
        else if (resultDTO.getImpactParameterDTO() != null) {
            logger.debug(String.format("get impact return with parameter successfully, request key: %s , status %d", key, resultDTO.getStatusCode()));
            //獲取已緩存參數
            ImpactParameterDTO parameterDTO = this.getImpactParameterDTO(resultDTO.getImpactParameterDTO());

            //保存到緩存
            this.saveToCache(key, resultDTO, parameterDTO);
        }
        else {
            logger.debug(String.format("fail to get any return values, request key: %s , status %d", key, resultDTO.getStatusCode()));
        }
    }

    /**
     * 保存Impact緩存
     * @param key 請求緩存Key
     * @param resultDTO 結果
     * @param parameterDTO 請求參數
     */
    private void  saveToCache(String key, DynamicDieLineResultDTO resultDTO, ImpactParameterDTO parameterDTO){
        //生成緩存Key
        String cacheKey = keyGenerator.getDieLineCacheKey(parameterDTO);

        if (resultDTO.getStatusCode() == ResultStatus.Sucess.value()) {
            // 生成成功的数据保存到 mongo 数据库中
            DynamicSizeDieLineResult successResult = new DynamicSizeDieLineResult();
            BeanUtils.copyProperties(resultDTO, successResult);
            successResult.setId(cacheKey);
            successResult.setSource(parameterDTO.getSource());
            successResult.setCreatedDate(new Date());
            dynamicSizeDieLineResultManager.save(successResult);
            logger.info(String.format("cache impact generated data to mongodb successfully, request key: %s , key %s", key, cacheKey));

            // 判断是否需要缓存到 redis 中
            if (Objects.equals(appSettings.getImpact().getEnableRedisCache(), true)) {
                //返回前端數據時，不返回此字段
                resultDTO.setImpactParameterDTO(null);

                redisComponent.hPutAll(cacheKey, mapper.convertValue(resultDTO, Map.class));

                // 成功， 默认设置 7 day 缓存过期时间
                long cacheTimeout = appSettings.getImpact().getCacheTimeout().getSeconds();
                redisComponent.expire(cacheKey, cacheTimeout, TimeUnit.SECONDS);

                logger.info(String.format("cache impact generated data to redis successfully and expire after %s sec, request key: %s , key %s", cacheTimeout, key, cacheKey));
            }
        } else {
            //返回前端數據時，不返回此字段
            resultDTO.setImpactParameterDTO(null);

            redisComponent.hPutAll(cacheKey, mapper.convertValue(resultDTO, Map.class));

            // 失败或重试， 默认设置 1 min 缓存过期时间
            long retryTimeout = appSettings.getImpact().getRetryTimeout().getSeconds();
            redisComponent.expire(cacheKey, retryTimeout, TimeUnit.SECONDS);

            logger.info(String.format("cache impact data(retry or failed) to redis successfully and expired after %s sec, request key: %s , key %s", retryTimeout, key, cacheKey));
        }
    }


    /**
     * 創建請求參數
     * @param kafkaParameterDTO
     * @return
     */
    private ImpactParameterDTO getImpactParameterDTO(ImpactKafkaParameterDTO kafkaParameterDTO) {
        ImpactParameterDTO parameterDTO = new ImpactParameterDTO();

        parameterDTO.setFormat(ReturnFileFormat.nameOf(kafkaParameterDTO.getFormat()));

        if (kafkaParameterDTO.getFormat() == ReturnFileFormat.Svg.value()) {
            parameterDTO.setDpi(kafkaParameterDTO.getDpi());
        }

        parameterDTO.setStandard(kafkaParameterDTO.getStandard());
        parameterDTO.setPalettes(kafkaParameterDTO.getPalettes());
        parameterDTO.setExcludePalettes(kafkaParameterDTO.getExcludePalettes());
        parameterDTO.setVariables(kafkaParameterDTO.getVariables());
        parameterDTO.setMasterToolSettings(kafkaParameterDTO.getMasterToolSettings());

        return parameterDTO;
    }
}
