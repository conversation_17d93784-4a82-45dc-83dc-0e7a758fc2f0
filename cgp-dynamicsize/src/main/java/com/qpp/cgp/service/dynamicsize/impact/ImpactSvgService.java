package com.qpp.cgp.service.dynamicsize.impact;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.configuration.dynamicsize.DynamicSizeProperties;
import com.qpp.cgp.domain.dynamicsize.configuration.*;
import com.qpp.cgp.manager.dynamicsize.configuration.RequestTemplateManager;
import com.qpp.cgp.manager.dynamicsize.configuration.UrlTemplateManager;
import com.qpp.cgp.manager.dynamicsize.impact.ImpactContentGenerator;
import com.qpp.cgp.service.dynamicsize.cache.CacheService;
import com.qpp.cgp.utils.ObjectMapperBeanCreateUtils;
import com.qpp.core.dto.ObjectDTO;
import com.qpp.core.exception.BusinessException;
import com.qpp.service.script.NashornScriptService;
import com.qpp.service.script.ScriptService;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import freemarker.template.Configuration;
import org.apache.batik.anim.dom.SAXSVGDocumentFactory;
import org.apache.batik.bridge.*;
import org.apache.batik.dom.svg.SVGOMRect;
import org.apache.batik.util.XMLResourceDescriptor;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.parser.Parser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.svg.SVGDocument;
import org.w3c.dom.svg.SVGLocatable;
import org.w3c.dom.svg.SVGRect;
import org.w3c.dom.xpath.XPathEvaluator;
import org.w3c.dom.xpath.XPathResult;

import java.io.IOException;
import java.io.StringReader;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by admin on 2017/8/12.
 */
@Service
public abstract class ImpactSvgService implements ImpactService {

    @Autowired
    UrlTemplateManager utManager;

    @Autowired
    RequestTemplateManager reqManager;

    @Autowired
    @Qualifier("dynamicsizeCacheService")
    CacheService cacheService;

    @Autowired
    DynamicSizeProperties appSetting;

    @Autowired
    ImpactContentGenerator impactContentGenerator;

    ScriptService jsService = new NashornScriptService();

    Log logger = LogFactory.getLog(this.getClass());

    Configuration freemarkerCfg;

    ObjectMapper mapper = ObjectMapperBeanCreateUtils.getObjectMapper();;

    /****************** ImpactService接口成員 ************************/

    /**
     *初始化
     * @param isAsyn
     */
    @Override
    public void init(boolean isAsyn) {
        impactContentGenerator.init(isAsyn);
    }

    /**
     * 公共接口調用
     * @param keyValue
     * @param data
     * @return
     * @throws IOException
     */
    @Override
    public Object selectValue(DsKeyValue keyValue, ObjectDTO data, AtomicInteger dsVersion) throws IOException {
        return selectSvgDataValue((ImpactKeyValue) keyValue, data, dsVersion);
    }

    /****************** ImpactService接口成員 ************************/

    /****************** ImpactSvgService方法 ************************/

    /**
     * 獲取SVG內的數據
     *
     * @param keyValue
     * @param data
     * @return
     */
    protected abstract Object selectSvgDataValue(ImpactKeyValue keyValue, ObjectDTO data, AtomicInteger dsVersion) throws IOException;

    /**
     * 獲取外包矩形
     *
     * @param document
     * @return
     */
    protected SVGRect getSvgBBox(String document, AtomicInteger dsVersion) {
        Document svgDoc = Jsoup.parse(document, "", Parser.xmlParser());
        org.jsoup.nodes.Element svgElement = svgDoc.select("svg").first();
        String viewBox = svgElement.attr("viewBox");
        double width = Double.parseDouble(svgElement.attr("width"));
        double height = Double.parseDouble(svgElement.attr("height"));

        double viewBoxWidth;
        if (StringUtils.isNotBlank(viewBox)) {
            String[] viewBoxValues = viewBox.split(" ");
            viewBoxWidth = Double.parseDouble(viewBoxValues[2]);
        } else {
            viewBoxWidth = width;
        }

        if (Math.abs(width - viewBoxWidth) > 1) {
            return new SVGOMRect(0, 0, (float) width, (float) height);
        } else {
            return getSvgBBox(document);
        }
    }

    protected SVGRect getSvgBBox(String document) {
        SVGDocument doc = (SVGDocument) createDocumentFromFile(document);
        UserAgent userAgent = new UserAgentAdapter();
        DocumentLoader loader = new DocumentLoader(userAgent);
        BridgeContext ctx = new BridgeContext(userAgent, loader);
        ctx.setDynamicState(BridgeContext.DYNAMIC);
        GVTBuilder builder = new GVTBuilder();
        builder.build(ctx, doc);

        Element element = doc.getDocumentElement();

        return ((SVGLocatable) element).getBBox();
    }

    /**
     * 獲取外包矩形
     *
     * @param document
     * @return
     */
    protected SVGRect getSvgBBox(String document, String xPathSelector) {

        SVGDocument doc = (SVGDocument) createDocumentFromFile(document);
        UserAgent userAgent = new UserAgentAdapter();
        DocumentLoader loader = new DocumentLoader(userAgent);
        BridgeContext ctx = new BridgeContext(userAgent, loader);
        ctx.setDynamicState(BridgeContext.DYNAMIC);
        GVTBuilder builder = new GVTBuilder();
        builder.build(ctx, doc);

        Element element = doc.getDocumentElement();

        XPathEvaluator xpathEvaluator = (XPathEvaluator) doc;

        //查詢結果
        XPathResult result = (XPathResult) xpathEvaluator.evaluate(xPathSelector, element, null, XPathResult.ORDERED_NODE_ITERATOR_TYPE, null);

        Node node;

        SVGRect rect = null;

        while ((node = result.iterateNext()) != null) {
            SVGLocatable locatable = (SVGLocatable) node;
            SVGRect bbox = locatable.getBBox();

            if (rect == null || rect.getWidth() * rect.getHeight() < bbox.getWidth() * bbox.getHeight()) {
                rect = bbox;
            }
        }

        return rect;
    }

    /**
     * @param svg
     * @return
     */
    protected org.w3c.dom.Document createDocumentFromFile(String svg) {
        String parser = XMLResourceDescriptor.getXMLParserClassName();
        SAXSVGDocumentFactory f = new SAXSVGDocumentFactory(parser);
        org.w3c.dom.Document doc = null;
        try {
            doc = f.createDocument(null, new StringReader(svg));
        } catch (IOException e) {
            BusinessException err = BusinessExceptionBuilder.of(2700013, ImmutableMap.of("message", e.getMessage()));
            err.setMoreInfo(svg);
            throw err;
        }
        return doc;
    }

    /**
     * 將內容進行編碼
     *
     * @param encoder
     * @param value
     * @return
     */
    protected String encodeValue(String encoder, String value) {

        switch (encoder) {
            case "Base64":
                return Base64.getEncoder().encodeToString(value.getBytes()).replace("\r", "").replace("\n", "");
            default:
                throw BusinessExceptionBuilder.of(2700014, ImmutableMap.of("encoder", encoder));
        }
    }


    /**
     * 類型轉換器
     *
     * @param value
     * @param typeName
     * @return
     */
    protected Object convertValue(String value, String typeName) {

        switch (typeName.toLowerCase()) {
            case "int":
                return new Integer(value);
            case "long":
                return new Long(value);
            case "float":
                return new Float(value);
            case "double":
                return new Double(value);
            case "string":
                return value;
            default:
                throw BusinessExceptionBuilder.of(2700015, ImmutableMap.of("typeName", typeName));
        }
    }

    /****************** ImpactSvgService方法 ************************/
}
