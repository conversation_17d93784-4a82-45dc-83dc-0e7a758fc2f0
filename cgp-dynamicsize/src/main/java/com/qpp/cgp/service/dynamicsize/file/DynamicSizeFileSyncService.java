package com.qpp.cgp.service.dynamicsize.file;

import com.qpp.cgp.domain.dynamicsize.file.DynamicSizeFileType;
import com.qpp.cgp.domain.product.sync.ProjectDeployEnv;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.net.URI;

/**
 * <AUTHOR>
 * @Date 2024/4/16 16:51
 */
@FeignClient(name = "cgp-service", configuration = {CgpRestRequestInterceptor.class, MultipartRequestInterceptor.class})
public interface DynamicSizeFileSyncService {

    @PostMapping(value = "/api/dynamicsize/file/upload", produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
            consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    void syncCgpDSFile(URI uri, @RequestParam(name = "dev") ProjectDeployEnv env,
                       @RequestPart(name = "files") MultipartFile[] files,
                       @RequestParam(name = "type") DynamicSizeFileType type);

}
