package com.qpp.cgp.service.dynamicsize.cache;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.qpp.cgp.configuration.dynamicsize.DynamicSizeProperties;
import com.qpp.cgp.manager.dynamicsize.configuration.UrlTemplateManager;
import com.qpp.cgp.utils.ObjectMapperBeanCreateUtils;
import com.qpp.core.exception.BusinessException;
import io.sentry.Sentry;
import io.sentry.event.Breadcrumb;
import io.sentry.event.BreadcrumbBuilder;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.*;
import java.net.URI;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * Created by admin on 2017/8/12.
 */
@Service("dynamicsizeCacheService")
public class CacheService {

    @Autowired
    UrlTemplateManager utManager;

    Log logger = LogFactory.getLog(this.getClass());

    @Autowired
    DynamicSizeProperties appSetting;

    ObjectMapper mapper = ObjectMapperBeanCreateUtils.getObjectMapper();

    @Autowired
    @Qualifier("dsRestTemplate")
    RestTemplate restTemplate;

    /**
     * 獲取impactSvg
     *
     * @param uri
     * @return
     */
    @Cacheable(cacheNames = "impactSvg", key = "T(org.springframework.util.DigestUtils).md5DigestAsHex(#uri.toString().getBytes())")
    public String getImpactSvg(URI uri) {
        logger.info(String.format("get svg from url : %s", uri.toString()));

        Date d1 = new Date();

        try {
            String result = this.restTemplate.getForObject(uri, String.class);

            logger.info(String.format("get svg cost %d milliseconds from url : %s", this.getTimeTick(d1), uri));

            return result;

        } catch (Exception err) {

            String msg = String.format("error happen when getting Impact SVG, url: %s", uri);

            Sentry.getContext().recordBreadcrumb(
                    new BreadcrumbBuilder()
                            .setType(Breadcrumb.Type.HTTP)
                            .setLevel(Breadcrumb.Level.ERROR)
                            .setMessage(msg)
                            .build()
            );

            Sentry.capture(err);

            logger.info(msg);
            throw new BusinessException(msg);
        }
    }

    /**
     * 獲取impactSvg
     * @param method 請求方法（GET/POST）
     * @param uri 地址
     * @param formBody 內容
     * @return svg內容
     * @throws IOException
     */
    @Cacheable(cacheNames = "impactSvg", key = "T(org.springframework.util.DigestUtils).md5DigestAsHex((#uri.toString()+#method+#formBody).getBytes())")
    public String generateImpactSvg(String method, URI uri, String formBody) {

        logger.info(String.format("generate svg from url : %s, body : %s", uri.toString(), formBody));

        Date d1 = new Date();

        String result;

        try {
            switch (method.toUpperCase()) {
                case "POST":
                    HttpHeaders headers = new HttpHeaders();
                    headers.add("Content-Type", MediaType.APPLICATION_JSON_VALUE);
                    HttpEntity<String> httpEntity = new HttpEntity<String>(formBody, headers);

                    ResponseEntity<String> response = this.restTemplate.postForEntity(uri, httpEntity, String.class);
                    result = response.getBody();
                    break;
                case "GET":
                default:
                    result = this.restTemplate.getForObject(uri, String.class);
                    break;
            }

            logger.info(String.format("generate svg cost %d milliseconds from url : %s, body : %s", this.getTimeTick(d1), uri, formBody));

            return result;

        } catch (Exception err) {
            String msg = String.format("error happen when generating Impact SVG, url: %s, body : %s", uri, formBody);

            Sentry.getContext().recordBreadcrumb(
                    new BreadcrumbBuilder()
                            .setType(Breadcrumb.Type.HTTP)
                            .setLevel(Breadcrumb.Level.ERROR)
                            .setMessage(msg)
                            .build()
            );

            Sentry.capture(err);

            logger.info(msg);
            throw new BusinessException(msg);
        }
    }


    /**
     * 獲取impactSvg
     *
     * @param uri
     * @return
     */
    @Cacheable(cacheNames = "impactPdf", key = "T(org.springframework.util.DigestUtils).md5DigestAsHex(#uri.toString().getBytes())")
    public  byte[] getImpactPdf(URI uri) {
        return this.generateImpactPdf("GET", uri, "");
    }


    /**
     * 獲取impactSvg
     * @param method 請求方法（GET/POST）
     * @param uri 地址
     * @param formBody 內容
     * @return svg內容
     * @throws IOException
     */
    @Cacheable(cacheNames = "impactPdf", key = "T(org.springframework.util.DigestUtils).md5DigestAsHex((#uri.toString()+#method+#formBody).getBytes())")
    public byte[] generateImpactPdf(String method, URI uri, String formBody) {

        logger.info(String.format("generate pdf from url : %s, body : %s", uri.toString(), formBody));

        Date d1 = new Date();

        try {
            ResponseEntity<byte[]> response;

            switch (method.toUpperCase()) {
                case "POST":
                    HttpHeaders headers = new HttpHeaders();
                    headers.add("Content-Type", MediaType.APPLICATION_JSON_VALUE);

                    List list = new ArrayList<>();
                    list.add(MediaType.valueOf(MediaType.APPLICATION_PDF_VALUE));
                    headers.setAccept(list);
                    HttpEntity<String> httpEntity = new HttpEntity<String>(formBody, headers);

                    response = this.restTemplate.postForEntity(uri, httpEntity, byte[].class);
                    break;
                case "GET":
                default:
                    response = this.restTemplate.getForEntity(uri, byte[].class);
                    break;
            }

            byte[] result = response.getBody();

            logger.info(String.format("generate pdf cost %d milliseconds from url : %s, body : %s", this.getTimeTick(d1), uri, formBody));

            return result;

        } catch (Exception err) {
            String msg = String.format("error happen when generating Impact pdf, url: %s, body : %s", uri, formBody);

            Sentry.getContext().recordBreadcrumb(
                    new BreadcrumbBuilder()
                            .setType(Breadcrumb.Type.HTTP)
                            .setLevel(Breadcrumb.Level.ERROR)
                            .setMessage(msg)
                            .build()
            );
            logger.info(err.getMessage());
            Sentry.capture(err);

            logger.info(msg, err);
            throw new BusinessException(msg);
        }
    }


    private long getTimeTick(Date startTime) {
        return new Date().getTime() - startTime.getTime();
    }
}
