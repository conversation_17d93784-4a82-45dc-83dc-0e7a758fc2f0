package com.qpp.cgp.service.dynamicsize.v2;

import com.qpp.cgp.model.dynamicsize.impact.*;
import com.qpp.cgp.service.dynamicsize.cache.ImpactCacheService;
import com.qpp.cgp.service.dynamicsize.cache.RedisKeyGenerator;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/6/5
 */
@Service
public class QppDynamicSizeTemplateService {

    @Autowired
    private QppDynamicSizeRemoteService qppDynamicSizeRemoteService;

    @Autowired
    private RedisKeyGenerator keyGenerator;

    @Autowired
    private ImpactCacheService impactCacheService;


    public DynamicDieLineResultDTO generate(ImpactParameterDTO parameterDTO, boolean isCache) {
        String key = keyGenerator.getKafkaRequestCacheKey(parameterDTO);
        ImpactKafkaParameterDTO impactKafkaParameterDTO = new ImpactKafkaParameterDTO();
        BeanUtils.copyProperties(parameterDTO, impactKafkaParameterDTO);
        impactKafkaParameterDTO.setKey(key);
        impactKafkaParameterDTO.setFormat(ReturnFileFormat.of(parameterDTO.getFormat()).value());

        DynamicDieLineResultDTO result = qppDynamicSizeRemoteService.generate(impactKafkaParameterDTO);
        if (isCache && result.getStatusCode() == ResultStatus.Sucess.value()) {
            result.setImpactParameterDTO(impactKafkaParameterDTO);
            impactCacheService.saveImpactResult(key, result);
        }

        return result;
    }
}
