package com.qpp.cgp.service.dynamicsize.template;

import com.qpp.cgp.domain.dynamicsize.configuration.ImpressionPlaceholder;
import com.qpp.cgp.domain.dynamicsize.configuration.SheetTemplateConfig;
import com.qpp.cgp.manager.dynamicsize.configuration.SheetTemplateConfigManager;
import com.qpp.id.generator.IdGenerator;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class SheetTemplateConfigCopier {
    @Autowired
    private SheetTemplateConfigManager sheetTemplateConfigManager;
    @Autowired
    private IdGenerator idGenerator;
    public Map<Long, Long> copy(List<Long> ids, Map<Long, Object> map) {
        HashMap<Long, Long> idMap = new HashMap<Long, Long>();
        List<SheetTemplateConfig> saveNewConfigs = new ArrayList<>();
        List<SheetTemplateConfig> sheetTemplateConfigs = sheetTemplateConfigManager.findSheetTemplateConfigByIds(ids);
        for (SheetTemplateConfig sheetTemplateConfig : sheetTemplateConfigs) {
            // 如果已经拷贝过了
            if (map.containsKey(sheetTemplateConfig.get_id())) {
                idMap.put(sheetTemplateConfig.get_id(), Long.valueOf(map.get(sheetTemplateConfig.get_id()).toString()));
            }else{
                // 如果没拷贝过
                Long id = sheetTemplateConfig.get_id();
                Long newId = idGenerator.generateId();
                idMap.put(id, newId);
                sheetTemplateConfig.set_id(newId);
                this.replacePlaceHolderId(sheetTemplateConfig, map);
                saveNewConfigs.add(sheetTemplateConfig);
            }
        }
        sheetTemplateConfigManager.saveAll(saveNewConfigs);
        return idMap;
    }

    private void replacePlaceHolderId(SheetTemplateConfig sheetTemplateConfig, Map<Long, Object> map) {
        List<ImpressionPlaceholder> impressionPlaceholders = sheetTemplateConfig.getImpressionPlaceholders();
        if (CollectionUtils.isNotEmpty(impressionPlaceholders)) {
            for (ImpressionPlaceholder impressionPlaceholder : impressionPlaceholders) {
                Long contentTypeId = impressionPlaceholder.getContentTypeId();
                if (contentTypeId != null && map.containsKey(contentTypeId)) {
                    impressionPlaceholder.setContentTypeId(Long.parseLong(map.get(contentTypeId).toString()));
                }
            }
        }
    }
}
