package com.qpp.cgp.model.dynamicsize.common;

import com.qpp.cgp.domain.dynamicsize.dto.SheetTemplateConfigV2OutputDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class DataResultDTO {

    /**
     * 返回結果狀態代碼
     *
     * Sucess:1
     * Retry:2
     * Fail:0
     */
    private int statusCode;

    /**
     * 描述信息
     */
    private String message;

    /**
     * 結果
     */
    private Object data;

    /**
     * 结果
     */
    private List<SheetTemplateConfigV2OutputDTO> results;
}
