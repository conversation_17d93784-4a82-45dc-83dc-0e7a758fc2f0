package com.qpp.cgp.model.dynamicsize.template;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @Package com.qpp.cgp.model.dynamicsize.template
 * @Date 2024/2/6 13:43
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("模板极值测试结果")
public class TemplatePeakTestResultDTO {

    @ApiModelProperty("模板名称")
    private String templateName;

    @ApiModelProperty("生成的模板信息")
    private List<TemplatePeakTestResultFileInfoDTO> fileInfos;
}
