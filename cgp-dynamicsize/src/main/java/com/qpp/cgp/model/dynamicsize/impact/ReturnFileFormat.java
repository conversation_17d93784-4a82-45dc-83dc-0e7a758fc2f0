package com.qpp.cgp.model.dynamicsize.impact;

public enum ReturnFileFormat {
    Svg(-1),
    Impact(0),
    Pdf(12);

    private int value;

    private ReturnFileFormat(int value) {
        this.value = value;
    }

    public int value() {
        return this.value;
    }

    public static ReturnFileFormat of(String name) {

        for (ReturnFileFormat fileFormat :ReturnFileFormat.values()) {
            if(fileFormat.name().equalsIgnoreCase(name)){
                return fileFormat;
            }
        }

        return ReturnFileFormat.Pdf;
    }

    public  static String nameOf(int value) {
        for (ReturnFileFormat fileFormat : ReturnFileFormat.values()) {
            if (fileFormat.value() == value) {
                return fileFormat.name();
            }
        }

        return ReturnFileFormat.Pdf.name();
    }
}
