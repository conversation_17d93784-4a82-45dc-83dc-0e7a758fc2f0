package com.qpp.cgp.model.dynamicsize.common;

import com.qpp.cgp.domain.dynamicsize.dto.SheetTemplateConfigV2InputDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Package com.qpp.cgp.model.dynamicsize.common
 * @Date 2025/1/16 18:28
 */
@NoArgsConstructor
@Data
public class DataInputDTO {

    private Map<String, Object> pageObject;

    private List<SheetTemplateConfigV2InputDTO> dtoList;
}
