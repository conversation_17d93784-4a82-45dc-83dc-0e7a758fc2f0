package com.qpp.cgp.model.dynamicsize.template;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Package com.qpp.cgp.model.dynamicsize.template
 * @Date 2024/2/6 13:44
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class TemplateParamDTO {

    @ApiModelProperty("参数名称")
    private String name;

    @ApiModelProperty("参数值描述")
    private String description;

    @ApiModelProperty("参数值")
    private Object value;
}
