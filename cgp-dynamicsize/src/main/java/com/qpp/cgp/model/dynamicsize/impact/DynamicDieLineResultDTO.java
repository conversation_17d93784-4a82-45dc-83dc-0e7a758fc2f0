package com.qpp.cgp.model.dynamicsize.impact;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

import java.util.Map;

@NoArgsConstructor
@Data
@ApiModel
public class DynamicDieLineResultDTO {

    /**
     * 返回結果狀態代碼
     * <p>
     * Sucess:1
     * Retry:2
     * Fail:0
     */
    @ApiModelProperty(value = "Status code of file generation, Sucess:1, Retry:2, Fail:0")
    private int statusCode;

    /**
     * 描述信息
     */
    @ApiModelProperty(value = "Error message")
    private String message;

    /**
     * 請求
     */
    @ApiModelProperty(value = "Requset parameter of kafka")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ImpactKafkaParameterDTO impactParameterDTO;

    /**
     * 文件服務器返回信息
     */
    @ApiModelProperty(value = "File object, same as file service return.")
    private Map<String, Object> file;
}
