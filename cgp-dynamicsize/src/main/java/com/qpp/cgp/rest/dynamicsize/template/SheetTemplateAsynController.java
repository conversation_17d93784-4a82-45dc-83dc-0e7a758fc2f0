package com.qpp.cgp.rest.dynamicsize.template;

import com.qpp.cgp.domain.dynamicsize.bom.BomLibrary;
import com.qpp.cgp.manager.dynamicsize.template.SheetTemplateAsynManager;
import com.qpp.cgp.model.dynamicsize.common.DataInputDTO;
import com.qpp.cgp.model.dynamicsize.common.DataResultDTO;
import com.qpp.core.dto.ObjectDTO;
import com.qpp.core.exception.BusinessException;
import com.qpp.core.exception.BusinessExceptionLevel;
import com.qpp.operation.log.OperationLog;
import com.qpp.operation.log.OperationLogModule;
import com.qpp.operation.log.OperationLogTag;
import freemarker.template.TemplateException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.*;

@Api(description = "Sheet asyn template")
@OperationLogModule("SheetAsynTemplate")
@Controller
public class SheetTemplateAsynController {

    @Autowired
    SheetTemplateAsynManager stManager;

    @ApiOperation(value = "Generate sheet", notes = "Generate dynamic sheet template for impression service.", response = BomLibrary.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "skuId", value = "CGP sku product id", paramType = "path")
            , @ApiImplicitParam(name = "producttype", value = "Impression product type", paramType = "path")
            , @ApiImplicitParam(name = "sheetType", value = "Impression sheet type", paramType = "path")
    })
    @RequestMapping(value = "/api/dynamicsize/sku/{skuId}/producttype/{productType}/sheettype/{sheetType}/sheet/asyn", method = RequestMethod.POST, produces = {MediaType.APPLICATION_JSON_VALUE})
    @ResponseBody
    public DataResultDTO generateSheetBySkuAndPageType(@PathVariable Long skuId, @PathVariable String productType, @PathVariable String sheetType) throws IOException, TemplateException {

        return stManager.generateSheet(skuId, productType, sheetType);
    }

    @ApiOperation(value = "Generate sheet", notes = "Generate dynamic sheet template for impression service.", response = BomLibrary.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "producttype", value = "Impression product type", paramType = "path")
            , @ApiImplicitParam(name = "sheetType", value = "Impression sheet type", paramType = "path")
            , @ApiImplicitParam(name = "pageObject", value = "Map of page message")
    })
    @RequestMapping(value = "/api/dynamicsize/producttype/{productType}/sheettype/{sheetType}/sheet/asyn", method = RequestMethod.POST, produces = {MediaType.APPLICATION_JSON_VALUE})
    @ResponseBody
    public DataResultDTO generateSheetTemplate(@PathVariable String productType, @PathVariable String sheetType, @RequestBody Map<String, Object> pageObject) throws IOException, TemplateException {
        return stManager.generateSheet(productType, sheetType, pageObject);
    }

    @ApiOperation(value = "Generate sheet", notes = "Generate dynamic sheet template for impression service.", response = BomLibrary.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "producttype", value = "Impression product type", paramType = "path")
            , @ApiImplicitParam(name = "pageObject", value = "Map of page message, including sheet type")
    })
    @RequestMapping(value = "/api/dynamicsize/producttype/{productType}/impression/sheet/asyn", method = RequestMethod.POST, produces = {MediaType.APPLICATION_JSON_VALUE})
    @ResponseBody
    public DataResultDTO generateProductSheetTemplate(@PathVariable String productType, @RequestBody Map<String, Object> pageObject) throws IOException, TemplateException {
        return stManager.generateSheet(productType, (String) pageObject.get("type"), pageObject);
    }

    @ApiOperation(value = "Generate sheet", notes = "Generate dynamic sheet template for impression service.", response = BomLibrary.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sheetTemplateConfigId", value = "SheetTemplateConfig id", paramType = "path"),
            @ApiImplicitParam(name = "pageObject", value = "Map of page message, including sheet type")
    })
    @OperationLog(description = "Generate dynamic sheet template for impression service.",
            level = OperationLog.Level.WARN,
            operator = OperationLog.Operation.CREATE,
            tags = {
                    @OperationLogTag(key = "sheetTemplateConfigId", value = "{#params['sheetTemplateConfigId']}")
            }
    )
    @RequestMapping(value = "/api/dynamicsize/sheetTemplateConfig/{sheetTemplateConfigId}/sheet/asyn", method = RequestMethod.POST, produces = {MediaType.APPLICATION_JSON_VALUE})
    @ResponseBody
    public DataResultDTO generateSheetBySheetTemplateConfigId(@PathVariable long sheetTemplateConfigId, @RequestBody Map<String, Object> pageObject) throws IOException, TemplateException {
        return stManager.generateSheet(sheetTemplateConfigId, pageObject);
    }

    @ApiOperation(value = "Generate sheet V2", notes = "Generate dynamic sheet template for impression service.", response = BomLibrary.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "sheetTemplateConfigId", value = "SheetTemplateConfig id", paramType = "path"),
            @ApiImplicitParam(name = "inputDTO", value = "inputDTO including sheet type and sheetTemplateConfigV2DtoList")
    })
    @RequestMapping(value = "/api/dynamicsize/sheetTemplateConfig/{sheetTemplateConfigId}/sheet/asyn/v2", method = RequestMethod.POST, produces = {MediaType.APPLICATION_JSON_VALUE})
    @ResponseBody
    public DataResultDTO generateSheetBySheetTemplateConfigIdV2(@PathVariable long sheetTemplateConfigId,
                                                                @RequestBody DataInputDTO inputDTO) throws IOException, TemplateException {
        return stManager.generateSheetV2(sheetTemplateConfigId, inputDTO);
    }


}
