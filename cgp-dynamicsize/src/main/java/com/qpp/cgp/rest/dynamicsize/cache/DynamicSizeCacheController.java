package com.qpp.cgp.rest.dynamicsize.cache;

import com.qpp.cgp.model.dynamicsize.impact.ImpactParameterDTO;
import com.qpp.cgp.service.dynamicsize.cache.CacheClearService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "DS服务缓存清除接口，仅测试环境使用")
@RequestMapping("/api/dynamicsSize/caches")
@RestController
public class DynamicSizeCacheController {

    @Autowired
    private CacheClearService cacheClearService;

    @ApiOperation("清除所有 DS 缓存")
    @DeleteMapping
    public void clearAllCaches() {
        cacheClearService.clearAllCache();
    }

    @ApiOperation("清除所有 DieLine 缓存")
    @DeleteMapping("/dieLines")
    public void clearAllDieLineCache() {
        cacheClearService.clearAllDieLineCache();
    }

    @ApiOperation("清除所有 KafkaRequest 缓存")
    @DeleteMapping("/kafkaRequests")
    public void clearAllKafkaRequestCache() {
        cacheClearService.clearAllKafkaRequestCache();
    }

    @ApiOperation("清除redis缓存和mongo数据库缓存")
    @DeleteMapping("/all")
    public void clearAllRedisCachesAndMongoCaches() {
        cacheClearService.clearAllRedisCachesAndMongoCaches();
    }

    @ApiOperation("清除指定key的缓存")
    @DeleteMapping("/keys/{key}")
    public void clearCacheByKey(@PathVariable String key) {
        cacheClearService.clearCacheByKey(key);
    }

    @ApiOperation("批量清除指定key的缓存")
    @PostMapping("/keys")
    public void clearCacheByKeys(@RequestBody List<String> keys) {
        cacheClearService.clearCacheByKeys(keys);
    }

    @ApiOperation("清除指定参数的缓存")
    @PutMapping("/impact/parameter/clean")
    public void clearCacheByImpactParameter(@RequestBody List<ImpactParameterDTO> impactParameters) {
        cacheClearService.clearCacheByImpactParameters(impactParameters);
    }
}
