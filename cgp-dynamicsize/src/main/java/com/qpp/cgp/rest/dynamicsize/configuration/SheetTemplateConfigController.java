package com.qpp.cgp.rest.dynamicsize.configuration;

import com.qpp.cgp.domain.dynamicsize.dto.PageTemplateConfigDTO;
import com.qpp.cgp.domain.dynamicsize.dto.SheetTemplateConfigDTO;
import com.qpp.cgp.manager.dynamicsize.configuration.SheetTemplateConfigManager;
import com.qpp.cgp.model.dynamicsize.sheet.TemplateConfigCopyParameter;
import com.qpp.cgp.rest.dynamicsize.common.ConfigManagementBaseController;
import com.qpp.cgp.service.dynamicsize.configuration.SheetTemplateConfigService;
import com.qpp.cgp.service.dynamicsize.template.SheetTemplateConfigCopier;
import com.qpp.operation.log.OperationLog;
import com.qpp.operation.log.OperationLogModule;
import com.qpp.operation.log.OperationLogTag;
import com.qpp.web.business.controller.AbstractJsonRestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Map;
import java.util.Set;

/**
 * Created by admin on 2019/4/8.
 */
@Api("SheetTemplate Config")
@OperationLogModule("DynamicSizeSheetTemplate")
@RestController
@RequestMapping(value = "api/dynamicsize/sheettemplates")
public class SheetTemplateConfigController extends ConfigManagementBaseController<SheetTemplateConfigManager> {
    private SheetTemplateConfigService sheetTemplateConfigService;

    private SheetTemplateConfigCopier sheetTemplateConfigCopier;

    @Autowired
    public SheetTemplateConfigController(SheetTemplateConfigManager manager, SheetTemplateConfigService sheetTemplateConfigService,
                                         SheetTemplateConfigCopier configCopier) {
        super(manager);
        this.sheetTemplateConfigService = sheetTemplateConfigService;
        this.sheetTemplateConfigCopier = configCopier;
    }

    @ApiOperation("通过id查找impact模板的格式")
    @GetMapping("/{id}/types")
    public Set<String> getSheetTypesByConfigId(@PathVariable("id") Long id) {
        return sheetTemplateConfigService.getTypesByConfigId(id);
    }

    @ApiOperation("通过id查找sheetTemplateConfig的全部数据")
    @GetMapping("/{id}/full")
    public SheetTemplateConfigDTO getSheetFullDataByConfigId(@PathVariable("id") Long id) {
        return sheetTemplateConfigService.getFullDataByConfigId(id);
    }

    @ApiOperation("拷贝 sheetTemplateConfig")
    @PostMapping("/copy")
    public Map<Long, Long> copy(@RequestBody TemplateConfigCopyParameter templateConfigCopyParameter) {
        return sheetTemplateConfigCopier.copy(new ArrayList<>(templateConfigCopyParameter.getIds()),
                templateConfigCopyParameter.getIdMap());
    }
}
