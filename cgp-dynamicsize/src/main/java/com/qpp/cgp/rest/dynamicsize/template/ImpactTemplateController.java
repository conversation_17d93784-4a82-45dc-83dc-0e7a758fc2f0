package com.qpp.cgp.rest.dynamicsize.template;

import com.qpp.cgp.configuration.dynamicsize.DynamicSizeProperties;
import com.qpp.cgp.manager.dynamicsize.record.DynamicSizeDieLineRecorder;
import com.qpp.cgp.manager.dynamicsize.template.DynamicSizeDieLineFileUrlProcessor;
import com.qpp.cgp.manager.dynamicsize.template.ImpactTemplateManager;
import com.qpp.cgp.model.dynamicsize.impact.DynamicDieLineResultDTO;
import com.qpp.cgp.model.dynamicsize.impact.ImpactParameterDTO;
import com.qpp.cgp.service.dynamicsize.DynamicSizeTemplateService;
import freemarker.template.TemplateException;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

@Api(description = "Impact dynamic dieline template")
@Controller
public class ImpactTemplateController {

    @Autowired
    ImpactTemplateManager manager;

    @Autowired
    DynamicSizeDieLineFileUrlProcessor dynamicSizeDieLineFileUrlProcessor;

    @Autowired
    private DynamicSizeDieLineRecorder recorder;

    @Autowired
    private DynamicSizeTemplateService dynamicSizeTemplateService;

    @Autowired
    private DynamicSizeProperties dynamicSizeProperties;

    @ApiOperation(value = "generate impact dynamic dieline", notes = "Generate dynamic dieline template file of PDF or SVG.", response = DynamicDieLineResultDTO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "parameterDTO", value = "parameters of generating dynamic dieline template, try with this: <br/>{<br/>\"format\":\"PDF\",<br/>\"standard\":\"PS014028002-Easy_Top\",<br/>\"variables\":{<br/>&nbsp;&nbsp;\"w\":100,<br/>&nbsp;&nbsp;\"l\":100,<br/>&nbsp;&nbsp;\"h\":50<br/>}<br/>}", paramType = "body", dataType = "ImpactParameterDTO", dataTypeClass = ImpactParameterDTO.class ,
                    examples = @Example(value = @ExampleProperty(value = "{\"format\":\"PDF\",\"standard\":\"PS014028002-Easy_Top\",\"variables\":{\"w\":100,\"l\":100,\"h\":50}}", mediaType = MediaType.APPLICATION_JSON_VALUE)
            ))
    })
    @RequestMapping(value = "/api/dynamicsize/impact/template/download", method = RequestMethod.POST, produces = {MediaType.APPLICATION_JSON_VALUE})
    @ResponseBody
    public DynamicDieLineResultDTO generateDieLineTemplate(@RequestBody ImpactParameterDTO parameterDTO) throws IOException, TemplateException {
        try {
            recorder.init();

            recorder.recordReceivedRequest(parameterDTO);

            DynamicDieLineResultDTO dynamicDieLineResultDTO = manager.generateDieLineTemplate(parameterDTO);

            if (dynamicDieLineResultDTO != null) {
                dynamicSizeDieLineFileUrlProcessor.processFileUrl(dynamicDieLineResultDTO);
            }

            return dynamicDieLineResultDTO;
        } finally {
            recorder.clear();
        }
    }

    @ApiOperation(value = "generate impact dynamic dieline v2", notes = "Generate dynamic dieline template file of PDF or SVG.", response = DynamicDieLineResultDTO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "parameterDTO", value = "parameters of generating dynamic dieline template, try with this: <br/>{<br/>\"format\":\"PDF\",<br/>\"standard\":\"PS014028002-Easy_Top\",<br/>\"variables\":{<br/>&nbsp;&nbsp;\"w\":100,<br/>&nbsp;&nbsp;\"l\":100,<br/>&nbsp;&nbsp;\"h\":50<br/>}<br/>}", paramType = "body", dataType = "ImpactParameterDTO", dataTypeClass = ImpactParameterDTO.class ,
                    examples = @Example(value = @ExampleProperty(value = "{\"format\":\"PDF\",\"standard\":\"PS014028002-Easy_Top\",\"variables\":{\"w\":100,\"l\":100,\"h\":50}}", mediaType = MediaType.APPLICATION_JSON_VALUE)
                    ))
    })
    @RequestMapping(value = "/api/dynamicsize/impact/template/download/v2", method = RequestMethod.POST, produces = {MediaType.APPLICATION_JSON_VALUE})
    @ResponseBody
    public DynamicDieLineResultDTO generateDieLineTemplateV2(@RequestBody ImpactParameterDTO parameterDTO) throws IOException, TemplateException {
        return dynamicSizeTemplateService.generateV2(parameterDTO);
    }

    @ApiOperation(value = "callback founction from Impact", notes = "When impact generate dieline template completed, call this api and cache the result.", response = DynamicDieLineResultDTO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "Unique ID", paramType = "path")
            , @ApiImplicitParam(name = "resultDTO", value = "result of the generation", paramType = "body", dataTypeClass = DynamicDieLineResultDTO.class)
    })
    @RequestMapping(value = "/api/dynamicsize/impact/message/{id}", method = RequestMethod.POST, produces = {MediaType.APPLICATION_JSON_VALUE})
    @ResponseBody
    public void impactCallBack(@PathVariable String id, @RequestBody DynamicDieLineResultDTO resultDTO) {
        try {
            recorder.init();
            recorder.recordDsKafkaKey(id);
            recorder.recordReceivedDieLineResult(resultDTO);

            manager.impactCallBack(id, resultDTO);

        } finally {
            recorder.recordSavedDieLineResult();
            recorder.clear();
        }
    }

    @GetMapping(value = "/api/dynamicsize/newLogic", produces = {MediaType.APPLICATION_JSON_VALUE})
    public @ResponseBody boolean isNewLogic() {
        return dynamicSizeProperties.isNewLogic();
    }

    @PutMapping(value = "/api/dynamicsize/newLogic", produces = {MediaType.APPLICATION_JSON_VALUE})
    public @ResponseBody void updateIsNewLogic(@RequestParam boolean isNewLogic) {
        dynamicSizeProperties.setNewLogic(isNewLogic);
    }
}
