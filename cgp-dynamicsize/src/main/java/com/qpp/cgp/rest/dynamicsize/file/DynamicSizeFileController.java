package com.qpp.cgp.rest.dynamicsize.file;

import com.qpp.cgp.domain.dynamicsize.file.DynamicSizeFileType;
import com.qpp.cgp.domain.dynamicsize.file.DynamicSizeFileUploadResult;
import com.qpp.cgp.domain.dynamicsize.file.DynamicSizeTemplateFile;
import com.qpp.cgp.manager.dynamicsize.file.DynamicSizeFileManager;
import com.qpp.core.dto.PageDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * DynamicSize 文件接口
 *
 * <AUTHOR>
 * @since 2021/2/25
 */
@RequestMapping("/api/dynamicsize/file")
@RestController
public class DynamicSizeFileController {

    @Autowired
    public DynamicSizeFileManager dynamicSizeFileManager;

    /**
     * 上传 DynamicSize 相关文件
     *
     * @param files 需要上传的文件
     * @param type  文件类别-决定存放的目录
     */
    @PostMapping("/upload")
    public List<DynamicSizeFileUploadResult> uploadFile(@RequestPart(name = "files") MultipartFile[] files, @RequestParam(name = "type") DynamicSizeFileType type) {
        List<DynamicSizeFileUploadResult> uploadResults = new ArrayList<>();
        for (MultipartFile file : files) {
            uploadResults.add(dynamicSizeFileManager.uploadFile(file, type));
        }
        return uploadResults;
    }


    /**
     * 下载 DynamicSize 文件
     * @param fileName 下载的文件名
     * @param type 模板
     * @param response HttpServletResponse
     */
    @GetMapping("/download")
    public void downloadFile(@RequestParam(name = "fileName") String fileName,@RequestParam(name = "type") DynamicSizeFileType type, HttpServletResponse response) {
        dynamicSizeFileManager.downFile(fileName, type, response);
    }


    /**
     * 根据模板类型查找文件
     * @param page 第几页
     * @param limit 每页展示文件的数量
     * @param type 模板类型
     * @return 封装好的文件信息
     */
    @GetMapping("/type")
    public PageDTO<DynamicSizeTemplateFile> findTemplateFileInfoByType(@RequestParam("page") int page,
                                                                       @RequestParam("limit") int limit,
                                                                       @RequestParam(name = "type") DynamicSizeFileType type) {
        return dynamicSizeFileManager.findTemplateFileInfoByType(page, limit, type);
    }

}
