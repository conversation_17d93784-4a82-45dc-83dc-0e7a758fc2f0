package com.qpp.cgp.rest.dynamicsize.configuration;

import com.qpp.cgp.manager.dynamicsize.configuration.RequestTemplateManager;
import com.qpp.operation.log.OperationLogModule;
import com.qpp.web.business.controller.AbstractJsonRestController;
import io.swagger.annotations.Api;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by admin on 2019/4/9.
 */
@Api("RequestTemplate Config")
@OperationLogModule("DynamicSizeRequestTemplate")
@RestController
@RequestMapping(value = "api/dynamicsize/requesttemplates")
public class RequestTemplateConfigController extends AbstractJsonRestController<Document, Long, RequestTemplateManager> {

    @Autowired
    public RequestTemplateConfigController(RequestTemplateManager manager) {
        super(manager);
    }
}
