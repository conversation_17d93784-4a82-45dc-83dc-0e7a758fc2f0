package com.qpp.cgp.rest.dynamicsize.template;

import com.qpp.cgp.service.dynamicsize.configuration.PageTemplateConfigCopyService;
import com.qpp.cgp.service.dynamicsize.configuration.SheetTemplateConfigCopyService;
import org.bson.Document;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date 2024/4/19 16:01
 */
@Api(description = "TemplateConfig Copy")
@RestController
@RequestMapping("/api/copy/templateconfigs")
public class TemplateConfigCopyController {

    @Autowired
    private PageTemplateConfigCopyService pageTemplateConfigCopyService;

    @Autowired
    private SheetTemplateConfigCopyService sheetTemplateConfigCopyService;

    @ApiOperation("复制小版")
    @GetMapping("/page")
    public Document copyPageTemplateConfig(@RequestParam Long id) {
        return pageTemplateConfigCopyService.copyPageTemplateConfig(id);
    }


    @ApiOperation("复制大版")
    @GetMapping("/sheet")
    public Document copySheetTemplateConfig(@RequestParam Long id) {
        return sheetTemplateConfigCopyService.copySheetTemplateConfig(id);
    }
}
