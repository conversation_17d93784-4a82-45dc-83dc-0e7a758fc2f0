package com.qpp.cgp.rest.dynamicsize.template;

import com.qpp.cgp.domain.dynamicsize.bom.BomLibrary;
import com.qpp.cgp.manager.dynamicsize.template.PageTemplateManager;
import freemarker.template.TemplateException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * Created by admin on 2017/8/9.
 */
@Api(description = "Page Template")
@Controller
public class PageTemplateController {

    @Autowired
    @Qualifier("pageTemplateManager")
    PageTemplateManager ptManager;

    @ApiOperation(value = "Generate page", notes = "Generate dynamic page template for impression page service.", response = BomLibrary.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "skuId", value = "CGP sku product id", paramType = "path")
            , @ApiImplicitParam(name = "pageType", value = "Impression page type", paramType = "path")
    })
    @RequestMapping(value = "/api/dynamicsize/sku/{skuId}/pagetype/{pageType}/page", method = RequestMethod.POST, produces = {MediaType.APPLICATION_JSON_VALUE})
    @ResponseBody
    public String getPageBySkuAndPageType(@PathVariable Long skuId, @PathVariable String pagetype) throws IOException, TemplateException {

        return ptManager.generatePage(skuId, pagetype);
    }

    @ApiOperation(value = "Generate page", notes = "Generate dynamic page template for impression page service.", response = BomLibrary.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageType", value = "Impression page type", paramType = "path")
            , @ApiImplicitParam(name = "orderDTO", value = "Order info, first item is the printing item")
    })
    @RequestMapping(value = "/api/dynamicsize/pagetype/{pageType}/page", method = RequestMethod.POST, produces = {MediaType.APPLICATION_JSON_VALUE})
    @ResponseBody
    public String generateProductPageTemplate(@PathVariable String pageType, @RequestBody String orderDTO) throws IOException, TemplateException {
        return ptManager.generatePageTemplateFromOrder(pageType, orderDTO);
    }

}
