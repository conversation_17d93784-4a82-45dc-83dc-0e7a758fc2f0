package com.qpp.cgp.rest.dynamicsize.configuration;

import com.qpp.cgp.manager.dynamicsize.template.DynamicSizeDieLineFileUrlProcessor;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "Dynamic Size 配置接口")
@RequestMapping("/api/dynamicsSize/configurations")
@RestController
public class DynamicSizeConfigurationController {

    @Autowired
    private DynamicSizeDieLineFileUrlProcessor dynamicSizeDieLineFileUrlProcessor;

    @GetMapping("/fileServer/download/server/process//enable")
    public boolean getDownloadServerEnableProcessUrl() {
        return dynamicSizeDieLineFileUrlProcessor.getEnable();
    }

    @PutMapping("/fileServer/download/server/process/enable")
    public void enableDownloadServerProcessUrl() {
        dynamicSizeDieLineFileUrlProcessor.setEnable(true);
    }

    @PutMapping("/fileServer/download/server/process/disable")
    public void disableDownloadServerProcessUrl() {
        dynamicSizeDieLineFileUrlProcessor.setEnable(false);
    }

    @GetMapping("/fileServer/download/server")
    public String getDownloadFileServer() {
        return dynamicSizeDieLineFileUrlProcessor.getFileServerUrl();
    }

    @PutMapping("/fileServer/download/server")
    public void setDownloadFileServer(@RequestBody String downloadServer) {
        dynamicSizeDieLineFileUrlProcessor.setFileServerUrl(downloadServer);
    }

}
