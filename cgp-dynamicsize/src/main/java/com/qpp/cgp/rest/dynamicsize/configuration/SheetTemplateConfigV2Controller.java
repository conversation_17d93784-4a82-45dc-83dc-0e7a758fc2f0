package com.qpp.cgp.rest.dynamicsize.configuration;

import com.qpp.cgp.domain.dynamicsize.dto.SheetTemplateConfigV2InputDTO;
import com.qpp.cgp.model.dynamicsize.common.DataResultDTO;
import com.qpp.cgp.service.dynamicsize.configuration.SheetTemplateConfigV2Service;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Package com.qpp.cgp.rest.dynamicsize.configuration
 * @Date 2025/1/11 16:04
 */
@RestController
@RequestMapping(value = "api/dynamicsize/sheettemplatev2s")
public class SheetTemplateConfigV2Controller {

    @Autowired
    private SheetTemplateConfigV2Service sheetTemplateConfigV2Service;

    @ApiOperation("自动生成不同动态尺寸刀线的组合版")
    @PostMapping("/{templateId}/combination")
    public DataResultDTO generateCombinationSheet(@PathVariable("templateId") Long templateId,
                                                  @RequestBody List<SheetTemplateConfigV2InputDTO> dtoList) {
        return sheetTemplateConfigV2Service.generateCombinationSheet(dtoList, templateId);
    }

}
