package com.qpp.cgp.domain.dynamicsize.dto;

import com.qpp.cgp.domain.dynamicsize.configuration.ImpressionPlaceholder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @Package com.qpp.cgp.domain.dynamicsize.dto
 * @Date 2025/1/11 16:11
 */
@Getter
@Setter
@NoArgsConstructor
public class SheetTemplateConfigV2OutputDTO {

    private Double width;

    private Double height;

    private String content;

    private List<PlaceholderV2DTO> placeholders;

    private List<ImpressionPlaceholder> impressionPlaceholders;

    public SheetTemplateConfigV2OutputDTO(Double width, Double height, String content, List<PlaceholderV2DTO> placeholders, List<ImpressionPlaceholder> impressionPlaceholders) {
        this.width = width;
        this.height = height;
        this.content = content;
        this.placeholders = placeholders;
        this.impressionPlaceholders = impressionPlaceholders;
    }
}
