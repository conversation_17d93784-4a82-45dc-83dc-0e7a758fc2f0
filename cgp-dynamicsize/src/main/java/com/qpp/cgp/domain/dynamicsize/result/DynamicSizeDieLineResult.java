package com.qpp.cgp.domain.dynamicsize.result;

import com.qpp.cgp.model.dynamicsize.impact.DynamicDieLineResultDTO;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

@Data
@Document(collection = "dsdielineresults")
public class DynamicSizeDieLineResult extends DynamicDieLineResultDTO {

    @Id
    private String id;

    private String source;

    private Date createdDate;

}
