package com.qpp.cgp.domain.dynamicsize.configuration.v2;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Package com.qpp.cgp.domain.dynamicsize.configuration.v2
 * @Date 2025/1/11 15:33
 */
@Getter
@Setter
@NoArgsConstructor
public class LayoutElement {

    private Boolean isRotateCombination;

    private Double CombinationRotate;

    private Long contentTypeId;

    private Integer contentSortOrder;

    private Integer sortOrder;

}
