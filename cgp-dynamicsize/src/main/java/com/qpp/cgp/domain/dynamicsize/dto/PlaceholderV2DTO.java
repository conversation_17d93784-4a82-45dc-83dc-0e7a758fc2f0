package com.qpp.cgp.domain.dynamicsize.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Package com.qpp.cgp.domain.dynamicsize.dto
 * @Date 2025/1/11 16:12
 */
@Getter
@Setter
@NoArgsConstructor
public class PlaceholderV2DTO {

    private Double x;

    private Double y;

    private Double width;

    private Double height;

    private Double rotate;

    private Long contentTypeId;

    private Integer contentSortOrder;

    private Integer sortOrder;

    public PlaceholderV2DTO(Double x, Double y, Double width, Double height, Double rotate, Long contentTypeId, Integer contentSortOrder, Integer sortOrder) {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
        this.rotate = rotate;
        this.contentTypeId = contentTypeId;
        this.contentSortOrder = contentSortOrder;
        this.sortOrder = sortOrder;
    }

}
