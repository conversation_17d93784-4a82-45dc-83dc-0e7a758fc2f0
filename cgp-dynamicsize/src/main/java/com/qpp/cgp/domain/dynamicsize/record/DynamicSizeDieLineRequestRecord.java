package com.qpp.cgp.domain.dynamicsize.record;

import com.qpp.cgp.model.dynamicsize.impact.DynamicDieLineResultDTO;
import com.qpp.cgp.model.dynamicsize.impact.ImpactKafkaParameterDTO;
import com.qpp.cgp.model.dynamicsize.impact.ImpactParameterDTO;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

@Data
@Document(collection = "dsdielinerequestrecords")
public class DynamicSizeDieLineRequestRecord {

    @Id
    private String _id;

    private String dsKey;

    private String dsKafkaKey;

    private Date receivedRequestTime;

    private ImpactParameterDTO requestParameter;

    private ImpactKafkaParameterDTO kafkaRequestParameter;

    private DynamicDieLineResultDTO dynamicSizeDieLineResult;

    private Boolean success;

    private String message;

    private Boolean hitCache;

}
