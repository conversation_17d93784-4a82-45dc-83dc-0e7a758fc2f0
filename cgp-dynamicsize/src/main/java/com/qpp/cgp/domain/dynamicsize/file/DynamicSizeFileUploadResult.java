package com.qpp.cgp.domain.dynamicsize.file;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DynamicSize 文件上传结果
 *
 * <AUTHOR>
 * @since 2021/2/25
 */
@NoArgsConstructor
@Data
public class DynamicSizeFileUploadResult {

    /**
     * 文件类型
     */
    private DynamicSizeFileType fileType;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 存放位置
     */
    private String location;

}
