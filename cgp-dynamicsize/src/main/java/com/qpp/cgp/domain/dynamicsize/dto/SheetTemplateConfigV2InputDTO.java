package com.qpp.cgp.domain.dynamicsize.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Package com.qpp.cgp.domain.dynamicsize.dto
 * @Date 2025/1/11 16:10
 */
@Getter
@Setter
@NoArgsConstructor
public class SheetTemplateConfigV2InputDTO {

    private Long contentTypeId;

    private Integer sortOrder;

    private Double width;

    private Double height;

    public SheetTemplateConfigV2InputDTO(Long contentTypeId, Integer sortOrder, Double width, Double height) {
        this.contentTypeId = contentTypeId;
        this.sortOrder = sortOrder;
        this.width = width;
        this.height = height;
    }
}
