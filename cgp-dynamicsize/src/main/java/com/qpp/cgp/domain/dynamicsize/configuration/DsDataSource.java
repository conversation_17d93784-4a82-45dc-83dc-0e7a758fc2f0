package com.qpp.cgp.domain.dynamicsize.configuration;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.List;

/**
 * Created by admin on 2017/8/10.
 */
@NoArgsConstructor
@Data
@Document(collection="dsdatasources")
public class DsDataSource {

    @Id
    private Long _id;

    private String type;

    private int version = 1;

    @JsonTypeInfo(
            use = JsonTypeInfo.Id.NAME,
            include = JsonTypeInfo.As.PROPERTY,
            property = "type",
            defaultImpl = ImpactKeyValue.class
    )
    @JsonSubTypes({
            @JsonSubTypes.Type(value = ImpactKeyValue.class, name = "ImpactSvg")
    })
    private List<DsKeyValue> selectors;
}
