package com.qpp.cgp.domain.dynamicsize.configuration.v2;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @Package com.qpp.cgp.domain.dynamicsize.configuration.v2
 * @Date 2025/1/11 15:52
 */
@Getter
@Setter
@NoArgsConstructor
public class LayoutElementSequence {

    private LayoutElementAlign align;

    private BoundingBoxMargin margin;

    private Integer sortOrder;

    private List<LayoutPlaceholder> placeholders;

    public LayoutElementSequence(LayoutElementAlign align, BoundingBoxMargin margin, Integer sortOrder) {
        this.align = align;
        this.margin = margin;
        this.sortOrder = sortOrder;
    }
}
