package com.qpp.cgp.domain.dynamicsize.file;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 模板文件信息
 * <AUTHOR>
 * @Package com.qpp.cgp.domain.dynamicsize.file
 * @Date 2024/1/12 13:47
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DynamicSizeTemplateFile {
    /**
     * 模板文件名称
     */
    private String fileName;

    /**
     * 模板文件创建时间
     */
    private String createTime;

    /**
     * 模板文件修改时间
     */
    private String modifyTime;

    /**
     * 模板文件大小
     */
    private String fileSize;
}
