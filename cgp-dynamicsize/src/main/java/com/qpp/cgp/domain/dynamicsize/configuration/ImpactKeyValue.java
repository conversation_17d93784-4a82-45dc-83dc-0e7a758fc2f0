package com.qpp.cgp.domain.dynamicsize.configuration;

import com.fasterxml.jackson.annotation.JsonTypeName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

/**
 * Created by admin on 2017/8/8.
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper=false)
public class ImpactKeyValue extends DsKeyValue {

    private String selector;

    private String attr;

    private Long urlTemplateId;

    private String handler;

    private double svgWidthOffset = 2;

    private double svgHeightOffset = 2;

    private double strokeWidth = 2;

    private Boolean useOffset;
}                                                                                                                                                       
