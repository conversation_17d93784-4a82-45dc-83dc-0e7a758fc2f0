package com.qpp.cgp.configuration.dynamicsize;

import com.qpp.cgp.manager.dynamicsize.common.DsDataSourceObjectGenerator;
import com.qpp.cgp.manager.dynamicsize.file.DynamicSizeFileManager;
import com.qpp.cgp.manager.dynamicsize.template.BuilderTemplateManager;
import com.qpp.cgp.manager.dynamicsize.template.PageTemplateManager;
import com.qpp.cgp.manager.dynamicsize.template.SheetTemplateManager;
import com.qpp.core.utils.http.RestTemplateBuildUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * 可變尺寸服務專用自動配置
 */
@Configuration
@EnableConfigurationProperties({DynamicSizeProperties.class})
@EnableCaching
public class DynamicSizeAutoConfiguration {

    @Autowired
    DynamicSizeProperties appSetting;

    @Bean("dsRestTemplate")
    public RestTemplate restTemplate() {
        return RestTemplateBuildUtils.buildRestTemplate((int) appSetting.getImpact().getRequest().getConnectTimeout().toMillis(), (int) appSetting.getImpact().getRequest().getReadTimeout().toMillis());
    }

//    @Bean("dsObjectMapper")
//    public ObjectMapper objectMapper() {
//        ObjectMapper mapper = new ObjectMapper();
//        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
//        mapper.configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES,true);
//        return mapper;
//    }

//    @Bean("builderTemplateManager")
    public BuilderTemplateManager builderTemplateManager(DsDataSourceObjectGenerator objectGenerator){
        return new BuilderTemplateManager(objectGenerator);
    }

    @Bean("asynBuilderTemplateManager")
    public BuilderTemplateManager asynBuilderTemplateManager(DsDataSourceObjectGenerator objectGenerator){
        objectGenerator.setAsyn(true);
        return new BuilderTemplateManager(objectGenerator);
    }

//    @Bean("pageTemplateManager")
    public PageTemplateManager pageTemplateManager(DsDataSourceObjectGenerator objectGenerator) {
        return new PageTemplateManager(objectGenerator);
    }

    @Bean("asynPageTemplateManager")
    public PageTemplateManager asynPageTemplateManager(DsDataSourceObjectGenerator objectGenerator) {
        objectGenerator.setAsyn(true);
        return new PageTemplateManager(objectGenerator);
    }

//    @Bean("sheetTemplateManager")
    public SheetTemplateManager sheetTemplateManager(DsDataSourceObjectGenerator objectGenerator){
        return new SheetTemplateManager(objectGenerator);
    }

    @Bean("asynSheetTemplateManager")
    public SheetTemplateManager asynSheetTemplateManager(DsDataSourceObjectGenerator objectGenerator){
        objectGenerator.setAsyn(true);
        return new SheetTemplateManager(objectGenerator);
    }

    @Bean("dynamicSizeFileManager")
    public DynamicSizeFileManager dynamicSizeFileManager(DynamicSizeProperties dynamicSizeProperties) {
        return new DynamicSizeFileManager(dynamicSizeProperties);
    }

}
