package com.qpp.cgp.configuration.dynamicsize;


import com.qpp.cgp.manager.application.ApplicationConfigService;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 可變尺寸服務專用配置
 */
@NoArgsConstructor
@Data
@ConfigurationProperties(prefix="com.qpp.dynamic-size")
public class DynamicSizeProperties {

    /*************** 文件目錄配置 ******************/
    @Value("${builder-template-dir}")
    private String builderTemplatesFolder;

    @Value("${page-template-dir}")
    private String pageTemplatesFolder;

    @Value("${sheet-template-dir}")
    private String sheetTemplatesFolder;

    @Value("${temporary-dir}")
    private String tempFolder;

    /*************** 請求網絡URL配置 ******************/
    @Value("${file-service}")
    private String fileServiceUrl;

    @Value("${file-download-service:}")
    private String downloadFileServiceUrl;

    @Value("${new-logic:true}")
    private boolean newLogic;

    /*************** Impact 相關功能配置 ******************/
    private final Impact impact = new Impact();

    @NoArgsConstructor
    @Data
    public static class Impact {

        /**
         * 緩存過期時間
         */
        private Duration cacheTimeout = Duration.ofDays(7);

        /**
         * 失敗數據過期時間
         */
        private Duration retryTimeout = Duration.ofMinutes(1);

        /**
         * 是否启用 redis 缓存
         */
        private Boolean enableRedisCache = true;

        /**
         * impact Kafka配置
         */
        private final ImpactRequest request = new ImpactRequest();

        @NoArgsConstructor
        @Data
        public static class ImpactRequest {

            /**
             * Http請求超時
             */
            private Duration readTimeout = Duration.ofSeconds(30);

            /**
             * Http連接超時
             */
            private Duration connectTimeout = Duration.ofSeconds(45);

            /**
             * dsrequesttemplates默認配置id，用於舊有用Get方式獲取的配置
             */
            private long defaultTemplateId = 0;

            /**
             * 刀線配置中，對應類型刀線替換后，加入到palettes中
             */
            private final Map<String, List<String>> includePalettes = new HashMap<>();

            /**
             * 刀線配置中，對應類型刀線替換后，加入到excludePalettes中
             */
            private final Map<String, List<String>> excludePalettes = new HashMap<>();

            /**
             * 參數配置中，對應條件的參數，會從參數中移除
             */
            private final Map<String, Object> excludeParameters = new HashMap<>();

            /**
             * Monogo DB request template公共參數
             */
            private Map<String, Object> parameters = new HashMap<String, Object>();
        }

        /**
         * impact Kafka配置
         */
        private final ImpactKafka kafka = new ImpactKafka();

        @NoArgsConstructor
        @Data
        public static class ImpactKafka {

            /**
             * dynamic size 專用kafka topic
             */
            private String topic;

            /**
             * Impact 服务生成结果后回调的接口，可选
             */
            private String defaultNotifyUrl;

            /**
             * Stage 环境下，Impact Kafka 处理结果回调接口，可选
             *
             * 优先级: stageNotifyUrl > defaultNotifyUrl
             *
             * @see com.qpp.cgp.manager.application.ApplicationMode#Stage
             * @see ApplicationConfigService#getApplicationMode()
             */
            private String stageNotifyUrl;

            /**
             * Production 环境下，Impact Kafka 处理结果回调接口，可选
             *
             * 优先级: productionNotifyUrl > defaultNotifyUrl
             *
             * @see com.qpp.cgp.manager.application.ApplicationMode#Production
             * @see ApplicationConfigService#getApplicationMode()
             */
            private String productionNotifyUrl;

        }
    }
}
