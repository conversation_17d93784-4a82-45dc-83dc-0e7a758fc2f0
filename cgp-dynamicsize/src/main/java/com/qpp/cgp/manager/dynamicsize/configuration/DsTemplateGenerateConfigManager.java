package com.qpp.cgp.manager.dynamicsize.configuration;

import com.qpp.cgp.config.mongo.MongoTemplateBeanNames;
import com.qpp.cgp.domain.dynamicsize.configuration.DsTemplateGenerateConfig;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.driver.HybridMongoTemplate;
import com.qpp.web.business.manager.AbstractLongMongoCurdManager;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/6/6
 */
@Service
public class DsTemplateGenerateConfigManager extends AbstractLongMongoCurdManager<DsTemplateGenerateConfig, Long> {

    public DsTemplateGenerateConfigManager(@Qualifier(MongoTemplateBeanNames.CONFIG) HybridMongoTemplate mongoTemplate,
                                           IdGenerator idgenerator) {
        super(mongoTemplate, idgenerator);
    }

    public Optional<DsTemplateGenerateConfig> findDefault() {
        Criteria criteria = Criteria.where("templateName").exists(false).and("enable").is(true);
        Query query = Query.query(criteria);
        DsTemplateGenerateConfig config = mongoTemplate.findOne(query, DsTemplateGenerateConfig.class);
        return Optional.ofNullable(config);
    }

    public Optional<DsTemplateGenerateConfig> findByTemplateNameAndEnable(String templateName, boolean enable) {
        Criteria criteria = Criteria.where("templateName").is(templateName).and("enable").is(enable);
        Query query = Query.query(criteria);
        DsTemplateGenerateConfig config = mongoTemplate.findOne(query, DsTemplateGenerateConfig.class);
        return Optional.ofNullable(config);
    }

    public List<DsTemplateGenerateConfig> findByTemplateName(String templateName) {
        return mongoTemplate.find(Query.query(Criteria.where("templateName").is(templateName)),DsTemplateGenerateConfig.class);
    }

    public int getUsedVersion(String templateName) {
        Optional<DsTemplateGenerateConfig> configOptional = findByTemplateNameAndEnable(templateName, true);
        if (!configOptional.isPresent()) {
            configOptional = findDefault();
        }

        if (!configOptional.isPresent()) {
            return 1;
        } else {
            DsTemplateGenerateConfig config = configOptional.get();
            return config.getVersion();
        }
    }
}
