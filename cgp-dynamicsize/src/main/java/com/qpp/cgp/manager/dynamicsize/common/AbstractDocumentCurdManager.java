package com.qpp.cgp.manager.dynamicsize.common;

import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.model.ReplaceOptions;
import com.qpp.core.aware.UserGetterAware;
import com.qpp.core.dto.FilterDTO;
import com.qpp.core.dto.PageDTO;
import com.qpp.core.exception.BusinessException;
import com.qpp.core.exception.BusinessExceptionLevel;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.driver.HybridMongoTemplate;
import com.qpp.web.business.manager.CurdManager;
import net.sf.json.JSONArray;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.query.Criteria;

import java.io.Serializable;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;
import java.util.regex.Pattern;

/**
 * Created by admin on 2019/4/4.
 */
public abstract class AbstractDocumentCurdManager<PK extends Serializable & Comparable<PK>> implements CurdManager<Document, PK>, UserGetterAware {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    protected HybridMongoTemplate mongoTemplate;

    protected Supplier<Optional<Long>> userGetter = () -> Optional.empty();

    protected IdGenerator idgenerator;

    protected String collectionName;

    protected MongoCollection<Document> mongoCollection;

    public AbstractDocumentCurdManager(String collectionName, HybridMongoTemplate mongoTemplate, IdGenerator idGenerator) {

        this.collectionName = collectionName;

        this.mongoTemplate = mongoTemplate;

        this.idgenerator = idGenerator;

        this.mongoCollection = mongoTemplate.getCollection(this.collectionName);
    }

    @Override
    public Document saveNew(Document dto) {
        this.addAudit(dto);
        if (dto.get("_id") == null) {
            //todo:根據類型轉換值類型
            dto.put("_id", this.generateId());
        }

        mongoCollection.insertOne(dto);

        return dto;
    }

    @Override
    public Document saveUpdate(Document dto, PK id) {

        this.addAudit(dto);
        mongoCollection.replaceOne(new Document("_id", id), dto, new ReplaceOptions().upsert(true));
        return dto;
    }

    @Override
    public void delete(PK id) {

        mongoCollection.deleteOne(new Document().append("_id", id));

    }

    @Override
    public Document findById(PK id) {
        Document document = mongoTemplate.findById(id, Document.class, this.collectionName);

        if (document == null) {
            throw new BusinessException(this.collectionName,
                    this.collectionName + " '" + id + "' is not exists!", BusinessExceptionLevel.DEBUG);
        }

        return document;

    }

    @Override
    public PageDTO<Document> findAll(Pageable pageRequest, List<FilterDTO> filters) {


        List<Criteria> criterias = new ArrayList<>();

        for (FilterDTO filter : filters) {

            Criteria criteria = Criteria.where(filter.getName());

            Object value = null;
            if (filter.getName().equalsIgnoreCase("_id")) {

                criteria.is(filter.getValue());
            } else if (filter.getType().equalsIgnoreCase("number")) {
                criteria.is(Long.parseLong(filter.getValue().toString()));
            } else if ("date".equalsIgnoreCase(filter.getType())) {
                criteria = this.queryDateCriteria(filter);
            } else if (filter.getName().equals("excludeIds")) {
                //是一个json数组  如果不是 不进行处理
                String    idJsonArrayString = filter.getValue().toString();
                JSONArray idJsonArray       = JSONArray.fromObject(idJsonArrayString);

                if (idJsonArray.size() > 0) {
                    List<Object> list = new ArrayList<>();
                    for (int i = 0; i < idJsonArray.size(); i++) {
                        if ("string".equalsIgnoreCase(filter.getType())) {
                            list.add(idJsonArray.get(i).toString());
                        } else if ("number".equalsIgnoreCase(filter.getType())) {
                            list.add(Long.parseLong(idJsonArray.get(i).toString()));
                        }
                    }
                    criteria.not().in(list);

                }
            } else if (filter.getName().equalsIgnoreCase("clazz")) {
                criteria.is(filter.getValue());
            } else {
                criteria.regex(Pattern.compile(filter.getValue().toString()));
            }
            criterias.add(criteria);
        }

        int pagesize   = pageRequest.getPageSize();
        int pageNumber = pageRequest.getPageNumber();

        Document mongoFilter = null;
        FindIterable<Document> documents;
        if (!criterias.isEmpty()) {
            mongoFilter = new Criteria().andOperator(criterias).getCriteriaObject();
            documents   = mongoCollection.find(mongoFilter).skip(pagesize * (pageNumber)).limit(pagesize);
        }else{
            documents   = mongoCollection.find().skip(pagesize * (pageNumber)).limit(pagesize);
        }
        long                   totalSize;
        if (mongoFilter == null) {
            totalSize   = mongoTemplate.count(this.collectionName);
        }else{
            totalSize   = mongoTemplate.count(this.collectionName, mongoFilter);
        }
        Long                   totalPage   = totalSize / pagesize + (totalSize % pagesize > 0 ? 1 : 0);

        List<Document>        pageData    = new ArrayList<>();
        MongoCursor<Document> mongoCursor = documents.iterator();
        while (mongoCursor.hasNext()) {
            pageData.add(mongoCursor.next());
        }


        PageDTO<Document> page = new PageDTO<>(pageNumber + 1, pagesize, pageData);
        page.setTotalCount(totalSize);
        page.setTotalPages(totalPage.intValue());


        return page;
    }


    private static DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public Criteria queryDateCriteria(FilterDTO filter) {

        Criteria criteria = null;

        if ("date".equalsIgnoreCase(filter.getType())) {

            String name = filter.getName();
            if (name.contains("@from")) {
                name = name.replace("@from", "");
                criteria = Criteria.where(name);
                try {
                    criteria.gt(dateFormat.parse(filter.getValue().toString()));
                } catch (ParseException e) {
                    e.printStackTrace();
                }
            } else if (name.contains("@to")) {
                name = name.replace("@to", "");
                criteria = Criteria.where(name);
                try {
                    criteria.lt(dateFormat.parse(filter.getValue().toString()));
                } catch (ParseException e) {
                    e.printStackTrace();
                }
            }

        }
        return criteria;
    }


    protected void addAudit(Document dto) {

        Document existsDocument = null;
        Object id = dto.get("_id");
        if (id != null) {
            existsDocument = mongoCollection.find(new Document("_id", id)).first();
        }

        if (existsDocument == null) {
            dto.put("createdDate", new Date());
            dto.put("createdBy", userGetter.get().orElse(null));
        } else {
            dto.put("createdDate", Optional.ofNullable(existsDocument.get("createdDate")).orElse(new Date()));
            dto.put("createdBy", Optional.ofNullable(existsDocument.get("createdBy")).orElse(userGetter.get().orElse(null)));
            dto.put("modifiedDate", new Date());
            dto.put("modifiedBy", userGetter.get().orElse(null));
        }
    }

    /**
     * 按類型生成ID
     *
     * @return
     */
    protected Object generateId() {
        Long value = idgenerator.generateId();

        Type t = this.getClass().getGenericSuperclass();
        Type[] params = ((ParameterizedType) t).getActualTypeArguments();
        Class<PK> clazz = (Class<PK>) params[0];

        if (clazz.equals(Long.class))
            return value;
        else if (clazz.equals(Double.class))
            return value.doubleValue();
        else if (clazz.equals(Integer.class))
            return value.intValue();
        else if (clazz.equals(Float.class))
            return value.floatValue();
        else if (clazz.equals(Byte.class))
            return value.byteValue();
        else if (clazz.equals(Short.class))
            return value.shortValue();

        return value.toString();
    }

    @Override
    public void setUserGetter(Supplier<Optional<Long>> userGetter) {
        this.userGetter = userGetter;
    }
}
