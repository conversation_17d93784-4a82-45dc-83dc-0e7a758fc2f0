package com.qpp.cgp.manager.dynamicsize.impact;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.qpp.cgp.cache.RedisComponent;
import com.qpp.cgp.configuration.dynamicsize.DynamicSizeProperties;
import com.qpp.cgp.manager.application.ApplicationConfigService;
import com.qpp.cgp.manager.application.ApplicationMode;
import com.qpp.cgp.manager.dynamicsize.record.DynamicSizeDieLineRecorder;
import com.qpp.cgp.model.dynamicsize.impact.*;
import com.qpp.cgp.service.dynamicsize.cache.RedisKeyGenerator;
import com.qpp.cgp.utils.ObjectMapperBeanCreateUtils;
import com.qpp.cgp.utils.UriTemplateHandlerBeanCreateUtils;
import io.sentry.Sentry;
import io.sentry.event.Event;
import io.sentry.event.EventBuilder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriTemplateHandler;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
public class KafkaImpactRequestService {

    @Autowired
    private ApplicationConfigService applicationConfigService;

    @Autowired
    private DynamicSizeDieLineRecorder recorder;

    @Autowired
    RedisKeyGenerator keyGenerator;

    @Autowired
    DynamicSizeProperties appSettings;

    @Autowired
    private RedisComponent<String,Object> redisComponent;

    @Autowired
    KafkaTemplate<Integer, String> kafkaTemplate;

    ObjectMapper mapper = ObjectMapperBeanCreateUtils.getObjectMapper();

    UriTemplateHandler uriTemplateHandler = UriTemplateHandlerBeanCreateUtils.getUriTemplateHandler();

    /**
     * 調用Kafka生成刀線信息
     * 多個客戶端同時調用相同的刀線時（所有參數相同），不重複調用Kafka
     *
     * @param parameterDTO
     * @return
     */
    public DynamicDieLineResultDTO generate(ImpactParameterDTO parameterDTO) {


        //參數緩存Key
        String kafkaKey = keyGenerator.getKafkaRequestCacheKey(parameterDTO);
        //返回狀態
        ResultStatus resultStatus = ResultStatus.Retry;
        //返回信息
        String msg = "Please try again after a moment, while the dieline template is still generating.";

        recorder.recordDsKafkaKey(kafkaKey);

        //無緩存時，表示之前沒有相同的刀線正在生成，則請求Kafka生成刀線
        if (!redisComponent.hasKey(kafkaKey)) {

            ImpactKafkaParameterDTO kafkaParameter = this.getImpactKafkaParameterDTO(kafkaKey, parameterDTO);

            try {
                //調用隊列生成刀線
                kafkaTemplate.send(appSettings.getImpact().getKafka().getTopic(), mapper.writeValueAsString(kafkaParameter));
                //緩存請求參數，避免同樣的刀線多次調用Kafka
                redisComponent.hPutAll(kafkaKey, mapper.convertValue(parameterDTO, Map.class));
                redisComponent.expire(kafkaKey, appSettings.getImpact().getRetryTimeout().getSeconds(), TimeUnit.SECONDS);

                recorder.recordSentKafkaRequest(kafkaParameter);
            } catch (Exception e) {
                msg = "Error happen when generate dynamic dieline message from Kafka.";
                resultStatus = ResultStatus.Fail;

                EventBuilder eventBuilder = new EventBuilder()
                        .withMessage(e.getMessage())
                        .withLevel(Event.Level.ERROR)
                        .withExtra("parameterDto", parameterDTO)
                        .withExtra("kafkaParameter", kafkaParameter)
                        .withExtra("Developer Message", e)
                        .withExtra("Exception StackTrace", e.getStackTrace());
                Sentry.capture(eventBuilder);

                recorder.recordException(e);
            }
        }

        DynamicDieLineResultDTO resultDTO = new DynamicDieLineResultDTO();
        resultDTO.setStatusCode(resultStatus.value());
        resultDTO.setMessage(msg);
        return resultDTO;
    }


    /**
     * 創建Kafka參數
     * @param key
     * @param parameterDTO
     * @return
     */
    private ImpactKafkaParameterDTO getImpactKafkaParameterDTO(String key, ImpactParameterDTO parameterDTO) {
        if (parameterDTO.getPalettes() != null) {
            //包含刀線列表
            List<String> includes = new ArrayList<>();

            //排除刀線列表
            List<String> excludes = new ArrayList<>();

            parameterDTO.getPalettes().stream().forEach(
                    p -> {

                        //兼容V1版本Palette的All配置，新版本All傳空
                        if (appSettings.getImpact().getRequest().getIncludePalettes().containsKey(p)) {
                            List<String> includesPalettes = appSettings.getImpact().getRequest().getIncludePalettes().get(p);
                            if (includesPalettes == null || includesPalettes.isEmpty()) {
                                includes.clear();
                            } else {
                                includes.addAll(includesPalettes);
                            }
                        }
                        //兼容V1版本Palette的Other配置
                        else if (appSettings.getImpact().getRequest().getExcludePalettes().containsKey(p)) {
                            List<String> excludesPalettes = appSettings.getImpact().getRequest().getExcludePalettes().get(p);
                            if (excludesPalettes == null || excludesPalettes.isEmpty()) {
                                excludes.clear();
                            } else {
                                excludes.addAll(excludesPalettes);
                            }
                        }
                        else {
                            includes.add(p);
                        }
                    }
            );

            parameterDTO.setPalettes(includes);
            parameterDTO.setExcludePalettes(excludes);
        }
        ImpactKafkaParameterDTO kafkaParameterDTO = new ImpactKafkaParameterDTO();

        kafkaParameterDTO.setKey(key);
        kafkaParameterDTO.setSource(parameterDTO.getSource());

        configureNotifyUrl(kafkaParameterDTO, key);

        kafkaParameterDTO.setFormat(ReturnFileFormat.of(parameterDTO.getFormat()).value());

        if (kafkaParameterDTO.getFormat() == ReturnFileFormat.Svg.value()) {
            kafkaParameterDTO.setDpi(parameterDTO.getDpi());
        }

        kafkaParameterDTO.setStandard(parameterDTO.getStandard());
        kafkaParameterDTO.setPalettes(parameterDTO.getPalettes());
        kafkaParameterDTO.setExcludePalettes(parameterDTO.getExcludePalettes());
        kafkaParameterDTO.setVariables(parameterDTO.getVariables());
        kafkaParameterDTO.setMasterToolSettings(parameterDTO.getMasterToolSettings());

        return kafkaParameterDTO;
    }

    private void configureNotifyUrl(ImpactKafkaParameterDTO impactKafkaParameterDTO, String notifyKey) {
        String notifyUrl = null;

        ApplicationMode mode = applicationConfigService.getApplicationMode();
        switch (mode) {
            case Stage:
                if (StringUtils.isNotBlank(appSettings.getImpact().getKafka().getStageNotifyUrl())) {
                    notifyUrl = appSettings.getImpact().getKafka().getStageNotifyUrl();
                }
                break;
            case Production:
                if (StringUtils.isNotBlank(appSettings.getImpact().getKafka().getProductionNotifyUrl())) {
                    notifyUrl = appSettings.getImpact().getKafka().getProductionNotifyUrl();
                }
                break;
        }

        // fallback defaultNotifyUrl
        if (StringUtils.isBlank(notifyUrl)) {
            notifyUrl = appSettings.getImpact().getKafka().getDefaultNotifyUrl();
        }

        if (StringUtils.isNotBlank(notifyUrl)) {
            notifyUrl = uriTemplateHandler.expand(notifyUrl, notifyKey).toString();
            impactKafkaParameterDTO.setNotifyUrl(notifyUrl);
        }
    }

}
