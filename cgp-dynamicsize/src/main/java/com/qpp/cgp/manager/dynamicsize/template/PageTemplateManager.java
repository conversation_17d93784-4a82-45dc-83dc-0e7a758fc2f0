package com.qpp.cgp.manager.dynamicsize.template;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Charsets;
import com.google.common.collect.ImmutableMap;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.qpp.cgp.configuration.dynamicsize.DynamicSizeProperties;
import com.qpp.cgp.domain.dynamicsize.configuration.*;
import com.qpp.cgp.domain.product.SkuProduct;
import com.qpp.cgp.manager.dynamicsize.common.DsDataSourceObjectGenerator;
import com.qpp.cgp.manager.dynamicsize.configuration.DsDataSourceManager;
import com.qpp.cgp.manager.dynamicsize.configuration.DsTemplateGenerateConfigManager;
import com.qpp.cgp.manager.dynamicsize.configuration.PageTemplateConfigManager;
import com.qpp.cgp.manager.dynamicsize.configuration.UrlTemplateManager;
import com.qpp.cgp.manager.dynamicsize.handler.FreemarkerTemplateHandler;
import com.qpp.cgp.model.dynamicsize.page.ProductPageTemplateDTO;
import com.qpp.cgp.repository.product.SkuProductRepository;
import com.qpp.cgp.utils.ObjectMapperBeanCreateUtils;
import com.qpp.service.script.NashornScriptService;
import com.qpp.service.script.ScriptService;
import com.qpp.core.dto.ObjectDTO;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import freemarker.template.TemplateException;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.Optional;

/**
 * Created by admin on 2017/8/9.
 */

@Service
public class PageTemplateManager {

    @Autowired
    SkuProductRepository skuProductRepository;

    @Autowired
    PageTemplateConfigManager ptcManager;

    @Autowired
    DsDataSourceManager dsManager;

    DsDataSourceObjectGenerator dsoGenerator;

    @Autowired
    DynamicSizeProperties appSetting;

    @Autowired
    private DsTemplateGenerateConfigManager dsTemplateGenerateConfigManager;

    @Autowired
    private UrlTemplateManager utManager;

    ScriptService jsService = new NashornScriptService();

    Log logger = LogFactory.getLog(this.getClass());

    ObjectMapper mapper = ObjectMapperBeanCreateUtils.getObjectMapper();


    /**
     * 構造函數，初始化DsDataSourceObjectGenerator對象
     * @param objectGenerator
     */
    public PageTemplateManager(DsDataSourceObjectGenerator objectGenerator) {
        this.dsoGenerator = objectGenerator;
    }

    /**
     * 根據Page類型、SKUID，獲取相應的Page
     *
     * @param pageType 頁類型
     * @param skuId    SKU編號
     * @return
     */
    public String generatePage(Long skuId, String pageType) throws IOException, TemplateException {

        logger.debug(String.format("get page from sku: %s, page type: %s", skuId, pageType));

        //獲取產品信息
        Optional<SkuProduct> sku = skuProductRepository.findById(skuId);

        if (!sku.isPresent())
            throw BusinessExceptionBuilder.of(2700001, ImmutableMap.of("productId", skuId));

        logger.debug(String.format("get sku complete, sku: %s", skuId));

        //過濾數據，生成源對象
        String pData = this.mapper.writeValueAsString(sku.get());

        ObjectDTO data = this.mapper.readValue(pData, ObjectDTO.class);

        return this.generatePage(pageType, data);
    }

    /**
     * @param pageType
     * @param data
     * @return
     * @throws IOException
     */
    private String generatePage(String pageType, ObjectDTO data) throws IOException, TemplateException {

        //獲取PageType信息
        Optional<PageTemplateConfig> pageConfig = ptcManager.findOneByPageType(pageType);

        if (!pageConfig.isPresent())
            throw BusinessExceptionBuilder.of(2700201, ImmutableMap.of("pageType", pageType));

        return doGeneratePage(pageConfig.get(), data);
    }

    /**
     * @param pageTemplateConfigId
     * @param data
     * @return
     * @throws IOException
     * @throws TemplateException
     */
    private String generatePage(long pageTemplateConfigId, ObjectDTO data) throws IOException, TemplateException {

        //獲取PageType信息
        Optional<PageTemplateConfig> pageConfig = ptcManager.getOne(pageTemplateConfigId);

        if (!pageConfig.isPresent())
            throw BusinessExceptionBuilder.of(2700211, ImmutableMap.of("pageTemplateConfigId", pageTemplateConfigId));

        return doGeneratePage(pageConfig.get(), data);
    }

    private String doGeneratePage(PageTemplateConfig pageConfig, ObjectDTO data) throws IOException, TemplateException {

        String pageType = pageConfig.getPageType();

        //獲取數據源
        Long dataSourceId = pageConfig.getDataSourceId();
        Optional<ObjectDTO> dataSourceObject = dsoGenerator.getDataSourceObject(dataSourceId, data);

        if (!dataSourceObject.isPresent())
            throw BusinessExceptionBuilder.of(2700002, ImmutableMap.of("dataSourceId", dataSourceId));

        dataSourceObject.get().put("runtimeContext", new HashMap<String, Object>() {{
            put("data", data);
            put("pageType", pageType);
        }});

        //獲取Paget模板
        Optional<String> pageTemplate = Optional.ofNullable(FileUtils.readFileToString(new File(appSetting.getPageTemplatesFolder() + pageConfig.getTemplateFileName()), Charsets.UTF_8));

        if (!pageTemplate.isPresent() || pageTemplate.get().isEmpty())
            throw BusinessExceptionBuilder.of(2700202, ImmutableMap.of("pageType", pageType, "templateFileName", pageConfig.getTemplateFileName()));

        //臨時處理，V1&V2 Freemarker默認配置
        Optional<DsDataSource> datasource = dsManager.getOne(dataSourceId);

        String result = FreemarkerTemplateHandler.execute(pageConfig.getTemplateFileName(), pageTemplate.get(), FreemarkerTemplateHandler.getDecimalConfiguration(), dataSourceObject.get());

        //統一輸出格式，XML類型的Base64，Josn直接輸出
        if (datasource.get().getType().isEmpty() || datasource.get().getType().toLowerCase().equals("impactsvg")) {
            return Base64.getEncoder().encodeToString(result.getBytes()).replace("\r", "").replace("\n", "");
        } else if (datasource.get().getType().toLowerCase().equals("impactpdf")) {
            return result;
        } else {
            throw BusinessExceptionBuilder.of(2700022, ImmutableMap.of("id", dataSourceId, "type", datasource.get().getType()));
        }

    }

    private ProductPageTemplateDTO generatePageDTO(long pageTemplateConfigId, ObjectDTO data) throws IOException, TemplateException {
        //獲取PageType信息
        Optional<PageTemplateConfig> pageConfig = ptcManager.getOne(pageTemplateConfigId);

        if (!pageConfig.isPresent())
            throw BusinessExceptionBuilder.of(2700211, ImmutableMap.of("pageTemplateConfigId", pageTemplateConfigId));

        return doGeneratePageDTO(pageConfig.get(), data);
    }

    private ProductPageTemplateDTO doGeneratePageDTO(PageTemplateConfig pageConfig, ObjectDTO data) throws IOException, TemplateException {

        ProductPageTemplateDTO dto = new ProductPageTemplateDTO();

        /* content */

        String pageType = pageConfig.getPageType();

        //獲取數據源(调用Impact服务)
        Long dataSourceId = pageConfig.getDataSourceId();
        Optional<ObjectDTO> dataSourceObject = dsoGenerator.getDataSourceObject(dataSourceId, data);

        if (!dataSourceObject.isPresent())
            throw BusinessExceptionBuilder.of(2700002, ImmutableMap.of("dataSourceId", dataSourceId));

        dataSourceObject.get().put("runtimeContext", new HashMap<String, Object>() {{
            put("data", data);
            put("pageType", pageType);
        }});

        //獲取Page模板
        int version = dataSourceObject.get().containsKey("dsVersion") ? (int) dataSourceObject.get().get("dsVersion") : 1;
        String templateFileName;
        if (version > 1 && StringUtils.isNotBlank(pageConfig.getTemplateFileNameV2())) {
            templateFileName = pageConfig.getTemplateFileNameV2();
        } else {
            templateFileName = pageConfig.getTemplateFileName();
        }
        Optional<String> pageTemplate = Optional.ofNullable(FileUtils.readFileToString(new File(appSetting.getPageTemplatesFolder() + templateFileName), Charsets.UTF_8));

        if (!pageTemplate.isPresent() || pageTemplate.get().isEmpty())
            throw BusinessExceptionBuilder.of(2700202, ImmutableMap.of("pageType", pageType, "templateFileName", pageConfig.getTemplateFileName()));

        //臨時處理，V1&V2 Freemarker默認配置
        Optional<DsDataSource> datasource = dsManager.getOne(dataSourceId);

        String content = FreemarkerTemplateHandler.execute(pageConfig.getTemplateFileName(), pageTemplate.get(), FreemarkerTemplateHandler.getDecimalConfiguration(), dataSourceObject.get());

        //統一輸出格式，XML類型的Base64，Josn直接輸出
        if (datasource.get().getType().isEmpty() || datasource.get().getType().toLowerCase().equals("impactsvg")) {
            dto.setContent(
                    Base64.getEncoder().encodeToString(content.getBytes()).replace("\r", "").replace("\n", "")
            );
        } else if (datasource.get().getType().toLowerCase().equals("impactpdf")) {
            dto.setContent(
                    content
            );
        } else {
            throw BusinessExceptionBuilder.of(2700022, ImmutableMap.of("id", dataSourceId, "type", datasource.get().getType()));
        }

        /* placeHolder */

        if (pageConfig.getImpressionPlaceholders() != null && !pageConfig.getImpressionPlaceholders().isEmpty()) {
            dto.setPlaceHolders(pageConfig.getImpressionPlaceholders());
            if (pageConfig.getPlaceholders() != null && pageConfig.getPlaceholders().size() > 0) {

                String result = this.mapper.writeValueAsString(dto);

                for (TemplatePlaceholder placeholder : pageConfig.getPlaceholders()) {

                    Object value = jsService.evalJavascriptFunction(placeholder.getExpression(), "expression", Object.class, dataSourceObject.get());

                    if (placeholder.getAttributes() != null && placeholder.getAttributes().size() > 0) {
                        if (placeholder.getSelector() == null || placeholder.getSelector().isEmpty()) {
                            for (String attr : placeholder.getAttributes()) {
                                DocumentContext context = JsonPath.parse(result).set(attr, value);
                                result = context.jsonString();
                            }
                        } else {
                            for (String attr : placeholder.getAttributes()) {
                                DocumentContext context = JsonPath.parse(result).put(placeholder.getSelector(), attr, value);
                                result = context.jsonString();
                            }
                        }
                    }
                }

                dto = this.mapper.readValue(result, ProductPageTemplateDTO.class);
            }
        }

        return dto;
    }

    /**
     * 通過頁類開和訂單數據生成產品
     *
     * @param pageType
     * @param orderJson
     * @return
     */
    public String generatePageTemplateFromOrder(String pageType, String orderJson) throws IOException, TemplateException {

        ObjectDTO data = this.mapper.readValue(orderJson, ObjectDTO.class);

        return this.generatePage(pageType, data);
    }

    /**
     * 通過 Page 模板配置 id 和訂單數據生成產品
     *
     * @param pageTemplateConfigId
     * @param orderJson
     * @return
     */
    public String generatePageTemplateFromOrder(long pageTemplateConfigId, String orderJson) throws IOException, TemplateException {

        ObjectDTO data = this.mapper.readValue(orderJson, ObjectDTO.class);

        return this.generatePage(pageTemplateConfigId, data);
    }

    public ProductPageTemplateDTO generatePageTemplateDTOFromOrder(long pageTemplateConfigId, String orderJson) throws IOException, TemplateException {

        ObjectDTO data = this.mapper.readValue(orderJson, ObjectDTO.class);

        return this.generatePageDTO(pageTemplateConfigId, data);
    }

}
