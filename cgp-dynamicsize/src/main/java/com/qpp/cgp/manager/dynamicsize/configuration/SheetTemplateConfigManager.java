package com.qpp.cgp.manager.dynamicsize.configuration;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.dynamicsize.configuration.SheetTemplateConfig;
import com.qpp.cgp.manager.dynamicsize.common.AbstractDocumentCurdManager;
import com.qpp.cgp.manager.dynamicsize.common.IDataOperationManager;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.driver.HybridMongoTemplate;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.bson.conversions.Bson;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by admin on 2017/8/14.
 */
@Service
public class SheetTemplateConfigManager extends AbstractDocumentCurdManager<Long> implements IDataOperationManager<Long> {

    @Autowired
    DsDataSourceManager dataSourceManager;

    ObjectMapper mapper;

    public ObjectMapper getMapper() {
        if (mapper == null) {
            mapper = new ObjectMapper();
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        }
        return mapper;
    }

    @Autowired
    public SheetTemplateConfigManager(HybridMongoTemplate mongoTemplate, IdGenerator idGenerator) {
        super("dssheettemplateconfigs", mongoTemplate, idGenerator);
    }

    /**
     * 根據排版產品類型及sheet類型獲取模板
     *
     * @param productType
     * @param sheetType
     * @return
     * @throws IOException
     */
    public List<SheetTemplateConfig> findByProductTypeAndSheetType(String productType, String sheetType) throws IOException {

        Bson filter = Document.parse(String.format("{ \"$and\":[{ \"productType\": \"%s\" },{ \"sheetType\": \"%s\" }]}", productType, sheetType));

        Bson sort = Document.parse(String.format("{\"index\":1}"));

        Iterable<Document> sheetTemplates = mongoTemplate.query("dssheettemplateconfigs", filter, sort);

        if (sheetTemplates == null)
            return null;

        List<SheetTemplateConfig> results = new ArrayList<>();

        if (sheetTemplates != null) {
            sheetTemplates.forEach(sheet -> {
                try {
                    results.add(this.getMapper().readValue(this.getMapper().writeValueAsString(sheet), SheetTemplateConfig.class));
                } catch (IOException e) {
                    throw BusinessExceptionBuilder.of(2700205, ImmutableMap.of("productType", productType, "sheetType", sheetType));
                }
            });
        }

        return results;
    }

    public Optional<SheetTemplateConfig> getById(long id) {
        return Optional.ofNullable(mongoTemplate.findOne(Query.query(Criteria.where("_id").is(id)), SheetTemplateConfig.class));
    }

    /**
     * 複製Sheet模板配置及其相關聯的DataSource
     *
     * @param id
     * @return
     */
    @Override
    public Document copy(Long id) {
        Document data = this.findById(id);

        if (data == null) {
            throw BusinessExceptionBuilder.of(2700023, ImmutableMap.of("id", id, "entity", "dssheettemplateconfigs"));
        }

        this.addAudit(data);
        data.put("_id", this.generateId());

        if (data.containsKey("dataSourceId")) {
            Long sourceId = Long.parseLong(data.get("dataSourceId").toString());

            Document dataSource = dataSourceManager.copy(sourceId);
            data.put("dataSourceId", dataSource.getLong("_id"));
        }

        mongoCollection.insertOne(data);
        return data;
    }

    public List<Long> getDataSourceIdsByIds(List<Long> ids) {
        return ids.stream()
                .map(this::getDataSourceIdById)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());
    }

    public Optional<Long> getDataSourceIdById(Long id) {
        Query query = Query.query(Criteria.where("_id").is(id));
        query.fields().include("dataSourceId","clazz");
        SheetTemplateConfig sheetTemplateConfig = mongoTemplate.findOne(query, SheetTemplateConfig.class);
        if (null == sheetTemplateConfig) {
            throw BusinessExceptionBuilder.of(15600001, ImmutableMap.of("sheetTemplateConfigId", id));
        }

        return Optional.ofNullable(sheetTemplateConfig.getDataSourceId());
    }


    public List<SheetTemplateConfig> findSheetTemplateConfigByIds(List<Long> dsSheetTemplateConfigIds) {
        return mongoTemplate.find(Query.query(Criteria.where("_id").in(dsSheetTemplateConfigIds)), SheetTemplateConfig.class);
    }

    public void saveAll(List<SheetTemplateConfig> sheetTemplateConfigs) {
        mongoTemplate.insertAll(sheetTemplateConfigs);
    }
}
