package com.qpp.cgp.manager.dynamicsize.configuration.v2;

import com.qpp.cgp.config.mongo.MongoTemplateBeanNames;
import com.qpp.cgp.domain.dynamicsize.configuration.v2.SheetTemplateConfigV2;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.driver.HybridMongoTemplate;
import com.qpp.web.business.manager.AbstractLongMongoCurdManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Package com.qpp.cgp.manager.dynamicsize.configuration.v2
 * @Date 2025/1/11 15:58
 */
@Service
public class SheetTemplateConfigV2Manager extends AbstractLongMongoCurdManager<SheetTemplateConfigV2, Long> {

    @Autowired
    public SheetTemplateConfigV2Manager(@Qualifier(value = MongoTemplateBeanNames.CONFIG) HybridMongoTemplate mongoTemplate,
                                        IdGenerator idgenerator) {
        super(mongoTemplate, idgenerator);
    }

    public boolean existsId(Long templateId) {
        return mongoTemplate.exists(Query.query(Criteria.where("_id").is(templateId)), SheetTemplateConfigV2.class);
    }

}
