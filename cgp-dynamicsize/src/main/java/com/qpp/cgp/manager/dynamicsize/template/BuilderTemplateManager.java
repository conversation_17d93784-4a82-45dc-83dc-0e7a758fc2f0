package com.qpp.cgp.manager.dynamicsize.template;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Charsets;
import com.google.common.collect.ImmutableMap;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.qpp.cgp.configuration.dynamicsize.DynamicSizeProperties;
import com.qpp.cgp.manager.dynamicsize.common.DsDataSourceObjectGenerator;
import com.qpp.cgp.model.dynamicsize.exception.ImpactWaitForGeneratingException;
import com.qpp.cgp.utils.ObjectMapperBeanCreateUtils;
import com.qpp.id.generator.IdGenerator;
import com.qpp.cgp.domain.product.SkuProduct;
import com.qpp.cgp.domain.dynamicsize.bom.BomLibrary;
import com.qpp.cgp.domain.dynamicsize.configuration.BuilderTemplateConfig;
import com.qpp.cgp.domain.dynamicsize.configuration.TemplatePlaceholder;
import com.qpp.cgp.manager.dynamicsize.bom.MaterialViewTypeManager;
import com.qpp.cgp.manager.dynamicsize.bom.PageContentSchemaManager;
import com.qpp.cgp.manager.dynamicsize.configuration.BuilderTemplateConfigManager;
import com.qpp.cgp.manager.dynamicsize.configuration.DsDataSourceManager;
import com.qpp.cgp.model.dynamicsize.builder.ProductBuilderConfig;
import com.qpp.cgp.repository.product.SkuProductRepository;
import com.qpp.service.script.NashornScriptService;
import com.qpp.service.script.ScriptService;
import com.qpp.core.dto.ObjectDTO;
import com.qpp.core.exception.BusinessException;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.apache.commons.io.FileUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;


import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * Created by admin on 2017/8/9.
 */

@Service
public class BuilderTemplateManager {

    @Autowired
    BuilderTemplateConfigManager btcManager;

    @Autowired
    DsDataSourceManager dsManager;

    DsDataSourceObjectGenerator dsoGenerator;

    @Autowired
    DynamicSizeProperties appSetting;

    @Autowired
    SkuProductRepository skuProductRepository;

    @Autowired
    @Qualifier("dynamicPageContentSchemaManager")
    PageContentSchemaManager pcsManager;

    @Autowired
    @Qualifier("dynamicMaterialViewTypeManager")
    MaterialViewTypeManager mvtManager;

    @Autowired
    IdGenerator idService;

    ScriptService jsService = new NashornScriptService();

    Log logger = LogFactory.getLog(this.getClass());

    ObjectMapper mapper = ObjectMapperBeanCreateUtils.getObjectMapper();;

    /**
     * 構造函數，初始化DsDataSourceObjectGenerator對象
     * @param objectGenerator
     */
    public BuilderTemplateManager(DsDataSourceObjectGenerator objectGenerator) {
        this.dsoGenerator = objectGenerator;
    }

    /**
     * 生成Builder配置（V1版）
     *
     * @param productId
     * @param attributes
     * @return
     * @throws IOException
     */
    public ProductBuilderConfig generateProductBuilderTemplateV1(String productId, Map<String, Object> attributes) throws IOException {
        logger.info(String.format("start to read product builder template, Product Id : %s , Attributes : %s", productId, this.mapper.writeValueAsString(attributes)));

        // 獲取產品Builder配置信息
        Optional<BuilderTemplateConfig> builderConfig = btcManager.findOneByProductId(productId);

        if (!builderConfig.isPresent())
            throw BusinessExceptionBuilder.of(2700101, ImmutableMap.of("productId", productId));

        ObjectDTO data = new ObjectDTO();

        data.copyFromMap(attributes);

        // 生成Builder配置數據
        return this.getBuilderTemplateV1(builderConfig.get(), data);
    }

    /**
     * 生成Builder配置（V1版）
     *
     * @param builderConfig
     * @param data
     * @return
     */
    protected ProductBuilderConfig getBuilderTemplateV1(BuilderTemplateConfig builderConfig, ObjectDTO data) throws IOException {

        Optional<ObjectDTO> dataSourceObject = dsoGenerator.getDataSourceObject(builderConfig.getDataSourceId(), data);

        if (!dataSourceObject.isPresent())
            throw BusinessExceptionBuilder.of(2700102, ImmutableMap.of("productId", builderConfig.getProductId(),"dataSourceId", builderConfig.getDataSourceId()));

        //獲取Builder模板
        Optional<String> builderTemplate = Optional.ofNullable(FileUtils.readFileToString(new File(appSetting.getBuilderTemplatesFolder() + builderConfig.getTemplateFileName()), Charsets.UTF_8));

        if (!builderTemplate.isPresent() || builderTemplate.get().isEmpty())
            throw BusinessExceptionBuilder.of(2700103, ImmutableMap.of("productId", builderConfig.getProductId(),"templateFileName", builderConfig.getTemplateFileName()));

        Optional<String> builderJson = Optional.of(builderTemplate.get());

        //替換配置數據
        for (TemplatePlaceholder placeholder : builderConfig.getPlaceholders()) {

            Object value = jsService.evalJavascriptFunction(placeholder.getExpression(), "expression", Object.class, dataSourceObject.get());

            if (placeholder.getAttributes() != null && placeholder.getAttributes().size() > 0) {
                if (placeholder.getSelector() == null || placeholder.getSelector().isEmpty()) {
                    for (String attr : placeholder.getAttributes()) {
                        DocumentContext context = JsonPath.parse(builderJson.get()).set(attr, value);
                        builderJson = Optional.of(context.jsonString());
                    }
                } else {
                    for (String attr : placeholder.getAttributes()) {
                        DocumentContext context = JsonPath.parse(builderJson.get()).put(placeholder.getSelector(), attr, value);
                        builderJson = Optional.of(context.jsonString());
                    }
                }
            }
        }

        if (!builderJson.isPresent() || builderJson.get().isEmpty())
            throw BusinessExceptionBuilder.of(2700104, ImmutableMap.of("productId", builderConfig.getProductId(),"templateFileName", builderConfig.getTemplateFileName()));

        //返回Builder配置對象
        ProductBuilderConfig pbConfig = new ProductBuilderConfig();
        pbConfig.setId(builderConfig.getProductId());
        pbConfig.setImageDpi(builderConfig.getDpi());
        Number imageWidth = jsService.evalJavascriptFunction(builderConfig.getWidthExpression(), "expression", Number.class, dataSourceObject.get());
        pbConfig.setImageWidth(imageWidth.intValue());
        Number imageHeight = jsService.evalJavascriptFunction(builderConfig.getHeightExpression(), "expression", Number.class, dataSourceObject.get());
        pbConfig.setImageHeight(imageHeight.intValue());
        pbConfig.setBuilderConfig(builderJson.get());

        return pbConfig;
    }


    /**
     * 生成動態尺寸的產品物料ViewType(CGP2結構)
     *
     * @param product SKU產品信息
     * @param productMaterialViewTypeTemplate 產品物料ViewType
     * @return
     */
    public Document generateProductMaterialViewType(SkuProduct product,Document productMaterialViewTypeTemplate) throws IOException {

        if (product == null){
            throw BusinessExceptionBuilder.of(200070, ImmutableMap.of("paramName", "product"));
        }

        if (productMaterialViewTypeTemplate == null){
            throw BusinessExceptionBuilder.of(200070, ImmutableMap.of("paramName", "productMaterialViewTypeTemplate"));
        }

        logger.debug("start to generate ProductMaterialViewType completed");

        //獲取materialViewType
        String mvtId = JsonPath.read(productMaterialViewTypeTemplate, "$.materialViewType._id");

        if (mvtId == null || mvtId.isEmpty())
            throw BusinessExceptionBuilder.of(2700105, ImmutableMap.of("productId", product.getId(),"template", productMaterialViewTypeTemplate.toJson()));

        logger.debug(String.format("start to get materialViewType %s",mvtId));

        net.minidev.json.JSONArray materialViewTypes = JsonPath.read(productMaterialViewTypeTemplate.toJson(), String.format("$.libraries.materialViewTypes[?(@._id=='%s')]", mvtId));

        if (materialViewTypes == null || materialViewTypes.isEmpty())
            throw BusinessExceptionBuilder.of(2700106, ImmutableMap.of("materialViewTypeId", mvtId));

        Document materialViewType = Document.parse(this.mapper.writeValueAsString(materialViewTypes.get(0)));

        if (materialViewType == null || materialViewType.isEmpty())
            throw BusinessExceptionBuilder.of(2700106, ImmutableMap.of("materialViewTypeId", mvtId));

        //獲取pageContentSchema
        String pcsId = JsonPath.read(materialViewType, "$.pageContentSchema._id");

        if (pcsId == null || pcsId.isEmpty())
            throw BusinessExceptionBuilder.of(2700107, ImmutableMap.of("materialViewTypeId", mvtId));

        logger.debug(String.format("start to get pageContentSchema %s",pcsId));

        net.minidev.json.JSONArray pageContentSchemas = JsonPath.read(productMaterialViewTypeTemplate.toJson(), String.format("$.libraries.pageContentSchemas[?(@._id=='%s')]", pcsId));

        if (pageContentSchemas == null || pageContentSchemas.isEmpty())
            throw BusinessExceptionBuilder.of(2700108, ImmutableMap.of("pageContentSchemaId", pcsId));

        Document pageContentSchema = Document.parse(this.mapper.writeValueAsString(pageContentSchemas.get(0)));

        if (pageContentSchema == null || pageContentSchema.isEmpty())
            throw BusinessExceptionBuilder.of(2700108, ImmutableMap.of("pageContentSchemaId", pcsId));

        //獲取占位符
        if (!materialViewType.containsKey("pcsPlaceholders"))
            throw BusinessExceptionBuilder.of(2700109, ImmutableMap.of("materialViewTypeId", mvtId));

        logger.debug(String.format("start to get pcsPlaceholders of MaterialViewType %s",mvtId));

        net.minidev.json.JSONArray pcsPlaceholders = JsonPath.read(materialViewType.toJson(), "$.pcsPlaceholders");

        if (pcsPlaceholders == null || pcsPlaceholders.isEmpty())
            return productMaterialViewTypeTemplate;

        List<TemplatePlaceholder> placeholders = new ArrayList<>();

        for (Object pcs : pcsPlaceholders) {
            placeholders.add(this.mapper.readValue(this.mapper.writeValueAsString(pcs), TemplatePlaceholder.class));
        }

        //獲取dsDataSource
        if (!materialViewType.containsKey("dsDataSource"))
            throw BusinessExceptionBuilder.of(2700110, ImmutableMap.of("materialViewTypeId", mvtId));

        Object dsId = JsonPath.read(materialViewType.toJson(), "$.dsDataSource._id");

        Long dataSourceId = this.mapper.readValue(dsId.toString(), Long.class);

        logger.debug(String.format("start to generate runtime DataSourceObject %s",dataSourceId));

        //生成運行時數據對象
        ObjectDTO runtimeData = this.mapper.readValue(this.mapper.writeValueAsString(product), ObjectDTO.class);

        Optional<ObjectDTO> dataSourceObject = dsoGenerator.getDataSourceObject(dataSourceId, runtimeData);

        if (!dataSourceObject.isPresent())
            throw BusinessExceptionBuilder.of(2700002, ImmutableMap.of("dataSourceId", dataSourceId));

        logger.debug(String.format("generate runtime DataSourceObject %s completed",dataSourceId));

        //生成PageContentSchema
        logger.debug(String.format("start to generate new PageContentSchema %s",pcsId));

        String pcsTemplate = pageContentSchema.toJson();

        Optional<String> runtimePageContentSchema = this.generatePageContentSchema(dataSourceObject.get(), pcsTemplate, placeholders, runtimeData);

        if (!runtimePageContentSchema.isPresent())
            throw BusinessExceptionBuilder.of(2700112, ImmutableMap.of("materialViewTypeId", mvtId));

        logger.debug(String.format("generate new PageContentSchema %s completed",pcsId));

        //處理返回值
        Document newLibraries = Document.parse(this.mapper.writeValueAsString(productMaterialViewTypeTemplate.get("libraries")));

        //PageContentSchema
        List<Document> newPageContentSchemas = new ArrayList<Document>();
        Document pcsDocument = Document.parse(runtimePageContentSchema.get());
        String newPcsId = idService.generateId().toString();
        pcsDocument.put("_id", newPcsId);
        newPageContentSchemas.add(pcsDocument);
        newLibraries.put("pageContentSchemas", newPageContentSchemas);

        //MaterialViewType
        List<Document> newMaterialViewTypes = new ArrayList<Document>();
        String newMvtId = idService.generateId().toString();
        materialViewType.put("_id", newMvtId);
        materialViewType.get("pageContentSchema", Document.class).put("_id", newPcsId);
        materialViewType.remove("dsDataSource");
        materialViewType.remove("pcsPlaceholders");
        newMaterialViewTypes.add(materialViewType);
        newLibraries.put("materialViewTypes", newMaterialViewTypes);

        productMaterialViewTypeTemplate.put("libraries", newLibraries);

        //ProductMaterialViewType
        String newPmvtId = idService.generateId().toString();
        productMaterialViewTypeTemplate.get("materialViewType", Document.class).put("_id", newMvtId);
        productMaterialViewTypeTemplate.put("_id", newPmvtId);

        logger.debug("generate ProductMaterialViewType completed");

        return productMaterialViewTypeTemplate;
    }


    /**
     * 生成PageContentSchema列表(CGP1結構)
     *
     * @param productId sku product id
     * @param pmvtJson  ProductMatelViewType的JSON數據
     * @return
     */
    public BomLibrary generateBomLibrary(Long productId, String pmvtJson) throws IOException {

        //驗證數據
        Optional<SkuProduct> product = skuProductRepository.findById(productId);

        if (!product.isPresent())
            throw BusinessExceptionBuilder.of(2700001, ImmutableMap.of("productId", productId));

        List<ObjectDTO> pmvts = this.mapper.readValue(pmvtJson, this.mapper.getTypeFactory().constructParametricType(List.class, ObjectDTO.class));

        if (pmvts == null || pmvts.size() <= 0)
            throw BusinessExceptionBuilder.of(2700114);

        //过滤不符合條件的ProductMaterialViewType，并將符合條件的生成PageContentSchema
        BomLibrary result = new BomLibrary();

        //是否需要重試
        boolean isRetry = false;

        for (ObjectDTO productMaterialView : pmvts) {

            String condition = productMaterialView.getString("conditionExpression");

            Boolean pass = false;

            if (condition == null || condition.isEmpty())
                pass = true;
            else
                pass = jsService.evalJavascriptFunction(condition, "expression", Boolean.class, product.get());

            if (pass) {

                //獲取PageContentSchema模板
                ObjectDTO materialView = new ObjectDTO();

                if (!productMaterialView.containsKey("materialViewType"))
                    throw BusinessExceptionBuilder.of(2700115, ImmutableMap.of("productMaterialViewTypeId", this.getIdFromMap(productMaterialView)));

                if (!productMaterialView.getMap("materialViewType").containsKey("pageContentSchema")) {
                    Object mvtId = this.getIdFromMap(productMaterialView.getMap("materialViewType"));

                    if (mvtId == null)
                        throw BusinessExceptionBuilder.of(2700116, ImmutableMap.of("materialViewTypeId", mvtId));

                    Optional<ObjectDTO> mvtObj = mvtManager.getJsonById((String) mvtId);

                    if (!mvtObj.isPresent())
                        throw BusinessExceptionBuilder.of(2700116, ImmutableMap.of("materialViewTypeId", mvtId));

                    materialView.copyFromMap(mvtObj.get());
                } else {

                    materialView.copyFromMap(productMaterialView.getMap("materialViewType"));
                }

                //獲取pageContentSchema模板
                Optional<String> pcsTemplate = Optional.empty();

                if (!materialView.containsKey("pageContentSchema")) {
                    throw BusinessExceptionBuilder.of(2700117, ImmutableMap.of("materialViewTypeId", this.getIdFromMap(materialView)));
                }

                if (materialView.getMap("pageContentSchema").containsKey("width")) {
                    pcsTemplate = Optional.of(this.mapper.writeValueAsString(materialView.getMap("pageContentSchema")));
                } else {
                    String pcsId = (String) this.getIdFromMap(materialView.getMap("pageContentSchema"));
                    pcsTemplate = pcsManager.getJsonById(pcsId);
                }

                //獲取占位符
                List<Object> pcs = materialView.getArray("pcsPlaceholders");

                if (pcs == null || pcs.size() <= 0) {

                    //返回值處理
                    result.addProductMaterialViewType(productMaterialView);
                    ObjectDTO pcsDTO = this.mapper.readValue(pcsTemplate.get(), ObjectDTO.class);
                    result.addPageContentSchema(pcsDTO);
                    result.addMaterialViewType(materialView);
                    continue;
                }

                List<TemplatePlaceholder> placeholders = new ArrayList<>();

                for (Object o : pcs) {
                    if (o instanceof TemplatePlaceholder) {
                        placeholders.add((TemplatePlaceholder) o);
                    } else {
                        placeholders.add(this.mapper.readValue(this.mapper.writeValueAsString(o), TemplatePlaceholder.class));
                    }
                }

                if (placeholders == null || placeholders.size() <= 0) {
                    //返回值處理
                    result.addProductMaterialViewType(productMaterialView);
                    ObjectDTO pcsDTO = this.mapper.readValue(pcsTemplate.get(), ObjectDTO.class);
                    result.addPageContentSchema(pcsDTO);
                    result.addMaterialViewType(materialView);
                    continue;
                }

                //生成運行時數據對象
                Long dataSourceId = Long.parseLong((String) this.getIdFromMap(materialView.getMap("dsDataSource")));

                if (dataSourceId == null)
                    throw BusinessExceptionBuilder.of(2700118, ImmutableMap.of("materialViewId", this.getIdFromObject(materialView)));

                ObjectDTO data = this.mapper.readValue(this.mapper.writeValueAsString(product.get()), ObjectDTO.class);

                try {
                    Optional<ObjectDTO> dataSourceObject = dsoGenerator.getDataSourceObject(dataSourceId, data);

                    if (!dataSourceObject.isPresent())
                        throw BusinessExceptionBuilder.of(2700119, ImmutableMap.of("dataSourceId", dataSourceId));

                    //生成PageContentSchema
                    Optional<String> pageContentSchema = this.generatePageContentSchema(dataSourceObject.get(), pcsTemplate.get(), placeholders, data);

                    if (!pageContentSchema.isPresent())
                        throw BusinessExceptionBuilder.of(2700120, ImmutableMap.of("productMaterialViewId", this.getIdFromObject(productMaterialView)));

                    //返回值處理
                    result.addProductMaterialViewType(productMaterialView);
                    ObjectDTO pcsDTO = this.mapper.readValue(pageContentSchema.get(), ObjectDTO.class);
                    result.addPageContentSchema(pcsDTO);
                    result.addMaterialViewType(materialView);
                } catch (ImpactWaitForGeneratingException e) {
                    isRetry = true;
                    continue;
                }
            }
        }

        //有刀線還沒生成成功，需要重試
        if (isRetry) {
            throw new ImpactWaitForGeneratingException();
        }

        return result;
    }

    /**
     * 保存新的物料配置并返回(CGP1結構)
     *
     * @param library
     * @return
     */
    public ObjectDTO createNewBomLibrary(BomLibrary library) throws IOException {

        //新建的productMaterialTypes配置
        ObjectDTO newProductMaterialViewTypes = new ObjectDTO();

        //新舊id，用於替換值
        Map<String, String> keys = new HashMap<>();

        if (library.getPageContentSchemas() != null) {

            List<String> jsonList = new ArrayList<>();

            for (ObjectDTO pcsObj : library.getPageContentSchemas()) {

                //替換id，保存新的PageContentSchemas
                String oid = this.getIdFromObject(pcsObj).toString();

                ObjectDTO newObj;

                if (keys.containsKey(oid)) {
                    newObj = this.setId(pcsObj, keys.get(oid));
                } else {
                    newObj = this.setNewId(pcsObj);
                    String nid = this.getIdFromObject(newObj).toString();
                    keys.put(oid, nid);
                }

                String jdata = this.mapper.writeValueAsString(newObj);
                jsonList.add(jdata);
            }

            pcsManager.saveMany(jsonList);
        }

        if (library.getMaterialViewTypes() != null) {

            List<String> jsonList = new ArrayList<>();

            for (ObjectDTO mvtObj : library.getMaterialViewTypes()) {

                //替換id，保存新的MaterialViewTypes
                String oid = this.getIdFromObject(mvtObj).toString();

                ObjectDTO newObj;

                if (keys.containsKey(oid)) {
                    newObj = this.setId(mvtObj, keys.get(oid));
                } else {
                    newObj = this.setNewId(mvtObj);
                    String nid = this.getIdFromObject(newObj).toString();
                    keys.put(oid, nid);
                }

                String jdata = this.mapper.writeValueAsString(newObj);

                //將舊有的PageContentSchemasid替換為新的id
                Iterator<String> iter = keys.keySet().iterator();

                while (iter.hasNext()) {

                    String key = iter.next();
                    String value = keys.get(key);

                    jdata = jdata.replace(key, value);
                }

                jsonList.add(jdata);
            }

            mvtManager.saveMany(jsonList);
        }

        String pmvtList = this.mapper.writeValueAsString(library.getProductMaterialViewTypes());

        if (pmvtList == null || pmvtList.isEmpty())
            throw BusinessExceptionBuilder.of(2700121);

        //將舊有id替換為新的id
        Iterator<String> iter = keys.keySet().iterator();

        while (iter.hasNext()) {

            String key = iter.next();
            String value = keys.get(key);

            pmvtList = pmvtList.replace(key, value);
        }

        //將結果轉換成productMaterialViewTypes對象
        List<ObjectDTO> pmvtObjects = this.mapper.readValue(pmvtList, this.mapper.getTypeFactory().constructCollectionType(List.class, ObjectDTO.class));

        if (pmvtObjects != null) {
            for (ObjectDTO pmvtObj : pmvtObjects) {
                pmvtObj.put("id", idService.generateId().toString());
            }
        }

        newProductMaterialViewTypes.put("productMaterialViewTypes", pmvtObjects);

        return newProductMaterialViewTypes;
    }

    /**
     * 生成單個PageContentSchema(CGP1結構)
     *
     * @param dataSourceObject
     * @param pcsTemplate
     * @param placeholders
     * @param data
     * @return
     */
    protected Optional<String> generatePageContentSchema(ObjectDTO dataSourceObject, String pcsTemplate, List<TemplatePlaceholder> placeholders, ObjectDTO data) {

        Optional<String> pageContentSchema = Optional.of(pcsTemplate);

        int i = 0;

        //替換配置數據
        for (TemplatePlaceholder placeholder : placeholders) {

            try {
                Object value = jsService.evalJavascriptFunction(placeholder.getExpression(), "expression", Object.class, dataSourceObject);

                if (placeholder.getAttributes() != null && placeholder.getAttributes().size() > 0) {
                    if (placeholder.getSelector() == null || placeholder.getSelector().isEmpty()) {
                        for (String attr : placeholder.getAttributes()) {
                            DocumentContext context = JsonPath.parse(pageContentSchema.get()).set(attr, value);
                            pageContentSchema = Optional.of(context.jsonString());
                        }
                    } else {
                        for (String attr : placeholder.getAttributes()) {
                            DocumentContext context = JsonPath.parse(pageContentSchema.get()).put(placeholder.getSelector(), attr, value);
                            pageContentSchema = Optional.of(context.jsonString());
                        }
                    }
                }
            } catch (RuntimeException e) {
                ObjectDTO moreInfo = new ObjectDTO();
                moreInfo.put("datasource", dataSourceObject);
                moreInfo.put("pageContentSchema", pcsTemplate);
                moreInfo.put("placeholders", placeholders);
                moreInfo.put("data", data);

                BusinessException err = BusinessExceptionBuilder.of(2700122, ImmutableMap.of("index", i));
                err.setMoreInfo(moreInfo.toJsonString());

                throw err;
            }
            i++;
        }

        return pageContentSchema;
    }

    /**
     * 獲取ID
     *
     * @param obj
     * @return
     */
    private Object getIdFromObject(ObjectDTO obj) {

        if (obj.get("_id") != null) {
            return obj.get("_id");
        } else if (obj.get("id") != null) {
            return obj.get("id");
        }

        return null;
    }

    /**
     * 獲取ID
     *
     * @param obj
     * @return
     */
    private Object getIdFromMap(Map<String, Object> obj) {

        if (obj.get("_id") != null) {
            return obj.get("_id");
        } else if (obj.get("id") != null) {
            return obj.get("id");
        }

        return null;
    }

    /**
     * 新增Id
     *
     * @param obj
     * @return
     */
    private ObjectDTO setNewId(ObjectDTO obj) {

        String id = idService.generateId().toString();

        return this.setId(obj, id);
    }

    /**
     * 修改Id
     *
     * @param obj
     * @return
     */
    private ObjectDTO setId(ObjectDTO obj, Object newId) {

        //TODO: 臨時處理，用於兼容新舊版本
        if (obj.get("id") != null) {
            obj.put("id", newId);
        }

        obj.put("_id", newId);

        return obj;
    }
}
