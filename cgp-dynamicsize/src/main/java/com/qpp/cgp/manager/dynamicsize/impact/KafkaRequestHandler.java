package com.qpp.cgp.manager.dynamicsize.impact;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.qpp.cgp.configuration.dynamicsize.DynamicSizeProperties;
import com.qpp.cgp.domain.dynamicsize.configuration.ImpactUrlTemplate;
import com.qpp.cgp.domain.dynamicsize.configuration.RequestTemplate;
import com.qpp.cgp.manager.dynamicsize.configuration.RequestTemplateManager;
import com.qpp.cgp.manager.dynamicsize.handler.FreemarkerTemplateHandler;
import com.qpp.cgp.model.dynamicsize.exception.ImpactErrorException;
import com.qpp.cgp.model.dynamicsize.exception.ImpactWaitForGeneratingException;
import com.qpp.cgp.model.dynamicsize.impact.*;
import com.qpp.cgp.service.dynamicsize.cache.CacheService;
import com.qpp.cgp.service.dynamicsize.cache.ImpactCacheService;
import com.qpp.cgp.utils.ObjectMapperBeanCreateUtils;
import com.qpp.commons.json.JsonUtils;
import com.qpp.core.dto.ObjectDTO;
import com.qpp.core.exception.BusinessException;
import freemarker.template.TemplateException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;

@Service("imapctKafkaRequestHandler")
public class KafkaRequestHandler implements  RequestHandler {

    @Autowired
    DynamicSizeProperties appSettings;

    @Autowired
    ImpactCacheService impactCacheService;

    @Autowired
    @Qualifier("dynamicsizeCacheService")
    CacheService cacheService;

    @Autowired
    RequestTemplateManager templateManager;

    ObjectMapper mapper = ObjectMapperBeanCreateUtils.getObjectMapper();;

    /**
     * 通過kafka獲取內容
     *
     * @param urlTemplate
     * @param variables
     * @return
     */
    @Override
    public Object getContent(ImpactUrlTemplate urlTemplate, Map<String, Object> variables) {

        //獲取UrlTemplate配置
        Optional<RequestTemplate> reqTemplate = Optional.empty();

        if (urlTemplate.getRequestTemplateId() != null) {
            reqTemplate = templateManager.getOne(urlTemplate.getRequestTemplateId());
        }

        //如果沒有指定配置，獲取默認id為0的默認配置
        if (!reqTemplate.isPresent()) {
            reqTemplate = templateManager.getOne(appSettings.getImpact().getRequest().getDefaultTemplateId());

            if (!reqTemplate.isPresent()) {
                throw new BusinessException("Not correct for RequestTemplate initialization data.");
            }
        }

        //將UrlTemplate的Body參數轉換成
        ImpactParameterDTO parameterDTO = new ImpactParameterDTO();

        try {
            //賦值
            String body = FreemarkerTemplateHandler.execute("bodyTemplate",
                    reqTemplate.get().getBodyTemplate(),
                    FreemarkerTemplateHandler.getDecimalConfiguration(),
                    ObjectDTO.getInstance(variables));

            //轉換參數類型
            BodyTemplateDTO bodyTemplateDTO = mapper.readValue(body, BodyTemplateDTO.class);
            parameterDTO.setDpi(bodyTemplateDTO.getDpi());
            parameterDTO.setFormat(bodyTemplateDTO.getFormat() != null && bodyTemplateDTO.getFormat().toUpperCase().contains("PDF") ? "PDF" : "SVG");
            parameterDTO.setStandard(bodyTemplateDTO.getStandardName());
            parameterDTO.setMasterToolSettings(bodyTemplateDTO.getMasterToolSettings());

            if (bodyTemplateDTO.getPalette() != null) {

                //包含刀線列表
                List<String> includes = new ArrayList<>();

                //排除刀線列表
                List<String> excludes = new ArrayList<>();

                bodyTemplateDTO.getPalette().stream().forEach(
                        p -> {

                            //兼容V1版本Palette的All配置，新版本All傳空
                            if (appSettings.getImpact().getRequest().getIncludePalettes().containsKey(p)) {
                                List<String> includesPalettes = appSettings.getImpact().getRequest().getIncludePalettes().get(p);
                                if (includesPalettes == null || includesPalettes.isEmpty()) {
                                    includes.clear();
                                } else {
                                    includes.addAll(includesPalettes);
                                }
                            }
                            //兼容V1版本Palette的Other配置
                            else if (appSettings.getImpact().getRequest().getExcludePalettes().containsKey(p)) {
                                List<String> excludesPalettes = appSettings.getImpact().getRequest().getExcludePalettes().get(p);
                                if (excludesPalettes == null || excludesPalettes.isEmpty()) {
                                    excludes.clear();
                                } else {
                                    excludes.addAll(excludesPalettes);
                                }
                            }
                            else {
                                includes.add(p);
                            }
                        }
                );

                parameterDTO.setPalettes(includes);
                parameterDTO.setExcludePalettes(excludes);
            }

            //兼容V1版本，排除D為0的配置
            if (bodyTemplateDTO.getParameters() != null) {
                Map<String, Object> params = new HashMap<>();

                //從配置中找到需要排除的鍵值對，符合條件不添加到參數中
                bodyTemplateDTO.getParameters().entrySet().forEach(
                        entry -> {
                            if (appSettings.getImpact().getRequest().getExcludeParameters().containsKey(entry.getKey())) {
                                if (entry.getValue() == appSettings.getImpact().getRequest().getExcludeParameters().get(entry.getKey())) {
                                    return;
                                }
                            }

                            params.put(entry.getKey(), entry.getValue());
                        }
                );

                parameterDTO.setVariables(params);
            }

            if (variables.get("masterToolSettings") instanceof List) {
                List<MasterToolSettingDTO> masterToolSettingDTOS = new ArrayList<>();
                for (Object masterToolSettingDTOObj : (List)variables.get("masterToolSettings")) {
                    if (masterToolSettingDTOObj instanceof MasterToolSettingDTO) {
                        masterToolSettingDTOS.add((MasterToolSettingDTO) masterToolSettingDTOObj);
                    }
                }
                parameterDTO.setMasterToolSettings(masterToolSettingDTOS);
            }
        } catch (IOException | TemplateException e) {
            throw new BusinessException(
                    String.format("Error happen when generate ImpactRequestParameters, requestTemplateId: %s, template: %s, context: %s, msg: %s",
                            urlTemplate.getRequestTemplateId(),
                            reqTemplate.map(RequestTemplate::getBodyTemplate).orElse("null"),
                            variables != null ? JsonUtils.toJsonString(variables) : "null",
                            StringUtils.isNotBlank(e.getMessage()) ? e.getMessage() : e.getClass().getSimpleName()),
                    e);
        }

        // 文字模板
        if (variables.containsKey("text_template")) {
            parameterDTO.getVariables().put("text_template", variables.get("text_template"));
        }

        //通過Kafka接口獲取緩存數據
        DynamicDieLineResultDTO resultDTO = impactCacheService.generateDieLine(parameterDTO);

        if (resultDTO.getStatusCode() == ResultStatus.Sucess.value() && resultDTO.getFile() != null) {
            //todo：臨時處理，通過Http請求下載文件
            URI uri;
            try {
                uri = new URI(resultDTO.getFile().get("url").toString());
            } catch (URISyntaxException e) {
                throw new BusinessException("Error happen when getContent");
            }
            if (resultDTO.getFile().get("format").toString().equalsIgnoreCase("svg")) {
                return cacheService.getImpactSvg(uri);
            } else {
                return cacheService.getImpactPdf(uri);
            }
        } else if (resultDTO.getStatusCode() == ResultStatus.Retry.value()) {
            throw new ImpactWaitForGeneratingException();
        } else {
            String key = resultDTO.getImpactParameterDTO().getKey();
            throw new ImpactErrorException(resultDTO.getMessage() + " key: " + key);
        }
    }
}
