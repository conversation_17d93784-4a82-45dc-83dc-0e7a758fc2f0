package com.qpp.cgp.manager.dynamicsize.configuration;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.dynamicsize.configuration.DsDataSource;
import com.qpp.cgp.domain.dynamicsize.configuration.ImpactKeyValue;
import com.qpp.cgp.manager.dynamicsize.common.AbstractDocumentCurdManager;
import com.qpp.cgp.manager.dynamicsize.common.IDataOperationManager;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.driver.HybridMongoTemplate;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by admin on 2017/8/10.
 */
@Service
public class DsDataSourceManager extends AbstractDocumentCurdManager<Long> implements IDataOperationManager<Long> {

    ObjectMapper mapper;

    public ObjectMapper getMapper() {
        if (mapper == null) {
            mapper = new ObjectMapper();
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        }
        return mapper;
    }

    @Autowired
    UrlTemplateManager urlTemplateManager;

    @Autowired
    public DsDataSourceManager(HybridMongoTemplate mongoTemplate, IdGenerator idGenerator) {
        super("dsdatasources", mongoTemplate, idGenerator);
    }

    /**
     * 獲取DataSource對象
     *
     * @param id
     * @return
     * @throws IOException
     */
    public Optional<DsDataSource> getOne(Long id) {

        Document doc = mongoTemplate.findById("dsdatasources", id);

        if (doc == null)
            return Optional.empty();

        DsDataSource ds = null;

        try {
            ds = this.getMapper().readValue(this.getMapper().writeValueAsString(doc), DsDataSource.class);
        } catch (IOException e) {
            throw BusinessExceptionBuilder.of(2700016, ImmutableMap.of("id", id));
        }

        return Optional.ofNullable(ds);
    }
    /**
     * 複製Data Source及相關UrlTemplate
     *
     * @param id
     * @return
     */
    @Override
    public Document copy(Long id) {
        Document data = this.findById(id);

        if (data == null) {
            throw BusinessExceptionBuilder.of(2700023, ImmutableMap.of("id", id, "entity", "dsdatasources"));
        }

        this.addAudit(data);
        data.put("_id", this.generateId());

        List<Document> selectors = (List<Document>) data.get("selectors");

        if (selectors != null && selectors.size() > 0) {
            for (Document selector : selectors) {

                Long tmpId = Long.parseLong(selector.get("urlTemplateId").toString());
                Document template = urlTemplateManager.copy(tmpId);
                selector.put("urlTemplateId", template.getLong("_id"));
            }
        }

        mongoCollection.insertOne(data);
        return data;
    }

    public List<Long> getUrlTemplateIdsByIds(List<Long> ids) {
        return ids.stream()
                .map(this::getUrlTemplateIdsById)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    public List<Long> getUrlTemplateIdsById(Long id) {
        Optional<DsDataSource> dsDataSourceOptional = getOne(id);
        if (!dsDataSourceOptional.isPresent()) {
            throw BusinessExceptionBuilder.of(15500001, ImmutableMap.of("dsDataSourceId", id));
        }
        DsDataSource dsDataSource = dsDataSourceOptional.get();

        return dsDataSource.getSelectors().stream()
                .filter(selector -> selector instanceof ImpactKeyValue)
                .map(selector -> (ImpactKeyValue)selector)
                .map(ImpactKeyValue::getUrlTemplateId)
                .collect(Collectors.toList());
    }
}
