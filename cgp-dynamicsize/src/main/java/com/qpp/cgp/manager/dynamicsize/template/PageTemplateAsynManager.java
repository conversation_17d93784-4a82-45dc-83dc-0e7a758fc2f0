package com.qpp.cgp.manager.dynamicsize.template;

import com.qpp.cgp.manager.dynamicsize.common.BaseTemplateAsynManager;
import com.qpp.cgp.model.dynamicsize.common.DataResultDTO;
import com.qpp.cgp.model.dynamicsize.page.ProductPageTemplateDTO;
import freemarker.template.TemplateException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Callable;

/**
 * 異步代理類
 */
@Service
public class PageTemplateAsynManager extends BaseTemplateAsynManager {

    /**
     * 執行類
     */
    @Autowired
    @Qualifier("asynPageTemplateManager")
    PageTemplateManager actManager;

    /**
     * 根據Page類型、SKUID，獲取相應的Page
     *
     * @param pageType 頁類型
     * @param skuId    SKU編號
     * @return
     */
    public DataResultDTO generatePage(Long skuId, String pageType) throws IOException, TemplateException {

        //調用函數
        Callable<String> func = () -> {
            return actManager.generatePage(skuId, pageType);
        };

        //參數，Sentry需要記錄的對象
        Map<String, Object> data = new HashMap<>();
        data.put("skuId", skuId);
        data.put("pageType", pageType);

        //執行公共調用方法
        return super.run(func, data, String.format("Error happen when generating product page template, product: %s, page: %s", skuId, pageType));
    }

    /**
     * 通過頁類開和訂單數據生成產品
     *
     * @param pageType
     * @param orderJson
     * @return
     */
    public DataResultDTO generatePageTemplateFromOrder(String pageType, String orderJson) throws IOException, TemplateException {

        //調用函數
        Callable<String> func = () -> {
            return actManager.generatePageTemplateFromOrder(pageType, orderJson);
        };

        //參數，Sentry需要記錄的對象
        Map<String, Object> data = new HashMap<>();
        data.put("pageType", pageType);
        data.put("order", orderJson);

        //執行公共調用方法
        return super.run(func, data, String.format("Error happen when generating product page template from obder, pageType: %s", pageType));
    }

    /**
     * 通過 Page 模板配置 id 和訂單數據生成產品
     *
     * @param pageTemplateConfigId
     * @param orderJson
     * @return
     */
    public DataResultDTO generatePageTemplateFromOrder(long pageTemplateConfigId, String orderJson) throws IOException, TemplateException {

        //調用函數
        Callable<String> func = () -> {
            return actManager.generatePageTemplateFromOrder(pageTemplateConfigId, orderJson);
        };

        //參數，Sentry需要記錄的對象
        Map<String, Object> data = new HashMap<>();
        data.put("pageTemplateConfigId", pageTemplateConfigId);
        data.put("order", orderJson);

        //執行公共調用方法
        return super.run(func, data, String.format("Error happen when generating product page template from order, pageTemplateConfigId: %s", pageTemplateConfigId));
    }

    public DataResultDTO generatePageTemplateDTOFromOrder(long pageTemplateConfigId, String orderJson) throws IOException, TemplateException {

        //調用函數
        Callable<ProductPageTemplateDTO> func = () -> {
            return actManager.generatePageTemplateDTOFromOrder(pageTemplateConfigId, orderJson);
        };

        //參數，Sentry需要記錄的對象
        Map<String, Object> data = new HashMap<>();
        data.put("pageTemplateConfigId", pageTemplateConfigId);
        data.put("order", orderJson);

        //執行公共調用方法
        return super.run(func, data, String.format("Error happen when generating product page template from order, pageTemplateConfigId: %s", pageTemplateConfigId));
    }
}
