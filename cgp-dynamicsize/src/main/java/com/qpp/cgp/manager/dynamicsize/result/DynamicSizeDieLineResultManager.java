package com.qpp.cgp.manager.dynamicsize.result;

import com.qpp.cgp.domain.dynamicsize.result.DynamicSizeDieLineResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class DynamicSizeDieLineResultManager {

    @Autowired
    private MongoTemplate mongoTemplate;

    public Optional<DynamicSizeDieLineResult> findById(String id) {
        return Optional.ofNullable(
                mongoTemplate.findById(id, DynamicSizeDieLineResult.class)
        );
    }

    public DynamicSizeDieLineResult save(DynamicSizeDieLineResult dynamicSizeDieLineResult) {
        return mongoTemplate.save(dynamicSizeDieLineResult);
    }

    public void removeById(String id) {
        Query query = Query.query(Criteria.where("_id").is(id));
        mongoTemplate.remove(query, DynamicSizeDieLineResult.class);
    }

    public void removeAll() {
        mongoTemplate.remove(Query.query(new Criteria()), DynamicSizeDieLineResult.class);
    }

}
