package com.qpp.cgp.manager.execute;

import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.pcspreprocess.opertion.ParserParameterOperation;
import com.qpp.cgp.domain.pcspreprocess.opertion.SvgSourceOperation;
import com.qpp.cgp.util.HTTPSFileReadUtils;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

@Component("SvgSourceOperationExecutor")
public class SvgSourceOperationExecutor extends IOperationExecutor<SvgSourceOperation,Object> {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private HTTPSFileReadUtils httpsFileReadUtils;

    @Override
    public Object execute(SvgSourceOperation operation, Map<String,Object> context) {
        if (operation.getSourceOperation() == null) {
            throw BusinessExceptionBuilder.of(4800076, ImmutableMap.of("message", "SVGSourceOperation："+operation.getId()+"的source操作为空！"));
        }
        Object sourceResult = operationExecuteService.execute(operation.getSourceOperation(), context);
        switch (operation.getParserType()) {
            case File:
                //转换为一个
                final ByteArrayOutputStream byteArrayOutputStream;
                final InputStream inputStream;
                try {
                    inputStream = httpsFileReadUtils.getInputStreamFromUrl(sourceResult.toString());
                    byteArrayOutputStream = new ByteArrayOutputStream();
                    StreamUtils.copy(inputStream, byteArrayOutputStream);
                    inputStream.close();
                    byteArrayOutputStream.close();
                    return inputStream;
                } catch (IOException e) {
                    throw BusinessExceptionBuilder.of(4800076, ImmutableMap.of("message", "该SvgSourceOperation操作:" + operation.getId() + "的返回流操作异常！message:" + e.getMessage()));
                }
            case SvgToJson:
                ResponseEntity<String> entity = restTemplate.getForEntity(sourceResult.toString(), String.class);
                String document = entity.getBody();
                if (context.isEmpty()) {
                    context = new HashMap<>();
                    context.put("source",document);
                } else {
                    context.put("source", document);
                }
                if (operation.getParserParameters() != null && !operation.getParserParameters().isEmpty()) {
                    return setParameterResultMap(operation, context);
                }else{
                    return document;
                }
            case String:
                return sourceResult;
            case FileMD5:
                if (sourceResult == null || sourceResult.toString().isEmpty()) {
                    return ""; // 或者抛出异常，根据具体需求而定
                }

                String[] split = sourceResult.toString().split("/");
                if (split.length > 0) {
                    return split[split.length - 1];
                } else {
                    return ""; // 或者抛出异常，根据具体需求而定
                }
            default:
                throw BusinessExceptionBuilder.of(4800076, ImmutableMap.of("message", "该解析类型：" + operation.getParserType().toString() + "暂时不支持！"));
        }


    }

    /**
     * 设置对应的SVG解析的结果
     * @param operation 操作
     * @param context 上下文
     * @return 填充完解析数据的上下文
     */
    private Map<String, Object> setParameterResultMap(SvgSourceOperation operation, Map<String,Object> context) {
        Map<String, Object> parametersMap = new HashMap<>();
        for (ParserParameterOperation parserParameter : operation.getParserParameters()) {
            Object parserParameterResult = operationExecuteService.execute(parserParameter, context);
            parametersMap.put(parserParameter.getKey(), parserParameterResult);
        }
        return parametersMap;
    }
}
