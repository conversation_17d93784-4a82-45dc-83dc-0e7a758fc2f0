package com.qpp.cgp.manager.execute;


import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.pcspreprocess.opertion.IOperation;
import com.qpp.core.context.SpringApplicationContext;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class OperationExecuteService {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    public <T extends IOperation> Object execute(T operation, Map<String,Object> context) {
        //只有非Repeat操作才可以通过缓存机制
        IOperationExecutor<IOperation, Object> execute = getExecute(operation.getClass().getSimpleName());
        return execute.execute(operation, context);
    }


    /**
     * 获取对应的操作Execute
     * @param operationName
     * @return
     */
    private IOperationExecutor<IOperation, Object> getExecute(String operationName) {
        IOperationExecutor<IOperation, Object> IOperationExecutor = SpringApplicationContext.getBean(operationName + "Executor");
        if (IOperationExecutor == null) {
            throw BusinessExceptionBuilder.of(4800076, ImmutableMap.of("message", "找不到对应的执行器！"));
        }
        return IOperationExecutor;
    }

}
