package com.qpp.cgp.manager.execute;

import com.qpp.cgp.domain.pcspreprocess.opertion.RepeatIndexSourceOperation;
import com.qpp.cgp.expression.Expression;
import com.qpp.cgp.expression.ExpressionEngine;
import com.qpp.cgp.expression.calculator.ExpressionCalculatorService;
import com.qpp.cgp.value.ValueType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component("RepeatIndexSourceOperationExecutor")
public class RepeatIndexSourceOperationExecutor extends IOperationExecutor<RepeatIndexSourceOperation, Integer> {

    @Autowired
    private ExpressionCalculatorService expressionCalculatorService;

    @Override
    public Integer execute(RepeatIndexSourceOperation operation, Map<String, Object> context) {
        String indexExpresion = operation.getIndexExpresion();
        Expression expression = new Expression();
        expression.setResultType(ValueType.Number);
        expression.setExpressionEngine(ExpressionEngine.JavaScript);
        expression.setExpression(indexExpresion);
        Number calculate = this.whetherToTurnOnExpressCalculateV2(context) ?
                (Number) expressionCalculatorService.calculateV2(expression, context, context) :
                (Number) expressionCalculatorService.calculate(expression, context);
        return calculate.intValue();
    }
}
