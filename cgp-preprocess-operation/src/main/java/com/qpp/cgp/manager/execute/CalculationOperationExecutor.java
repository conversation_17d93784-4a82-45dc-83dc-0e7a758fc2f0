package com.qpp.cgp.manager.execute;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.pcspreprocess.opertion.CalculationOperation;
import com.qpp.cgp.domain.pcspreprocess.opertion.CalculationOperationArg;
import com.qpp.cgp.expression.Expression;
import com.qpp.cgp.expression.calculator.ExpressionCalculatorService;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component("CalculationOperationExecutor")
public class CalculationOperationExecutor extends IOperationExecutor<CalculationOperation, Object> {

    @Autowired
    private ExpressionCalculatorService expressionCalculatorService;

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public Object execute(CalculationOperation operation, Map<String, Object> context) {
        List<CalculationOperationArg> operationArgs = operation.getOperationArgs();
        //设置上下文
        if (operationArgs != null && !operationArgs.isEmpty()) {
            operationArgs.forEach(operationArg -> {
                Object executeResult = operationExecuteService.execute(operationArg.getValueOption(), context);
                context.put(operationArg.getKey(), executeResult);
            });
        }
        //计算对应的值
        Expression expression = operation.getExpression();
        //TODO 返回值结果校验
        Object calculate = null;
        try {
            calculate = this.whetherToTurnOnExpressCalculateV2(context) ?
                    expressionCalculatorService.calculateV2(expression, context, context) :
                    expressionCalculatorService.calculate(expression, context);
        } catch (Exception e) {
            try {
                logger.info("context:" + objectMapper.writeValueAsString(context));
                logger.info("expression:" + expression.getExpression());
            } catch (JsonProcessingException jsonProcessingException) {
                throw BusinessExceptionBuilder.of(4800076, ImmutableMap.of("message", "Operation:" + operation.getId() + "计算对应的上下文序列化异常，message:" + e.getMessage()));
            }
            throw BusinessExceptionBuilder.of(4800076, ImmutableMap.of("message", "Operation:" + operation.getId() + "计算对应的Expression计算错误，message:" + e.getMessage()));
        }
        return calculate;
    }
}
