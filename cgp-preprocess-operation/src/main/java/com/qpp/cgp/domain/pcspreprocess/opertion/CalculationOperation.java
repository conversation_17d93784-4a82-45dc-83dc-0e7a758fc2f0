package com.qpp.cgp.domain.pcspreprocess.opertion;

import com.qpp.cgp.domain.pcspreprocess.type.ResultType;
import com.qpp.cgp.expression.Expression;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@NoArgsConstructor
@Data
public class CalculationOperation extends IOperation {
    private ResultType resultType = ResultType.Object;

    private Map args;

    private List<CalculationOperationArg> operationArgs;

    private Expression expression;

}
