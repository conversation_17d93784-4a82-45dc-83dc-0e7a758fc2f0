package com.qpp.cgp.domain.pcspreprocess.opertion;

import com.qpp.cgp.domain.pcspreprocess.convert.IOperationConvertor;
import com.qpp.cgp.domain.pcspreprocess.convert.OperationConvertService;
import com.qpp.cgp.domain.pcspreprocess.operatorconfig.RepeatOperationConfig;
import com.qpp.cgp.expression.Expression;
import com.qpp.cgp.expression.ExpressionEngine;
import com.qpp.cgp.value.ExpressionValueEx;
import com.qpp.cgp.value.ValueType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component("RepeatOperationConvertor")
public class RepeatOperationConvertor implements IOperationConvertor<RepeatOperationConfig,RepeatOperation> {
    @Autowired
    private OperationConvertService operationConvertService;


    @Override
    public RepeatOperation convert(RepeatOperationConfig operationConfig) {
        RepeatOperation repeatOperation = new RepeatOperation();
        repeatOperation.set_id(operationConfig.get_id());
        if (repeatOperation.get_id() == null) {
            repeatOperation.set_id(UUID.randomUUID().toString());
        }
        ExpressionValueEx timeExpression = new ExpressionValueEx();
        timeExpression.setType(ValueType.Number);
        Expression expression = new Expression();
        expression.setExpressionEngine(ExpressionEngine.JavaScript);
        expression.setResultType(ValueType.Number);
        expression.setExpression(operationConfig.getTimesExpression());
        expression.setClazz(Expression.class.getName());
        timeExpression.setExpression(expression);
        timeExpression.setClazz(ExpressionValueEx.class.getName());
        repeatOperation.setTimeExpression(timeExpression);
        repeatOperation.setDescription(operationConfig.getDescription());
        repeatOperation.setRepeatOperation(operationConvertService.convert(operationConfig.getRepeat()));
        return repeatOperation;
    }
}
