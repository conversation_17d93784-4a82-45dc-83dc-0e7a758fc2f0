package com.qpp.cgp.domain.pcspreprocess.convert;

import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.pcspreprocess.operatorconfig.SvgSourceOperationConfig;
import com.qpp.cgp.domain.pcspreprocess.opertion.ICommonSourceOperation;
import com.qpp.cgp.domain.pcspreprocess.opertion.SvgSourceOperation;
import com.qpp.cgp.domain.pcspreprocess.source.PCSPreprocessCommonSource;
import com.qpp.cgp.domain.pcspreprocess.source.SvgSourceConfig;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

@Component("SvgSourceOperationConvertor")
public class SvgSourceOperationConvertor implements IOperationConvertor<SvgSourceOperationConfig, SvgSourceOperation> {


    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private PCSPreprocessCommonSourceConvertService pcsPreprocessCommonSourceConvertService;


    /**
     * 将SVG操作配置转换为SVG操作
     * @param operationConfig
     * @return
     */
    @Override
    public SvgSourceOperation convert(SvgSourceOperationConfig operationConfig) {
        PCSPreprocessCommonSource source = operationConfig.getSource();
        if (source == null || source.getId() == null) {
            throw BusinessExceptionBuilder.of(4800076, ImmutableMap.of("message", "该SvgSourceOperationConfig的Source为空！"));
        }
        //查询对应的实体
        SvgSourceConfig pcsPreprocessCommonSource = mongoTemplate.findById(source.getId(), SvgSourceConfig.class);
        if (pcsPreprocessCommonSource == null) {
            throw BusinessExceptionBuilder.of(4800076, ImmutableMap.of("message", "该SvgSourceOperationConfig的Source:"+source.getId()+"对应的实体不存在！"));
        }
        operationConfig.setSource(pcsPreprocessCommonSource);
        ICommonSourceOperation iOperation = pcsPreprocessCommonSourceConvertService.convert(operationConfig);
        return (SvgSourceOperation) iOperation;
    }
}
