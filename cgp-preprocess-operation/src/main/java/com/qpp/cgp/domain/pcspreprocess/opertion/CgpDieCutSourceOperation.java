package com.qpp.cgp.domain.pcspreprocess.opertion;

import com.qpp.cgp.domain.pcspreprocess.operatorconfig.OptionProjectionGroup;
import com.qpp.cgp.domain.pcspreprocess.parsertype.ParserType;
import com.qpp.cgp.domain.pcspreprocess.type.ResultType;
import com.qpp.cgp.value.ValueEx;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@NoArgsConstructor
@Data
public class CgpDieCutSourceOperation extends ICommonSourceOperation {

    private ResultType resultType = ResultType.String;

    private String format;

    private int dpi;

    private String standard;

    private List<String> palettes;

    private List<String> excludePalettes;

    /**
     * 等同standard，优先级比standard低，只有管理的standard没有值的时候才做替换
     */
    private ValueEx standardExpression;

    private Map<String, ValueEx> variables;

    private Boolean isExclude = Boolean.FALSE;

    private ParserType parserType;

    /**
     * 可选的参数
     */
    private List<OptionProjectionGroup> optionProjections;

}
