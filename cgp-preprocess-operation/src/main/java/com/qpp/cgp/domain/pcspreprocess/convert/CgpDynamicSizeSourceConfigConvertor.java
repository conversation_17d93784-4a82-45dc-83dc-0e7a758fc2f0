package com.qpp.cgp.domain.pcspreprocess.convert;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.pcspreprocess.operatorconfig.*;
import com.qpp.cgp.domain.pcspreprocess.opertion.*;
import com.qpp.cgp.value.ValueEx;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component("SvgSourceOperationConfigCgpDynamicSizeSourceConfigConvertor")
public class CgpDynamicSizeSourceConfigConvertor implements PCSPreprocessCommonSourceConvertor<SvgSourceOperationConfig, SvgSourceOperation> {

    @Autowired
    private ObjectMapper objectMapper;


    @Override
    public SvgSourceOperation convert(SvgSourceOperationConfig operationConfig) {
        SvgSourceOperation svgSourceOperation = new SvgSourceOperation();
        svgSourceOperation.setId(operationConfig.getId());
        svgSourceOperation.setParserType(operationConfig.getParserType());
        CgpDieCutSourceOperation sourceOperation = new CgpDieCutSourceOperation();
        BeanUtils.copyProperties(operationConfig.getSource(), sourceOperation);
        if (operationConfig.getProjection() != null && !operationConfig.getProjection().isEmpty()) {
            //序列化再返回序列化
            Map<String, ValueEx> valueExMap = new HashMap<>();
            try {
                Map<String, ValueEx> projection = operationConfig.getProjection();
                for (String key : projection.keySet()) {
                    String json = objectMapper.writeValueAsString(projection.get(key));
                    ValueEx valueEx = objectMapper.readValue(json, ValueEx.class);
                    valueExMap.put(key, valueEx);
                }
            } catch (IOException e) {
                throw BusinessExceptionBuilder.of(4800076, ImmutableMap.of("message", "序列化操作：" + operationConfig.getId() + "Projection错误"));
            }
            sourceOperation.setVariables(valueExMap);
        }
        if (operationConfig.getOptionProjections() != null && !operationConfig.getOptionProjections().isEmpty()) {
            List<OptionProjectionGroup> optionProjectionGroups = new ArrayList<>();
            for (OptionProjectionGroup optionProjection : operationConfig.getOptionProjections()) {
                OptionProjectionGroup optionProjectionGroup = new OptionProjectionGroup();
                //序列化再返回序列化
                Map<String, ValueEx> valueExMap = new HashMap<>();
                try {
                    Map<String, ValueEx> projection = optionProjection.getProjection();
                    for (String key : projection.keySet()) {
                        String json = objectMapper.writeValueAsString(projection.get(key));
                        ValueEx valueEx = objectMapper.readValue(json, ValueEx.class);
                        valueExMap.put(key, valueEx);
                    }
                    if (optionProjection.getCondition() != null) {
                        String json = objectMapper.writeValueAsString(optionProjection.getCondition());
                        ValueEx valueEx = objectMapper.readValue(json, ValueEx.class);
                        optionProjectionGroup.setCondition(valueEx);
                    }
                } catch (IOException e) {
                    throw BusinessExceptionBuilder.of(4800076, ImmutableMap.of("message", "序列化操作：" + operationConfig.getId() + " 可选Projection错误"));
                }
                optionProjectionGroup.setProjection(valueExMap);
                optionProjectionGroups.add(optionProjectionGroup);
            }
            sourceOperation.setOptionProjections(optionProjectionGroups);
        }
        if (operationConfig.getParserParameters() != null && !operationConfig.getParserParameters().isEmpty()) {
            List<ParserParameterOperation> parserParameterOperationList = new ArrayList<>();
            for (ParserParameter parserParameter : operationConfig.getParserParameters()) {
                if (parserParameter instanceof BBoxParserParameter) {
                    SvgBBoxOperation svgBBoxOperation = new SvgBBoxOperation();
                    svgBBoxOperation.setXpath(((BBoxParserParameter) parserParameter).getElementSelector());
                    svgBBoxOperation.setKey(parserParameter.getKey());
                    parserParameterOperationList.add(svgBBoxOperation);
                } else if (parserParameter instanceof AttributeParserParameter) {
                    SvgSelectorOperation svgSelectorOperation = new SvgSelectorOperation();
                    svgSelectorOperation.setAttribute(((AttributeParserParameter) parserParameter).getAttributeName());
                    svgSelectorOperation.setSelector(((AttributeParserParameter) parserParameter).getElementSelector());
                    svgSelectorOperation.setKey(parserParameter.getKey());
                    parserParameterOperationList.add(svgSelectorOperation);
                } else {
                    throw BusinessExceptionBuilder.of(4800076, ImmutableMap.of("message", "操作：" + operationConfig.getId() + "该类型:" + parserParameter.getClass().getSimpleName() + "解析参数 key:" + parserParameter.getKey() + "暂时不支持"));
                }
            }
            svgSourceOperation.setParserParameters(parserParameterOperationList);
        }
        svgSourceOperation.setSourceOperation(sourceOperation);
        return svgSourceOperation;
    }
}
