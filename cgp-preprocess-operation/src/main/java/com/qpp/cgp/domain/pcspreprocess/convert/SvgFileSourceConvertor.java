package com.qpp.cgp.domain.pcspreprocess.convert;

import com.qpp.cgp.domain.pcspreprocess.operatorconfig.SvgSourceOperationConfig;
import com.qpp.cgp.domain.pcspreprocess.opertion.SvgFileSourceOperation;
import com.qpp.cgp.domain.pcspreprocess.opertion.SvgSourceOperation;
import com.qpp.cgp.domain.pcspreprocess.source.SvgFileSourceConfig;
import org.springframework.stereotype.Component;

@Component("SvgSourceOperationConfigSvgFileSourceConfigConvertor")
public class SvgFileSourceConvertor implements PCSPreprocessCommonSourceConvertor<SvgSourceOperationConfig, SvgSourceOperation>{
    @Override
    public SvgSourceOperation convert(SvgSourceOperationConfig operationConfig) {
        SvgSourceOperation svgSourceOperation = new SvgSourceOperation();
        svgSourceOperation.setId(operationConfig.getId());
        svgSourceOperation.setParserType(operationConfig.getParserType());
        SvgFileSourceOperation sourceOperation = new SvgFileSourceOperation();
        sourceOperation.setPathValueEx(((SvgFileSourceConfig) operationConfig.getSource()).getPathValueEx());
        svgSourceOperation.setSourceOperation(sourceOperation);
        return svgSourceOperation;
    }
}
