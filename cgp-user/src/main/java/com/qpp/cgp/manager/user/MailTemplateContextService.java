package com.qpp.cgp.manager.user;

import com.qpp.cgp.domain.dto.mail.MailAttachment;
import com.qpp.cgp.domain.dto.mail.MailItem;
import com.qpp.cgp.domain.dto.mail.MailSenderInfo;
import com.qpp.cgp.domain.dto.user.*;
import com.qpp.cgp.domain.mail.MailReceiver;
import com.qpp.cgp.domain.mail.MailTemplateConfig;
import com.qpp.cgp.domain.user.UserRegistDTO;
import com.qpp.cgp.manager.mail.MailSenderService;
import com.qpp.cgp.manager.mail.process.MailReceiverService;
import freemarker.cache.StringTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2018/1/24
 */
@Service
public class MailTemplateContextService {

    /**
     * 组装邮件主题、内容填充数据
     *
     * @param userVerifyToken
     * @param registerDTO
     * @param activeMethod
     * @return
     */
    public MailTemplateContext getMailTemplateContext(UserVerifyToken userVerifyToken, RegisterDTO registerDTO, AbstractActiveMethod activeMethod) {
        MailTemplateContext context = new MailTemplateContext();
        context.setToken(userVerifyToken.getToken());
        context.setUsername(registerDTO.getFirstName());
        context.setPassword(registerDTO.getPassword());
        context.setUrl(registerDTO.getActiveUrl() + "?token=" + userVerifyToken.getToken());

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(activeMethod.getDateFormat())
                // .withLocale(Locale.getDefault())
                .withZone(ZoneId.systemDefault());

        context.setInvalidInterval(formatter.format(userVerifyToken.getInvalidInstant()));

        context.setClientId(registerDTO.getClientId());

        return context;
    }
}
