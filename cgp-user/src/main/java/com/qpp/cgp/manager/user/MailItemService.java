package com.qpp.cgp.manager.user;

import com.qpp.cgp.domain.dto.mail.MailAttachment;
import com.qpp.cgp.domain.dto.mail.MailItem;
import com.qpp.cgp.domain.dto.mail.MailSenderInfo;
import com.qpp.cgp.domain.mail.MailReceiver;
import com.qpp.cgp.domain.mail.MailTemplateConfig;
import com.qpp.cgp.manager.mail.MailSenderService;
import com.qpp.cgp.manager.mail.process.MailReceiverService;
import freemarker.cache.StringTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2018/1/25
 */
@Service
public class MailItemService {

    @Autowired
    private MailSenderService mailSenderService;

    @Autowired
    private MailReceiverService mailReceiverService;

    /**
     * 较之原方法添加了一个参数，指定为partner验证邮箱发件人的配置
     *
     * @param mailTemplateConfig
     * @param email 收件人邮箱地址
     * @param websiteId 用于确定发件人
     * @param context
     * @param category 用于确定发件人
     * @return
     */
    public MailItem getMailItem(MailTemplateConfig mailTemplateConfig, String email, Long websiteId, Object context, String category) {
        MailItem mailItem = new MailItem();
        MailSenderInfo mailSenderInfo = mailSenderService.initMailSenderInfo(websiteId, null, category);
        mailItem.setMailSender(mailSenderInfo);
        mailItem.setFrom(mailSenderInfo.getUsername());
        mailItem.setWebsiteId(websiteId);
        mailItem.setSubject(this.freemarkerStringTemplate(mailTemplateConfig.getSubject(), context));
        mailItem.setText(this.freemarkerStringTemplate(mailTemplateConfig.getContent(), context));

        if (Objects.nonNull(mailTemplateConfig.getAttachments()) && mailTemplateConfig.getAttachments().size() > 0) {
            mailItem.setAttachments(mailTemplateConfig.getAttachments().toArray(new MailAttachment[mailTemplateConfig.getAttachments().size()]));
        }

        List<String> to = new ArrayList<>();
        to.add(email);
        if (Objects.nonNull(mailTemplateConfig.getTo())) {
            for (int i = 0; i < mailTemplateConfig.getTo().size(); i++) {
                MailReceiver mailReceiver = mailTemplateConfig.getTo().get(i);
                to.addAll(mailReceiverService.getEmails(mailReceiver));
            }
        }
        mailItem.setTo(to.toArray(new String[to.size()]));

        List<String> bcc = new ArrayList<>();
        if (Objects.nonNull(mailTemplateConfig.getBcc())) {
            for (int i = 0; i < mailTemplateConfig.getBcc().size(); i++) {
                MailReceiver mailReceiver = mailTemplateConfig.getBcc().get(i);
                bcc.addAll(mailReceiverService.getEmails(mailReceiver));
            }
        }
        mailItem.setBcc(bcc.toArray(new String[bcc.size()]));

        List<String> cc = new ArrayList<>();
        if (Objects.nonNull(mailTemplateConfig.getCc())) {
            for (int i = 0; i < mailTemplateConfig.getCc().size(); i++) {
                MailReceiver mailReceiver = mailTemplateConfig.getCc().get(i);
                cc.addAll(mailReceiverService.getEmails(mailReceiver));
            }
        }
        mailItem.setCc(cc.toArray(new String[cc.size()]));
        return mailItem;
    }

    public MailItem getMailItem(MailTemplateConfig mailTemplateConfig, String email, Long websiteId, Object context) {
        MailItem mailItem = new MailItem();
        MailSenderInfo mailSenderInfo = mailSenderService.initMailSenderInfo(websiteId, null, MailSenderService.CATEGORY_SERVICE);
        mailItem.setMailSender(mailSenderInfo);
        mailItem.setFrom(mailSenderInfo.getUsername());
        mailItem.setWebsiteId(websiteId);
        mailItem.setSubject(this.freemarkerStringTemplate(mailTemplateConfig.getSubject(), context));
        mailItem.setText(this.freemarkerStringTemplate(mailTemplateConfig.getContent(), context));

        if (Objects.nonNull(mailTemplateConfig.getAttachments()) && mailTemplateConfig.getAttachments().size() > 0) {
            mailItem.setAttachments(mailTemplateConfig.getAttachments().toArray(new MailAttachment[mailTemplateConfig.getAttachments().size()]));
        }

        List<String> to = new ArrayList<>();
        to.add(email);
        if (Objects.nonNull(mailTemplateConfig.getTo())) {
            for (int i = 0; i < mailTemplateConfig.getTo().size(); i++) {
                MailReceiver mailReceiver = mailTemplateConfig.getTo().get(i);
                to.addAll(mailReceiverService.getEmails(mailReceiver));
            }
        }
        mailItem.setTo(to.toArray(new String[to.size()]));

        List<String> bcc = new ArrayList<>();
        if (Objects.nonNull(mailTemplateConfig.getBcc())) {
            for (int i = 0; i < mailTemplateConfig.getBcc().size(); i++) {
                MailReceiver mailReceiver = mailTemplateConfig.getBcc().get(i);
                bcc.addAll(mailReceiverService.getEmails(mailReceiver));
            }
        }
        mailItem.setBcc(bcc.toArray(new String[bcc.size()]));

        List<String> cc = new ArrayList<>();
        if (Objects.nonNull(mailTemplateConfig.getCc())) {
            for (int i = 0; i < mailTemplateConfig.getCc().size(); i++) {
                MailReceiver mailReceiver = mailTemplateConfig.getCc().get(i);
                cc.addAll(mailReceiverService.getEmails(mailReceiver));
            }
        }
        mailItem.setCc(cc.toArray(new String[cc.size()]));
        return mailItem;
    }

    private String freemarkerStringTemplate(String str, Object context) {
        Configuration configuration = new Configuration(Configuration.VERSION_2_3_22);
        StringTemplateLoader loader = new StringTemplateLoader();
        loader.putTemplate("template", str);
        configuration.setTemplateLoader(loader);
        configuration.setNumberFormat("0.##");
        configuration.setLogTemplateExceptions(false);
        try {
            Template template = configuration.getTemplate("template", "UTF-8");
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            template.process(context, new OutputStreamWriter(outputStream));
            return outputStream.toString();
        } catch (IOException | TemplateException e) {
            e.printStackTrace();
            return null;
        }
    }


}
