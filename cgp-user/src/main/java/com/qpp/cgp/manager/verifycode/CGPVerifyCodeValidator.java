package com.qpp.cgp.manager.verifycode;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.common.verifycode.AbstractVerificationCodeConfig;
import com.qpp.cgp.domain.dto.validateCode.CGPRequestBody;
import com.qpp.cgp.manager.common.verifycode.VerifyCodeConfigManager;
import com.qpp.cgp.manager.user.UserVerifyService;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @version : 1.0
 * Description : CGP验证码校验实现类
 * Create Date : 2020-03-17 14:09
 **/
@Component(value = "CGPVerifyCodeValidator")
public class CGPVerifyCodeValidator implements VerifyCodeValidator{
    @Autowired
    private UserVerifyService userVerifyService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private VerifyCodeConfigManager verifyCodeConfigManager;


    @Override
    public void validate(Map requestBody) {
        try {
            final String json = objectMapper.writeValueAsString(requestBody);
            final CGPRequestBody cgpRequestBody = objectMapper.readValue(json, CGPRequestBody.class);
            if (cgpRequestBody.getWebsiteId() == null ) {
                throw BusinessExceptionBuilder.of(2000067, ImmutableMap.of("message", "CGP验证码请求网站Id为空！"));
            }
            AbstractVerificationCodeConfig verificationCodeConfig = verifyCodeConfigManager.findByWebsiteId(cgpRequestBody.getWebsiteId());
            if (verificationCodeConfig != null) {
                if (cgpRequestBody.getVerifyCode() == null || StringUtils.isBlank(cgpRequestBody.getVerifyCode())) {
                    throw BusinessExceptionBuilder.of(2000067, ImmutableMap.of("message", "CGP验证码请求验证码为空！"));
                }
                if (cgpRequestBody.getVerifyCodeToken() == null || StringUtils.isBlank(cgpRequestBody.getVerifyCodeToken())) {
                    throw BusinessExceptionBuilder.of(2000067, ImmutableMap.of("message", "CGP验证码请求验证码Token为空！"));
                }
                userVerifyService.checkVerificationCode(cgpRequestBody.getVerifyCodeToken(), cgpRequestBody.getVerifyCode(), verificationCodeConfig);
            }
        } catch (IOException e) {
            throw BusinessExceptionBuilder.of(2000067, ImmutableMap.of("message","解析失败！"));
        }
    }
}
