package com.qpp.cgp.manager.user;

import com.qpp.cgp.domain.dto.user.AbstractTokenGenerator;
import com.qpp.cgp.domain.dto.user.UUIDTokenGenerator;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2018/1/24
 */
public class UUIDTokenGeneratorProcessor extends AbstractTokenGeneratorProcessor {
    @Override
    public String generatorToken(AbstractTokenGenerator tokenGenerator) {
        String tokenCode = "";
        UUIDTokenGenerator uuidTokenGenerator = (UUIDTokenGenerator) tokenGenerator;

        String s = UUID.randomUUID().toString();
        // 去除“-”后的字符串
        StringBuilder replace = new StringBuilder(s.replace("-", ""));

        int length = uuidTokenGenerator.getLength();
        if (length == 0) {
            length = 64;
        }
        Integer splitSize = uuidTokenGenerator.getSplitSize();
        String splitStr = uuidTokenGenerator.getSplitStr();

        // 处理长度
        while (replace.length() < length) {
            replace.append(UUID.randomUUID().toString().replace("-", ""));
        }
        replace = new StringBuilder(replace.substring(0, length));
        tokenCode = replace.toString();

        // 加入指定分隔符
        if (StringUtils.isNotBlank(splitStr) && splitSize != null) {
            List<String> stringList = new ArrayList<>();
            if (splitSize < replace.length()) {
                while (replace.length() >= splitSize) {
                    String substring = replace.substring(0, splitSize);
                    replace = new StringBuilder(replace.substring(splitSize, replace.length()));
                    stringList.add(substring + splitStr);
                }
            }
            StringBuilder s1 = new StringBuilder();
            for (String aStringList : stringList) {
                s1.append(aStringList);
            }
            tokenCode = s1.substring(0, length);
        }
        // 配置token的大小写
        if (UUIDTokenGenerator.UPPER_CASE.equals(uuidTokenGenerator.getTokenCase())) {
            tokenCode = tokenCode.toUpperCase();
        } else if (UUIDTokenGenerator.LOWER_CASE.equals(uuidTokenGenerator.getTokenCase())) {
            tokenCode = tokenCode.toLowerCase();
        }
        return tokenCode;
    }

}
