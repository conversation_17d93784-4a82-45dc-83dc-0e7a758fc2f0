package com.qpp.cgp.manager.user.preference;

import com.qpp.cgp.config.mongo.MongoTemplateBeanNames;
import com.qpp.cgp.domain.user.preference.UserPreference;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.driver.HybridMongoTemplate;
import com.qpp.web.business.manager.AbstractMongoCurdManager;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

/**
 * @Author: lw
 * @Date: 2024/12/02/17:00
 * @Description:
 */
@Service
public class UserPreferenceManager extends AbstractMongoCurdManager<UserPreference, String> {
    public UserPreferenceManager(@Qualifier(MongoTemplateBeanNames.CONFIG) HybridMongoTemplate mongoTemplate, IdGenerator idgenerator) {
        super(mongoTemplate, idgenerator);
    }

    public UserPreference findByPlatformAndUser(String platformCode, Long userId) {
        return mongoTemplate.findOne(Query.query(Criteria.where("platformCode").regex(platformCode, "i").and("userId").is(userId)), UserPreference.class);
    }
}
