package com.qpp.cgp.manager.verifycode;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.dto.validateCode.GoogleRequestBody;
import com.qpp.cgp.domain.verifyCode.GoogleVerifyCodeConfig;
import com.qpp.cgp.manager.application.ApplicationConfigService;
import com.qpp.cgp.manager.common.ConfigurationManager;
import com.qpp.core.utils.http.RestTemplateUtils;
import com.qpp.core.utils.http.UrlBuilder;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version : 1.0
 * Description : Google验证码验证器
 * Create Date : 2020-03-17 14:29
 **/
@Component(value = "GoogleVerifyCodeValidator")
public class GoogleVerifyCodeValidator implements VerifyCodeValidator {
    @Autowired
    private ConfigurationManager configurationManager;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RestTemplateBuilder restTemplateBuilder;

    @Autowired
    private Environment environment;

    @Autowired
    private GoogleVerifyCodeConfigManager googleVerifyCodeConfigManager;

    @Autowired
    private ApplicationConfigService applicationConfigService;

    /**
     * 校验验证码是否正确
     *
     * @param requestBody 请求参数
     */
    @Override
    public void validate(Map requestBody) {
        GoogleVerifyCodeConfig config = googleVerifyCodeConfigManager.findConfigByEnvironment(applicationConfigService.getApplicationMode());
        this.validate(requestBody, config);
    }

    public void validate(Map requestBody, GoogleVerifyCodeConfig config) {
        this.validateConfig(config);
        String requestBodyJson = null;
        GoogleRequestBody googleRequestBody = null;
        try {
            requestBodyJson = objectMapper.writeValueAsString(requestBody);
            googleRequestBody = objectMapper.readValue(requestBodyJson, GoogleRequestBody.class);
        } catch (IOException e) {
            throw BusinessExceptionBuilder.of(2000067, ImmutableMap.of("message", "Google请求参数解析失败！message:" + e.getMessage()));
        }
        if (StringUtils.isBlank(googleRequestBody.getResponse())) {
            throw BusinessExceptionBuilder.of(2000067, ImmutableMap.of("message", "Google请求参数Response请求参数为空"));
        }
        RestTemplate restTemplate = RestTemplateUtils.buildRestTemplate(config.getConnectTimeout(), config.getReadTimeout(), this.getProxy(config));
        String requestUrl = UrlBuilder.fromBaseUrl(config.getGoogleRequestUrl())
                .queryParam("secret", config.getGoogleBackendSecret())
                .queryParam("response", googleRequestBody.getResponse())
                .build();
        ResponseEntity<Map> response = null;
        try {
            response = restTemplate.postForEntity(requestUrl, null, Map.class);
        } catch (RestClientException ex) {
            throw BusinessExceptionBuilder.of(2000080, ImmutableMap.of("message", Optional.ofNullable(ex.getMessage()).orElse(ex.toString())));
        }
        if (response.getStatusCode().equals(HttpStatus.OK)) {
            Map body = response.getBody();
            Object success = body.get("success");
            if (success == null) {
                throw BusinessExceptionBuilder.of(2000067, ImmutableMap.of("message", "Google请求的返回的数据没有状态码，body:" + body));
            }
            Boolean isSuccess = (Boolean) success;
            if (isSuccess) {
                double score = (double) body.get("score");
                if (score < config.getHumanMachineAuthSuccessThreshold()) {
                    throw BusinessExceptionBuilder.of(2000090, ImmutableMap.of("body", body,
                            "humanMachineAuthSuccessThreshold", config.getHumanMachineAuthSuccessThreshold()));
                }
            } else {
                throw BusinessExceptionBuilder.of(2000068, ImmutableMap.of("message", "验证码不通过，body:" + body));
            }
        } else {
            throw BusinessExceptionBuilder.of(2000067, ImmutableMap.of("message", "Goolge请求失败,错误码：" + response.getStatusCode() + "body:" + response.getBody()));
        }
    }

    private RestTemplateUtils.Proxy getProxy(GoogleVerifyCodeConfig config) {
        if (config.isNeedProxy()) {
            return new RestTemplateUtils.Proxy(config.getProxy().getAddress(), config.getProxy().getPort());
        }
        return null;
    }

    private void validateConfig(GoogleVerifyCodeConfig config) {
        if (null == config) {
            throw BusinessExceptionBuilder.of(2000067, ImmutableMap.of("message", "没有谷歌验证码配置"));
        }
        if (StringUtils.isBlank(config.getGoogleBackendSecret()) || StringUtils.isBlank(config.getGoogleRequestUrl())) {
            throw BusinessExceptionBuilder.of(2000067, ImmutableMap.of("message", "只有请求的GoogleURL或则秘钥，两者没有同时有！"));
        }
    }
}
