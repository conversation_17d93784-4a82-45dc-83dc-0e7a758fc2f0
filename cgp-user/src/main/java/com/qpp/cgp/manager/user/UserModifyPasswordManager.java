package com.qpp.cgp.manager.user;

import com.qpp.cgp.domain.dto.user.ModifyPasswordDTO;
import com.qpp.cgp.domain.dto.user.UserDTO;
import com.qpp.cgp.domain.dto.user.UserRegistConfig;
import com.qpp.core.exception.BusinessException;
import com.qpp.core.utils.SecurityUtils;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;


import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2018/2/6
 */
@Service

public class UserModifyPasswordManager {

    @Autowired
    private UserRegistConfigManager userRegistConfigManager;

    @Autowired
    private UserVerifyService userVerifyService;

    @Autowired
    private UserManager userManager;

    public void modifyPassword(ModifyPasswordDTO modifyPasswordDTO) {
        Long loginedInUserId = SecurityUtils.getLoginedInUserId();
        UserDTO userDTO = userManager.findById(loginedInUserId);
        String oldPassword = modifyPasswordDTO.getOldPassword();
        checkOldPasswordCorrect(userDTO.getPassword(), oldPassword);
        String newPassword = modifyPasswordDTO.getNewPassword();
        String confirmNewPassword = modifyPasswordDTO.getConfirmNewPassword();
        Long websiteId = modifyPasswordDTO.getWebsiteId();
        UserRegistConfig config = userRegistConfigManager.findByWebsiteId(websiteId);
        userVerifyService.checkPassword(newPassword,
                Objects.isNull(config) ? null : config.getPasswordRuleConfig());
        userDTO.setPassword(newPassword);
        userManager.saveUpdate(userDTO, userDTO.getId());
    }

    public void checkOldPasswordCorrect(String password, String oldPassword) {
        if (!Objects.equals(password, oldPassword)) {
            throw BusinessExceptionBuilder.of(3000015);
        }
    }
}
