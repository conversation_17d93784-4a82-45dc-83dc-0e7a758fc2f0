package com.qpp.cgp.manager.user;

import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.config.mongo.MongoTemplateBeanNames;
import com.qpp.cgp.domain.common.Zone;
import com.qpp.cgp.manager.FilterUtils;
import com.qpp.cgp.repository.common.ZoneRepository;
import com.qpp.core.dto.FilterDTO;
import com.qpp.web.business.message.ExtMessages;
import com.qpp.cgp.domain.common.Country;
import com.qpp.cgp.domain.dto.user.AddressBookDTO;
import com.qpp.cgp.domain.dto.user.MyAddressBookDTO;
import com.qpp.cgp.domain.user.AddressBook;
import com.qpp.cgp.domain.user.User;
import com.qpp.cgp.repository.common.CountryRepository;
import com.qpp.cgp.repository.user.AddressBookRepository;
import com.qpp.cgp.repository.user.UserRepository;
import com.qpp.cgp.util.UserUtils;
import com.qpp.core.exception.BusinessException;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.validator.EmailValidator;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;


import java.util.*;
import java.util.stream.Collectors;

@SuppressWarnings({"ConstantConditions", "JavaDoc", "SpellCheckingInspection"})
@Service

public class AddressBookManager {

    @Autowired
    private AddressBookRepository addressBookRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private CountryRepository countryRepository;

    @Autowired
    private ZoneRepository zoneRepository;

    @Autowired
    @Qualifier(MongoTemplateBeanNames.CONFIG)
    private MongoTemplate mongoTemplate;

    public List<AddressBookDTO> findAllByUserId(Long userId) {
        List<AddressBookDTO> reList = new ArrayList<>();
        List<AddressBook> list = addressBookRepository
                .findByUserIdOrderBySortOrderAsc(userId);
        for (AddressBook entity : list) {
            AddressBookDTO dto = this.convertAddressBookToDTO(entity);
            reList.add(dto);
        }
        return reList;
    }

    public List<AddressBookDTO> findAll(List<FilterDTO> filterDTOS) {
        List<AddressBookDTO> reList = new ArrayList<>();
        List<AddressBook> list = addressBookRepository
                .find(FilterUtils.getQuery(filterDTOS));
        for (AddressBook entity : list) {
            AddressBookDTO dto = this.convertAddressBookToDTO(entity);
            reList.add(dto);
        }
        return reList;
    }

    public AddressBookDTO saveUpdate(AddressBookDTO dto) {
        this.doCheck(dto);
        AddressBook entity = this.dtoToEntity(dto);
        entity = addressBookRepository.save(entity);
        dto.setId(entity.getId());
        dto.setCountryName(entity.getCountryName());
        return dto;
    }

    /**
     * 修改指定id对应的地址信息.
     * remark: wirte by hungcm.
     *
     * @param addressBookDTO
     * @param id
     * @return
     */
    public AddressBookDTO saveUpdate(MyAddressBookDTO addressBookDTO, Long id) {
        Optional<AddressBook> optionalAddressBook = addressBookRepository.findById(id);
        if (!optionalAddressBook.isPresent()) {
            throw new BusinessException("AddressBook " + id + " is not exist!");
        }
        AddressBook existAddress = optionalAddressBook.get();
        User user = UserUtils.getCurrentUserDetails();
        //校验是否手机号码和邮箱地址是否存在
        if (StringUtils.isBlank(addressBookDTO.getEmailAddress()) || StringUtils.isBlank(addressBookDTO.getMobile())) {
            throw new BusinessException("The saveUpdate AddressBook contain EmailAddress is null or mobile  is null！");
        }
        // 若dto的suburb字段值为空，则赋city字段的值
        if (StringUtils.isEmpty(addressBookDTO.getSuburb())) {
            addressBookDTO.setSuburb(addressBookDTO.getCity());
        }
        // 判断id对应的AddressBook是否属于当前用户
        if (!existAddress.getUser().getId().equals(user.getId())) {
            throw new BusinessException("AddressBook " + id + " not belongs to you!");
        }
        List<AddressBook> addressBooks = user.getAddressBooks();
        /*
        判断是否需要设置为默认地址，如果需要，则将sortOrder小于他的所有的AddressBook的
        sortOrder值加1
         */
        if (Boolean.TRUE.equals(addressBookDTO.getIsDefault())) {
            // 判断id对应的地址是否已经是默认地址，默认地址的标志为sortOrder为0
            if (existAddress.getSortOrder() != 0) {
                List<AddressBook> collect = addressBooks.stream()
                        .filter(addressBook -> addressBook.getSortOrder() < existAddress.getSortOrder())
                        .collect(Collectors.toList());
                if (Objects.nonNull(collect) && collect.size() > 0) {
                    collect.forEach(address -> {
                        address.setSortOrder(address.getSortOrder() + 1);
                        addressBookRepository.save(address);
                    });
                }
            }
            AddressBook saveAddress = this.dtoToEntity(typeConverter(addressBookDTO));
            saveAddress.setId(id);
            saveAddress.setSortOrder(NumberUtils.INTEGER_ZERO);
            AddressBookDTO dto = saveUpdate(convertAddressBookToDTO(saveAddress));
            // 修改用户默认地址
            user.setDefaultAddressBook(dtoToEntity(dto));
            userRepository.save(user);
            return dto;
        } else {
            if (Objects.nonNull(addressBooks) && addressBooks.size() > 0) {
                // 判断id对应的AddressBook是否是默认地址
                if (existAddress.getSortOrder() == 0) {
                    // 如果是默认地址，则将用户的所有地址的sortOrder加1，并将当前用户的默认地址设置为null
                    addressBooks.forEach(addressBook -> addressBook.setSortOrder(addressBook.getSortOrder() + 1));
                    user.setDefaultAddressBook(null);
                    userRepository.save(user);
                } else {
                    processSortOrderWhenUpdateNotSetDefault(addressBooks, existAddress, addressBookDTO);
                }
            }
            AddressBook saveAddress = this.dtoToEntity(typeConverter(addressBookDTO));
            saveAddress.setId(id);
            // 保存更改
            return saveNew(convertAddressBookToDTO(saveAddress));
        }
    }

    /**
     * @param entity 转 DTO
     * @return
     */
    private AddressBookDTO convertAddressBookToDTO(AddressBook entity) {
        AddressBookDTO dto = new AddressBookDTO();
        dto.setId(entity.getId());
        dto.setGender(entity.getGender());
        dto.setFirstName(entity.getFirstName());
        dto.setLastName(entity.getLastName());
        dto.setCountryCode2(entity.getCountryCode2());
        dto.setCountryName(entity.getCountryName());
        dto.setState(entity.getState());
        dto.setStateCode(entity.getStateCode());
        dto.setCity(entity.getCity());
        dto.setPostcode(entity.getPostcode());
        dto.setSuburb(entity.getSuburb());
        dto.setStreetAddress1(entity.getStreetAddress1());
        dto.setStreetAddress2(entity.getStreetAddress2());
        dto.setTelephone(entity.getTelephone());
        dto.setMobile(entity.getMobile());
        dto.setEmailAddress(entity.getEmailAddress());
        dto.setCompany(entity.getCompany());
        dto.setSortOrder(entity.getSortOrder());
        dto.setLocationType(entity.getLocationType());
        dto.setLocationTypeValue(entity.getLocationTypeValue());
        dto.setLocationTypeCode(entity.getLocationTypeCode());
        dto.setUserId(entity.getUser().getId());
        return dto;
    }

    /**
     * 转 entity
     *
     * @return
     */
    private AddressBook dtoToEntity(AddressBookDTO dto) {
        AddressBook entity = null;
        if (dto.getId() == null) {
            entity = new AddressBook();
            entity.setUser(userRepository.findById(dto.getUserId()).get());
        } else {
            entity = addressBookRepository.findById(dto.getId()).get();
            if (dto.getUserId() != null) {
                entity.setUser(userRepository.findById(dto.getUserId()).get());
            }
        }
        if (dto.getGender() == null) {
            entity.setGender('U');
        } else {
            entity.setGender(dto.getGender());
        }
        entity.setFirstName(dto.getFirstName());
        entity.setLastName(dto.getLastName());

        entity.setCountryCode2(dto.getCountryCode2());
        if (dto.getCountryCode2() != null) {
            Country country = countryRepository.findByIsoCode2(dto
                    .getCountryCode2());
            if (country != null) {
                entity.setCountryName(country.getName());
            }
        }
        entity.setState(dto.getState());
        entity.setCity(dto.getCity());
        entity.setPostcode(dto.getPostcode());
        entity.setSuburb(dto.getSuburb());
        entity.setStreetAddress1(dto.getStreetAddress1());
        entity.setStreetAddress2(dto.getStreetAddress2());
        entity.setTelephone(dto.getTelephone());
        entity.setMobile(dto.getMobile());
        entity.setEmailAddress(dto.getEmailAddress());
        entity.setCompany(dto.getCompany());
        entity.setSortOrder(dto.getSortOrder());
        entity.setLocationType(dto.getLocationType());
        return entity;
    }

    private void doCheck(AddressBookDTO dto) {

        //检查国家code是否有效
        if (Objects.isNull(countryRepository.findByIsoCode2(dto.getCountryCode2()))) {
            throw BusinessExceptionBuilder.of(1500010, ImmutableMap.of());
        }

        ExtMessages message = new ExtMessages();
        if (dto.getId() == null) {
            if (dto.getUserId() == null) {
                message.addMessage("AddressBook", "Not User");
            }
        } else {
            AddressBook address = addressBookRepository.findById(dto.getId()).get();
            if (!address.getUser().getId().equals(dto.getUserId())) {
                message.addMessage("user", "UserId Can't Change");
            }
        }
        if (message.size() != 0) {
            throw new BusinessException(message.toString());
        }

    }

    public AddressBookDTO delete(Long id) {
        AddressBook address = addressBookRepository.findById(id).get();
        User user = address.getUser();
        user.setDefaultAddressBook(null);
        userRepository.save(user);
        addressBookRepository.delete(address);

        return this.convertAddressBookToDTO(address);
    }


    /**
     * 验证地址是否正确
     *
     * @param addressBook
     */
    public void validateDeliveryAddress(AddressBook addressBook) {
        if (addressBook == null) {

            throw BusinessExceptionBuilder.of(1500001, ImmutableMap.of());
        } else {

            if (StringUtils.isEmpty(addressBook.getEmailAddress())) {

                throw BusinessExceptionBuilder.of(1500003, ImmutableMap.of());
            } else {
                if (!EmailValidator.getInstance().isValid(addressBook.getEmailAddress())) {

                    throw BusinessExceptionBuilder.of(1500004, ImmutableMap.of());
                }
            }

//            //省份
//            if (StringUtils.isEmpty(addressBook.getState())) {
//
//                throw BusinessExceptionBuilder.of(1500005, ImmutableMap.of());
//            }
//
//            //城市
//            if (StringUtils.isEmpty(addressBook.getCity())) {
//
//                throw BusinessExceptionBuilder.of(1500006, ImmutableMap.of());
//            }

//            //区域
//            if (StringUtils.isEmpty(addressBook.getSuburb())) {
//
//                throw BusinessExceptionBuilder.of(1500007, ImmutableMap.of());
//            }


            //详细地址1
            if (StringUtils.isEmpty(addressBook.getStreetAddress1())) {

                throw BusinessExceptionBuilder.of(1500008, ImmutableMap.of());
            }

            //收件人
            if (StringUtils.isEmpty(addressBook.getFirstName())) {

                throw BusinessExceptionBuilder.of(1500009, ImmutableMap.of());
            }


            //检查国家code是否有效
            String countryCode2 = addressBook.getCountryCode2();
            Country country = null;
            if (StringUtils.isNotBlank(countryCode2)) {
                Optional<Country> countryOptional = Optional.ofNullable(countryRepository.findByIsoCode2(countryCode2));
                country = countryOptional.orElse(null);
            }

            if (country == null) {
                throw BusinessExceptionBuilder.of(1500010, ImmutableMap.of());
            }

            addressBook.setCountryCode2(country.getIsoCode2());
            addressBook.setCountryName(country.getName());

            //sort order
            if (addressBook.getSortOrder() == null) {
                addressBook.setSortOrder(1);
            }
        }

    }

    public void checkAddressBook(AddressBook addressBook) {
        if (Objects.isNull(addressBook)) {
            throw BusinessExceptionBuilder.of(1500001, ImmutableMap.of());
        }
        if (Strings.isBlank(addressBook.getCountryCode2())) {
            throw BusinessExceptionBuilder.of(1500011, ImmutableMap.of());
        }
        Country country = countryRepository.findByIsoCode2(addressBook.getCountryCode2());
        if (Objects.isNull(country)) {
            throw BusinessExceptionBuilder.of(1500010, ImmutableMap.of());
        }

        if (zoneRepository.countByCountryId(country.getId()) > 0) {
            // 该国家有配置 Zone, 需要检查
            String stateName = addressBook.getState();
            if (Strings.isBlank(stateName)) {
                throw BusinessExceptionBuilder.of(1500005, ImmutableMap.of());
            }

            Zone zone = zoneRepository.findByName(stateName);
            if (zone == null) {
                throw BusinessExceptionBuilder.of(1500021);
            }
        }

//        if (Strings.isBlank(addressBook.getState())){
//            throw BusinessExceptionBuilder.of(1500005, ImmutableMap.of());
//        }
//        if (Strings.isBlank(addressBook.getCity())){
//            throw BusinessExceptionBuilder.of(1500006, ImmutableMap.of());
//        }

        if (Strings.isBlank(addressBook.getStreetAddress1())) {
            throw BusinessExceptionBuilder.of(1500008, ImmutableMap.of());
        }
        if (Strings.isBlank(addressBook.getFirstName())) {
            throw BusinessExceptionBuilder.of(1500009, ImmutableMap.of());
        }
        if (Strings.isBlank(addressBook.getMobile())) {
            throw BusinessExceptionBuilder.of(1500012, ImmutableMap.of());
        }
        if (Strings.isBlank(addressBook.getEmailAddress())) {
            throw BusinessExceptionBuilder.of(1500003, ImmutableMap.of());
        }
    }

    /**
     * 移除指定id对应的AddressBook.
     * remark: write by hungcm
     *
     * @param id
     * @return
     */
    public AddressBookDTO remove(Long id) {
        User user = UserUtils.getCurrentUserDetails();
        List<AddressBook> addressBooks = user.getAddressBooks();
        if (Objects.nonNull(addressBooks) && addressBooks.size() > 0) {
            // 判断要删除的AddressBook是否存在
            Optional<AddressBook> optionalAddressBook = addressBookRepository.findById(id);
            if (!optionalAddressBook.isPresent()) {
                throw new BusinessException("AddressBook " + id + " is not exist!");
            }
            // 判断要删除的AddressBook是否属于当前用户
            List<Long> ids = addressBooks.stream().map(AddressBook::getId).collect(Collectors.toList());
            if (!ids.contains(id)) {
                throw new BusinessException("AddressBook " + id + " which you " +
                        "want to delete is not belongs to you!");
            }
            AddressBook addressBook = optionalAddressBook.get();
            // 判断删除的AddressBook是否为默认的AddressBook
            if (addressBook.getSortOrder() == 0) {
                // 如果要刪除的AddressBook是默认的AddressBook，则删除用户的默认AddressBook信息
                user.setDefaultAddressBook(null);
                userRepository.save(user);
            } else {
                /*
                修改不需要删除的AddressBook的sortOrder(将AddressBook的sortOrder大于
                要删除的AddressBook的sortOrder的值减1)
                 */
                Integer sortOrder = addressBook.getSortOrder();
                List<AddressBook> collect = addressBooks.stream()
                        .filter(address -> address.getSortOrder() > sortOrder)
                        .collect(Collectors.toList());
                if (Objects.nonNull(collect) && collect.size() > 0) {
                    collect.forEach(address -> address.setSortOrder(address.getSortOrder() - 1));
                }
            }
//            addressBooks.remove(addressBook);
            addressBooks.removeIf(item -> item != null && item.getId().equals(id));
            userRepository.save(user);
            addressBookRepository.deleteById(id);
            return convertAddressBookToDTO(addressBook);
        }
        return null;
    }

    /**
     * 新增用户的地址信息
     * remark: write by hungcm
     *
     * @param dto
     * @return
     */
    public AddressBookDTO save(MyAddressBookDTO dto) {
        // 获取当前登录的用户的信息
        User user = UserUtils.getCurrentUserDetails();
        // 获取用户的所有地址信息
        List<AddressBook> addressBooks = user.getAddressBooks();
        //校验是否手机号码和邮箱地址是否存在
        if (StringUtils.isEmpty(dto.getEmailAddress()) || StringUtils.isEmpty(dto.getMobile()) || StringUtils.isBlank(dto.getEmailAddress()) || StringUtils.isBlank(dto.getMobile())) {
            throw new BusinessException("The save AddressBook contain EmailAddress is null or Mobile  is null！");
        }
        // 若dto的suburb字段值为空，则赋city字段的值
        if (StringUtils.isEmpty(dto.getSuburb())) {
            dto.setSuburb(dto.getCity());
        }
        if (Boolean.TRUE.equals(dto.getIsDefault())) {
            // sortOrder处理
            processSortOrderWhenAddSetDefault(addressBooks, dto);
            // 修改用户的默认地址
            AddressBookDTO addressBookDTO = saveNew(typeConverter(dto));
            user.setDefaultAddressBook(dtoToEntity(addressBookDTO));
            userRepository.save(user);
            return addressBookDTO;
        } else {
            // sortOrder处理
            if (dto.getSortOrder() <= 0) {
                // 判断传入的sortOrder是否小于0，将sortOrder设置为最大值
                dto.setSortOrder(getMaxSortOrder(addressBooks));
            } else {
                processSortOrderWhenAddNotSetDefault(addressBooks, dto);
            }
            // 持久化要保存的AddressBook信息
            return saveNew(typeConverter(dto));
        }
    }

    public AddressBookDTO saveNew(AddressBookDTO model) {
        this.doCheck(model);
        AddressBook entity = addressBookRepository
                .save(this.dtoToEntity(model));
        model.setCountryName(entity.getCountryName());
        model.setId(entity.getId());
        return model;
    }

    /**
     * 设置默认地址.
     * remark: write by hungcm
     *
     * @param id
     * @return
     */
    public AddressBookDTO setDefaultAddress(Long id) {
        // 判断id对应的AddressBook是否存在
        Optional<AddressBook> optionalAddressBook = addressBookRepository.findById(id);
        if (!optionalAddressBook.isPresent()) {
            throw new BusinessException("AddressBook " + id + " is not exist!");
        }
        // 如果存在，获取其对应信息
        AddressBook addressBook = optionalAddressBook.get();
        // 获取当前登录用户的所有地址信息
        List<AddressBookDTO> dtos = findAllByUserId(UserUtils.getCurrentUserDetails().getId());
        // 判断Addressbook是否属于当前用户
        if (Objects.nonNull(dtos) && dtos.size() > 0) {
            List<Long> collect = dtos.stream().map(AddressBookDTO::getId).collect(Collectors.toList());
            if (!collect.contains(id)) {
                throw new BusinessException("AddressBook " + id + " is not belongs to you!");
            }
        }
        // 判断AddressBook是否已经是当前用户的默认地址
        if (addressBook.getSortOrder() != 0) {
            if (Objects.nonNull(dtos) && dtos.size() > 0) {
                User currentUserDetails = UserUtils.getCurrentUserDetails();
                AddressBook defaultAddressBook = currentUserDetails.getDefaultAddressBook();
                // 判断当前用户的默认地址是否为空
                if (Objects.nonNull(defaultAddressBook)) {
                    List<AddressBookDTO> collect = dtos.stream().filter(dto ->
                            dto.getSortOrder() < addressBook.getSortOrder()
                    ).collect(Collectors.toList());
                    if (Objects.nonNull(collect) && collect.size() > 0) {
                        collect.forEach(address -> {
                            address.setSortOrder(address.getSortOrder() + 1);
                            saveUpdate(address);
                        });
                    }
                    // addressBookRepository.save(addressBook);
                } else {
                    // 获取将会受到影响的AddressBook
                    List<AddressBookDTO> collect = dtos.stream()
                            .filter(address -> address.getSortOrder() > addressBook.getSortOrder())
                            .collect(Collectors.toList());
                    // 保存对其他受影响的AddressBook的修改
                    if (Objects.nonNull(collect) && collect.size() > 0) {
                        collect.forEach(address -> {
                            address.setSortOrder(address.getSortOrder() - 1);
                            saveNew(address);
                        });
                    }
                }
                // 修改id对应的AddressBook的sortOrder为0
                addressBook.setSortOrder(NumberUtils.INTEGER_ZERO);
                AddressBookDTO dto = saveUpdate(convertAddressBookToDTO(addressBook));
                // 更新User中的默认地址
                currentUserDetails.setDefaultAddressBook(dtoToEntity(dto));
                // 持久化User中的更改
                userRepository.save(currentUserDetails);
                return dto;
            }
            throw new BusinessException("No AddressBook can be set to default " +
                    "because you have no AddressBook!");
        }
        // 当前登录用户的地址信息不存在返回一个初始化AddressBook对象
        return convertAddressBookToDTO(addressBook);
    }

    public AddressBook copyForUser(AddressBook old, User user) {
        AddressBook address = new AddressBook();
        address.setCity(old.getCity());
        address.setCompany(old.getCompany());
        address.setCountryCode2(old.getCountryCode2());
        address.setCountryName(old.getCountryName());
        address.setEmailAddress(old.getEmailAddress());
        address.setFirstName(old.getFirstName());
        address.setGender(old.getGender());
        address.setLastName(old.getLastName());
        address.setLocationType(old.getLocationType());
        address.setLocationTypeValue(old.getLocationTypeValue());
        address.setLocationTypeCode(old.getLocationTypeCode());
        address.setPostcode(old.getPostcode());
        address.setSortOrder(old.getSortOrder());
        address.setState(old.getState());
        address.setStateCode(old.getStateCode());
        address.setStreetAddress1(old.getStreetAddress1());
        address.setStreetAddress2(old.getStreetAddress2());
        address.setTelephone(old.getTelephone());
        address.setSuburb(old.getSuburb());
        address.setMobile(old.getMobile());
        address.setUser(user);
        address = addressBookRepository.save(address);
//		AddressBook address = old.clone();  不能用对象个克隆方法，因为没有设置id的方法，将id克隆之后不能删除。也就不能生成新的地址对象并保存到数据库
//		address.getId();
//		address.setUser(user);
        return address;
    }


    private AddressBookDTO typeConverter(MyAddressBookDTO addressBook) {
        AddressBookDTO dto = new AddressBookDTO();
        BeanUtils.copyProperties(addressBook, dto);
        dto.setUserId(UserUtils.getCurrentUserDetails().getId());
        return dto;
    }

    private void processSortOrderWhenAddSetDefault(List<AddressBook> addressBooks,
                                                   MyAddressBookDTO dto) {
        dto.setSortOrder(NumberUtils.INTEGER_ZERO);
        if (Objects.nonNull(addressBooks) && addressBooks.size() > 0) {
            // 判断当前用户是否有默认地址，如果沒有默認地址，則不需要對其他的地址執行任何操作
            long count = addressBooks.stream()
                    .filter(addressBook -> addressBook.getSortOrder() == 0)
                    .count();
            if (count != 0) {
                // 所有的AddressBook的sortOrder值加1，默认sortOrder的值为0
                addressBooks.forEach(addressBook -> {
                    addressBook.setSortOrder(addressBook.getSortOrder() + 1);
                    addressBookRepository.save(addressBook);
                });
            }
        }
    }

    private void processSortOrderWhenAddNotSetDefault(List<AddressBook> addressBooks,
                                                      MyAddressBookDTO dto) {
        if (Objects.nonNull(addressBooks) && addressBooks.size() > 0) {
            // 获取sortOrder大于要添加的AddressBook的sortOrder的所有AddressBook
            List<AddressBook> collect = addressBooks.stream()
                    .filter(addressBook -> addressBook.getSortOrder() >= dto.getSortOrder())
                    .collect(Collectors.toList());
            if (Objects.nonNull(collect) && collect.size() > 0) {
                // 修改上述的所有的AddressBook的sortOrder为先前值加1,并保存到数据库中
                collect.forEach(addressBook -> {
                    addressBook.setSortOrder(addressBook.getSortOrder() + 1);
                    addressBookRepository.save(addressBook);
                });
            } else {
                /*
                如果传入的sortOrder大于当前的最大的sortOrder并且之间有间隔，
                则把传入的sortOrder设置为当前最大值加1，相当于不管传入的sortOrder，
                直接将他设置为最大值加一
                 */
                Integer maxSortOrder = getMaxSortOrder(addressBooks);
                dto.setSortOrder(maxSortOrder);
            }
        } else {
            Integer maxSortOrder = getMaxSortOrder(addressBooks);
            dto.setSortOrder(maxSortOrder);
        }
    }

    private void processSortOrderWhenUpdateNotSetDefault(List<AddressBook> addressBooks,
                                                         AddressBook existAddress,
                                                         MyAddressBookDTO dto) {
        Integer sortOrder = existAddress.getSortOrder();
        Integer dtoSortOrder = dto.getSortOrder();
        // 如果传入的sortOrder不合法，则直接将其设置为最大sortOrder值
        if (dtoSortOrder < 1) {
            dtoSortOrder = getMaxSortOrder(addressBooks);
        }
        if (sortOrder > dtoSortOrder) {
            Integer finalDtoSortOrder = dtoSortOrder;
            List<AddressBook> collect = addressBooks.stream().filter(addressBook ->
                    (addressBook.getSortOrder() >= finalDtoSortOrder) &&
                            (addressBook.getSortOrder() < sortOrder)
            ).collect(Collectors.toList());
            if (Objects.nonNull(collect) && collect.size() > 0) {
                collect.forEach(addressBook -> {
                    addressBook.setSortOrder(addressBook.getSortOrder() + 1);
                    addressBookRepository.save(addressBook);
                });
            }
        } else if (sortOrder < dtoSortOrder) {
            Integer finalDtoSortOrder1 = dtoSortOrder;
            List<AddressBook> collect = addressBooks.stream().filter(addressBook ->
                    (addressBook.getSortOrder() > sortOrder) &&
                            (addressBook.getSortOrder() <= finalDtoSortOrder1)
            ).collect(Collectors.toList());
            if (Objects.nonNull(collect) && collect.size() > 0) {
                collect.forEach(addressBook -> {
                    addressBook.setSortOrder(addressBook.getSortOrder() - 1);
                    addressBookRepository.save(addressBook);
                });
            }
            Integer maxSortOrder = getMaxSortOrder(addressBooks);
            if (dtoSortOrder > maxSortOrder) {
                dto.setSortOrder(maxSortOrder);
            }
        }
    }

    /**
     * 获取用户所有地址里面的{@code sortOrder}的最大值.
     *
     * @param addressBooks
     * @return
     */
    private Integer getMaxSortOrder(List<AddressBook> addressBooks) {
        int sortOrder = NumberUtils.INTEGER_ZERO;
        if (Objects.nonNull(addressBooks) && addressBooks.size() > 0) {
            sortOrder = addressBooks.stream()
                    .mapToInt(AddressBook::getSortOrder)
                    .max()
                    .orElse(NumberUtils.INTEGER_ZERO);
        }
        return sortOrder + 1;
    }

    public List<Long> getByIds(Set<Long> ids) {
        Query query = Query.query(Criteria.where("_id").in(ids));
        query.fields().include("_id", "clazz");
        List<AddressBook> addressBooks = mongoTemplate.find(query, AddressBook.class);
        return addressBooks.stream()
                .map(AddressBook::getId)
                .collect(Collectors.toList());
    }

}
