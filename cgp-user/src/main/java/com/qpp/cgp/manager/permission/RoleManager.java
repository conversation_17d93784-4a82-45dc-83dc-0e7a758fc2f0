package com.qpp.cgp.manager.permission;

import com.qpp.cgp.manager.FilterUtils;

import com.qpp.web.business.manager.CurdManager;
import com.qpp.web.business.message.ExtMessages;
import com.qpp.cgp.domain.dto.permission.RoleDTO;
import com.qpp.cgp.domain.permission.GroupPermission;
import com.qpp.cgp.domain.permission.Permission;
import com.qpp.cgp.domain.permission.Role;
import com.qpp.cgp.domain.user.User;
import com.qpp.cgp.repository.permission.GroupPermissionRepository;
import com.qpp.cgp.repository.permission.RoleRepository;
import com.qpp.cgp.repository.user.UserRepository;
import com.qpp.core.dto.FilterDTO;
import com.qpp.core.dto.PageDTO;
import com.qpp.core.exception.BusinessException;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import org.springframework.stereotype.Service;


import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service

@NeedPermission(resource = "role")
public class RoleManager implements CurdManager<RoleDTO, Long> {

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private UserRepository customerRepository;

    @Autowired
    private GroupPermissionRepository groupRepository;

    @Autowired
    private PermissionManager permissionManager;

    @Override
    public RoleDTO saveNew(RoleDTO dto) {

        this.dtoCheck(dto);
        Role entity = this.dtoTOEntity(dto);
        entity = roleRepository.save(entity);
        if (dto.getPermissionIds() != null && !dto.getPermissionIds().equals("")) {
            String str = dto.getPermissionIds();
            String[] array = str.split(",");
            Long[] list = new Long[array.length];
            for (int i = 0; i < array.length; i++) {
                list[i] = Long.parseLong(array[i]);
            }
            permissionManager.updatePermission(entity.getId(), list);
        }
        dto.setId(entity.getId());

        return dto;
    }

    @Override
    public RoleDTO saveUpdate(RoleDTO dto, Long id) {

        this.dtoCheck(dto);

        roleRepository.save(this.dtoTOEntity(dto));
        if (dto.getPermissionIds() != null) {
            String str = dto.getPermissionIds();
            String[] array = str.split(",");
            Long[] list = new Long[array.length];
            if (str != "") {
                for (int i = 0; i < array.length; i++) {
                    list[i] = Long.parseLong(array[i]);
                }
            } else {
                list = new Long[]{};
            }
            permissionManager.updatePermission(dto.getId(), list);
        }
        return dto;
    }


    @Override
    public void delete(Long id) {
        // 多对多的非关系维护方 需要先手动删除多对多关系
        Role role = roleRepository.findById(id).get();
        for (User customer : role.getUsers()) {
//            customer.getRoles().remove(role);
            if (customer != null) {
                Set<Role> roles = customer.getRoles();
                if (roles != null) {
                    roles.removeIf(item -> item != null && role.getId().equals(item.getId()));
                }
                customerRepository.save(customer);
            }
        }
        role.setUsers(new HashSet<User>());
        roleRepository.save(role);
        roleRepository.deleteById(id);
    }

    @Override
    public RoleDTO findById(Long id) {
        Role entity = roleRepository.findById(id).get();
        RoleDTO dto = this.entityToDTO(entity);
        List<GroupPermission> grouplist = groupRepository.findByRoleId(id);
        List<Permission> permissionlist = entity.getPermissions();
        Set<Long> ids = new HashSet<>();
        for (GroupPermission gPermission : grouplist) {
            if (gPermission != null) {
                ids.add(gPermission.getId());
            }
        }
        for (Permission permission : permissionlist) {
            if (permission != null) {
                ids.add(permission.getId());
            }
        }
        dto.setPermissionIds(StringUtils.join(ids, ","));
        return dto;
    }

    @Override
    public PageDTO<RoleDTO> findAll(Pageable pageRequest,
                                    final List<FilterDTO> filters) {
        if (filters != null) {
            for (FilterDTO filter : filters) {
                if (filter.getName().equals("userId")) {
                    filter.setName("userIds");
                }
            }
        }
        Page<Role> page = roleRepository.findAll(FilterUtils.getQuery(filters) , pageRequest);

        List<RoleDTO> list = new ArrayList<RoleDTO>();

        for (Role entity : page.getContent()) {

            list.add(this.entityToDTO(entity));
        }

        PageDTO<RoleDTO> result = new PageDTO<RoleDTO>(page.getNumber(),
                page.getSize(), list);
        result.setTotalCount(page.getTotalElements());
        result.setTotalPages(page.getTotalPages());

        return result;
    }

    private void dtoCheck(RoleDTO dto) {
        ExtMessages message = new ExtMessages();

        if (dto.getId() == null
                || (!roleRepository.findById(dto.getId()).get().getName()
                .equals(dto.getName()))) {

            if (roleRepository.findByName(dto.getName()) != null) {
                message.addMessage("name", "the name " + dto.getName() + " has been used");
//				message.addMessage("name",
//						this.getMessage(ExtMessages.EXISTMSG, dto.getName()));
            }
        }
        if (message.size() != 0) {
            throw new BusinessException(message.toString());
        }

    }

    public Role dtoTOEntity(RoleDTO dto) {
        Role entity = null;

        if (dto.getId() == null) {
            entity = new Role();
        } else {
            entity = roleRepository.findById(dto.getId()).get();
        }
        entity.setDescription(dto.getDescription());
        entity.setName(dto.getName());
        entity = roleRepository.save(entity);
        return entity;
    }

    public RoleDTO entityToDTO(Role entity) {

        RoleDTO dto = new RoleDTO();
        dto.setDescription(entity.getDescription());
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        return dto;

    }

    public boolean existById(Long roleId) {
        return roleRepository.existsById(roleId);
    }

}
