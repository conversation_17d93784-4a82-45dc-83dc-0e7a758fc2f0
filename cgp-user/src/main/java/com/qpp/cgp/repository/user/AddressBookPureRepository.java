package com.qpp.cgp.repository.user;

import com.qpp.cgp.domain.user.AddressBook;
import com.qpp.cgp.migration.CgpMongoDomainRepository;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
public class AddressBookPureRepository extends CgpMongoDomainRepository<AddressBook, Long> {

    public List<AddressBook> findByUserId(Long id) {
        Criteria criteria = Criteria.where("userId").is(id);
        Query query = new Query();
        query.addCriteria(criteria);
        return find(query);
    }

    public Optional<AddressBook> findByIdAndPartnerId(Long id,Long partnerId){
        Query query = Query.query(Criteria.where("_id").is(id).and("partnerId").is(partnerId));
        List<AddressBook> addressBooks = find(query);
        if (Objects.isNull(addressBooks) || addressBooks.isEmpty()){
            return Optional.empty();
        }
        return Optional.of(addressBooks.get(0));
    }

    public List<AddressBook> findByPartnerId(Long partnerId){
        Query query = Query.query(Criteria.where("partnerId").is(partnerId)).with(Sort.by(Sort.Order.desc("modifiedDate")));
        return find(query);
    }

    public Optional<AddressBook> findByPartnerIdAndIsDefaultTrue(Long partnerId) {
        Query query = Query.query(Criteria.where("partnerId").is(partnerId).and("isDefault").is(true));
        return Optional.ofNullable(
                mongoTemplate.findOne(query, AddressBook.class)
        );
    }

    public void cancelDefaultAddressByPartnerId(Long partnerId){
        Query query = Query.query(Criteria.where("partnerId").is(partnerId).and("isDefault").is(true));
        Update update = new Update().set("isDefault",false);
        mongoTemplate.updateFirst(query, update, AddressBook.class);
    }

    public long countByPartnerId(Long partnerId) {
        Query query = Query.query(Criteria.where("partnerId").is(partnerId));
        return mongoTemplate.count(query, AddressBook.class);
    }

    public AddressBook findByIdWithPartnerId(Long id) {
        Query query = Query.query(Criteria.where("_id").is(id));
        query.fields().include("partnerId");
        return mongoTemplate.findOne(query, AddressBook.class);
    }
}
