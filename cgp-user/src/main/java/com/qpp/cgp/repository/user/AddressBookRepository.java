package com.qpp.cgp.repository.user;

import com.qpp.cgp.domain.user.AddressBook;
import com.qpp.cgp.domain.user.User;
import com.qpp.cgp.migration.CgpMongoDomainRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;


import java.util.List;
import java.util.Optional;

/**
 * Created by smart on 8/24/2017.
 */
@Deprecated
@Service
public class AddressBookRepository extends CgpMongoDomainRepository<AddressBook, Long> {

    public AddressBook findByIdAndUserId(Long id, Long userId) {
        Criteria criteria = Criteria.where(AddressBook.idProperty).is(id).and("userId").is(userId);
        AddressBook addressBook = mongoTemplate.findOne(Query.query(criteria), tClass);
        return fillData(addressBook);
    }

    public List<AddressBook> findByUserIdOrderBySortOrderAsc(Long id) {
        Criteria criteria = Criteria.where("userId").is(id);
        Query query = new Query();
        query.addCriteria(criteria);
        query.with(Sort.by(Sort.Direction.ASC, "sortOrder"));
        return find(query);
    }

    @Autowired
    private UserPureRepository userPureRepository;

    @Override
    public <S extends AddressBook> S fillData(S entity) {
        if (entity != null) {
            Long userId = entity.getUserId();
            if (userId != null) {
                Optional<User> user = userPureRepository.findById(userId);
                user.ifPresent(entity::setUser);
            }
        }
        return super.fillData(entity);
    }
}
