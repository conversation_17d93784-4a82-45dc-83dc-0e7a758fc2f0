package com.qpp.cgp.repository.permission.cascade;

import com.qpp.cgp.domain.permission.*;
import com.qpp.cgp.repository.permission.OperationPureRepository;
import com.qpp.cgp.repository.permission.PermissionPureRepository;
import com.qpp.cgp.repository.permission.ResourcePureRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class PermissionWriteCascadeTrigger {

    @Autowired
    private OperationPureRepository operationPureRepository;

    @Autowired
    private ResourcePureRepository resourcePureRepository;

    @Autowired
    private PermissionPureRepository permissionPureRepository;

    private void updateGroupPermissionRemove(GroupPermission groupPermission) {
        if (groupPermission != null) {
            Long groupId = groupPermission.getId();
            List<Long> permissionIds = groupPermission.getPermissionIds();

            MongoTemplate permissionMongoTemplate = permissionPureRepository.getMongoTemplate();

            // update remove
            Criteria criteria = new Criteria();
            criteria = criteria.andOperator(
                    Criteria.where("permissionIds").is(groupId),
                    Criteria.where(GroupPermission.idProperty).nin(permissionIds),
                    Criteria.where("clazz").is(GroupPermission.class.getName())
            );
            List<GroupPermission> groupPermissions = permissionMongoTemplate.find(Query.query(criteria), GroupPermission.class);
            for (GroupPermission permission : groupPermissions) {
                List<Long> permissionIdList = permission.getPermissionIds();
                if (permissionIdList != null) {
                    permissionIdList.removeIf(aLong -> aLong != null && aLong.equals(groupId));
                }
            }

        }
    }

    /**
     * 修改groupPermission的permissionIds
     * ，若groupPermission的originalPermissionIds存在groupPermission的话
     * ，则对存在的groupPermission的permissionIds也进行修改
     */
    public void updateGroupPermissionSave(GroupPermission groupPermission) {
        if (groupPermission != null) {
            // update remove
            updateGroupPermissionRemove(groupPermission);

            Long groupId = groupPermission.getId();
            List<Long> permissionIds = groupPermission.getPermissionIds();

            MongoTemplate permissionMongoTemplate = permissionPureRepository.getMongoTemplate();

            // update add
            Criteria criteria = Criteria.where(GroupPermission.idProperty).in(permissionIds).and("clazz").is(GroupPermission.class.getName());
            List<GroupPermission> groupPermissionList = permissionMongoTemplate.find(Query.query(criteria), GroupPermission.class);

            for (GroupPermission permission : groupPermissionList) {
                List<Long> permissionIdList = permission.getPermissionIds();
                if (permissionIdList == null) {
                    permissionIdList = new ArrayList<>();
                }
                permissionIdList.add(groupId);
                permission.setPermissionIds(permissionIdList);
                permissionMongoTemplate.save(permission);
            }
        }
    }

    public void cascadeSave(Permission permission) {

        if (permission != null) {
            if (permission instanceof AtomPermission) {
                Operation operation = ((AtomPermission) permission).getOperation();
                if (operation != null) {
                    operationPureRepository.save(operation);
                }
                Resource resource = ((AtomPermission) permission).getResource();
                if (resource != null) {
                    resourcePureRepository.save(resource);
                }
            } else if (permission instanceof GroupPermission) {
                updateGroupPermissionSave((GroupPermission) permission);
            }
        }
    }

    public void cascadeDelete(Permission permission) {

        if (permission != null) {
            if (permission instanceof GroupPermission) {
                updateGroupPermissionRemove((GroupPermission) permission);
            }
        }
    }
}
