package com.qpp.cgp.repository.permission;

import com.qpp.cgp.domain.permission.AtomPermission;
import com.qpp.cgp.domain.permission.Operation;
import com.qpp.cgp.domain.permission.Resource;
import com.qpp.cgp.migration.CgpMongoDomainRepository;
import com.qpp.cgp.repository.permission.cascade.AtomPermissionFiller;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Deprecated
@Service
public class AtomPermissionRepository extends CgpMongoDomainRepository<AtomPermission, Long> {

    // 该repository仅两个自定义方法被调用，故其他查询方法不重写加过滤条件
    public List<AtomPermission> findByResource(Resource resource) {
        Criteria criteria = Criteria.where("resourceId").is(resource != null ? resource.getId() : null).and("clazz").is(AtomPermission.class.getName());
        return find(Query.query(criteria));
    }

    public List<AtomPermission> findByResourceAndOperationMaskLessThan(Resource resource, int mask) {

        Criteria criteria = Criteria.where("resourceId").is(resource != null ? resource.getId() : null).and("clazz").is(AtomPermission.class.getName());
        List<AtomPermission> atomPermissionList = mongoTemplate.find(Query.query(criteria), tClass);

        atomPermissionList.stream().filter(Objects::nonNull)
                .filter(atomPermission -> {
                    Long operationId = atomPermission.getOperationId();
                    if (operationId != null) {
                        Optional<Operation> optionalOperation = operationPureRepository.findById(operationId);
                        if (optionalOperation.isPresent()) {
                            Operation operation = optionalOperation.get();
                            int mask1 = operation.getMask();
                            if (mask1 < mask)
                                return true;
                        }
                    }
                    return false;
                })
                .forEach(this::fillData);

        return atomPermissionList;
    }

    @Autowired
    private OperationPureRepository operationPureRepository;

    @Autowired
    private AtomPermissionFiller atomPermissionFiller;

    @Override
    public <S extends AtomPermission> S fillData(S entity) {
        if (entity != null) {
            atomPermissionFiller.fill(entity);
        }
        return super.fillData(entity);
    }

    // 以下继承超类的方法均未被使用，故不重写
    @Deprecated
    @Override
    public <S extends AtomPermission> S save(S entity) {
        return super.save(entity);
    }

    @Deprecated
    @Override
    public <S extends AtomPermission> List<S> saveAll(Iterable<S> entites) {
        return super.saveAll(entites);
    }

    @Deprecated
    @Override
    public void deleteById(Long aLong) {
        super.deleteById(aLong);
    }

    @Deprecated
    @Override
    public void delete(AtomPermission entity) {
        super.delete(entity);
    }

    @Deprecated
    @Override
    public void deleteAll(Iterable<? extends AtomPermission> entities) {
        super.deleteAll(entities);
    }
}
