package com.qpp.cgp.repository.permission;

import com.qpp.cgp.domain.permission.Operation;
import com.qpp.cgp.domain.permission.Resource;
import com.qpp.cgp.migration.CgpMongoDomainRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Deprecated
@Service
public class OperationRepository extends CgpMongoDomainRepository<Operation, Long> {

    @Autowired
    private ResourcePureRepository resourcePureRepository;

    @Override
    public <S extends Operation> S fillData(S entity) {
        if (entity != null) {
            Long ownerId = entity.getOwnerId();
            if (ownerId != null) {
                Optional<Resource> optionalResource = resourcePureRepository.findById(ownerId);
                optionalResource.ifPresent(entity::setOwner);
            }
        }
        return super.fillData(entity);
    }
}
