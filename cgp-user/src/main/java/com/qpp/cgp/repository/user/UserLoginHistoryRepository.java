package com.qpp.cgp.repository.user;

import com.qpp.cgp.domain.user.User;
import com.qpp.cgp.domain.user.UserLoginHistory;
import com.qpp.cgp.migration.CgpMongoDomainRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.util.Optional;

@Deprecated
@Service
public class UserLoginHistoryRepository extends CgpMongoDomainRepository<UserLoginHistory, Long> {

    @Autowired
    private UserRepository userRepository;

    @Override
    public <S extends UserLoginHistory> S fillData(S entity) {
        Long userId = entity.getUserId();
        if (userId != null) {
            Optional<User> optionalUser = userRepository.findById(userId);
            optionalUser.ifPresent(entity::setUser);
        }
        return super.fillData(entity);
    }
}
