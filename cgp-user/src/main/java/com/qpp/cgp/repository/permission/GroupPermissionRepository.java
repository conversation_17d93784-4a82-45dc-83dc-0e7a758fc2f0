package com.qpp.cgp.repository.permission;

import com.qpp.cgp.domain.permission.AtomPermission;
import com.qpp.cgp.domain.permission.GroupPermission;
import com.qpp.cgp.domain.permission.Permission;
import com.qpp.cgp.migration.CgpMongoDomainRepository;
import com.qpp.cgp.repository.permission.cascade.AtomPermissionFiller;
import com.qpp.cgp.repository.permission.cascade.GroupPermissionFiller;
import com.qpp.cgp.repository.permission.cascade.PermissionWriteCascadeTrigger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;


import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Deprecated
@Service
public class GroupPermissionRepository extends CgpMongoDomainRepository<GroupPermission, Long> {

    private Criteria andGroupPermissionCriteria(Criteria criteria) {
        Criteria andCriteria = new Criteria();
        return andCriteria.andOperator(criteria, Criteria.where("clazz").is(GroupPermission.class.getName()));
    }

    //根据roleid 找到这个role拥有的权限组
//    @Query("select g from GroupPermission g left join g.roles r where r.id =:roleId")
    public List<GroupPermission> findByRoleId(Long roleId) {
        Criteria criteria = Criteria.where("roleIds").is(roleId);
        criteria = andGroupPermissionCriteria(criteria);
        return find(org.springframework.data.mongodb.core.query.Query.query(criteria));
    }

    public GroupPermission findByName(String name) {

        Criteria criteria = Criteria.where("name").is(name);
        criteria = andGroupPermissionCriteria(criteria);
        GroupPermission groupPermission = mongoTemplate.findOne(org.springframework.data.mongodb.core.query.Query.query(criteria), tClass);
        return fillData(groupPermission);
    }

    //根据id 找到这个组的所有权限（权限和权限组）
//    @Query("select p from GroupPermission g left join g.permissions p where  g.id = :groupId")
    public List<Permission> findPermissionsById(Long groupId) {
        List<Permission> result = new ArrayList<>();

        // 获取groupId对应的groupPermission
        Criteria criteria = Criteria.where(Permission.idProperty).is(groupId);
        criteria = andGroupPermissionCriteria(criteria);
        GroupPermission groupPermission = mongoTemplate.findOne(org.springframework.data.mongodb.core.query.Query.query(criteria), tClass);
        // 返回该groupPermission的permissions
        if (groupPermission != null) {
            List<Long> permissionIds = groupPermission.getPermissionIds();
            if (permissionIds != null) {
                result = mongoTemplate.find(org.springframework.data.mongodb.core.query.Query.query(Criteria.where(Permission.idProperty).in(permissionIds)), Permission.class)
                        .stream().map(this::fillPermission).collect(Collectors.toList());
            }
        }

         return result;
    }

//    @Query("select g from GroupPermission g left join g.permissions p where p.id = :groupId")
    public List<GroupPermission> findByGroupPermissionId(Long groupId) {

        Criteria criteria = Criteria.where("permissionIds").is(groupId);
        criteria = andGroupPermissionCriteria(criteria);
        return find(org.springframework.data.mongodb.core.query.Query.query(criteria));
   }

    @Autowired
    private GroupPermissionFiller groupPermissionFiller;

    @Override
    public <S extends GroupPermission> S fillData(S entity) {
        if (entity != null) {
            groupPermissionFiller.fill(entity);
        }
        return super.fillData(entity);
    }

    @Autowired
    private AtomPermissionFiller atomPermissionFiller;

    private Permission fillPermission(Permission entity) {
        if (entity != null) {
            if (entity instanceof AtomPermission) {
                atomPermissionFiller.fill(((AtomPermission) entity));
            } else if (entity instanceof GroupPermission) {
                groupPermissionFiller.fill(((GroupPermission) entity));
            }
        }

        return entity;
    }

    @Autowired
    private PermissionWriteCascadeTrigger permissionWriteCascadeTrigger;

    @Override
    public <S extends GroupPermission> S save(S entity) {
        if (entity != null) {
            fillIdIfNotExists(entity);

            entity.setPermissions(entity.getPermissions());
            entity.setRoles(entity.getRoles());

            permissionWriteCascadeTrigger.cascadeSave(entity);

            mongoTemplate.save(entity);
        }
        return fillData(entity);
    }

    @Override
    public <S extends GroupPermission> List<S> saveAll(Iterable<S> entites) {
        return super.saveAll(entites);
    }

    @Deprecated
    @Override
    public void delete(GroupPermission entity) {
        super.delete(entity);
    }

    @Override
    public void deleteById(Long aLong) {
        Criteria criteria = Criteria.where(GroupPermission.idProperty).is(aLong);
        criteria = andGroupPermissionCriteria(criteria);
        GroupPermission groupPermission = mongoTemplate.findOne(Query.query(criteria), tClass);

        if (groupPermission != null) {
            permissionWriteCascadeTrigger.cascadeDelete(groupPermission);
            mongoTemplate.remove(groupPermission);
        }
    }

    @Deprecated
    @Override
    public void deleteAll(Iterable<? extends GroupPermission> entities) {
        super.deleteAll(entities);
    }

    @Override
    public Page<GroupPermission> findAll(Query query, Pageable pageable) {
        query.addCriteria(Criteria.where("clazz").is(GroupPermission.class.getName()));
        return super.findAll(query, pageable);
    }

    @Override
    public Optional<GroupPermission> findById(Long aLong) {
        Criteria criteria = Criteria.where(GroupPermission.idProperty).is(aLong);
        criteria = andGroupPermissionCriteria(criteria);
        GroupPermission groupPermission = mongoTemplate.findOne(Query.query(criteria), tClass);

        if (groupPermission != null) {
            fillData(groupPermission);
        }
        return Optional.ofNullable(groupPermission);
    }

    @Override
    public GroupPermission getOne(Long aLong) {
        Criteria criteria = Criteria.where(GroupPermission.idProperty).is(aLong);
        criteria = andGroupPermissionCriteria(criteria);
        GroupPermission groupPermission = mongoTemplate.findOne(Query.query(criteria), tClass);

        if (groupPermission != null) {
            fillData(groupPermission);
        }
        return groupPermission;
    }

    @Override
    public List<GroupPermission> findAll() {
        return find(Query.query(Criteria.where("clazz").is(GroupPermission.class.getName())));
    }
}
