package com.qpp.cgp.repository.permission;

import com.qpp.cgp.domain.permission.Operation;
import com.qpp.cgp.domain.permission.Resource;
import com.qpp.cgp.migration.CgpMongoDomainRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;


import java.util.List;

@Deprecated
@Service
public class ResourceRepository extends CgpMongoDomainRepository<Resource, Long> {

    @Autowired
    private OperationPureRepository operationPureRepository;

    @Override
    public <S extends Resource> S fillData(S entity) {
        if (entity != null) {
            Long resourceId = entity.getId();
            if (resourceId != null) {
                List<Operation> operations = operationPureRepository.find(Query.query(Criteria.where("ownerId").is(resourceId)));
                entity.setOperations(operations);
            }
        }
        return super.fillData(entity);
    }
}
