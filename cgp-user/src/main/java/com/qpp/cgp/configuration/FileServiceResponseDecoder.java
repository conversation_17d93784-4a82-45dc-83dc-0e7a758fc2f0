package com.qpp.cgp.configuration;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qpp.cgp.service.parser.pdf.PdfParseResponse;
import com.qpp.core.exception.BusinessException;
import feign.FeignException;
import feign.Response;
import feign.codec.DecodeException;
import feign.codec.Decoder;
import org.springframework.cloud.openfeign.support.ResponseEntityDecoder;

import java.io.IOException;
import java.io.PushbackInputStream;
import java.lang.reflect.Type;

public class FileServiceResponseDecoder extends ResponseEntityDecoder {

    private ObjectMapper objectMapper;

    public FileServiceResponseDecoder(Decoder decoder, ObjectMapper objectMapper) {
        super(decoder);
        this.objectMapper = objectMapper;
    }

    @Override
    public PdfParseResponse decode(Response response, Type type) throws IOException, DecodeException, FeignException {
        String url = response.request().url();
        int status = response.status();
        // 获取body
        final PushbackInputStream pushbackInputStream = new PushbackInputStream(response.body().asInputStream());
        final JsonNode bodyTree = objectMapper.readTree(pushbackInputStream);

        pushbackInputStream.close();

        // success为true
        if (bodyTree.at("/success").asBoolean()) {
            String json = bodyTree.toString();
            PdfParseResponse data = objectMapper.readValue(json, PdfParseResponse.class);

            return data;
        } else {
            // success为false
            final String message = bodyTree.at("/message").asText();
            throw new BusinessException("Fail to access to remote server: "
                    + url + "! status: " + status + ", error message: " + message + ", data: " + bodyTree);
        }
    }

}
