package com.qpp.cgp.domain.user;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.cgp.migration.CgpMongoDomain;
import io.swagger.annotations.ApiModelProperty;
import jodd.util.StringUtil;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@JsonTypeInfo(use = JsonTypeInfo.Id.NONE)
@ConfigDomain
@Document(collection = "addressbooks")
public class AddressBook extends CgpMongoDomain {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "国家代号")
    private String countryCode2;

    @ApiModelProperty(value = "国家名称")
    private String countryName;

    @ApiModelProperty(value = "地区代号")
    private String stateCode;

    @ApiModelProperty(value = "地区", required = true)
    private String state;

    @ApiModelProperty(value = "城市", required = true)
    private String city;

    @ApiModelProperty(value = "郊区地址")
    private String suburb;

    @ApiModelProperty(value = "地址1", required = true)
    private String streetAddress1;

    @ApiModelProperty(value = "地址2")
    private String streetAddress2;

    @ApiModelProperty(value = "邮政编码")
    private String postcode;

    @ApiModelProperty(value = "名", required = true)
    private String firstName;

    @ApiModelProperty(value = "姓", required = true)
    private String lastName;

    @ApiModelProperty(value = "性别", required = true)
    private Character gender;

    @ApiModelProperty(value = "电话", required = true)
    private String telephone;

    @ApiModelProperty(value = "移动电话")
    private String mobile;

    @ApiModelProperty(value = "邮箱地址")
    private String emailAddress;

    @ApiModelProperty(value = "公司")
    private String company;

    @ApiModelProperty(value = "地址类型")
    private String locationType;

    @Getter
    @Setter
    @ApiModelProperty(value = "地址类型(同locationType)")
    private String locationTypeValue;

    @Getter
    @Setter
    @ApiModelProperty(value = "地址类型代号")
    private String locationTypeCode;

    @ApiModelProperty(value = "序号")
    private Integer sortOrder = 1;

    @ApiModelProperty(value = "标签")
    @Getter
    @Setter
    private List<String> label;

    @ApiModelProperty(value = "序号")
    @Getter
    @Setter
    private Long userId;

    @ApiModelProperty(value = "合作商编号")
    @Getter
    @Setter
    private Long partnerId;

    @ApiModelProperty(value = "是否是默认地址")
    @Getter
    @Setter
    private Boolean isDefault;

    @Transient
    @JsonIgnore
    private User user;

    public String getCountryCode2() {
        return countryCode2;
    }

    public void setCountryCode2(String countryCode2) {
        this.countryCode2 = countryCode2;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public String getStateCode() {
        return stateCode;
    }

    public void setStateCode(String stateCode) {
        this.stateCode = stateCode;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getSuburb() {
        return suburb;
    }

    public void setSuburb(String suburb) {
        this.suburb = suburb;
    }

    public String getStreetAddress1() {
        return streetAddress1;
    }

    public void setStreetAddress1(String streetAddress1) {
        this.streetAddress1 = streetAddress1;
    }

    public String getStreetAddress2() {
        return streetAddress2;
    }

    public void setStreetAddress2(String streetAddress2) {
        this.streetAddress2 = streetAddress2;
    }

    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public Character getGender() {
        return gender;
    }

    public void setGender(Character gender) {
        this.gender = gender;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getLocationType() {
        return locationType;
    }

    public void setLocationType(String locationType) {
        this.locationType = locationType;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(Long partnerId) {
        this.partnerId = partnerId;
    }

    public Boolean getDefault() {
        return isDefault;
    }

    public void setDefault(Boolean aDefault) {
        isDefault = aDefault;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
        this.userId = user != null ? user.getId() : null;
    }

    public void setLocationTypeCode(String locationTypeCode) {
        if (StringUtil.isBlank(this.locationType)) {
            this.locationType = locationTypeCode;
        }
        this.locationTypeCode = locationTypeCode;
    }

    @Override
    public String toString() {
        return "AddressBook{" +
                "countryCode2='" + countryCode2 + '\'' +
                ", countryName='" + countryName + '\'' +
                ", stateCode='" + stateCode + '\'' +
                ", state='" + state + '\'' +
                ", city='" + city + '\'' +
                ", suburb='" + suburb + '\'' +
                ", streetAddress1='" + streetAddress1 + '\'' +
                ", streetAddress2='" + streetAddress2 + '\'' +
                ", postcode='" + postcode + '\'' +
                ", firstName='" + firstName + '\'' +
                ", lastName='" + lastName + '\'' +
                ", gender=" + gender +
                ", telephone='" + telephone + '\'' +
                ", mobile='" + mobile + '\'' +
                ", emailAddress='" + emailAddress + '\'' +
                ", company='" + company + '\'' +
                ", locationType='" + locationType + '\'' +
                ", sortOrder=" + sortOrder +
                ", userId=" + userId +
                ", partnerId=" + partnerId +
                ", isDefault=" + isDefault +
                '}';
    }
}
