package com.qpp.cgp.domain.user.config;

import com.qpp.mongo.domain.MongoDomain;
import com.qpp.cgp.domain.dto.user.TokenTemplateConfig;
import com.qpp.cgp.domain.partner.config.VerifyNotificationConfig;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date 2018/1/30
 */
@NoArgsConstructor
@Data
@Document(collection = "passwordforgetconfigs")
public class PasswordForgetConfig extends MongoDomain {

    private Long websiteId;

    /**
     * 验证码配置
     */
    private Boolean needVerificationCode = false;

    /**
     * token模板配置
     */
    private TokenTemplateConfig tokenTemplateConfig;

    /**
     * 提醒方式配置
     */
    private VerifyNotificationConfig verifyNotificationConfig;

    {
        setClazz(this.getClass().getName());
    }
}
