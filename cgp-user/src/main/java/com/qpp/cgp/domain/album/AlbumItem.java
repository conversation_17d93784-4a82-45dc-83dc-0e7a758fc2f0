package com.qpp.cgp.domain.album;


import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.qpp.cgp.config.domaintype.RuntimeDomain;
import com.qpp.cgp.domain.upload.UploadFile;
import com.qpp.cgp.migration.CgpMongoDomain;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

@JsonTypeInfo(use = JsonTypeInfo.Id.NONE)
@RuntimeDomain
@Document(collection = "albumitems")
public class AlbumItem extends CgpMongoDomain {

    private static final long serialVersionUID = 1L;

    private String tags;

    @Transient
//    @ManyToOne(cascade = CascadeType.REFRESH, optional = false)
//    @JoinColumn(name = "album_id", nullable = false)
    private Album album;

    @Getter@Setter
    private long albumId;

    @Transient
//    @ManyToOne(cascade = CascadeType.REFRESH, optional = true)
//    @JoinColumn(name = "upload_file_id", nullable = true)
    private UploadFile uploadFile;

    @Getter@Setter
    private long uploadFileId;

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public Album getAlbum() {
        return album;
    }

    public void setAlbum(Album album) {
        this.album = album;
        if (album != null) {
            this.albumId = album.getId();
        }
    }

    public UploadFile getUploadFile() {
        return uploadFile;
    }

    public void setUploadFile(UploadFile uploadFile) {
        this.uploadFile = uploadFile;
        if (uploadFile != null) {
            this.uploadFileId = uploadFile.getId();
        }
    }
}
