package com.qpp.cgp.domain.user;

import com.qpp.mongo.domain.LongMongoDomain;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date 2024/6/6
 */
@Getter
@Setter
@ApiModel("地址类型配置")
@Document(collection = "locationtypeconfigs")
public class LocationTypeConfig extends LongMongoDomain {

    private String code;

    private String value;
}
