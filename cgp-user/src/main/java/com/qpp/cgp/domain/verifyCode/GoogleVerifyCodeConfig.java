package com.qpp.cgp.domain.verifyCode;

import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.cgp.manager.application.ApplicationMode;
import com.qpp.mongo.domain.LongMongoDomain;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * @className: GoogleVerifyCodeConfig
 * @description:
 * @author: TT-Berg
 * @date: 2023/8/11
 **/
@ConfigDomain
@Data
@Document("google_verify_code_configs")
public class GoogleVerifyCodeConfig extends LongMongoDomain {

    private String googleBackendSecret;

    private String googleRequestUrl;

    private Proxy proxy;

    private boolean isNeedProxy = false;

    private double humanMachineAuthSuccessThreshold = 0.7;

    private ApplicationMode mode;

    private int connectTimeout = -1;

    private int readTimeout = -1;

    @Data
    public static class Proxy {
        private String address;

        private int port;
    }
}
