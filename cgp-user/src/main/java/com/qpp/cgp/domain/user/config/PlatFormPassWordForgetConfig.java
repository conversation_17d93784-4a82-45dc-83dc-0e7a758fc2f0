package com.qpp.cgp.domain.user.config;

import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.cgp.domain.dto.user.TokenTemplateConfig;
import com.qpp.cgp.domain.mail.MailTemplateConfig;
import com.qpp.cgp.manager.application.ApplicationMode;
import com.qpp.core.utils.Platform;
import com.qpp.mongo.domain.LongMongoDomain;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * @className: PlatFormPassWordForgetConfig
 * @description:
 * @author: TT-Berg
 * @date: 2023/5/12
 **/
@Data
@ConfigDomain
@Document("platform_password_forget_configs")
public class PlatFormPassWordForgetConfig extends LongMongoDomain {

    private Platform platform;

    /**
     * token模板配置
     */
    private TokenTemplateConfig tokenTemplateConfig;

    /**
     * 邮件模板
     */
    private MailTemplateConfig mailTemplateConfig;

    /**
     * 重置密码URL
     */
    private String url;

    private ApplicationMode mode;
}
