package com.qpp.cgp.domain.permission;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.qpp.cgp.migration.CgpMongoDomain;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CollectionId;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Type;
import org.springframework.data.annotation.Transient;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class GroupPermission extends Permission {

    private static final long serialVersionUID = 1L;

    @JsonIgnore
    @Transient
//    @ManyToMany(cascade = CascadeType.REFRESH)
//    @JoinTable(name = "cgp_auth_group_permission",
//            joinColumns = @JoinColumn(name = "group_id"),
//            inverseJoinColumns = @JoinColumn(name = "permission_id"))
//    @CollectionId(type = @Type(type = "long"), columns = @Column(name = "id"), generator = "cgp_id_generator")
    private List<Permission> permissions = new ArrayList<>();

    @Getter@Setter
    private List<Long> permissionIds = new ArrayList<>();

    public List<Permission> getPermissions() {
        return permissions;
    }

    public void setPermissions(List<Permission> permissions) {
        this.permissions = permissions;
        this.permissionIds = permissions != null ? permissions.stream().map(CgpMongoDomain::getId).collect(Collectors.toList()) : null;
    }

}
