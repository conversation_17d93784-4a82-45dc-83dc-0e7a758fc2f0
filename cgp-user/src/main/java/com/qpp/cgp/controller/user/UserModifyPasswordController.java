package com.qpp.cgp.controller.user;

import com.qpp.cgp.domain.dto.user.ModifyPasswordDTO;
import com.qpp.cgp.manager.user.UserModifyPasswordManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2018/2/6
 */
@RestController
@RequestMapping(value = "api/users/passwords", produces = MediaType.APPLICATION_JSON_VALUE)
public class UserModifyPasswordController {

    @Autowired
    private UserModifyPasswordManager userModifyPasswordManager;

    @RequestMapping(method = RequestMethod.PUT)
    public void modifyPassword(@RequestBody ModifyPasswordDTO modifyPasswordDTO) {
        userModifyPasswordManager.modifyPassword(modifyPasswordDTO);
    }

}
