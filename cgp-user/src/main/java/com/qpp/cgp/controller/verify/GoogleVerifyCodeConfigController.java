package com.qpp.cgp.controller.verify;

import com.qpp.cgp.domain.verifyCode.GoogleVerifyCodeConfig;
import com.qpp.cgp.manager.verifycode.GoogleVerifyCodeConfigManager;
import com.qpp.web.business.controller.AbstractJsonRestController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @className: GoogleVerifyCodeConfigController
 * @description:
 * @author: TT-Berg
 * @date: 2023/8/11
 **/
@RestController
@RequestMapping("api/googleVerifyCodeConfigs")
public class GoogleVerifyCodeConfigController extends AbstractJsonRestController<GoogleVerifyCodeConfig, Long, GoogleVerifyCodeConfigManager> {

    public GoogleVerifyCodeConfigController(GoogleVerifyCodeConfigManager manager) {
        super(manager);
    }

}
