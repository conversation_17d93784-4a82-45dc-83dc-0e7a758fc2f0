package com.qpp.cgp.controller.user;

import com.qpp.cgp.domain.dto.user.UserDTO;
import com.qpp.cgp.manager.user.UserManager;
import com.qpp.cgp.repository.user.UserRepository;
import com.qpp.core.dto.ObjectDTO;
import com.qpp.core.utils.SecurityUtils;
import com.qpp.operation.log.OperationLog;
import com.qpp.operation.log.OperationLogModule;
import com.qpp.operation.log.OperationLogTag;
import com.qpp.web.business.controller.AbstractJsonRestController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@OperationLogModule("User")
@Controller
@RequestMapping(value = "api/users")
public class UserRestController extends AbstractJsonRestController<UserDTO, Long, UserManager> {

    @Autowired
    public UserRestController(UserManager manager) {
        super(manager);
    }

    @RequestMapping(value = "email", produces = MediaType.APPLICATION_JSON_VALUE, method = RequestMethod.GET)
    @ResponseBody
    public String getEmail() {
        String email = this.manager.findById(SecurityUtils.getLoginedInUserId()).getEmail();
        return email;
    }

    @RequestMapping(value = "/containsRole", produces = MediaType.APPLICATION_JSON_VALUE, method = RequestMethod.PUT)
    @ResponseBody
    public Boolean checkRoleContains(@RequestParam Long[] ids) {
        boolean own = manager.checkRoleContains(ids);
        return null;
    }

    @RequestMapping(value = "many", method = RequestMethod.GET)
    @ResponseBody
    public List<UserDTO> getCustomers(@RequestParam Long[] userIds) {
        return this.manager.getSameCustomer(userIds);
    }

    @OperationLog(description = "Reset password", level = OperationLog.Level.WARN,
            operator = OperationLog.Operation.UPDATE,
            tags = @OperationLogTag(key = "userId", value = "{#params['id']}"))
    @RequestMapping(value = "/{id}/resetPassword", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean restPassword(@PathVariable Long id, @RequestBody ObjectDTO dto) {
        this.manager.changePassword(id, dto.getString("newPassword"));
        return null;
    }

    @OperationLog(description = "Reset password", level = OperationLog.Level.WARN,
            operator = OperationLog.Operation.UPDATE)
    @RequestMapping(value = "password", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean restPassword(@RequestBody ObjectDTO dto) {
        this.manager.changePassword(dto.getString("currentPassword"), dto.getString("newPassword"));
        return null;
    }

    /**
     * 获取当前登录的用户信息
     */
    @RequestMapping(value = "/loginUser", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public UserDTO getLoginUser() {
        return this.manager.getLoginUser();
    }

    @Autowired
    private UserRepository userRepository;

    @GetMapping(value = "/checkUserId/{userId}", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Boolean checkExistsUserId(@PathVariable Long userId) {
        return userRepository.existsById(userId);
    }

}
