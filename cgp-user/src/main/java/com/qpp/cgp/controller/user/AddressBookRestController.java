package com.qpp.cgp.controller.user;

import com.qpp.cgp.domain.dto.user.AddressBookDTO;
import com.qpp.cgp.domain.user.AddressBook;
import com.qpp.cgp.manager.user.AddressBookManager;
import com.qpp.cgp.repository.user.AddressBookRepository;
import com.qpp.core.dto.FilterDTO;
import com.qpp.core.dto.ObjectDTO;
import com.qpp.core.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;

import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;


@Controller
@RequestMapping("api/addressBooks")
public class AddressBookRestController {

    @Autowired
    private AddressBookManager manager;

    @Autowired
    private AddressBookRepository repository;

    @RequestMapping(value = "{id}", method = RequestMethod.GET, produces = {MediaType.APPLICATION_JSON_VALUE})
    @ResponseBody
    
    public ObjectDTO getById(@PathVariable Long id) {

        AddressBook ab = repository.findById(id).get();
        if (ab == null) {
            throw new BusinessException("AddressBook " + id + " is not exists!");
        }

        ObjectDTO dto = convertAddressBookToObjectDTO(ab, new ObjectDTO());

        return dto;
    }

    /**
     * 負責將實體對象轉換爲DTO對象
     *
     * @param address
     * @param dto
     * @return
     */
    private ObjectDTO convertAddressBookToObjectDTO(AddressBook address, ObjectDTO dto) {

        dto.put("id", address.getId());
        dto.put("gender", address.getGender());
        dto.put("firstName", address.getFirstName());
        dto.put("lastName", address.getLastName());
        dto.put("state", address.getState());
        dto.put("stateCode", address.getStateCode());
        dto.put("city", address.getCity());
        dto.put("postcode", address.getPostcode());
        dto.put("suburb", address.getSuburb());
        dto.put("streetAddress1", address.getStreetAddress1());
        dto.put("streetAddress2", address.getStreetAddress2());
        dto.put("locationType", address.getLocationType());
        dto.put("locationTypeValue", address.getLocationTypeValue());
        dto.put("locationTypeCode", address.getLocationTypeCode());
        dto.put("telephone", address.getTelephone());
        dto.put("emailAddress", address.getEmailAddress());
        dto.put("company", address.getCompany());
        dto.put("countryCode2", address.getCountryCode2());
        dto.put("countryName", address.getCountryName());
        dto.put("mobile", address.getMobile());
        dto.put("sortOrder", address.getSortOrder());

        return dto;
    }

    /**
     * 创建一个地址
     *
     * @param model
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, produces = {MediaType.APPLICATION_JSON_VALUE}, consumes = {MediaType.APPLICATION_JSON_VALUE})
    @ResponseBody
    public AddressBookDTO create(@RequestBody AddressBookDTO model) {
        model = this.manager.saveNew(model);
        return model;
    }

    /**
     * 根据输入的对象信息，修改指定id的实体对象
     *
     * @param id
     * @param model
     * @return SingleResponse
     */
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = {MediaType.APPLICATION_JSON_VALUE}, consumes = {MediaType.APPLICATION_JSON_VALUE})
    @ResponseBody
    public AddressBookDTO update(@PathVariable Long id, @RequestBody AddressBookDTO model) {

        AddressBookDTO data = this.manager.saveUpdate(model);
        return data;

    }

    /**
     * 根据指定的UserId，获取该用户的所有 地址薄
     *
     * @param userId
     * @return SingleResponse
     * <p>
     * 为了用get delete create 同访问路径 这种方式放弃 value = "user/{userId}",
     */
    @RequestMapping(method = RequestMethod.GET, produces = {MediaType.APPLICATION_JSON_VALUE})
    @ResponseBody
    public List<AddressBookDTO> get(@RequestParam Long userId, @RequestParam(required = false) String filter) {

        if (filter != null) {
            List<FilterDTO> filters = FilterDTO.getFilters(filter);
            filters.removeIf(filterDTO -> ObjectUtils.nullSafeEquals(filterDTO.getName(), "userId"));
            FilterDTO userIdFilter = new FilterDTO();
            userIdFilter.setName("userId");
            userIdFilter.setType("number");
            userIdFilter.setValue(userId);
            filters.add(userIdFilter);
            return this.manager.findAll(filters);
        }
        List<AddressBookDTO> data = this.manager.findAllByUserId(userId);
        return data;

    }


    /**
     * 根据指定id，删除对象
     *
     * @param id
     * @return SingleResponse
     * @throws IOException
     */
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = {MediaType.APPLICATION_JSON_VALUE})
    @ResponseBody
    public AddressBookDTO delete(@PathVariable Long id) {
        AddressBookDTO model = manager.delete(id);
        return model;
    }
}
