package com.qpp.cgp.controller.permission;

import com.qpp.web.business.controller.AbstractJsonRestController;
import com.qpp.cgp.domain.dto.permission.RoleDTO;
import com.qpp.cgp.manager.permission.RoleManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping(value = "api/roles", produces = MediaType.APPLICATION_JSON_VALUE)
public class RoleRestController extends AbstractJsonRestController<RoleDTO, Long, RoleManager> {


    @Autowired
    public RoleRestController(RoleManager manager) {
        super(manager);
    }

}
