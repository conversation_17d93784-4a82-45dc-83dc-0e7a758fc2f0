package com.qpp.cgp.controller.user.v2;

import com.qpp.cgp.manager.user.UserPasswordForgetService;
import com.qpp.operation.log.OperationLog;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RequestMapping(value = "v2", produces = MediaType.APPLICATION_JSON_VALUE)
@RestController
public class UserPasswordController {

    @Autowired
    private UserPasswordForgetService passwordForgetService;

    @PostMapping(value = "users/password/forgetPassword")
    @ApiOperation("忘记密码")
    public Boolean forgetPasswordV2(@RequestBody @Valid ForgotPasswordDTO dto) {
        return passwordForgetService.forgetPasswordV2(dto);
    }

    @OperationLog(description = "Reset password", level = OperationLog.Level.WARN,
            operator = OperationLog.Operation.UPDATE)
    @PutMapping(value = "users/password/reset")
    @ApiOperation("重置密码")
    public Boolean resetPasswordV2(@RequestBody @Valid ResetPassWordDTO dto) {
        return passwordForgetService.resetPasswordV2(dto);
    }
}
