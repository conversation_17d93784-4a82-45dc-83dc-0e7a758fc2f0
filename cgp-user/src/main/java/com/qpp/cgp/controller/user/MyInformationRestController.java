package com.qpp.cgp.controller.user;

import com.qpp.cgp.domain.user.User;
import com.qpp.cgp.manager.user.CustomRemoteTokenServices;
import com.qpp.cgp.repository.user.UserRepository;
import com.qpp.core.dto.ObjectDTO;
import com.qpp.core.exception.BusinessException;
import com.qpp.core.utils.SecurityUtils;
import com.qpp.core.acp.ACPAnnotation;
import com.qpp.operation.log.OperationLog;
import com.qpp.operation.log.OperationLogModule;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.MediaType;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.crypto.password.StandardPasswordEncoder;
import org.springframework.security.oauth2.provider.token.DefaultAccessTokenConverter;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@OperationLogModule("Me")
@Controller
@RequestMapping("api/me")
public class MyInformationRestController {

    @Autowired
    private UserRepository userRepository;

    private PasswordEncoder encoder = new StandardPasswordEncoder();

    @Autowired
    private Environment env;

    private CustomRemoteTokenServices tokenService = new CustomRemoteTokenServices();


    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ACPAnnotation(name = "userInfo",resourceName = "com.qpp.cgp.controller.user")
    @ResponseBody
    public ObjectDTO getMyInformation() {

        Long id = SecurityUtils.getLoginedInUserId();

        User user = userRepository.getOne(id);
        ObjectDTO info = new ObjectDTO();

        info.put("emailAddress", user.getEmailAddress());
        info.put("id", user.getId());
        info.put("firstName", user.getFirstName());
        return info;
    }

    @OperationLog(description = "Modify self password", level = OperationLog.Level.WARN,
            operator = OperationLog.Operation.UPDATE)
    @RequestMapping(value = "password", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ObjectDTO modifyPassword(@RequestBody ObjectDTO dto) {


        String currentPassword = dto.getString("currentPassword");
        String newPassword = dto.getString("newPassword");
        Long userId = SecurityUtils.getLoginedInUserId();

        User u = userRepository.getOne(userId);
        if (encoder.matches(currentPassword, u.getPassword())) {
            u.setPassword(encoder.encode(newPassword));
            userRepository.save(u);
        } else {
            throw new BusinessException("Password is not correct!");
        }
        return null;
    }

    @RequestMapping(value = "token", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ObjectDTO getTokenInfo(HttpServletRequest request) {

        String token = getTokenFromRequst(request);
        tokenService.setAccessTokenConverter(new DefaultAccessTokenConverter());
        tokenService.setCheckTokenEndpointUrl(env
                .getProperty("oauth.auth.server") + "oauth/check_token");
        tokenService.setClientId(env.getProperty("oauth.auth.client_id"));
        tokenService.setClientSecret(env
                .getProperty("oauth.auth.client_password"));
        Map<String, Object> map = tokenService.readAccessTokenInfo(token);
        Long expiration = Long.parseLong(map.get("exp").toString()) * 1000;
        Long expiresIn = expiration != null ? Long.valueOf((expiration - System.currentTimeMillis()) / 1000L)
                .intValue() : 0L;
        map.remove("authorities");
        map.remove("client_id");
        map.remove("user_name");
        map.remove("aud");
        map.put("expiresIn", expiresIn);
        ObjectDTO dto = new ObjectDTO();
        dto.putAll(map);
        return dto;
    }

    public String getTokenFromRequst(HttpServletRequest request) {
        String token = request.getParameter("access_token");
        if (StringUtils.isNotEmpty(token)) {
            return token;
        }
        if (StringUtils.isEmpty(request.getHeader("Authorization"))) {
            return "";
        }
        token = request.getHeader("Authorization").substring(7);
        return token;
    }

}
