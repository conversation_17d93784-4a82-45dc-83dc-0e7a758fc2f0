package com.qpp.cgp.controller.user;

import com.qpp.web.business.sort.SortProcessor;
import com.qpp.cgp.manager.user.MailSubscribeManager;
import com.qpp.core.dto.FilterDTO;
import com.qpp.core.dto.ObjectDTO;
import com.qpp.core.dto.PageDTO;
import com.qpp.core.dto.SortDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("api/mailSubscribes")
public class MailSubscribeRestController {

    @Autowired
    private MailSubscribeManager mailSubscribeManager;

    // 查询所有
    @RequestMapping(method = RequestMethod.GET)
    public PageDTO<ObjectDTO> getAll(@RequestParam("page") int page, @RequestParam("limit") int limit,
                                     @RequestParam(value = "sort", required = false) String sort,
                                     @RequestParam(value = "filter", required = false) String filter) {
        Sort sorts = SortProcessor.getSort(SortDTO.getOrders(sort));
        List<FilterDTO> filters = FilterDTO.getFilters(filter);
        int pageNumber = 0;
        int pageSize = 0;
        pageNumber = Integer.valueOf(page) - 1;
        pageSize = Integer.valueOf(limit);

        PageRequest pageable = PageRequest.of(pageNumber, pageSize, sorts);
        PageDTO<ObjectDTO> pageDTO = this.mailSubscribeManager.findAll(pageable, filters);
        return pageDTO;

    }

    @RequestMapping(value = "pushHistories", method = RequestMethod.GET)
    public PageDTO<ObjectDTO> getAllPushHistories(@RequestParam("page") int page,
                                                  @RequestParam("limit") int limit, @RequestParam(value = "sort", required = false) String sort,
                                                  @RequestParam(value = "filter", required = false) String filter) {
        Sort sorts = SortProcessor.getSort(SortDTO.getOrders(sort));
        List<FilterDTO> filters = FilterDTO.getFilters(filter);
        int pageNumber = 0;
        int pageSize = 0;
        pageNumber = Integer.valueOf(page) - 1;
        pageSize = Integer.valueOf(limit);


        PageRequest pageable = PageRequest.of(pageNumber, pageSize, sorts);
        PageDTO<ObjectDTO> pageDTO = this.mailSubscribeManager.findAllPushHistories(pageable, filters);

        return pageDTO;
    }

    @RequestMapping(value = "pushHistories/{id}/content", method = RequestMethod.GET)
    public ObjectDTO getHistoryContent(@PathVariable Long id) {
        String content = this.mailSubscribeManager.getHistoryContentById(id);
        ObjectDTO dto = new ObjectDTO();
        dto.put("content", content);
        return dto;
    }


}
