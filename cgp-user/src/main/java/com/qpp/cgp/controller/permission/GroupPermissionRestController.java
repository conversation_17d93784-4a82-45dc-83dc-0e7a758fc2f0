package com.qpp.cgp.controller.permission;

import com.qpp.web.business.controller.AbstractJsonRestController;
import com.qpp.cgp.domain.dto.permission.GroupPermissionDTO;
import com.qpp.cgp.domain.dto.permission.PermissionDTO;
import com.qpp.cgp.domain.dto.permission.RoleDTO;
import com.qpp.cgp.manager.permission.GroupPermissionManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Controller
@RequestMapping(value = "api/groupPermissions", produces = MediaType.APPLICATION_JSON_VALUE)
public class GroupPermissionRestController extends
        AbstractJsonRestController<GroupPermissionDTO, Long, GroupPermissionManager> {

    @Autowired
    private GroupPermissionManager groupPermissionManager;

    @Autowired
    public GroupPermissionRestController(GroupPermissionManager manager) {
        super(manager);
    }


    /**
     * 返回 根据groupid 返回权限，id小于0  返回chencked为false的权限
     *
     * @return
     */
    @RequestMapping(value = "all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public PermissionDTO findAll(@RequestParam(value = "groupPermissionId", required = false) Long groupPermissionId) {

        if (groupPermissionId == null || groupPermissionId < 0) {
            return groupPermissionManager.findBlankAll();
        } else {
            // 只返回checked 为 true的 树  , 去除了checked为false的节点
            return groupPermissionManager.findAll(groupPermissionId, false);
        }
    }

    /**
     * 根据id查询 权限组是否关联了 role或组，关联了就返回这个组数据 没有关联返回空。
     */
    @RequestMapping(value = "isdelete", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public List<RoleDTO> isDelete(@RequestParam Long groupId) {
        List<RoleDTO> list = this.manager.predelete(groupId);
        if (list != null && list.size() > 0) {
            return list;
        } else {
            return null;
        }
    }

}
