package com.qpp.cgp.service.parser;

import com.qpp.cgp.configuration.FileServiceFeignAutoConfiguration;
import com.qpp.cgp.service.parser.pdf.PdfParseResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2022/10/11
 */
@FeignClient(name = "fileService", path = "${fileService.path:file}",
        contextId = "FileRemoteService", configuration = FileServiceFeignAutoConfiguration.class)
public interface FileRemoteService {

    @PostMapping("/file/parser/{fileName}")
    PdfParseResponse parse(@PathVariable("fileName") String fileName, @RequestBody String parseParamsJson);

}
