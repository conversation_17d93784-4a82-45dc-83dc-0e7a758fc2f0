package com.qpp.cgp.service.parser.user.preference;

import com.qpp.cgp.cache.RedisComponent;
import com.qpp.cgp.domain.common.CountrySetting;
import com.qpp.cgp.manager.application.ApplicationMode;
import com.qpp.cgp.manager.area.CountrySettingManager;
import com.qpp.cgp.service.common.CommonPropertiesService;
import com.qpp.cgp.service.common.CountryDetectService;
import com.qpp.cgp.service.common.currency.PlatformCurrencyService;
import com.qpp.core.utils.SentryUtils;
import com.qpp.core.utils.http.HttpRequestUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class UserCurrencyRecommender {

    @Autowired
    private CountryDetectService countryDetectService;

    @Autowired
    private CountrySettingManager countrySettingManager;

    @Autowired
    private PlatformCurrencyService platformCurrencyService;

    @Autowired
    private CommonPropertiesService commonPropertiesService;

    @Autowired
    private RedisComponent<String, String> redisComponent;

    private static final long DEFAULT_TIME_OUT = 7;

    public String recommend(String platform, ApplicationMode applicationMode) {
        String currencyCode = null;

        try {
            String clientIP = HttpRequestUtils.getClientIP();
            if (StringUtils.isNotBlank(clientIP)) {
                String countryCode = getAndCacheCountryCodeByClientIP(clientIP);
                if (StringUtils.isNotBlank(countryCode)) {
                    currencyCode = countrySettingManager
                            .findByCountryCode(countryCode)
                            .map(CountrySetting::getCurrencyCode)
                            .orElse(null);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            SentryUtils.recordEx(e);
        }

        if (currencyCode != null) {
            return currencyCode;
        }

        return platformCurrencyService.getDefaultCurrency(platform, applicationMode);
    }

    private String getAndCacheCountryCodeByClientIP(String clientIP) {
        String cacheKey = "UserPreference" + ":" + "ip" + ":" + clientIP;
        // 获取缓存值
        String countryCode = redisComponent.get(cacheKey);
        if (StringUtils.isBlank(countryCode)) {
            // 缓存值为空则调用远程接口获取
            countryCode = countryDetectService.detectByIpV4(clientIP).orElse(null);

            log.info("countryDetectService.detectByIpV4({}) => {}", clientIP, countryCode);

            if (StringUtils.isNotBlank(countryCode)) {
                String timeoutConfig = commonPropertiesService.getKeyValueAndApplicationModeForNoException("ip.country.cacheTimeout");
                long timeout = StringUtils.isNotBlank(timeoutConfig) ? NumberUtils.toLong(timeoutConfig, DEFAULT_TIME_OUT) : DEFAULT_TIME_OUT;
                // 缓存值
                redisComponent.set(cacheKey, countryCode, timeout, TimeUnit.DAYS);
            }
        } else {
            log.info("Hit Cache! IP {} => CountryCode {}", clientIP, countryCode);
        }
        return countryCode;
    }

}
