package com.qpp.cgp.service.parser.user.preference;

import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.common.Currency;
import com.qpp.cgp.domain.common.platform.Platform;
import com.qpp.cgp.domain.common.platform.UsageScope;
import com.qpp.cgp.domain.dto.user.UserPreferenceDTO;
import com.qpp.cgp.domain.dto.user.UserPreferenceUpdateDTO;
import com.qpp.cgp.domain.user.User;
import com.qpp.cgp.domain.user.preference.UserPreference;
import com.qpp.cgp.manager.application.ApplicationConfigService;
import com.qpp.cgp.manager.application.ApplicationMode;
import com.qpp.cgp.manager.common.platform.PlatformManager;
import com.qpp.cgp.manager.user.UserManager;
import com.qpp.cgp.manager.user.preference.UserPreferenceManager;
import com.qpp.cgp.repository.common.CurrencyRepository;
import com.qpp.cgp.service.common.currency.PlatformCurrencyService;
import com.qpp.cgp.service.common.partner.CommonPartnerShoppingCartService;
import com.qpp.cgp.service.common.platform.PlatformService;
import com.qpp.cgp.service.common.user.CommonUserPreferenceService;
import com.qpp.cgp.service.common.user.CommonUserShoppingCartService;
import com.qpp.cgp.vo.CurrencyVO;
import com.qpp.core.utils.SecurityUtils;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: lw
 * @Date: 2024/12/03/14:22
 * @Description:
 */
@Service
@Log4j2
public class UserPreferenceService implements CommonUserPreferenceService {

    @Autowired
    private UserPreferenceManager userPreferenceManager;

    @Autowired
    private CurrencyRepository currencyRepository;

    @Autowired
    private UserManager userManager;

    @Autowired
    private PlatformManager platformManager;

    @Autowired
    private PlatformService platformService;

    @Autowired
    private ApplicationConfigService applicationConfigService;

    @Autowired
    private PlatformCurrencyService platformCurrencyService;

    @Autowired
    private UserCurrencyRecommender userCurrencyRecommender;

    @Autowired
    private CommonPartnerShoppingCartService partnerShoppingCartService;

    @Autowired
    private CommonUserShoppingCartService userShoppingCartService;


    /**
     * 获取用户偏好设置的货币，用户没有设置货币则分配platform默认货币
     *
     * @param platform 平台
     * @param userId   用户id
     * @return 偏好货币
     */
    @Override
    public Currency userPreferenceCurrency(String platform, String userId) {

        UserPreference userPreference = getOrCreateUserPreference(platform, Long.valueOf(userId));

        String currencyCode = userPreference.getCurrencyCode();

        Currency currency = currencyRepository.findByCodeAndWebsiteId(currencyCode, 11L);
        if (currency == null) {
            //currencyCode不存在
            throw BusinessExceptionBuilder.of(3000604, ImmutableMap.of("currencyCode", currencyCode));
        }

        return currency;
    }

    @Override
    public String checkOrGetUserPreferenceCurrency(String currencyCode, String platform, String userId, UsageScope usageScope) {
        if (StringUtils.isNotBlank(currencyCode)) {
            //校验用户货币
            platformCurrencyService.checkPlatformSupportCurrency(platform, usageScope, currencyCode);
        } else {
            //为空获取用户偏好配置货币
            currencyCode = this.userPreferenceCurrency(platform, userId).getCode();
        }
        return currencyCode;
    }


    /**
     * 修改用户偏好设置的货币
     *
     * @param platform 平台
     * @param userId   用户id
     */
    public void changeUserPreferenceCurrency(String platform, String userId, String currencyCode, UsageScope usageScope) {
        //校验货币
        platformCurrencyService.checkPlatformSupportCurrency(platform, usageScope, currencyCode);
        UserPreference userPreference = userPreferenceManager.findByPlatformAndUser(platform, Long.valueOf(userId));
        if (userPreference == null) {
            userPreference = new UserPreference();
            userPreference.setUserId(Long.valueOf(userId));
            userPreference.setPlatformCode(platform);
        }
        userPreference.setCurrencyCode(currencyCode);
        userPreferenceManager.saveNew(userPreference);
    }

    /**
     * 校验货币是否与用户偏好货币一致,用户没有设置偏好货币时与平台默认货币对比
     *
     * @param platform     平台代码
     * @param userId       用户id
     * @param currencyCode 待校验货币代码
     * @param usageScope   配置范围
     */
    public void checkUserCurrency(String platform, String userId, String currencyCode, UsageScope usageScope) {
        if (usageScope == null) {
            usageScope = UsageScope.Frontend;
        }
        ApplicationMode applicationMode = applicationConfigService.getApplicationMode();
        //先校验平台是否支持此货币
        platformCurrencyService.checkPlatformSupportCurrency(platform, usageScope, currencyCode);
        UserPreference userPreference = userPreferenceManager.findByPlatformAndUser(platform, Long.valueOf(userId));
        //用户没有配置偏好货币则取平台默认货币
        String userCurrencyCode = platformCurrencyService.getDefaultCurrency(platform, applicationMode);
        if (userPreference != null && StringUtils.isNotBlank(userPreference.getCurrencyCode())) {
            userCurrencyCode = userPreference.getCurrencyCode();
        }
        if (!StringUtils.equalsIgnoreCase(userCurrencyCode, currencyCode)) {
            //不一致
            Currency userCurrency = currencyRepository.findByCodeAndWebsiteId(userCurrencyCode, 11L);

            throw BusinessExceptionBuilder.of(3000605,
                    ImmutableMap.of("currencyCode", currencyCode, "userCurrencyCode", userCurrencyCode,
                            "userCurrencyTitle", userCurrency.getTitle()));
        }

    }

    /**
     * 获取用户偏好配置，用户没有偏好配置则新建
     *
     * @param platformCode 平台code
     * @param userId       用户id
     * @return 用户偏好配置
     */
    public UserPreference getOrCreateUserPreference(String platformCode, Long userId) {
        //校验platform
        ApplicationMode applicationMode = applicationConfigService.getApplicationMode();
        platformService.checkPlatform(platformCode, applicationMode);

        //校验用户是否存在
        if (!userManager.exists(userId)) {
            throw BusinessExceptionBuilder.of(3000606, ImmutableMap.of("userId", userId));
        }

        UserPreference userPreference = userPreferenceManager.findByPlatformAndUser(platformCode, userId);
        if (userPreference == null) {
            userPreference = new UserPreference();
            userPreference.setUserId(userId);
            userPreference.setPlatformCode(platformCode);
        }

        String currencyCode = userPreference.getCurrencyCode();
        if (StringUtils.isBlank(currencyCode)) {

            currencyCode = userCurrencyRecommender.recommend(platformCode, applicationMode);
            log.info("userCurrencyRecommender.recommend({}, {}) => {}", platformCode, platformCode, currencyCode);

            // 校验网站是否支持此货币
            try {
                platformCurrencyService.checkPlatformSupportCurrency(platformCode, UsageScope.Frontend, currencyCode);
            } catch (Exception ignored) {
                // 获取平台默认货币
                currencyCode = platformCurrencyService.getDefaultCurrency(platformCode, applicationMode);
                log.info("Unsupported! Fallback to platformCurrencyService.getDefaultCurrency({}, {}) => {}", platformCode, platformCode, currencyCode);
            }

            userPreference.setCurrencyCode(currencyCode);

            userPreferenceManager.saveNew(userPreference);
        }

        return userPreference;
    }

    public UserPreferenceDTO getLoggedInUserPreference(String platformCode) {
        Long userId = SecurityUtils.getLoginedInUserId();

        UserPreference userPreference = getOrCreateUserPreference(platformCode, userId);

        String currencyCode = userPreference.getCurrencyCode();
        Currency currency = currencyRepository.findByCodeAndWebsiteId(currencyCode, 11L);
        CurrencyVO currencyVO = new CurrencyVO();
        BeanUtils.copyProperties(currency, currencyVO);

        ApplicationMode applicationMode = applicationConfigService.getApplicationMode();
        Platform platform = platformManager.findByCode(platformCode, applicationMode);

        //购物车项数量
        Integer cartQuantity = 0;
        //判断用户是否为匿名用户
        User user = userManager.findByIdNotFill(userId);
        if (user.getIsTemp() != null) {
            if (user.getIsTemp()) {
                //匿名用户
                cartQuantity = userShoppingCartService.getCartQuantity();
            } else {
                //登录用户
                cartQuantity = partnerShoppingCartService.getCartQuantity();
            }
        }

        return UserPreferenceDTO.builder()
                .cartItemsQty(cartQuantity)
                .currency(currencyVO)
                .platform(platform)
                .build();

    }

    public void updateLoggedInUserPreference(String platform, UserPreferenceUpdateDTO userPreferenceUpdateDTO) {
        Long userId = SecurityUtils.getLoginedInUserId();
        UserPreference userPreference = getOrCreateUserPreference(platform, userId);

        if (userPreferenceUpdateDTO != null) {
            String currencyCode = userPreferenceUpdateDTO.getCurrencyCode();
            platformCurrencyService.checkPlatformSupportCurrency(platform, UsageScope.Frontend, currencyCode);
            userPreference.setCurrencyCode(currencyCode);
        }

        userPreferenceManager.saveNew(userPreference);
    }
}
