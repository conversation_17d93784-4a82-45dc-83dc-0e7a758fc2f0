package com.qpp.cgp.util;

import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.test.context.support.WithSecurityContextFactory;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

/**
 * Customize a {@link WithSecurityContextFactory} used to conjunction with
 * {@link WithCustomerUserDetails}.
 * <p>This class is used for the convenience of testing with a logged in user,
 * When encountered {@link WithCustomerUserDetails},will use this class to create
 * a {@link SecurityContext}, the {@link Authentication} is {@link UsernamePasswordAuthenticationToken},
 * and the {@code principal} is {@link WithCustomerUserDetails#value()}, the {@code credentials}
 * is {@link com.qpp.cgp.domain.user.User#password}. the {@code authorities} is just a
 * role of{@code admin}.
 * </p>
 *
 * <AUTHOR>
 * @date 2018/4/20
 */
public final class WithCustomerSecurityContextFactory implements WithSecurityContextFactory<WithCustomerUserDetails> {

    @Autowired
    private BeanFactory beanFactory;

    @Override
    public SecurityContext createSecurityContext(WithCustomerUserDetails withUser) {
        String beanName = withUser.userDetailsServiceBeanName();
        UserDetailsService userDetailsService = StringUtils.hasLength(beanName)
                ? this.beanFactory.getBean(beanName, UserDetailsService.class)
                : this.beanFactory.getBean(UserDetailsService.class);
        String username = withUser.value();
        Assert.hasLength(username, "value() must be non empty String");
        UserDetails principal = userDetailsService.loadUserByUsername(username);
        Authentication authentication = new UsernamePasswordAuthenticationToken(
                principal.getUsername(), principal.getPassword(), principal.getAuthorities());
        SecurityContext context = SecurityContextHolder.createEmptyContext();
        context.setAuthentication(authentication);
        return context;
    }
}
