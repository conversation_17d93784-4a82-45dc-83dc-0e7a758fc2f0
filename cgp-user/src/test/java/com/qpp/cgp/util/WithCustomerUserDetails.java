package com.qpp.cgp.util;

import com.qpp.cgp.domain.user.User;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.test.context.support.WithSecurityContext;
import org.springframework.security.test.context.support.WithUserDetails;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2018/4/20
 * @see WithUserDetails
 */
@SuppressWarnings("JavaDoc")
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@WithSecurityContext(factory = WithCustomerSecurityContextFactory.class)
public @interface WithCustomerUserDetails {

    /**
     * The user's identify.
     * <p>You must transfer {@code emailAddress} or {@code id} of the {@link User}
     * which you want to Analog login, others will not work.
     * </p>
     *
     * @return
     */
    String value();

    /**
     * The bean name for the {@link UserDetailsService} to use. If this is not
     * provided, then the lookup is done by type and expects only a single
     * {@link UserDetailsService} bean to be exposed.
     *
     * @return the bean name for the {@link UserDetailsService} to use.
     */
    String userDetailsServiceBeanName() default "userDetailsService";

}
