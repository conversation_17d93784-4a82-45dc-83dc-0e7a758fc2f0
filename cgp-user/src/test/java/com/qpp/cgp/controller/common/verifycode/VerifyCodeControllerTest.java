//package com.qpp.cgp.controller.common.verifycode;
//
//import com.fasterxml.jackson.databind.ObjectMapper;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.context.annotation.PropertySource;
//import org.springframework.test.context.junit4.SpringRunner;
//import org.springframework.test.context.web.WebAppConfiguration;
//import org.springframework.test.web.servlet.MockMvc;
//import org.springframework.test.web.servlet.setup.MockMvcBuilders;
//
//import org.springframework.web.context.WebApplicationContext;
//
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
//import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
//
///**
// * <AUTHOR>
// * @date 2018/2/3
// */
//@RunWith(SpringRunner.class)
//// @WebAppConfiguration
//// @ContextConfiguration(classes = {KafkaConfig.class, RootConfig.class, PersistenceConfig.class, MongoConfig.class, CacheConfig.class})
//// @PropertySource("classpath:application.properties")
//@SpringBootTest
//@AutoConfigureMockMvc(addFilters = false)
//
//public class VerifyCodeControllerTest {
//
//    @Autowired
//    private WebApplicationContext context;
//
//    @Autowired
//    private MockMvc mock;
//
//    @Autowired
//    private ObjectMapper objectMapper;
//
//    // @Before
//    // public void setup() {
//    //
//    //     this.mock = MockMvcBuilders.webAppContextSetup(context).build();
//    // }
//
//    @Test
//    public void generator() throws Exception {
//        mock.perform(get("/api/websites/44/verifyCode")).andDo(print());
//    }
//
//    @Test
//    public void getVerifyCode() throws Exception {
//        mock.perform(get("/common/websites/11/verifyCode/460586c5589d48f6936f0fcbb556064b")).andDo(print());
//    }
//}