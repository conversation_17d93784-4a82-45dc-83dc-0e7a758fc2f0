//package com.qpp.cgp.controller.user;
//
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.http.MediaType;
//import org.springframework.test.annotation.Commit;
//import org.springframework.test.context.junit4.SpringRunner;
//import org.springframework.test.web.servlet.MockMvc;
//
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
//import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
//
///**
// * <AUTHOR>
// * @since 2018/8/6
// */
//@RunWith(SpringRunner.class)
//@SpringBootTest
//@AutoConfigureMockMvc(addFilters = false)
//public class UserRestControllerTest {
//
//    @Autowired
//    private MockMvc mockMvc;
//
//    @Test
//    public void create() throws Exception {
//        //language=JSON
//        String content = "{\n" +
//                "  \"type\": \"ADMIN\",\n" +
//                "  \"gender\": \"M\",\n" +
//                "  \"firstName\": \"\",\n" +
//                "  \"lastName\": \"\",\n" +
//                "  \"password\": \"123456\",\n" +
//                "  \"email\": \"<EMAIL>\",\n" +
//                "  \"enable\": true,\n" +
//                "  \"roles\": [\n" +
//                "    {\n" +
//                "      \"id\": 1,\n" +
//                "      \"name\": \"admin\",\n" +
//                "      \"description\": \"\\u7db2\\u7ad9\\u7ba1\\u7406\\u54e1\"\n" +
//                "    }\n" +
//                "  ],\n" +
//                "  \"website\": {\n" +
//                "    \"id\": 11,\n" +
//                "    \"name\": \"WhiteLabel\",\n" +
//                "    \"code\": \"WHITELABEL\",\n" +
//                "    \"url\": \"http://*************/whitelabel/\"\n" +
//                "  },\n" +
//                "  \"source\": null,\n" +
//                "  \"dob\": *************\n" +
//                "}";
//        mockMvc.perform(post("/api/users")
//                .contentType(MediaType.APPLICATION_JSON_VALUE)
//                .content(content)
//        ).andDo(print());
//    }
//
//    @Test
//    @Commit
//    public void update() throws Exception {
//        //language = JSON
//        String content = "{\n" +
//                "  \"id\": 767827,\n" +
//                "  \"type\": \"ADMIN\",\n" +
//                "  \"gender\": \"m\",\n" +
//                "  \"firstName\": \"\",\n" +
//                "  \"lastName\": \"\",\n" +
//                "  \"password\": \"password5\",\n" +
//                "  \"email\": \"<EMAIL>\",\n" +
//                "  \"enable\": true,\n" +
//                "  \"roles\": [\n" +
//                "    {\n" +
//                "      \"id\": 2,\n" +
//                "      \"name\": \"ACCOUNT\",\n" +
//                "      \"description\": \"\\u6703\\u8a08\\u90e8\\u4eba\\u54e1\",\n" +
//                "      \"permissionIds\": \"\"\n" +
//                "    },\n" +
//                "    {\n" +
//                "      \"id\": 4,\n" +
//                "      \"name\": \"manager\",\n" +
//                "      \"description\": \"company Manager\",\n" +
//                "      \"permissionIds\": \"\"\n" +
//                "    }\n" +
//                "  ],\n" +
//                "  \"website\": {\n" +
//                "    \"id\": 11,\n" +
//                "    \"code\": \"WHITELABEL\",\n" +
//                "    \"name\": \"WhiteLabel\",\n" +
//                "    \"url\": \"http://*************/whitelabel/\"\n" +
//                "  },\n" +
//                "  \"source\": null,\n" +
//                "  \"defaultAddressBookId\": null,\n" +
//                "  \"dob\": *************\n" +
//                "}";
//        mockMvc.perform(put("/api/users/767827")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(content)
//        ).andDo(print());
//    }
//
//    @Test
//    public void removeRole() throws Exception {
//        mockMvc.perform(delete("/api/userRoles")
//                .param("roleId", "4")
//                .param("customerId", "767827")
//        ).andDo(print());
//    }
//}