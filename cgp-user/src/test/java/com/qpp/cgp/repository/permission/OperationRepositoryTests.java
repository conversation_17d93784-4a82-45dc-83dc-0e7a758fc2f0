package com.qpp.cgp.repository.permission;

import com.qpp.cgp.domain.permission.Operation;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class OperationRepositoryTests {

    @InjectMocks
    private OperationRepository operationRepository;

    @Mock
    private ResourcePureRepository resourcePureRepository;

    @Before
    public void setUp() throws Exception {

        operationRepository = Mockito.spy(operationRepository);

    }

    @Test
    public void testFillDataGivenNull() {

        operationRepository.fillData(null);

        Mockito.verifyZeroInteractions(resourcePureRepository);
    }

    @Test
    public void testFillDataGivenOwnerIdNotNull() {

        Operation operation = Mockito.mock(Operation.class);
        operation.setOwnerId(123L);
        operationRepository.fillData(operation);

        Mockito.verify(operationRepository).fillData(Mockito.any());
        Mockito.verify(resourcePureRepository).findById(Mockito.any());

    }

    @Test
    public void testFillDataGivenEmpty() {

        operationRepository.fillData(new Operation());

        Mockito.verifyZeroInteractions(resourcePureRepository);

    }
}