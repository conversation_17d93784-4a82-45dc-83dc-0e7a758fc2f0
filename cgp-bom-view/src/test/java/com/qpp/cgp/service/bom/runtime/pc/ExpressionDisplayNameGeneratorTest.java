package com.qpp.cgp.service.bom.runtime.pc;

import com.google.common.collect.ImmutableList;
import com.qpp.cgp.domain.bom.runtime.pc.ExpressionDisplayName;
import com.qpp.cgp.domain.management.LanguageResource;
import com.qpp.cgp.expression.Expression;
import com.qpp.cgp.expression.calculator.ExpressionCalculatorService;
import com.qpp.cgp.repository.common.management.LanguageResourceRepository;
import com.qpp.cgp.value.ValueType;
import com.qpp.service.tempalte.FreeMarkerService;
import io.swagger.models.auth.In;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @date 2024/4/28
 */
@ExtendWith(MockitoExtension.class)
public class ExpressionDisplayNameGeneratorTest {

    @Spy
    @InjectMocks
    private ExpressionDisplayNameGenerator expressionDisplayNameGenerator;

    @Mock
    private LanguageResourceRepository languageResourceRepository;

    @Mock
    private ExpressionCalculatorService expressionCalculatorService;

    @Test
    public void testGenerateByNeedReplaceIsTrue() {
        Expression expression = new Expression();
        expression.setExpression("function expression(context) {\n" +
                "    if (context.context.pcTotalCount >= 1 && context.context.pcTotalCount <= 3) {\n" +
                "        return context.context.languageKeys[0];\n" +
                "    } else if (context.context.pcTotalCount > 3 && context.context.pcTotalCount <= 6) {\n" +
                "        return context.context.languageKeys[1];\n" +
                "    } else {\n" +
                "        return context.context.languageKeys[2];\n" +
                "    }\n" +
                "}");
        expression.setResultType(ValueType.Array);

        List<String> languageKeys = new ArrayList<>();
        languageKeys.add("A_Key");
        languageKeys.add("B_Key");
        languageKeys.add("C_Key");

        Mockito.when(expressionCalculatorService.calculateV2(any(),any(Map.class), any(Map.class)))
                        .thenReturn(ImmutableList.of("A_${index + 1}", "A_${index + 1}", "A_${index + 1}", "B_${index + 1}",
                                "B_${index + 1}", "B_${index + 1}", "C_${index + 1}", "C_${index + 1}", "C_${index + 1}", "C_${index + 1}"));

        LanguageResource languageResourceA = new LanguageResource();
        languageResourceA.setName("A_Key");
        languageResourceA.setValue("A_${index + 1}");
        LanguageResource languageResourceB = new LanguageResource();
        languageResourceB.setName("B_Key");
        languageResourceB.setValue("B_${index + 1}");
        LanguageResource languageResourceC = new LanguageResource();
        languageResourceC.setName("C_Key");
        languageResourceC.setValue("C_${index + 1}");

        Mockito.when(languageResourceRepository.findByNameInAndCultureCode(any(), any()))
                .thenReturn(ImmutableList.of(languageResourceA, languageResourceB, languageResourceC));

        ExpressionDisplayName expressionDisplayName = new ExpressionDisplayName();
        expressionDisplayName.setExpression(expression);
        expressionDisplayName.setLanguageKeys(languageKeys);
        expressionDisplayName.setNeedReplace(true);

        List<String> result = expressionDisplayNameGenerator
                .generate(expressionDisplayName, 10, "zh");

        Assertions.assertThat(result.size()).isEqualTo(10);
        Assertions.assertThat(result.get(0)).isEqualTo("A_1");
        Assertions.assertThat(result.get(1)).isEqualTo("A_2");
        Assertions.assertThat(result.get(2)).isEqualTo("A_3");
        Assertions.assertThat(result.get(3)).isEqualTo("B_4");
        Assertions.assertThat(result.get(4)).isEqualTo("B_5");
        Assertions.assertThat(result.get(5)).isEqualTo("B_6");
        Assertions.assertThat(result.get(6)).isEqualTo("C_7");
        Assertions.assertThat(result.get(7)).isEqualTo("C_8");
        Assertions.assertThat(result.get(8)).isEqualTo("C_9");
        Assertions.assertThat(result.get(9)).isEqualTo("C_10");
    }

    @Test
    public void testGenerateByNeedReplaceIsFalse() {
        Expression expression = new Expression();
        expression.setExpression("function expression(context) {\n" +
                "    if (context.context.pcTotalCount >= 1 && context.context.pcTotalCount <= 3) {\n" +
                "        return context.context.languageKeys[0];\n" +
                "    } else if (context.context.pcTotalCount > 3 && context.context.pcTotalCount <= 6) {\n" +
                "        return context.context.languageKeys[1];\n" +
                "    } else {\n" +
                "        return context.context.languageKeys[2];\n" +
                "    }\n" +
                "}");
        expression.setResultType(ValueType.Array);

        List<String> languageKeys = new ArrayList<>();
        languageKeys.add("A_Key");
        languageKeys.add("B_Key");
        languageKeys.add("C_Key");

        Mockito.when(expressionCalculatorService.calculateV2(any(),any(Map.class), any(Map.class)))
                .thenReturn(ImmutableList.of("A_${index + 1}", "A_${index + 1}", "A_${index + 1}", "B_${index + 1}",
                        "B_${index + 1}", "B_${index + 1}", "C_${index + 1}", "C_${index + 1}", "C_${index + 1}", "C_${index + 1}"));

        LanguageResource languageResourceA = new LanguageResource();
        languageResourceA.setName("A_Key");
        languageResourceA.setValue("A_${index + 1}");
        LanguageResource languageResourceB = new LanguageResource();
        languageResourceB.setName("B_Key");
        languageResourceB.setValue("B_${index + 1}");
        LanguageResource languageResourceC = new LanguageResource();
        languageResourceC.setName("C_Key");
        languageResourceC.setValue("C_${index + 1}");

        Mockito.when(languageResourceRepository.findByNameInAndCultureCode(any(), any()))
                .thenReturn(ImmutableList.of(languageResourceA, languageResourceB, languageResourceC));

        ExpressionDisplayName expressionDisplayName = new ExpressionDisplayName();
        expressionDisplayName.setExpression(expression);
        expressionDisplayName.setLanguageKeys(languageKeys);
        expressionDisplayName.setNeedReplace(false);

        List<String> result = expressionDisplayNameGenerator
                .generate(expressionDisplayName, 10, "zh");

        Assertions.assertThat(result.size()).isEqualTo(10);
        Assertions.assertThat(result.get(0)).isEqualTo("A_${index + 1}");
        Assertions.assertThat(result.get(1)).isEqualTo("A_${index + 1}");
        Assertions.assertThat(result.get(2)).isEqualTo("A_${index + 1}");
        Assertions.assertThat(result.get(3)).isEqualTo("B_${index + 1}");
        Assertions.assertThat(result.get(4)).isEqualTo("B_${index + 1}");
        Assertions.assertThat(result.get(5)).isEqualTo("B_${index + 1}");
        Assertions.assertThat(result.get(6)).isEqualTo("C_${index + 1}");
        Assertions.assertThat(result.get(7)).isEqualTo("C_${index + 1}");
        Assertions.assertThat(result.get(8)).isEqualTo("C_${index + 1}");
        Assertions.assertThat(result.get(9)).isEqualTo("C_${index + 1}");
    }
}
