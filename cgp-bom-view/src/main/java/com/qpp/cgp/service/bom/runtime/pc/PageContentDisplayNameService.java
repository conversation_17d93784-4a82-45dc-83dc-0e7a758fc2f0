package com.qpp.cgp.service.bom.runtime.pc;

import com.qpp.cgp.domain.bom.runtime.pc.PCDisplayName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/26
 */
@Service
public class PageContentDisplayNameService {

    @Autowired
    @SuppressWarnings("rawtypes")
    private List<AbstractDisplayNameGenerator> generators;

    @SuppressWarnings({"rawtypes", "unchecked"})
    public List<String> generate(PCDisplayName pcDisplayName, int pageCount, String cultureCode) {
        if (null == pcDisplayName) {
            return new ArrayList<>();
        }

        String errorMsg = String.format("Can not find display name generator for '%s'!",
                pcDisplayName.getClass().getName());
        AbstractDisplayNameGenerator displayNameGenerator = generators.stream()
                .filter(generator -> generator.isMatch(pcDisplayName))
                .findFirst()
                .orElseThrow(() -> new UnsupportedOperationException(errorMsg));
        return displayNameGenerator.generate(pcDisplayName, pageCount, cultureCode);
    }
}
