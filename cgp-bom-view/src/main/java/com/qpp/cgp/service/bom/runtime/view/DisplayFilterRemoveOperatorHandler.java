package com.qpp.cgp.service.bom.runtime.view;

import com.qpp.cgp.domain.bom.runtime.PageContent;
import com.qpp.cgp.domain.bom.runtime.view.DisplayObjectFilter;
import com.qpp.cgp.domain.bom.runtime.view.ViewElementFilter;
import com.qpp.cgp.domain.dto.bom.runtime.PageContentViewOperator;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/6
 */
@Component
public class DisplayFilterRemoveOperatorHandler extends ViewElementFilterHandler<DisplayObjectFilter> {

    @Override
    public PageContent handle(PageContent pageContent, List<DisplayObjectFilter> filters, boolean isInclude) {
        throw new UnsupportedOperationException();
    }

    @Override
    public boolean isMatch(List<ViewElementFilter> filters, PageContentViewOperator operator) {
        return filters.get(0) instanceof DisplayObjectFilter && operator == PageContentViewOperator.REMOVE;
    }
}
