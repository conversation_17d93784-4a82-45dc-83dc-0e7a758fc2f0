package com.qpp.cgp.domain.product.config;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.cgp.migration.CgpMongoDomain;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@JsonTypeInfo(use = JsonTypeInfo.Id.NONE)
@NoArgsConstructor
@Data
@ConfigDomain
@Document(collection = "productconfigdesigns")
public class ProductConfigDesign extends ProductConfigItem {

    private String configValue;

    @Transient
    @JsonSerialize(using = ProductConfigBomSerializer.class)
//    @ManyToMany(cascade = CascadeType.REFRESH)
//    @JoinTable(name = "cgp_builder_config_design_compatibility_to_bom", joinColumns = @JoinColumn(name = "bcd_id"), inverseJoinColumns = @JoinColumn(name = "bcb_id"))
//    @CollectionId(type = @Type(type = "long"), columns = @Column(name = "id"), generator = "cgp_id_generator")
    private List<ProductConfigBom> bomCompatibilities = new ArrayList<>();

    private List<Long> bomCompatibilityIds = new ArrayList<>();

    public void setBomCompatibilities(List<ProductConfigBom> bomCompatibilities) {
        if (bomCompatibilities != null) {
            this.bomCompatibilityIds = bomCompatibilities.stream().map(CgpMongoDomain::getId).collect(Collectors.toList());
        }
        this.bomCompatibilities = bomCompatibilities;
    }
}
