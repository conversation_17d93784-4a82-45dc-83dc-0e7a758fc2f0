package com.qpp.cgp.domain.product.config.material.mapping2;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * ubi 生成的 obi 的映射配置
 *
 * <AUTHOR>
 * @since 2019/10/8
 */
@NoArgsConstructor
@Data
public class UBIToOBIItemMappingConfig extends OptionalBOMItemMappingConfig {

    /**
     * 用于获取 obi 的子物料的映射配置
     */
    private List<ItemMaterialMappingConfig> itemMaterialMappingConfigs = new ArrayList<>();

}
