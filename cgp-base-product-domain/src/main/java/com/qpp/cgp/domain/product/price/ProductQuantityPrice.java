package com.qpp.cgp.domain.product.price;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.qpp.cgp.config.domaintype.RuntimeDomain;
import com.qpp.cgp.domain.product.AbstractProduct;
import com.qpp.cgp.migration.CgpMongoDomain;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import java.math.BigDecimal;

@RuntimeDomain
@Document(collection = "productqtyprices")
public class ProductQuantityPrice extends CgpMongoDomain {

    private Integer qtyFrom;

    private Integer qtyTo;

    private BigDecimal price;

    @Transient
    @JsonIgnore
//    @ManyToOne(cascade = CascadeType.REFRESH, optional = false)
//    @JoinColumn(name = "product_id", nullable = false)
    private AbstractProduct product;

    @Getter@Setter
    private Long productId;

    public Integer getQtyFrom() {
        return qtyFrom;
    }

    public void setQtyFrom(Integer qtyFrom) {
        this.qtyFrom = qtyFrom;
    }

    public Integer getQtyTo() {
        return qtyTo;
    }

    public void setQtyTo(Integer qtyTo) {
        this.qtyTo = qtyTo;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public AbstractProduct getProduct() {
        return product;
    }

    public void setProduct(AbstractProduct product) {
        if (product != null) {
            this.productId = product.getId();
        }
        this.product = product;
    }

}
