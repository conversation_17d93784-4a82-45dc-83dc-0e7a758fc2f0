package com.qpp.cgp.domain.product.config.material.mapping2dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/03/30
 */
@NoArgsConstructor
@Data
public class OptionalBOMItemMappingDTOConfig extends BOMItemMappingDTOConfig {

    private List<MappingRule> qtyMappingRules = new ArrayList<>();

    private List<MaterialPredicateDTO> materialPredicates = new ArrayList<>();


}
