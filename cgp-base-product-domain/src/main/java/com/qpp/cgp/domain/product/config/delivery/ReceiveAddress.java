package com.qpp.cgp.domain.product.config.delivery;

import com.qpp.cgp.domain.common.Website;
import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * The model of produce partner's receive address.
 *
 * <AUTHOR>
 * @date 2018/4/16
 */
@SuppressWarnings("SpellCheckingInspection")
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
@Document(collection = "receiveaddresses")
public class ReceiveAddress extends MongoDomain {

    /**
     * This {@code ReceiveAddress} belongs to which {@link Website}.
     */
    private Long websiteId;

    /**
     * The name of {@link Address}.
     */
    private String name;

    /**
     * The detail address informations.
     */
    private Address address;

}
