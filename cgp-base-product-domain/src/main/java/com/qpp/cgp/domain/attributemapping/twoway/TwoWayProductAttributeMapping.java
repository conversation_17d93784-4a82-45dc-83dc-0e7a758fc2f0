package com.qpp.cgp.domain.attributemapping.twoway;

import com.qpp.cgp.domain.attributeconfig.MultiAttributeTwoWayPropertyValueConfig;
import com.qpp.cgp.domain.attributemapping.ProductAttributeMapping;
import com.qpp.cgp.domain.executecondition.ExecuteCondition;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

/**
 * 双向产品属性映射
 *
 * <AUTHOR>
 * @since 2019/10/29
 * @see com.qpp.cgp.domain.product.ConfigurableProductSkuAttribute
 */
@NoArgsConstructor
@Data
@Document(collection = "twowayproductattributemappings")
public class TwoWayProductAttributeMapping extends ProductAttributeMapping {

    protected MultiAttributeTwoWayPropertyValueConfig attributeMappingDomain;

    private ExecuteCondition executeCondition;

    private List<TwoWayAttributeMappingGrid> mappingGrids = new ArrayList<>();

    private List<Long> leftSkuAttributeIds = new ArrayList<>();

    private List<Long> rightSkuAttributeIds = new ArrayList<>();

}
