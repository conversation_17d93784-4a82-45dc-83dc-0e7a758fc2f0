package com.qpp.cgp.domain.product;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;

/**
 * <AUTHOR>
 * @date 2020/3/2
 */
@NoArgsConstructor
@Data
public abstract class BusinessSkuProduct extends BusinessProduct {
    //sku编号唯一
    private String sku;

    private Long businessProductConfigurableProductId;
}
