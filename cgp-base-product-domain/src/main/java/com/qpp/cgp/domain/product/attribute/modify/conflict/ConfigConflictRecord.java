package com.qpp.cgp.domain.product.attribute.modify.conflict;

import com.qpp.cgp.config.domaintype.RuntimeDomain;
import com.qpp.cgp.domain.product.attribute.modify.AttrOperation;
import com.qpp.cgp.domain.product.attribute.modify.AttrsModifyOperation;
import com.qpp.mongo.domain.LongMongoDomain;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * 属性修改后，有产生冲突影响的配置的记录。
 */
@Data
@RuntimeDomain
@Document("config_conflict_records")
public abstract class ConfigConflictRecord extends LongMongoDomain {

    private AttrsModifyOperation attrsModifyOperation;

    private List<AttrOperation> attrOperations;
}
