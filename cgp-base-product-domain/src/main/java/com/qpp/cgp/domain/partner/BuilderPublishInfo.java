package com.qpp.cgp.domain.partner;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * As a return type of {@link com.qpp.cgp.controller.product.config.BuilderController#publish(Long)}.
 *
 * <AUTHOR>
 * @date 2018/3/15
 */
@SuppressWarnings("ALL")
@NoArgsConstructor
@Data
public class BuilderPublishInfo {

    /**
     * {@link Product#id}
     */
    private Long productId;

    /**
     * Id who published the builder
     */
    private Long publishBy;

    private List<ProductConfigViewList> productConfigViewLists;
}
