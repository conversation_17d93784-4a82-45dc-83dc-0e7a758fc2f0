package com.qpp.cgp.domain.dto.product;


import com.qpp.cgp.domain.product.category.MainProductCategory;
import com.qpp.cgp.domain.product.category.SubProductCategory;
import com.qpp.core.dto.DTO;
import lombok.Getter;
import lombok.Setter;

public class ProductCategoryDTO implements DTO {

	private static final long serialVersionUID = 1L;
	
	private Long id;
	
	private int sortOrder;
	
	private boolean invisible;
	
	private String name;
	
	private String description1;
	
	private String description2;
	
	private String description3;
	
	private String shortDescription;
	
	private Long parentId;
	
	private boolean leaf; 
	
	private String pageTitle;
	
	private String pageKeyWords;
	
	private String pageDescription;
	
	private String pageUrl;
	
	private Boolean isMain;
	
	private Long website;
	
	private Object productsInfo;

	private Boolean showAsProductCatalog;

	@Getter
	@Setter
	private String catalogPath;
	@Getter
	@Setter
	private int publishStatus;

	@Getter
	@Setter
	private Boolean isRelease;


	public Boolean getShowAsProductCatalog() {
		return showAsProductCatalog;
	}

	public void setShowAsProductCatalog(Boolean showAsProductCatalog) {
		this.showAsProductCatalog = showAsProductCatalog;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public int getSortOrder() {
		return sortOrder;
	}

	public void setSortOrder(int sortOrder) {
		this.sortOrder = sortOrder;
	}

	public boolean isInvisible() {
		return invisible;
	}

	public void setInvisible(boolean invisible) {
		this.invisible = invisible;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription1() {
		return description1;
	}

	public void setDescription1(String description1) {
		this.description1 = description1;
	}

	public String getDescription2() {
		return description2;
	}

	public void setDescription2(String description2) {
		this.description2 = description2;
	}

	public String getDescription3() {
		return description3;
	}

	public void setDescription3(String description3) {
		this.description3 = description3;
	}

	public Long getParentId() {
		return parentId;
	}

	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}

	public boolean isLeaf() {
		return leaf;
	}

	public void setLeaf(boolean leaf) {
		this.leaf = leaf;
	}

	public String getPageTitle() {
		return pageTitle;
	}

	public void setPageTitle(String pageTitle) {
		this.pageTitle = pageTitle;
	}
	

	public Long getWebsite() {
		return website;
	}

	public void setWebsite(Long website) {
		this.website = website;
	}

	public String getPageKeyWords() {
		return pageKeyWords;
	}

	public void setPageKeyWords(String pageKeyWords) {
		this.pageKeyWords = pageKeyWords;
	}

	public String getPageDescription() {
		return pageDescription;
	}

	public void setPageDescription(String pageDescription) {
		this.pageDescription = pageDescription;
	}

	public String getPageUrl() {
		return pageUrl;
	}

	public void setPageUrl(String pageUrl) {
		this.pageUrl = pageUrl;
	}

	public Boolean getIsMain() {
		return isMain;
	}

	public void setIsMain(Boolean isMain) {
		this.isMain = isMain;
	}

	public String getShortDescription() {
		return shortDescription;
	}

	public void setShortDescription(String shortDescription) {
		this.shortDescription = shortDescription;
	}

	public Object getProductsInfo() {
		return productsInfo;
	}

	public void setProductsInfo(Object productsInfo) {
		this.productsInfo = productsInfo;
	}
}
