package com.qpp.cgp.domain.product.config.material;

import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * Created by smart on 9/22/2017.
 */
@NoArgsConstructor
@Data
@Document(collection = "materialtypetospuconfigs")
public class MaterialTypeToSpuConfig extends MongoDomain {

    // ProductConfigMapping 和 ProductConfigDesign 只能关联其中一个

    private Long productConfigDesignId;

    private Long productConfigMappingId;

    private String materialPath;

    private List<BOMItemToFixedConfig> bomItemToFixedConfigs;

    private List<SpuRtObjectFillConfig> spuRtObjectFillConfigs;

}
