package com.qpp.cgp.domain.executecondition;

import java.util.ArrayList;
import java.util.List;

/**
 * 条件类型
 *
 * <AUTHOR>
 * @since 2019/10/29
 */
public class ConditionType {

    public static final String ELSE = "else";
    public static final String NORMAL = "normal";
    public static final String CUSTOM = "custom";

    private static List<String> values = new ArrayList<>();

    static {
        values.add(ELSE);
        values.add(NORMAL);
        values.add(CUSTOM);
    }

    public static List<String> getAll() {
        return values;
    }


}
