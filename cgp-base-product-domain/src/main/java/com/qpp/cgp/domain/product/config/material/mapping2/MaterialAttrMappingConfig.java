package com.qpp.cgp.domain.product.config.material.mapping2;

import com.qpp.cgp.value.ValueEx;
import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 物料属性映射配置
 *
 * <AUTHOR>
 * @since 2019/10/8
 */
@NoArgsConstructor
@Data
public class MaterialAttrMappingConfig extends MongoDomain {

    /**
     * 属性的路径（逗号分割）
     */
    private String path;

    /**
     * 属性值
     */
    private ValueEx value;

}
