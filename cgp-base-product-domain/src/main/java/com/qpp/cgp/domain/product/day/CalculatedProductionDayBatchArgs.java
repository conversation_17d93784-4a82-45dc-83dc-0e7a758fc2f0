package com.qpp.cgp.domain.product.day;

import lombok.Data;

import java.util.Map;
import java.util.Optional;

/**
 * @className: CalculatedProductionDayBatchArgs
 * @description:
 * @author: TT-Berg
 * @date: 2022/10/13
 **/
@Data
public class CalculatedProductionDayBatchArgs {

    private Map<String, Object> context;

    private Long qty;

    private Long productId;

    private Optional<Long> versionedAttributeId = Optional.empty();

}
