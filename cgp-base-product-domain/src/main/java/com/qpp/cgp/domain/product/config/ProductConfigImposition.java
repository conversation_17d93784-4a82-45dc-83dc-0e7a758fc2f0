package com.qpp.cgp.domain.product.config;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qpp.cgp.composing.config.JobGenerateConfig;
import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.cgp.domain.bom.attribute.RtType;
import com.qpp.cgp.domain.bom.runtime.RtObject;
import com.qpp.cgp.migration.CgpMongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@JsonTypeInfo(use = JsonTypeInfo.Id.NONE)
@NoArgsConstructor
@Data
@ConfigDomain
@Document(collection = "productconfigimpositions")
public class ProductConfigImposition extends ProductConfigItem {

    private String configValue;

    /**
     * 指定的排版版本
     * v2_1
     * v2
     */
    private String composingVersion;

    private String bomType;

    /**
     * 是否需要手动定制
     */
    private Boolean isManual;

    @Transient
    @JsonSerialize(using = ProductConfigBomSerializer.class)
//    @ManyToMany(cascade = CascadeType.REFRESH)
//    @JoinTable(name = "cgp_builder_config_imposition_compatibility_to_bom", joinColumns = @JoinColumn(name = "bci_id"), inverseJoinColumns = @JoinColumn(name = "bcb_id"))
//    @CollectionId(type = @Type(type = "long"), columns = @Column(name = "id"), generator = "cgp_id_generator")
    private List<ProductConfigBom> bomCompatibilities = new ArrayList<>();

    private List<Long> bomCompatibilityIds = new ArrayList<>();

    @JsonProperty
    @Transient
    private List<JobGenerateConfig> jobGenerateConfigs = new ArrayList<>();

    @JsonProperty
    @Transient
    private Set<Long> versionedAttributeIds;
    private RtType userParams;

    private RtObject userParamDefaultValues;

    public void setBomCompatibilities(List<ProductConfigBom> bomCompatibilities) {
        if (bomCompatibilities != null) {
            this.bomCompatibilityIds = bomCompatibilities.stream().map(CgpMongoDomain::getId).collect(Collectors.toList());
        }
        this.bomCompatibilities = bomCompatibilities;
    }
}
