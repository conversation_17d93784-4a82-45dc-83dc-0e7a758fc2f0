package com.qpp.cgp.domain.attributemapping.oneway;

import com.qpp.cgp.domain.attributeproperty.AttributePropertyValue;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2019/10/29
 * @see com.qpp.cgp.domain.product.ConfigurableProductSkuAttribute
 */
@NoArgsConstructor
@Data
public class OneWaySimpleCalculateValueMapping extends OneWayProductAttributeMapping {

    private List<Long> inSkuAttributeIds = new ArrayList<>();

    private List<AttributePropertyValue> outputs = new ArrayList<>();

}
