package com.qpp.cgp.domain.product.related;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.cgp.domain.product.BusinessProduct;
import com.qpp.cgp.migration.CgpMongoDomain;

import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.*;

@ConfigDomain
@Document(collection = "relatedproducts")
public class RelatedProduct extends CgpMongoDomain {

    private static final long serialVersionUID = 1L;

    @Transient
//    @ManyToOne(cascade = CascadeType.REFRESH, optional = false)
//    @JoinColumn(name = "related_product_id")
    private BusinessProduct relatedProduct;

    @Transient
    @JsonIgnore
//    @ManyToOne(cascade = CascadeType.REFRESH, optional = false)
//    @JoinColumn(name = "product_id")
    private BusinessProduct product;

    @Transient
//    @ManyToOne(cascade = CascadeType.REFRESH, optional = false)
//    @JoinColumn(name = "type_id")
    private RelatedProductType type;

    public BusinessProduct getRelatedProduct() {
        return relatedProduct;
    }

    public void setRelatedProduct(BusinessProduct relatedProduct) {
        this.relatedProduct = relatedProduct;
    }

    public BusinessProduct getProduct() {
        return product;
    }

    public void setProduct(BusinessProduct product) {
        this.product = product;
    }

    public RelatedProductType getType() {
        return type;
    }

    public void setType(RelatedProductType type) {
        this.type = type;
    }
}
