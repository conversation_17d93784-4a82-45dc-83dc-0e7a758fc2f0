package com.qpp.cgp.domain.attributemapping.oneway;

import com.qpp.cgp.domain.attributemapping.input.InputGroup;
import com.qpp.cgp.domain.executecondition.ExecuteCondition;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since  2020/8/19
 */
@NoArgsConstructor
@Data
public class OneWaySimpleCalculateValueConditionMapping extends OneWaySimpleCalculateValueMapping {

    private ExecuteCondition executeCondition;

    private List<InputGroup> inputGroups;

}
