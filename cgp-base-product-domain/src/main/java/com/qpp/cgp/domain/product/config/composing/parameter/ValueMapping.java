package com.qpp.cgp.domain.product.config.composing.parameter;

import com.qpp.cgp.domain.executecondition.InputCondition;
import com.qpp.cgp.domain.executecondition.operation.value.BaseValue;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class ValueMapping {

    private BaseValue value;

    private InputCondition condition;

    private String description;

}
