package com.qpp.cgp.domain.product.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.hibernate.collection.internal.PersistentBag;


import java.io.IOException;
import java.util.List;

/**
 * Created by smart on 9/6/2017.
 */

@SuppressWarnings("ALL")
public class ProductConfigBomSerializer extends JsonSerializer<Object> {

    @Override
    public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {

        if (value != null) {
            if (value instanceof PersistentBag) {
                gen.writeStartArray();
                PersistentBag persistentBag = (PersistentBag) value;
                persistentBag.stream()
                        .forEach(item -> {
                            try {
                                gen.writeStartObject();
                                ProductConfigBom productConfigBom = (ProductConfigBom) item;
                                if (productConfigBom.getId() != null) {
                                    gen.writeNumberField("id", productConfigBom.getId());
                                }
                                if (productConfigBom.getConfigVersion() != null) {
                                    gen.writeNumberField("configVersion", productConfigBom.getConfigVersion());
                                }
                                if (productConfigBom.getSchemaVersion() != null) {
                                    gen.writeStringField("schemaVersion", productConfigBom.getSchemaVersion());
                                }
                                gen.writeEndObject();
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        });
                gen.writeEndArray();

            } else if (value instanceof List) {
                List<ProductConfigBom> productConfigBoms = (List<ProductConfigBom>) value;
                gen.writeStartArray();
                productConfigBoms.stream()
                        .forEach(item -> {
                            try {
                                gen.writeStartObject();
                                if (item.getId() != null) {
                                    gen.writeNumberField("id", item.getId());
                                }
                                if (item.getConfigVersion() != null) {
                                    gen.writeNumberField("configVersion", item.getConfigVersion());
                                }
                                if (item.getSchemaVersion() != null) {
                                    gen.writeStringField("schemaVersion", item.getSchemaVersion());
                                }
                                gen.writeEndObject();
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        });
                gen.writeEndArray();
            }
        } else {
            serializers.defaultSerializeNull(gen);
        }

    }
}
