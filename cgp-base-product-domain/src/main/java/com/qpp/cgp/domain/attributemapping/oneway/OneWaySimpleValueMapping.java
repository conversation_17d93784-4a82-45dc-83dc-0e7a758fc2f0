package com.qpp.cgp.domain.attributemapping.oneway;

import com.qpp.cgp.domain.attributeproperty.AttributePropertyValue;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2019/10/29
 */
@NoArgsConstructor
@Data
public class OneWaySimpleValueMapping extends OneWayProductAttributeMapping {

    private List<AttributePropertyValue> inputs = new ArrayList<>();

    private List<AttributePropertyValue> outputs = new ArrayList<>();

}
