package com.qpp.cgp.domain.product.attribute.constraint2.single;

import com.qpp.mongo.domain.MongoDomain;
import com.qpp.cgp.expression.Expression;
import com.qpp.cgp.value.ValueEx;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 单离散值约束的单个值<br>
 * Created by smart on 12/29/2017.
 */
@NoArgsConstructor
@Data
public class DiscreteValueConstraintItem extends MongoDomain {

    /**
     * 该值生效的条件
     */
    private List<Expression> conditions = new ArrayList<>();

    /**
     * 返回值
     */
    private ValueEx value;

}
