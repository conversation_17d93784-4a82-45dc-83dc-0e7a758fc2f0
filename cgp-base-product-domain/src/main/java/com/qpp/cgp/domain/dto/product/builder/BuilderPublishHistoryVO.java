package com.qpp.cgp.domain.dto.product.builder;

import com.qpp.mongo.domain.MongoDomain;
import com.qpp.cgp.domain.product.config.ProductConfigView;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

import java.time.Instant;

/**
 * <AUTHOR> 2018/5/17
 */
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
public class BuilderPublishHistoryVO extends MongoDomain {

    private Long productId;

    /**
     * {@link com.qpp.cgp.domain.product.config.BuilderConfig#_id}
     */
    private String builderConfigId;

    /**
     * {@link ProductConfigView#id}
     */
    private Long productConfigViewId;

    /**
     * {@link ProductConfigView#configVersion}
     */
    private String productConfigViewVersion;

    /**
     * The language of the builder
     */
    private String language;

    /**
     * Status before builder published
     */
    private String preStatus;

    /**
     * Status after builder published
     */
    private String postStatus;

    /**
     * Whether publish success
     */
    private Boolean success;

    /**
     * Remark of publish success or failed
     */
    private String message;

    /**
     * Instant to publish the builder
     */
    private Instant publishDate;

    /**
     * Id who published the builder
     */
    private Long publishBy;

    /**
     * The name who published the builder
     */
    private String username;

}
