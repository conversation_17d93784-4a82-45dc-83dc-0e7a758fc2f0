package com.qpp.cgp.domain.product.category;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.cgp.domain.common.Website;
import com.qpp.cgp.migration.CgpMongoDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.HashSet;
import java.util.Set;

@ConfigDomain
@Document(collection = "productcategories")
public abstract class ProductCategory extends CgpMongoDomain {

    protected static final long serialVersionUID = 1L;


    public static final int STATUS_NORMAL = 1;
    public static final int STATUS_REMOVED = 0;

    @Transient
    @JsonIgnore
    protected Website website;

    @Getter
    @Setter
    protected Long websiteId;

    protected Integer sortOrder;

    protected Boolean invisible;

    @Transient
    @JsonIgnore
    protected ProductCategory parent;

    @Getter
    @Setter
    protected Long parentId;

    @Transient
    @JsonIgnore
    protected Set<ProductCategory> children = new HashSet<>();

    protected String name;

    protected String shortDescription;

    protected String description1;

    protected String description2;

    protected String description3;

    @Transient
    @JsonIgnore
    protected ProductCategoryTemplate template;

    @Getter
    @Setter
    protected Long templateId;

    protected Boolean isLeaf;

    protected int status = 1;

    @Getter
    @Setter
    private Integer publishStatus = 1; //1: 发布，2：草稿

    @Transient
    @Getter
    @Setter
    private String catalogPath;

    @ApiModelProperty("是否是正式分类")
    private Boolean isRelease;

    public Long getWebsiteId() {
        return websiteId;
    }

    public void setWebsiteId(Long websiteId) {
        this.websiteId = websiteId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public Boolean getLeaf() {
        return isLeaf;
    }

    public void setLeaf(Boolean leaf) {
        isLeaf = leaf;
    }

    public Integer getPublishStatus() {
        return publishStatus;
    }

    public void setPublishStatus(Integer publishStatus) {
        this.publishStatus = publishStatus;
    }

    public String getCatalogPath() {
        return catalogPath;
    }

    public void setCatalogPath(String catalogPath) {
        this.catalogPath = catalogPath;
    }

    public Boolean getRelease() {
        return isRelease;
    }

    public void setRelease(Boolean release) {
        isRelease = release;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Boolean getInvisible() {
        return invisible;
    }

    public void setInvisible(Boolean invisible) {
        this.invisible = invisible;
    }

    public ProductCategory getParent() {
        return parent;
    }

    public void setParent(ProductCategory parent) {
        if (parent != null) {
            this.setParentId(parent.getId());
        }
        this.parent = parent;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getShortDescription() {
        return shortDescription;
    }

    public void setShortDescription(String shortDescription) {
        this.shortDescription = shortDescription;
    }

    public String getDescription1() {
        return description1;
    }

    public void setDescription1(String description1) {
        this.description1 = description1;
    }

    public String getDescription2() {
        return description2;
    }

    public void setDescription2(String description2) {
        this.description2 = description2;
    }

    public String getDescription3() {
        return description3;
    }

    public void setDescription3(String description3) {
        this.description3 = description3;
    }


    @JsonProperty
    public ProductCategoryTemplate getTemplate() {
        return template;
    }

    @JsonIgnore
    public void setTemplate(ProductCategoryTemplate template) {
        if (template != null) {
            this.setTemplateId(template.getId());
        }
        this.template = template;
    }

    public Boolean getIsLeaf() {
        return isLeaf;
    }

    public void setIsLeaf(Boolean isLeaf) {
        this.isLeaf = isLeaf;
    }

    public Set<ProductCategory> getChildren() {
        return children;
    }

    public void setChildren(Set<ProductCategory> children) {
        this.children = children;
    }

    @JsonProperty
    public Website getWebsite() {
        return website;
    }

    @JsonIgnore
    public void setWebsite(Website website) {
        if (website != null) {
            this.setWebsiteId(website.getId());
        }
        this.website = website;
    }
}
