package com.qpp.cgp.domain.product.category;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.cgp.domain.attribute.Attribute;
import com.qpp.cgp.migration.CgpMongoDomain;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

@ConfigDomain
@Document(collection = "mainproductcategoryattributes")
public class MainProductCategoryAttribute extends CgpMongoDomain {
    private static final long serialVersionUID = 1L;

    @Transient
    @JsonIgnore
    private MainProductCategory category;

    @Getter@Setter
    private Long categoryId;

    @Transient
    @JsonIgnore
    private Attribute attribute;

    @Getter@Setter
    private Long attributeId;

    private int sortOrder;

    private boolean belongToParent;

    private String value;

    public MainProductCategory getCategory() {
        return category;
    }

    public void setCategory(MainProductCategory category) {
        if (category != null) {
            this.setCategoryId(category.getId());
        }
        this.category = category;
    }

    @JsonProperty
    public Attribute getAttribute() {
        return attribute;
    }

    @JsonIgnore
    public void setAttribute(Attribute attribute) {
        if (attribute != null) {
            this.setAttributeId(attribute.getId());
        }
        this.attribute = attribute;
    }

    public int getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(int sortOrder) {
        this.sortOrder = sortOrder;
    }

    public boolean getBelongToParent() {
        return belongToParent;
    }

    public void setBelongToParent(boolean belongToParent) {
        this.belongToParent = belongToParent;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

}
