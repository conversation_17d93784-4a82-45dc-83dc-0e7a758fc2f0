package com.qpp.cgp.domain.product.weight;

import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.cgp.domain.product.AbstractProduct;
import com.qpp.cgp.domain.product.attribute.VersionedProductAttribute;
import com.qpp.cgp.expression.Expression;
import com.qpp.mongo.domain.LongMongoDomain;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Map;

@Document(collection = "productweightconfigs")
@ConfigDomain
@Data
public class ProductWeightConfig extends AbstractProductWeightConfig {

    private Expression value;

    /**
     * 前端存储
     */
    private Map<String, Object> valueDto;

    private Long version;
}
