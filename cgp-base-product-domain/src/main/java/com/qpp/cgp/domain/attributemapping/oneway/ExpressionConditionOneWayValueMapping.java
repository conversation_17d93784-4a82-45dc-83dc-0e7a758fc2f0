package com.qpp.cgp.domain.attributemapping.oneway;

import com.qpp.cgp.domain.attributeproperty.AttributePropertyValue;
import com.qpp.cgp.domain.executecondition.ExecuteCondition;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/1
 */
@Getter 
@Setter
public class ExpressionConditionOneWayValueMapping extends OneWayProductAttributeMapping {

    private ExecuteCondition executeCondition;

    private List<AttributePropertyValue> outputs;

    /**
     * @see com.qpp.cgp.domain.product.ConfigurableProductSkuAttribute
     */
    private List<Long> inSkuAttributeIds = new ArrayList<>();

    /**
     * @see com.qpp.cgp.domain.product.ConfigurableProductSkuAttribute
     */
    private List<Long> outSkuAttributeIds = new ArrayList<>();
}
