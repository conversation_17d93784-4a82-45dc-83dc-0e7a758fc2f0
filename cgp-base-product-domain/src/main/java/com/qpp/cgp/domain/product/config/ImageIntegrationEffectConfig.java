package com.qpp.cgp.domain.product.config;

import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 图片集成用途配置
 *
 * <AUTHOR>
 * @since 2021/4/17
 */
@NoArgsConstructor
@Data
public class ImageIntegrationEffectConfig extends MongoDomain {

    /**
     * 用途(印刷CMYK, 烫金FOIL...)
     */
    private String effect;

    /**
     * PageContent 图片填充路径
     */
    private List<String> imagePageContentPaths;

}
