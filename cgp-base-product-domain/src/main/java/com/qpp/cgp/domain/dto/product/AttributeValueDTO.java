package com.qpp.cgp.domain.dto.product;


import com.qpp.cgp.domain.dto.product.attribute.AttributeDTO;
import com.qpp.core.dto.DTO;

/**
 * 属性值的DTO ConfigurableProductAttributeValue SkuProductAttributeValue
 *
 * <AUTHOR>
 */
public class AttributeValueDTO implements DTO {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String value;

    private String optionIds;

    private Long attributeId;

    private Boolean isSku;

    private AttributeDTO attribute;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getOptionIds() {
        return optionIds;
    }

    public void setOptionIds(String optionIds) {
        this.optionIds = optionIds;
    }

    public Long getAttributeId() {
        return attributeId;
    }

    public void setAttributeId(Long attributeId) {
        this.attributeId = attributeId;
    }

    public boolean isSku() {
        return isSku;
    }

    public void setSku(boolean isSku) {
        this.isSku = isSku;
    }

    public AttributeDTO getAttribute() {
        return attribute;
    }

    public void setAttribute(AttributeDTO attribute) {
        this.attribute = attribute;
    }
}
