package com.qpp.cgp.domain.background;

import com.qpp.mongo.domain.MongoDomain;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@ApiModel("background所有images的sizes配置，与background一一对应")
@Getter@Setter
@Document(collection = "backgroundgenerateconfigs")
public class BackgroundGenerateConfig extends MongoDomain {
//public class BackgroundGenerateConfig {

    /** idRef */
    private Background background;

    private List<BackgroundSizeConfig> sizes;

    /** 用于backgroundImage的生成 */
    private Integer outputDPI;

    /** 用于backgroundImage的生成 */
    private String outputFormat;
}
