package com.qpp.cgp.domain.product.category;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.qpp.cgp.migration.CgpMongoDomain;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Transient;

import javax.persistence.OrderBy;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class MainProductCategory extends ProductCategory {

	private static final long serialVersionUID = 1L;

	private boolean isMain = true;

	@Transient
	@JsonIgnore
	@OrderBy("sortOrder ASC")
	private List<MainProductCategoryAttribute> attributes = new ArrayList<>();

	@Getter@Setter
	private List<Long> attributeIds = new ArrayList<>();

	@Override
	public MainProductCategory getParent() {
		return (MainProductCategory) super.getParent();
	}

	@JsonProperty
	public List<MainProductCategoryAttribute> getAttributes() {
		return attributes;
	}

	@JsonIgnore
	public void setAttributes(List<MainProductCategoryAttribute> attributes) {
		if (attributes != null) {
			attributeIds = attributes.stream().map(CgpMongoDomain::getId).collect(Collectors.toList());
		}
		this.attributes = attributes;
	}

}
