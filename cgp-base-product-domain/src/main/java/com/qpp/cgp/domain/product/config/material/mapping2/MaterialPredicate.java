package com.qpp.cgp.domain.product.config.material.mapping2;

import com.qpp.cgp.value.ValueEx;
import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 物料断言器
 *
 * <AUTHOR>
 * @since 2019/10/8
 */
@NoArgsConstructor
@Data
public abstract class MaterialPredicate extends MongoDomain {

    /**
     * condition 为 true 才有效
     */
    protected ValueEx condition;

}
