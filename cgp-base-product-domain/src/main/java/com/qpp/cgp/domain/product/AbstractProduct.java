package com.qpp.cgp.domain.product;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.qpp.cgp.domain.common.Currency;
import com.qpp.cgp.domain.product.category.MainProductCategory;
import com.qpp.cgp.domain.product.category.SubProductCategory;
import com.qpp.cgp.domain.product.media.ProductMedia;
import com.qpp.cgp.domain.product.price.ProductPriceRule;
import com.qpp.cgp.domain.product.price.ProductQuantityPrice;
import com.qpp.cgp.domain.product.producedays.ProductProduceDaysTemplate;
import com.qpp.cgp.migration.CgpMongoDomain;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Transient;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
public abstract class AbstractProduct extends CgpMongoDomain {

    private static final long serialVersionUID = 1L;

    public static String BUILDER_TYPE_H5 = "H5";
    public static String BUILDER_TYPE_FLASH = "FLASH";
    public static String BUILDER_TYPE_CHILI = "CHILI";
    public static String BUILDER_TYPE_QPP = "QPP";
    public static String BUILDER_TYPE_COMPATIBILITY = "COMPATIBILITY"; //compatibility

    //一个model对应一个ConfigurableProduct
    protected String model;

    protected String name;

    protected String shortDescription;

    protected String description1;

    protected String description2;

    protected String description3;

//    @JsonIgnore
    protected Integer status;

//    @JsonIgnore
    @Temporal(TemporalType.TIMESTAMP)
    protected Date dateAvailable;

    protected Double salePrice;

    /**
     * 价格货币
     */
    private Currency currency;

    /**
     * 最低价格：用于QPMN的类目展示产品的时候显示的价格
     */
    protected BigDecimal lowestPrice;

    protected Double weight;

//    @JsonIgnore
    protected Boolean invisible;

    protected String compositeId;

    @Transient
//    @OneToMany(cascade = CascadeType.ALL, mappedBy = "product", orphanRemoval = true)
    @OrderBy("sortOrder ASC")
    protected List<ProductMedia> medias = new ArrayList<>();

    @Transient
//    @ManyToOne(cascade = CascadeType.ALL, optional = true)
//    @JoinColumn(name = "product_default_image_id", nullable = true)
    protected ProductMedia defaultImage;

    protected Long defaultImageId;

    @Transient
//    @OneToOne(cascade = CascadeType.ALL, optional = false)
//    @JoinColumn(name = "product_template_id", nullable = false)
    protected ProductTemplate template;

    protected Long templateId;

    @Transient
//    @JsonIgnore
//    @ManyToOne(cascade = CascadeType.REFRESH, optional = false)
//    @JoinColumn(name = "product_main_category_id", nullable = false)
    protected MainProductCategory mainCategory;

    protected Long mainCategoryId;

    @Transient
//    @ManyToMany(cascade = CascadeType.REFRESH,fetch = FetchType.EAGER)
//    @JoinTable(name = "cgp_product_to_sub_category", joinColumns = @JoinColumn(name = "product_id"), inverseJoinColumns = @JoinColumn(name = "sub_category_id"))
//    @CollectionId(type = @Type(type = "long"), columns = @Column(name = "id"), generator = "cgp_id_generator")
    protected List<SubProductCategory> subCategories = new ArrayList<>();

    /** 原多对多表并入产品表，使用数组字段存储 */
    protected List<Long> subCategoryIds = new ArrayList<>();

    @Transient
//    @ManyToOne(cascade = CascadeType.REFRESH, optional = true)
//    @JoinColumn(name = "product_paiban_type_id", nullable = true)
    protected ComposingType composingType;

    protected Long composingTypeId;

    private String builderType;

    private int mustOrderQuantity;

    /** 空表，产品表内该字段值全为null */
    @Transient
    @JsonIgnore
//    @OneToOne(cascade = CascadeType.ALL, optional = true)
//    @JoinColumn(name = "product_price_rule_id")
    private ProductPriceRule priceRule;


    /** 空表，产品表内该字段值全为null */
    @Transient
    @JsonIgnore
//    @ManyToOne(cascade = CascadeType.REFRESH, optional = true)
//    @JoinColumn(name = "produce_days_template_id")
    private ProductProduceDaysTemplate produceDaysTemplate;

    @Transient
//    @OneToMany(cascade = {CascadeType.ALL}, orphanRemoval = true, mappedBy = "product")
    private List<ProductQuantityPrice> quantityPrices = new ArrayList<>();


    /**
     * 产品所属模式
     */
    @Enumerated(EnumType.STRING)
    private ProductMode mode;

    /**
     * 是否是成品产品
     */
    private Boolean isFinished = false;

    //region 重写setter方法
    public void setDefaultImage(ProductMedia defaultImage) {
        if (defaultImage != null) {
            this.defaultImageId = defaultImage.getId();
        }
        this.defaultImage = defaultImage;
    }

    public void setTemplate(ProductTemplate template) {
        if (template != null) {
            this.templateId = template.getId();
        }
        this.template = template;
    }

    public void setMainCategory(MainProductCategory mainCategory) {
        if (mainCategory != null) {
            this.mainCategoryId = mainCategory.getId();
        }
        this.mainCategory = mainCategory;
    }

    public void setSubCategories(List<SubProductCategory> subCategories) {
        if (subCategories != null) {
            this.subCategoryIds = subCategories.stream().map(CgpMongoDomain::getId).collect(Collectors.toList());
        }
        this.subCategories = subCategories;
    }

    public void setComposingType(ComposingType composingType) {
        if (composingType != null) {
            this.composingTypeId = composingType.getId();
        }
        this.composingType = composingType;
    }

    //endregion
}
