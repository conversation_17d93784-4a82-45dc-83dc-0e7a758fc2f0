package com.qpp.cgp.domain.attribute;

import java.util.ArrayList;
import java.util.List;

public class AttributeInputType {

	//input value
	public static final String TEXTFIELD = "TextField"; //textfield

	public static final String TEXTAREA = "TextArea";//textarea
	
	public static final String DATE = "Date";//datefield
	
	public static final String FILE = "File";//fileuploadfield
	 
	public static final String CANVAS = "Canvas";//canvas
//	
	public static final String DIYCONFIG = "DiyConfig";//textarea
	
	public static final String DIYDESIGN = "DiyDesign";//textfield
	
	//optional value
	public static final String DROPDOWN = "DropList";//combo

	public static final String CHECKBOX = "CheckBox";//checkbox

	public static final String RADIOBUTTONS = "RadioButtons";//radiobuttons

	public static final String YESORNO = "YesOrNo";//radiobuttons yes和no的单选

	public static final String COLOR = "Color";//combo single
	
	
	
	
	public static List<String> optional;
	public static List<String> all;
	
	static {
		optional = new ArrayList<>();
		optional.add(DROPDOWN);
		optional.add(CHECKBOX);
		optional.add(RADIOBUTTONS);
		optional.add(YESORNO);
		optional.add(COLOR);
		
		all = new ArrayList<>();
		all.add(TEXTFIELD);
		all.add(TEXTAREA);
		all.add(DATE);
		all.add(FILE);
		all.add(CANVAS);
		all.add(DIYCONFIG);
		all.add(DIYDESIGN);
		all.add(DROPDOWN);
		all.add(CHECKBOX);
		all.add(RADIOBUTTONS);
		all.add(YESORNO);
		all.add(COLOR);
		
	}

}
