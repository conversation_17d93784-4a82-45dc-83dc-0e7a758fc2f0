package com.qpp.cgp.domain.attributeconfig;

import com.qpp.cgp.domain.attributecalculate.MappingType;
import com.qpp.cgp.domain.attributecalculate.PropertyPath;
import com.qpp.cgp.expression.Expression;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * <AUTHOR>
 * 单向属性配置
 */

@NoArgsConstructor
@Data
@Document(collection = "multiattributeonewaypropertyvalueconfigs")
public class MultiAttributeOneWayPropertyValueConfig extends MappingType {


    /**
     * 单向配置的影响的Sku属性的PropertyPath
     */
    private PropertyPath propertyPath;


    /**
     * 触发条件
     */
    private Expression condition;

    /**
     * 计算后的值
     */
    private List<AttributePropertyValue> propertyValues;

    /**
     * 多触发点配置
     */
    private List<MultiPropertyPath> multiPropertyPaths;

}
