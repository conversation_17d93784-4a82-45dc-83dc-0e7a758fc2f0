package com.qpp.cgp.domain.product.config;

import com.qpp.cgp.domain.common.font.Font;
import com.qpp.cgp.domain.common.font.FontDTO;
import com.qpp.cgp.dto.LocaleBuilderLocationConfigDTO;
import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * Created by smart on 9/28/2017.
 */
@NoArgsConstructor
@Data
@Document(collection = "builderconfigs")
public class BuilderConfig extends MongoDomain {

    private Long productConfigViewId;

    private List<LocaleBuilderLocationConfig> builderLocations;

    @Transient
    private List<LocaleBuilderLocationConfigDTO> builderLocationDTOs;

    /**
     * WhiteLabel产品的可用字体集合
     */
    @Transient
    private List<FontDTO> fontDTOs;

    /**
     * WhiteLabel产品的可用字体集合
     */
    private List<Font> fonts;

    /**
     * 默认字体Id
     */
    private String defaultFont;

    @Transient
    private FontDTO defaultFontDTO;


}
