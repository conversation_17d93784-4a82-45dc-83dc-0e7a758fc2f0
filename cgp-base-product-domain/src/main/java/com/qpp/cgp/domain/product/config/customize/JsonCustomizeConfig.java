package com.qpp.cgp.domain.product.config.customize;

import com.qpp.cgp.domain.bom.attribute.RtType;
import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

@NoArgsConstructor
@Data
@Document(collection = "jsoncustomizeconfigs")
public class JsonCustomizeConfig extends MongoDomain {

    private Long productConfigDesignId;

    private String productMaterialViewTypeId;

    private String productMaterialViewTypeCode;

    /**
     * AbstractMaterialViewType#code
     */
    private String materialViewTypeCode;

    private String materialPath;

    private RtType designDataTypeSchema;

    private String designDataJsonSchema;

    private String pageContentId;

    private String operatorConfigId;

}
