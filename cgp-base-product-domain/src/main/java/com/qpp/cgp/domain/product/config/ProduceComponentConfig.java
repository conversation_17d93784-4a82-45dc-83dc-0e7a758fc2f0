package com.qpp.cgp.domain.product.config;

import com.qpp.mongo.domain.MongoDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * 生产组件报表配置
 *
 * <AUTHOR> Lee 2018/10/22 10:31
 */
@ApiModel("生产组件报表配置")
@NoArgsConstructor
@Data
@Document(collection = "producecomponentconfigs")
public class ProduceComponentConfig extends MongoDomain {


    @ApiModelProperty(value = "生产组件名称，")
    private String name;

    @ApiModelProperty(value = "生产组件对应物料名称")
    private String materialName;

    @ApiModelProperty(value = "关联的ProductConfigBom的id", required = true)
    private Long productConfigBomId;

    @ApiModelProperty(value = "物料路径", required = true)
    private String materialPath;

    @ApiModelProperty(value = "该组件是否需要打印", required = true)
    private Boolean isNeedPrint;

    @ApiModelProperty(value = "该组件排版可用的打印机,只有当需要打印时才生效")
    private List<String> availablePrinters;

}
