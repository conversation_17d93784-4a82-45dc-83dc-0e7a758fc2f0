package com.qpp.cgp.domain.product.config.material.mapping;

import com.qpp.mongo.domain.MongoDomain;
import com.qpp.cgp.expression.Expression;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 待定件映射的物料选项
 */
@NoArgsConstructor
@Data
public class OptionalBOMItemMaterialOption extends MongoDomain {

    /**
     * 匹配表达式，满足时materialId有效
     */
    private Expression matchExpression;

    /**
     * 该物料选项对应的物料
     */
    private String materialId;

}
