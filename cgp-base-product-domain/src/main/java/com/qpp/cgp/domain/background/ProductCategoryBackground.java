package com.qpp.cgp.domain.background;

import com.qpp.cgp.domain.product.category.ProductCategory;
import com.qpp.mongo.domain.MongoDomain;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Document;

@Getter@Setter
@Document(collection = "productcategorybackgrounds")
public class ProductCategoryBackground extends MongoDomain {

    private ProductCategory category;

    private BackgroundSeries backgroundSeries;
}
