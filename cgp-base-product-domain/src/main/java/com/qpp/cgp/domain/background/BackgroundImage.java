package com.qpp.cgp.domain.background;

import com.qpp.mongo.domain.MongoDomain;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Document;

@Getter@Setter
@Document(collection = "backgroundimages")
public class BackgroundImage extends MongoDomain {

    /** Unique */
    private Background background;

    /** Unique, png file name */
    private String imageName;

    /** PDF name */
    private String printFileName;

    private Integer imageWidth;

    private Integer imageHeight;

    private Number actualWidth;

    private Number actualHeight;

    private String unit;

    /** refer to {@link BackgroundSizeConfig} */
    private String sizeConfigId;
}
