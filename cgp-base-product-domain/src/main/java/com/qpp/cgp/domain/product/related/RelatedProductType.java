package com.qpp.cgp.domain.product.related;

import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.cgp.migration.CgpMongoDomain;

import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@ConfigDomain
@Document(collection = "relatedproducttypes")
public class RelatedProductType extends CgpMongoDomain {

    private static final long serialVersionUID = 1L;

    //推荐销售类型
    public static Long TYPE_SALE = 1L;
    public static Long TYPE_CART = 2L;
    public static Long TYPE_PRODUCT_PAGE = 3L;

    private String name;

    private String description;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
