package com.qpp.cgp.domain.product.config.material.mapping2dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/03/30
 * @see com.qpp.cgp.domain.product.config.material.mapping2.UnassignBOMItemMappingConfig
 */
@NoArgsConstructor
@Data
public class UnassignBOMItemMappingDTOConfig extends BOMItemMappingDTOConfig {

    private List<MappingRule> bomItemQtyMappingRules = new ArrayList<>();

    private List<ObiMappingIndexRule> obiMappingIndexRules = new ArrayList<>();

    private List<UBIToOBIItemMappingDTOConfig> obiMappings = new ArrayList<>();

}
