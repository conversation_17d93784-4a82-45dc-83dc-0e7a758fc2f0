package com.qpp.cgp.domain.product.config.material.mapping2;

import com.qpp.cgp.expression.Expression;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 物料排除过滤器, 通过表达式过滤物料
 *
 * <AUTHOR>
 * @since 2019/10/8
 */
@NoArgsConstructor
@Data
public class ExcludeFilter extends MaterialPredicate {

    /**
     * 当 condition 为 null 或 true 时,
     * filter 表达式结果为 true 的物料会被排除
     */
    private Expression filter;

}
