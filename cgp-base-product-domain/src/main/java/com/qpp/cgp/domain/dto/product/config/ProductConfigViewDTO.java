package com.qpp.cgp.domain.dto.product.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.cgp.domain.product.config.*;
import com.qpp.cgp.migration.CgpMongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by smart on 9/28/2017.
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.NONE)
@NoArgsConstructor
@Data
@ConfigDomain
@Document(collection = "productconfigviews")
public class ProductConfigViewDTO extends CgpMongoDomain {

    private String type;

    private Long configVersion;

    private String configValue;

    private Integer status;

    private String context;

    private String path;

    private String bomType;

    private Long productConfigId;

    private String builderViewVersion;

    private Integer modifyVersion = 1;

    private Boolean canUpgradeModifyVersion = false;

    private Set<Long> versionedAttributeIds;

    private Long sourceConfigId;

    @Transient
    @JsonSerialize(using = ProductConfigBomSerializer.class)
//    @ManyToMany(cascade = CascadeType.REFRESH)
//    @JoinTable(name = "cgp_builder_config_view_compatibility_to_bom", joinColumns = @JoinColumn(name = "bcv_id"), inverseJoinColumns = @JoinColumn(name = "bcb_id"))
//    @CollectionId(type = @Type(type = "long"), columns = @Column(name = "id"), generator = "cgp_id_generator")
    private List<ProductConfigBom> bomCompatibilities = new ArrayList<>();

    private List<Long> bomCompatibilityIds = new ArrayList<>();

    @Transient
    @JsonSerialize(using = ProductConfigViewSerializer.class)
//    @ManyToMany(cascade = CascadeType.REFRESH)
//    @JoinTable(name = "cgp_builder_config_view_compatibility_to_view", joinColumns = @JoinColumn(name = "bcv_id"), inverseJoinColumns = @JoinColumn(name = "cpt_bcv_id"))
//    @CollectionId(type = @Type(type = "long"), columns = @Column(name = "id"), generator = "cgp_id_generator")
    private List<ProductConfigView> viewCompatibilities = new ArrayList<>();

    private List<Long> viewCompatibilityIds = new ArrayList<>();

    @Transient
    @JsonProperty
    private BuilderConfig builderConfig;

    private String description;

    public void setBomCompatibilities(List<ProductConfigBom> bomCompatibilities) {
        if (bomCompatibilities != null) {
            this.bomCompatibilityIds = bomCompatibilities.stream().map(CgpMongoDomain::getId).collect(Collectors.toList());
        }
        this.bomCompatibilities = bomCompatibilities;
    }

    public void setViewCompatibilities(List<ProductConfigView> viewCompatibilities) {
        if (viewCompatibilities != null) {
            this.viewCompatibilityIds = viewCompatibilities.stream().map(CgpMongoDomain::getId).collect(Collectors.toList());
        }
        this.viewCompatibilities = viewCompatibilities;
    }
}
