package com.qpp.cgp.domain.product.config;

import com.qpp.cgp.domain.product.attribute.VersionedProductAttribute;
import com.qpp.cgp.migration.CgpMongoDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Transient;

import java.util.List;

/**
 * <AUTHOR> Lee 2022/3/2 17:43
 */
@Getter
@Setter
public abstract class ProductConfigItem extends CgpMongoDomain {

    private static final long serialVersionUID = 1L;

    public static final int STATUS_DELETED = 0;//已刪除

    public static final int STATUS_DRAFT = 1;//草稿

    public static final int STATUS_TEST = 2;//測試

    public static final int STATUS_RELEASE = 3;//發佈

    protected Long configVersion;

    protected Integer status;

    protected Long productConfigId;

    protected int modifyVersion = 1;

    @ApiModelProperty("当前配置是否可以升级小版本")
    @Transient
    protected Boolean canUpgradeModifyVersion = false;

    @ApiModelProperty("原配置ID（指向由哪个配置拷贝生成的）")
    protected Long sourceConfigId;

    @ApiModelProperty("描述")
    private String description;
}
