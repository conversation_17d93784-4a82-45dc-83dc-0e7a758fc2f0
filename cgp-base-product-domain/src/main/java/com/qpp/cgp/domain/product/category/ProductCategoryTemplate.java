package com.qpp.cgp.domain.product.category;

import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.cgp.migration.CgpMongoDomain;
import org.springframework.data.mongodb.core.mapping.Document;

@ConfigDomain
@Document(collection = "productcategorytemplates")
public class ProductCategoryTemplate extends CgpMongoDomain {

    private static final long serialVersionUID = 1L;

    private String pageTitle;

    private String pageKeyWords;

    private String pageDescription;

    private String pageUrl;

    public String getPageTitle() {
        return pageTitle;
    }

    public void setPageTitle(String pageTitle) {
        this.pageTitle = pageTitle;
    }

    public String getPageKeyWords() {
        return pageKeyWords;
    }

    public void setPageKeyWords(String pageKeyWords) {
        this.pageKeyWords = pageKeyWords;
    }

    public String getPageDescription() {
        return pageDescription;
    }

    public void setPageDescription(String pageDescription) {
        this.pageDescription = pageDescription;
    }

    public String getPageUrl() {
        return pageUrl;
    }

    public void setPageUrl(String pageUrl) {
        this.pageUrl = pageUrl;
    }
}
