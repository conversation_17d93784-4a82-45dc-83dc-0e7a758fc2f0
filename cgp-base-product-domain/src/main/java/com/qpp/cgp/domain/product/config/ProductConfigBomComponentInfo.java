package com.qpp.cgp.domain.product.config;

import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.mongo.domain.LongMongoDomain;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

@ConfigDomain
@Document(collection = "productconfigbomcomponentinfos")
@Data
public class ProductConfigBomComponentInfo extends LongMongoDomain {

    private ProductConfigBom productConfigBom;

    private String mainComponentMaterialPath;

}
