package com.qpp.cgp.domain.attributecalculate;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

import java.util.List;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;

/**
 * <AUTHOR> jerry
 * @version : 1.0
 * Description : 属性Model用于存储属性的值或则值
 * Create Date : 2019-04-26 16:10
 **/
@ApiModel(value = "属性值或则相关属性")
@JsonInclude(ALWAYS)
@NoArgsConstructor
@Data
public class SkuAttributeModel {

    @ApiModelProperty(value = "属于那个Profile的Id")
    private String profileId;

    @ApiModelProperty(value = "属于那个Profile的code")
    private String profileCode;

    @ApiModelProperty(value = "属于那个Group的名字")
    private String groupName;

    @ApiModelProperty(value = "属于那个Group的Id")
    private String groupId;

    @ApiModelProperty(value = "属于那个Group的Code")
    private String groupCode;

    @ApiModelProperty(value = "关联的Attribute的Id")
    private Long skuAttributeId;

    @ApiModelProperty(value = "关联的sku属性的code")
    private String skuAttributeCode;

    @ApiModelProperty(value = "attributeId")
    private Long attributeId;

    @ApiModelProperty(value = "属性展示的名字")
    private String displayName;

    @ApiModelProperty(value = "是否需填写")
    private Boolean required;

    @ApiModelProperty(value = "是否隐藏")
    private Boolean hidden;

    @ApiModelProperty(value = "是否启用")
    private Boolean enable;

    @ApiModelProperty(value = "值类型")
    private String valueType;

    @ApiModelProperty(value = "选择类型")
    private String selectType;

    @ApiModelProperty(value = "详情")
    private String detail;

    @ApiModelProperty(value = "属性的选项Model列表")
    private List<AttrOption> attrOptions;

    @ApiModelProperty(value = "属性的默认值")
    private Object defaultValue;

    @ApiModelProperty(value = "是否是合法的")
    private Boolean isLegal;

    @ApiModelProperty(value = "错误提示信息")
    private ErrorMsg errorMsg;

    @ApiModelProperty(value = "属性值")
    private Object value;

    @ApiModelProperty(value = "是否当前属性修改")
    private Boolean isDirty;

    @ApiModelProperty(value = "是否是Sku属性")
    private Boolean isSku;

    @ApiModelProperty(value = "占位符")
    private String placeHolder;

    @ApiModelProperty(value = "排序")
    private int sortOrder;

    @ApiModelProperty(value = "存储精确度")
    private Integer saveAccuracy;

    @ApiModelProperty(value = "显示精度")
    private Integer displayAccuracy;

    @ApiModelProperty(value = "是否只只读'")
    private Boolean readOnly;

    @ApiModelProperty(value = "默认DefaultEnableOptions")
    private String defaultEnableOptions;

    @ApiModelProperty(value = "默认defaultHiddenOption")
    private String defaultHiddenOptions;

    @ApiModelProperty(value = "默认选项展示")
    private String defaultDisplayOptions;

    public String getGroupCode() {
        if (StringUtils.isBlank(groupCode)) {
            return groupId;
        }
        return groupCode;
    }

    /**
     * 新版的错误信息
     */
    private List<ErrorInfo> errors;


}
