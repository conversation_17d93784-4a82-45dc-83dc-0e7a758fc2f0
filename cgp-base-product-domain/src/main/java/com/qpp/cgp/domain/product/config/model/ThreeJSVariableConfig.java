package com.qpp.cgp.domain.product.config.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class ThreeJSVariableConfig extends ThreeDModelVariableConfig{

    private Number minZoom;

    private Number maxZoom;

    private Number defaultZoom;

    private Number zoomStep;

    private ThreeDCamera camera;

    private List<ThreeDView> views;

    private ThreeDModel threeDModel;

    private String bgColor;
}
