package com.qpp.cgp.domain.product.config.model;

import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "threedmodelconfigs")
@NoArgsConstructor
@Data
public class ThreeDModelConfig extends MongoDomain {

    /**
     * 唯一约束
     */
    private String modelName;

    private String description;
}
