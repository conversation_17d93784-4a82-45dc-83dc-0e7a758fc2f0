package com.qpp.cgp.domain.productssuit;

import com.qpp.cgp.domain.product.AbstractWhitelabelProduct;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Document(collection = "productsets")
@NoArgsConstructor
@Data
public abstract class ProductSet extends AbstractWhitelabelProduct {

    @Transient
    private List<ProductSetItem> productSetItems;


    /**
     * description
     */
    private String description;

}
