package com.qpp.cgp.domain.productssuit;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
public class SkuProductSet extends ProductSet implements WhitelabelSkuProduct{

    private ConfigurableProductSet configurableProductSet;

    @Override
    public Double getSalePrice() {
        return super.getSalePrice();
    }


    @Override
    public BigDecimal getPrice() {
        if (salePrice != null) {
            return BigDecimal.valueOf(super.getSalePrice());
        }else{
            return BigDecimal.ZERO;
        }
    }
    @Override
    public Double getWeight() {
        return super.getWeight();
    }

    public SkuProductSet convertToIdRef() {
        SkuProductSet result = new SkuProductSet();
        result.setId(super.getId());
        result.setClazz(super.getClazz());
        return result;
    }
}
