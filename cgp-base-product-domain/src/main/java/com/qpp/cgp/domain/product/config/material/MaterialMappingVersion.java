package com.qpp.cgp.domain.product.config.material;

import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * BOM schemaVersion 5 的物料映射配置和物料映射器版本
 *
 * <AUTHOR>
 * @since 2019/10/25
 */
@NoArgsConstructor
@Data
@Document(collection = "materialMappingVersions")
public class MaterialMappingVersion extends MongoDomain {

    // ProductConfigMapping 和 ProductConfigDesign 只能关联其中一个

    /**
     * pcd id
     */
    private Long productConfigDesignId;

    /**
     * pcm id
     */
    private Long productConfigMappingId;

    /**
     * mappingVersion = 1:
     *      采用 {@link com.qpp.cgp.domain.product.config.material.mapping} 的映射配置和
     *      {@link com.qpp.cgp.manager.product.config.material.MaterialMapper} 的映射器
     *
     * mappingVersion = 2:
     *      采用 {@link com.qpp.cgp.domain.product.config.material.mapping2} 的映射配置和
     *      {@link com.qpp.cgp.manager.product.config.materialmapping.MaterialMapper} 的映射器
     */
    private String mappingVersion;


}
