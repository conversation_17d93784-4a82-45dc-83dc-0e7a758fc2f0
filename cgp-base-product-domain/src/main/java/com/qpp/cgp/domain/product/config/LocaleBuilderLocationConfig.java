package com.qpp.cgp.domain.product.config;

import com.qpp.cgp.domain.common.Language;
import com.qpp.cgp.domain.common.font.Font;
import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by smart on 9/28/2017.
 */
@NoArgsConstructor
@Data
public class LocaleBuilderLocationConfig extends MongoDomain {

    private String locale;

    private Language language;

    private String title;

    private String builderUrl;

    private String userPreviewUrl;

    private String manufacturePreviewUrl;

    /**
     * builder发布状态
     */
    private String builderPublishStatus;

    private int sortOrder;

    /**
     * Builder可用的字体集合
     */
    private List<Font> fonts;

    /**
     * 默认字体Id
     */
    private String defaultFont;
}
