package com.qpp.cgp.domain.attributeconfig;

import com.qpp.cgp.domain.attributecalculate.SkuAttributeModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class MappingLinkLog {

    /**
     * MappingLinkId
     */
    private String mappingLinkId;

    /**
     * MappingLinkName
     */
    private String mappingLinkName;

    /**
     * runMappingTypeLogs
     */
    private List<MappingTypeLog> mappingTypeLogs;

    /**
     * MappingLinkLog contain MappingTypeIds
     */
    private List<String> mappingLinkMappingTypeIds;

    /**
     * init modifySkuAttributeId
     */
    private Long initModifySkuAttributeId;

    /**
     * initModifyValue
     */
    private Object initModifyValue;

    /**
     * skuAttributeModel
     */
    private SkuAttributeModel skuAttributeModel;
}
