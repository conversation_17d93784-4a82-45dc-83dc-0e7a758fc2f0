package com.qpp.cgp.domain.product.config.material.mapping2;

import com.qpp.cgp.expression.Expression;
import com.qpp.cgp.value.ValueEx;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 待定件 BI 映射配置
 * <AUTHOR>
 * @since 2019/10/8
 */
@NoArgsConstructor
@Data
public class UnassignBOMItemMappingConfig extends BOMItemMappingConfig {

    /**
     * 生成 obi 的数量
     */
    private ValueEx generateObiQuantity;

    /**
     * 用于获取生成的 obi 的 BI 映射配置 id 的表达式
     */
    private Expression bomItemMappingIndexExpression;

    /**
     * 生成的 obi 的映射配置列表
     */
    private List<UBIToOBIItemMappingConfig> ubiToObiItemMappingConfigs = new ArrayList<>();

}
