package com.qpp.cgp.domain.background;

import com.qpp.mongo.domain.MongoDomain;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Getter@Setter
@Document(collection = "backgroundseries")
public class BackgroundSeries extends MongoDomain {

    /** Unique */
    private String name;

    private String displayName;

    @Transient
    private List<Background> backgrounds;

    public BackgroundSeries convert2IdRef() {
        BackgroundSeries result = new BackgroundSeries();
        result.setId(this.getId());
        result.setClazz(this.getClazz());
        return result;
    }
}
