package com.qpp.cgp.domain.attributemapping.oneway;

import com.qpp.cgp.domain.attributeconfig.AttributeGroup;
import com.qpp.cgp.domain.executecondition.ExecuteCondition;
import com.qpp.cgp.domain.product.ConfigurableProductSkuAttribute;
import lombok.Data;

import java.util.List;

@Data
public class ConditionOneWayGroupValueMapping extends OneWayProductAttributeMapping{

    private ExecuteCondition executeCondition;

    private List<AttributeMappingRule> mappingRules;

    private List<ConfigurableProductSkuAttribute> inSkuAttributes;

    private List<AttributeGroup> outGroups;
}
