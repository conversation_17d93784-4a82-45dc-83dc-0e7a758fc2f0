package com.qpp.cgp.dto.product.config.model;

import com.qpp.cgp.domain.product.config.model.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter@Setter
public class ThreeDModelRuntimeDTO {

//    private ThreeDModelRuntimeConfig threeDModelRuntimeConfig;

//    private ThreeDModelVariableConfig threeDModelVariableConfig;

    /**
     * {@link ThreeDModelRuntimeConfig}及其派生类的字段
     */
    private List<ThreeDRuntimeAsset> assets;

    /**
     * 下方均为{@link ThreeDModelVariableConfig}及其派生类的字段
     */
    private int version;

    /**
     * 状态，等同产品配置的版本，测试、正式、草稿
     */
    private int status;

    /**
     * 引擎：目前只有一种ThreeJS
     */
    private String engine;

    /**
     * 结构版本
     */
    private int structVersion;

    private String modelFileName;

    private Number minZoom;

    private Number maxZoom;

    private Number defaultZoom;

    private Number zoomStep;

    private ThreeDCamera camera;

    private List<ThreeDView> views;

    private ThreeDModel threeDModel;

    private String bgColor;

}
