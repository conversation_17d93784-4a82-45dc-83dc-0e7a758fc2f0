package com.qpp.cgp.dto;

import com.qpp.cgp.domain.common.Language;
import com.qpp.cgp.domain.common.font.FontDTO;
import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version : 1.0
 * Description : DTO
 * Create Date : 2020-03-20 11:45
 **/
@NoArgsConstructor
@Data
public class LocaleBuilderLocationConfigDTO extends MongoDomain {
    private String locale;

    private String title;

    private Language language;

    private String builderUrl;

    private String userPreviewUrl;

    private String manufacturePreviewUrl;

    /**
     * builder发布状态
     */
    private String builderPublishStatus;

    private int sortOrder;

    /**
     * Builder可用的字体集合
     */
    private List<FontDTO> fonts;

    /**
     * 默认字体
     */
    private FontDTO defaultFont;
}
