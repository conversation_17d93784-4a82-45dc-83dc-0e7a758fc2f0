package com.qpp.cgp.repository.product.cascade;

import com.qpp.cgp.config.mongo.MongoTemplateBeanNames;
import com.qpp.cgp.domain.common.EntityStatus;
import com.qpp.cgp.domain.product.ConfigurableProduct;
import com.qpp.cgp.domain.product.ConfigurableProductSkuAttribute;
import com.qpp.cgp.domain.product.SkuProduct;
import com.qpp.cgp.migration.CgpMongoDomain;
import com.qpp.cgp.repository.product.ConfigurableProductSkuAttributeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 基于{@link ProductSetter}，对{@link com.qpp.cgp.domain.product.ConfigurableProduct}进行补充填充
 */
@Component
public class SkuProductSetter {

    @Autowired
    private ProductSetter productSetter;

    @Autowired
    private ConfigurableProductSetter configurableProductSetter;

    @Autowired
    @Qualifier(MongoTemplateBeanNames.CONFIG)
    private MongoTemplate mongoTemplate;

    @Autowired
    ConfigurableProductSkuAttributeRepository configurableProductSkuAttributeRepository;
    public void populate(SkuProduct skuProduct) {
        // product populate
        productSetter.populate(skuProduct);
        // skuProduct populate
        // > set configurableProduct
        Long configurableProductId = skuProduct.getConfigurableProductId();
        if (configurableProductId != null) {
            ConfigurableProduct configurableProduct = mongoTemplate.findById(configurableProductId, ConfigurableProduct.class);
            configurableProductSetter.populate(configurableProduct);
            skuProduct.setConfigurableProduct(configurableProduct);
        }
        // > set skuAttributes
        List<ConfigurableProductSkuAttribute> skuAttributes = configurableProductSkuAttributeRepository.findByProductIdAndStatus(skuProduct.getId(), EntityStatus.ACTIVE);
        skuAttributes.sort(Comparator.comparingInt(ConfigurableProductSkuAttribute::getSortOrder));
        skuProduct.setSkuAttributes(skuAttributes);
    }

    public void populateOnVersionedAttributeId(SkuProduct skuProduct, Optional<Long> versionedAttributeId) {
        // product populate
        productSetter.populateOnVersionedAttributeId(skuProduct, versionedAttributeId);
        // skuProduct populate
        // > set configurableProduct
        Long configurableProductId = skuProduct.getConfigurableProductId();
        if (configurableProductId != null) {
            ConfigurableProduct configurableProduct = mongoTemplate.findById(configurableProductId, ConfigurableProduct.class);
            configurableProductSetter.populateOnVersionedAttribute(configurableProduct, versionedAttributeId);
            skuProduct.setConfigurableProduct(configurableProduct);
        }
        // > set skuAttributes
        List<ConfigurableProductSkuAttribute> skuAttributes = configurableProductSkuAttributeRepository.findByProductIdAndVersionAttributeId(skuProduct.getId(), versionedAttributeId)
                .stream()
                .map(configurableProductSkuAttributeRepository::fillData)
                .sorted(Comparator.comparingInt(ConfigurableProductSkuAttribute::getSortOrder))
                .collect(Collectors.toList());
        skuProduct.setSkuAttributes(skuAttributes);
    }

    public void preCascadeWrite(SkuProduct skuProduct) {
        productSetter.preCascadeWrite(skuProduct);
    }

    /**
     * {@link com.qpp.cgp.migration.CgpMongoDomainRepository#save(CgpMongoDomain)}方法执行后
     */
    public void postCascadeWrite(SkuProduct skuProduct) {
        productSetter.postCascadeWrite(skuProduct);
    }
}
