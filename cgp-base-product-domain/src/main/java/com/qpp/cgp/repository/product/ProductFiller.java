package com.qpp.cgp.repository.product;

import com.qpp.cgp.domain.product.ConfigurableProduct;
import com.qpp.cgp.domain.product.Product;
import com.qpp.cgp.domain.product.SkuProduct;
import com.qpp.cgp.repository.product.cascade.ConfigurableProductSetter;
import com.qpp.cgp.repository.product.cascade.SkuProductSetter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class ProductFiller {
    @Autowired
    private ConfigurableProductSetter configurableProductSetter;
    @Autowired
    private SkuProductSetter skuProductSetter;

    public Product fillDataNoVersionedAttribute(Product product) {
        if (product instanceof ConfigurableProduct) {
            configurableProductSetter.populateOnVersionedAttribute((ConfigurableProduct) product, Optional.empty());
        } else if (product instanceof SkuProduct) {
            skuProductSetter.populateOnVersionedAttributeId((SkuProduct) product, Optional.empty());
        }
        return product;
    }
}
