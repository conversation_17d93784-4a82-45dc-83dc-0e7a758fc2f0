package com.qpp.cgp.repository.product.producedays;

import com.qpp.cgp.domain.product.producedays.ProductProduceDays;
import com.qpp.cgp.migration.CgpMongoDomainRepository;
import org.springframework.stereotype.Service;


@Service
public class ProductProduceDaysRepository extends CgpMongoDomainRepository<ProductProduceDays, Long> {

    public ProductProduceDays findByQtyFromLessThanOrEqualAndQtyToGreaterThanOrEqualAndTemplateId(int qtyFrom, int qtyTo, Long templateId) {
        return null;
    }

}
