package com.qpp.cgp.repository.product;

import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.common.Website;
import com.qpp.cgp.domain.product.ConfigurableProduct;
import com.qpp.cgp.domain.product.category.MainProductCategory;
import com.qpp.cgp.domain.product.category.ProductCategory;
import com.qpp.cgp.migration.CgpMongoDomain;
import com.qpp.cgp.migration.CgpMongoDomainRepository;
import com.qpp.cgp.repository.product.cascade.ConfigurableProductSetter;
import com.qpp.cgp.repository.product.category.ProductCategoryRepository;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;


import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @deprecated use {@link ProductRepository} or {@link org.springframework.data.mongodb.core.MongoTemplate}
 */
@Service
public class ConfigurableProductRepository extends CgpMongoDomainRepository<ConfigurableProduct, Long> {

    public Optional<ConfigurableProduct> findByIdAndStatus(Long id, int status) {
        return Optional.ofNullable(
          fillData(mongoTemplate.findOne(Query.query(Criteria.where("_id").is(id)
                  .and("status").is(status)
          .and("clazz").is(ConfigurableProduct.class.getName())), ConfigurableProduct.class))
        );
    }

    @Autowired
    private ProductCategoryRepository productCategoryRepository;

    //region category字段相关方法
    public ConfigurableProduct findByModelAndMainCategoryWebsiteAndStatus(String model, Website website, int status) {

        if (website == null){
            throw BusinessExceptionBuilder.of(1600003, ImmutableMap.of("paramName", "website"));
        }

        ConfigurableProduct result = null;
        // productCategory query
        List<ProductCategory> categories = productCategoryRepository.find(Query.query(Criteria.where("websiteId").is(website.getId()).and("isMain").is(true)));
        // configurableProduct query
        result = this.fillData(this.getMongoTemplate().findOne(
                Query.query(Criteria.where("mainCategoryId").in(categories.stream().map(CgpMongoDomain::getId).collect(Collectors.toList()))
                        .and("model").is(model)
                        .and("status").is(status)
                        .and("clazz").is(ConfigurableProduct.class.getName()))
                , ConfigurableProduct.class));
        return result;
    }

    public List<ConfigurableProduct> findByStatusAndInvisibleAndMainCategoryWebsiteId(int active, boolean b, Long websiteId) {
        List<ConfigurableProduct> result = null;
        // productCategory query
        List<Long> mainCategoryIds = mongoTemplate.find(Query.query(Criteria.where("websiteId").is(websiteId).and("isMain").is(true)), MainProductCategory.class).stream().map(CgpMongoDomain::getId).collect(Collectors.toList());
        // configurableProduct query
        result = this.find(
                Query.query(Criteria.where("mainCategoryId").in(mainCategoryIds)
                        .and("status").is(active)
                        .and("invisible").is(b)
                        .and("clazz").is(ConfigurableProduct.class.getName()))
        );
        return result;
    }
    //endregion

    //region 重写部分基类方法
    @Override
    public Page<ConfigurableProduct> findAll(Query query, Pageable pageable) {
        query.addCriteria(Criteria.where("clazz").is(ConfigurableProduct.class.getName()));
        return super.findAll(query, pageable);
    }

    @Override
    public ConfigurableProduct getOne(Long id) {
        ConfigurableProduct result = this.getMongoTemplate().findOne(Query.query(Criteria.where(CgpMongoDomain.idProperty).is(id)
                .and("clazz").is(ConfigurableProduct.class.getName())), ConfigurableProduct.class);
        if (Objects.nonNull(result)) {
            return fillData(result);
        } else {
            throw BusinessExceptionBuilder.of(40000, ImmutableMap.of("class", tClass.getSimpleName(), "id", id));
        }
    }

    @Override
    public List<ConfigurableProduct> findAll() {
        return find(Query.query(new Criteria()));
    }

    @Override
    public List<ConfigurableProduct> find(Query query) {
        query.addCriteria(Criteria.where("clazz").is(ConfigurableProduct.class.getName()));
        return super.find(query);
    }

    @Override
    public boolean existsById(Long id) {
        return this.getMongoTemplate().exists(Query.query(Criteria.where(CgpMongoDomain.idProperty).is(id)
                .and("clazz").is(ConfigurableProduct.class.getName())), ConfigurableProduct.class);
    }

    @Override
    public Optional<ConfigurableProduct> findById(Long id) {
        ConfigurableProduct configurableProduct = mongoTemplate.findOne(Query.query(Criteria.where(CgpMongoDomain.idProperty).is(id)
                .and("clazz").is(ConfigurableProduct.class.getName())), ConfigurableProduct.class);
        return Optional.ofNullable(fillData(configurableProduct));
    }

    //endregion

    @Autowired
    private ConfigurableProductSetter configurableProductSetter;

    @Override
    public <S extends ConfigurableProduct> S fillData(S entity) {
        if (entity != null) {
            configurableProductSetter.populate(entity);
        }
        return super.fillData(entity);
    }
}
