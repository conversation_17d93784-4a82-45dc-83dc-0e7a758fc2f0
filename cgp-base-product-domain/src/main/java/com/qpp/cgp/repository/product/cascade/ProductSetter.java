package com.qpp.cgp.repository.product.cascade;

import com.qpp.cgp.domain.product.Product;
import com.qpp.cgp.domain.product.ProductAttributeValue;
import com.qpp.cgp.migration.CgpMongoDomain;
import com.qpp.cgp.repository.product.ProductAttributeValueRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 基于{@link BaseProductSetter}，对{@link com.qpp.cgp.domain.product.Product}进行补充填充
 */
@Component
public class ProductSetter {

    @Autowired
    private BaseProductSetter baseProductSetter;

    @Autowired
    private ProductAttributeValueRepository productAttributeValueRepository;

    public void populate(Product product) {
        // base populate
        baseProductSetter.populate(product);
        // product populate
        // > productAttributeValues
        List<ProductAttributeValue> productAttributeValues = productAttributeValueRepository.findByProductId(product.getId());
        product.setAttributeValues(productAttributeValues);
    }

    public void populateOnVersionedAttributeId(Product product, Optional<Long> versionedAttributeIdOptional) {
        // base populate
        baseProductSetter.populate(product);
        // product populate
        // > productAttributeValues

        List<ProductAttributeValue> productAttributeValues = versionedAttributeIdOptional
                .map(versionedAttributeId -> productAttributeValueRepository.findByVersionedProductAttributeId(product.getId(), versionedAttributeId))
                .orElse(productAttributeValueRepository.findNotVersionedAttributeByProductId(product.getId()))
                .stream()
                .map(productAttributeValueRepository::fillData)
                .collect(Collectors.toList());

        product.setAttributeValues(productAttributeValues);
    }

    /**
     * {@link com.qpp.cgp.migration.CgpMongoDomainRepository#save(CgpMongoDomain)}方法执行前
     */
    public void preCascadeWrite(Product product) {
        baseProductSetter.preCascadeWrite(product);
    }

    /**
     * {@link com.qpp.cgp.migration.CgpMongoDomainRepository#save(CgpMongoDomain)}方法执行后
     */
    public void postCascadeWrite(Product product) {
        baseProductSetter.postCascadeWrite(product);
        List<ProductAttributeValue> attributeValues = product.getAttributeValues();
        // 若为新增，则attributeValue的productId需重新设置一遍
        attributeValues.forEach(productAttributeValue -> productAttributeValue.setProductId(product.getId()));
        productAttributeValueRepository.saveAll(attributeValues);
    }
}
