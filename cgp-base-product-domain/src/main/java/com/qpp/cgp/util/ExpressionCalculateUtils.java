package com.qpp.cgp.util;

import com.google.common.collect.ImmutableMap;
import com.googlecode.aviator.AviatorEvaluator;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import freemarker.cache.StringTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.StringWriter;
import java.util.Map;

/**
 * <AUTHOR> jerry
 * @version : 1.0
 * Description : Freemarker工具类
 * Create Date : 2019-08-02 14:24
 **/
public class ExpressionCalculateUtils {

    final static Logger logger = LoggerFactory.getLogger(ExpressionCalculateUtils.class);

    public static Object parserExpression(Map context, String expression, String operatorConfigId) {

        try {
            return runFreeMarkExpression(context, expression);
        } catch (Exception e) {
            logger.info("OperationConfigId:" + operatorConfigId);
            logger.info("expression:" + expression);
            logger.info("context:" + context);
            logger.info("message:" + e.getMessage());
            // 报错不知道哪里报错
            throw BusinessExceptionBuilder.of(4700076, ImmutableMap.of("message",
                    "MaterialViewTypePreprocessConfigId：" + context.get(PreprocessContextName.MaterialViewTypePreprocessConfigId.toString()) +
                            " ConditionMappingConfigDescription:" + context.get(PreprocessContextName.ConditionMappingConfigDescription.toString()) +
                            " TargetMappingConfigDescription:" + context.get(PreprocessContextName.TargetMappingConfigDescription.toString()) +
                            " PageContentMappingConfigDescription：" + context.get(PreprocessContextName.PageContentMappingConfigDescription.toString()) +
                            "The operatorConfigId:" + operatorConfigId + " freeMark expression parser error! context:" + context + " expression:" + expression + "message:" + e.getMessage()));
        }

    }

    public static Object runFreeMarkExpression(Map context, String expression) throws IOException, TemplateException {
        StringWriter  writer;
        Configuration cfg = new Configuration(Configuration.VERSION_2_3_22);
        cfg.setNumberFormat("0.######");
        cfg.setDefaultEncoding("UTF-8");
        StringTemplateLoader stringLoader = new StringTemplateLoader();

        stringLoader.putTemplate("resutlTemplate", expression);
        cfg.setTemplateLoader(stringLoader);
        /* Get the template (uses cache internally) */
        Template temp;
        temp = cfg.getTemplate("resutlTemplate", "UTF-8");
        /*将data和template对象合并输出value*/
        writer = new StringWriter();
        temp.process(context, writer);
        return writer.toString();
    }

    /**
     * FreeMarker表达式计算器
     *
     * @param expression       freemarker表达式
     * @param context          上下文
     * @param operatorConfigId 操作配置Id
     * @return 计算以后的值
     */
    public static String convertSelector(String expression, Map context, String operatorConfigId) {
        if (context == null || context.size() == 0) {
            return expression;
        }
        return ExpressionCalculateUtils.parserExpression(context, expression, operatorConfigId).toString();
    }

    /**
     * Calculate计算器，可以运算简单的加减乘除和判断
     *
     * @param expression       calculate表达式
     * @param context          上下文
     * @param operatorConfigId 操作配置Id
     * @return 计算以后的值
     */
    public static Object calculate(String expression, Map context, String operatorConfigId) {
        try {
            return AviatorEvaluator.execute(expression, context);
        } catch (Exception e) {
            // 不知道哪里出错
            throw BusinessExceptionBuilder.of(4700076, ImmutableMap.of("message", "The CalculateExpression error expression：" + expression + " context:" + context + " message:" + e.getMessage()));
        }
    }

}