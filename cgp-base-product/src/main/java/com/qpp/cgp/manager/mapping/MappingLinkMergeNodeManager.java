package com.qpp.cgp.manager.mapping;

import com.qpp.cgp.config.mongo.MongoTemplateBeanNames;
import com.qpp.cgp.domain.attributecalculateV2.mapping.MappingLinkMergeNode;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.driver.HybridMongoTemplate;
import com.qpp.web.business.manager.AbstractLongMongoCurdManager;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

@Service
public class MappingLinkMergeNodeManager extends AbstractLongMongoCurdManager<MappingLinkMergeNode,Long> {
    public MappingLinkMergeNodeManager(@Qualifier(value = MongoTemplateBeanNames.CONFIG) HybridMongoTemplate mongoTemplate,
                                       IdGenerator idgenerator) {
        super(mongoTemplate, idgenerator);
    }

    public List<MappingLinkMergeNode> findByLinkId(Long linkId) {
        return this.mongoTemplate.find(Query.query(Criteria.where("link._id").is(linkId)), MappingLinkMergeNode.class);
    }

    public List<MappingLinkMergeNode> findByLinkIds(Set<Long> linkIds) {
        Query query = Query.query(Criteria.where("link._id").in(linkIds));
        return mongoTemplate.find(query, MappingLinkMergeNode.class);
    }

}
