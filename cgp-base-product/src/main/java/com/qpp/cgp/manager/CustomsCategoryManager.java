package com.qpp.cgp.manager;

import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.customs.CustomsCategory;
import com.qpp.cgp.domain.customs.CustomsElement;
import com.qpp.cgp.domain.dto.order.CustomsCategoryDTO;
import com.qpp.cgp.manager.customs.CustomsElementManager;
import com.qpp.cgp.service.product.attribute.BaseVersionedProductAttributeService;
import com.qpp.cgp.value.ValueType;
import com.qpp.cgp.value.calculator.ValueExCalculateService;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.driver.HybridMongoTemplate;
import com.qpp.web.business.manager.AbstractMongoCurdManager;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/3/2
 */
@Service
public class CustomsCategoryManager extends AbstractMongoCurdManager<CustomsCategory, String> {


    @Autowired
    private ValueExCalculateService valueExCalculateService;

    @Autowired
    private CustomsElementManager customsElementManager;

    @Autowired
    private BaseVersionedProductAttributeService baseVersionedProductAttributeService;

    @Autowired
    public CustomsCategoryManager(HybridMongoTemplate mongoTemplate, IdGenerator idgenerator) {
        super(mongoTemplate, idgenerator);
    }

    public List<CustomsCategoryDTO> findByIds(List<String> ids) {
        final List<CustomsCategory> categoryList = mongoTemplate.find(Query.query(Criteria.where("_id").in(ids)), CustomsCategory.class);
        List<CustomsCategoryDTO> customsCategoryList = new ArrayList<>();
        if (categoryList.size() > 0) {
            categoryList.forEach(e->{
                final CustomsCategoryDTO customsCategoryDTO = new CustomsCategoryDTO();
                BeanUtils.copyProperties(e, customsCategoryDTO);
                customsCategoryList.add(customsCategoryDTO);
            });
        }
        return customsCategoryList;
    }

    public List<CustomsCategoryDTO> findByProductId(Long productId, Long versionedAttributeId) {
        CustomsElement customsElement = baseVersionedProductAttributeService
                .getLatestCustomsElement(productId, Optional.ofNullable(versionedAttributeId));
        List<CustomsCategoryDTO> customsCategoryDTOS = new ArrayList<>();
        if (customsElement != null) {
            if (customsElement.getOutCustoms()) {
                if (!customsElement.getCustomsCategory().getType().equals(ValueType.Array)) {
                    throw BusinessExceptionBuilder.of(600014, ImmutableMap.of("customscategory ValueEx", "ValueExType error"));
                }
                List calculate = (List) valueExCalculateService.calculate(customsElement.getCustomsCategory(), null);
                if (calculate != null && calculate.size() > 0) {
                    calculate.forEach(e -> {
                        CustomsCategoryDTO customsCategoryDTO = new CustomsCategoryDTO();
                        BeanUtils.copyProperties(findById(String.valueOf(e)), customsCategoryDTO);
                        customsCategoryDTOS.add(customsCategoryDTO);
                    });
                    return customsCategoryDTOS;
                }
                throw BusinessExceptionBuilder.of(600015, ImmutableMap.of("The ValueEx calculate is null", "CustomElement CustomsCategory"));
            }
        }
        return customsCategoryDTOS;
    }
}
