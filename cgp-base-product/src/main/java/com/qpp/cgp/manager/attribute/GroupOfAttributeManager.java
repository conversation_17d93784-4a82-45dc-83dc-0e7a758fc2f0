package com.qpp.cgp.manager.attribute;

import com.qpp.cgp.config.mongo.MongoTemplateBeanNames;
import com.qpp.cgp.domain.attributecalculateV2.attribute.AbstractGroupAttribute;
import com.qpp.cgp.domain.attributecalculateV2.form.GroupOfAttribute;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.driver.HybridMongoTemplate;
import com.qpp.web.business.manager.AbstractLongMongoCurdManager;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class GroupOfAttributeManager extends AbstractLongMongoCurdManager<GroupOfAttribute, Long> {
    public GroupOfAttributeManager(@Qualifier(value = MongoTemplateBeanNames.CONFIG) HybridMongoTemplate mongoTemplate,
                                   IdGenerator idgenerator) {
        super(mongoTemplate, idgenerator);
    }

    public List<GroupOfAttribute> findByGroupIds(Set<Long> groupIds) {
        return this.mongoTemplate.find(Query.query(Criteria.where("group._id").in(groupIds)), GroupOfAttribute.class);
    }

    public List<GroupOfAttribute> findByGroupId(Long groupId) {
        Query query = Query.query(Criteria.where("group._id").is(groupId));
        return this.mongoTemplate.find(query, GroupOfAttribute.class);
    }

    public List<GroupOfAttribute> findAttributeByGroupId(Long groupId) {
        Query query = Query.query(Criteria.where("group._id").is(groupId));
        query.fields().include("_id", "clazz", "attribute");
        return this.mongoTemplate.find(query, GroupOfAttribute.class);
    }

    public GroupOfAttribute findByAttributeId(Long attributeId) {
        Query query = Query.query(Criteria.where("attribute._id").is(attributeId));
        return this.mongoTemplate.findOne(query, GroupOfAttribute.class);
    }

    public List<GroupOfAttribute> findByAttributeIds(Set<Long> attributeIds) {
        Query query = Query.query(Criteria.where("attribute._id").in(attributeIds));
        return this.mongoTemplate.find(query, GroupOfAttribute.class);
    }

    public Long findGroupIdByAttributeId(Long attributeId) {
        Query query = Query.query(Criteria.where("attribute._id").is(attributeId));
        query.fields().include("group._id");
        GroupOfAttribute groupOfAttribute = mongoTemplate.findOne(query, GroupOfAttribute.class);
        return groupOfAttribute != null ? groupOfAttribute.getGroup().getId() : null;
    }

    public List<Long> findAttributeIdsByGroupId(Long groupId) {
        Query query = Query.query(Criteria.where("group._id").is(groupId));
        query.fields().include("attribute", "attribute._id", "clazz");
        return mongoTemplate.findDistinct(query, "attribute._id", GroupOfAttribute.class, Long.class);
    }

    public Integer findMaxOrderByGroupId(Long groupId) {
        Query query = Query.query(Criteria.where("group._id").is(groupId));
        query.fields().include("order", "clazz");
        query.with(Sort.by(Sort.Direction.DESC, "order"));
        GroupOfAttribute groupOfAttribute = mongoTemplate.findOne(query, GroupOfAttribute.class);
        return null == groupOfAttribute || null == groupOfAttribute.getOrder() ? 0 : groupOfAttribute.getOrder();
    }

    public List<GroupOfAttribute> findByGroupIdAndAttributeIds(Long groupId, Set<Long> attributeIds) {
        Criteria criteria = Criteria.where("group._id").is(groupId).and("attribute._id").in(attributeIds);
        Query query = Query.query(criteria);
        return this.mongoTemplate.find(query, GroupOfAttribute.class);
    }

    public Set<Long> generateGroupAttributeIds(List<GroupOfAttribute> groupOfAttributes) {
        return groupOfAttributes.stream()
                .map(GroupOfAttribute::getAttribute)
                .filter(Objects::nonNull)
                .map(AbstractGroupAttribute::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    public void deleteByAttributeId(Long attributeId) {
        Query query = Query.query(Criteria.where("attribute._id").is(attributeId));
        this.mongoTemplate.remove(query, GroupOfAttribute.class);
    }

    public void deleteByGroupId(Long groupId) {
        Query query = Query.query(Criteria.where("group._id").is(groupId));
        this.mongoTemplate.remove(query, GroupOfAttribute.class);
    }
}
