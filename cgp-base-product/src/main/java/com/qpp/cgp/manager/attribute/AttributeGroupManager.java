package com.qpp.cgp.manager.attribute;

import com.mongodb.client.result.UpdateResult;
import com.qpp.cgp.config.mongo.MongoTemplateBeanNames;
import com.qpp.cgp.domain.attributecalculateV2.attribute.ui.AttributeRenderConfig;
import com.qpp.cgp.domain.attributecalculateV2.form.AttributeGroup;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.driver.HybridMongoTemplate;
import com.qpp.web.business.manager.AbstractLongMongoCurdManager;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class AttributeGroupManager extends AbstractLongMongoCurdManager<AttributeGroup, Long> {
    public AttributeGroupManager(@Qualifier(value = MongoTemplateBeanNames.CONFIG) HybridMongoTemplate mongoTemplate,
                                 IdGenerator idgenerator) {
        super(mongoTemplate, idgenerator);
    }

    public List<AttributeGroup> findByIds(Set<Long> ids) {
        return this.mongoTemplate.find(Query.query(Criteria.where("_id").in(ids)), AttributeGroup.class);
    }

    public boolean existsById(Long id) {
        Query query = Query.query(Criteria.where("_id").is(id));
        return this.mongoTemplate.exists(query, AttributeGroup.class);
    }

    public Set<Long> findAttributeRenderConfigIdsByIds(Set<Long> ids) {
        Query query = Query.query(Criteria.where("_id").in(ids));
        query.fields().include("_id", "clazz", "renderConfigs");
        return mongoTemplate.find(query, AttributeGroup.class)
                .stream()
                .map(AttributeGroup::getRenderConfigs)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .map(AttributeRenderConfig::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    public UpdateResult updateOrder(Long id, Integer order) {
        Query query = Query.query(Criteria.where("_id").is(id));
        Update update = new Update();
        update.set("order", order);
        return mongoTemplate.updateFirst(query, update, AttributeGroup.class);
    }

}
