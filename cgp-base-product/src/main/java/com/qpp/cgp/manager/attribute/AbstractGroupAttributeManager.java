package com.qpp.cgp.manager.attribute;

import com.qpp.cgp.config.mongo.MongoTemplateBeanNames;
import com.qpp.cgp.domain.attribute.Attribute;
import com.qpp.cgp.domain.attribute.AttributeOption;
import com.qpp.cgp.domain.attributecalculateV2.attribute.AbstractGroupAttribute;
import com.qpp.cgp.domain.attributecalculateV2.attribute.CustomGroupAttribute;
import com.qpp.cgp.domain.attributecalculateV2.attribute.DaGroupAttribute;
import com.qpp.cgp.domain.attributecalculateV2.form.GroupOfAttribute;
import com.qpp.cgp.domain.common.EntityStatus;
import com.qpp.cgp.domain.product.ConfigurableProductSkuAttribute;
import com.qpp.cgp.dto.attributecalculateV2.data.AbstractGroupAttributeResponseDTO;
import com.qpp.cgp.dto.attributecalculateV2.data.ConfigurableProductSkuAttributeDTO;
import com.qpp.cgp.dto.attributecalculateV2.data.CustomGroupAttributeResponseDTO;
import com.qpp.cgp.dto.attributecalculateV2.data.DaGroupAttributeResponseDTO;
import com.qpp.cgp.repository.product.ConfigurableProductSkuAttributeRepository;
import com.qpp.cgp.repository.product.attribute.AttributeOptionRepository;
import com.qpp.cgp.repository.product.attribute.AttributeRepository;
import com.qpp.core.dto.FilterDTO;
import com.qpp.core.dto.PageDTO;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.driver.HybridMongoTemplate;
import com.qpp.web.business.manager.AbstractLongMongoCurdManager;
import com.qpp.web.business.utils.MongoFilterUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class AbstractGroupAttributeManager extends AbstractLongMongoCurdManager<AbstractGroupAttribute, Long> {

    @Autowired
    private GroupOfAttributeManager groupOfAttributeManager;

    @Autowired
    private ConfigurableProductSkuAttributeRepository skuAttributeRepository;

    @Autowired
    private AttributeRepository attributeRepository;

    @Autowired
    private AttributeOptionRepository attributeOptionRepository;

    public AbstractGroupAttributeManager(@Qualifier(value = MongoTemplateBeanNames.CONFIG) HybridMongoTemplate mongoTemplate,
                                         IdGenerator idgenerator) {
        super(mongoTemplate, idgenerator);
    }

    public List<AbstractGroupAttribute> findByIds(Set<Long> ids) {
        return this.mongoTemplate.find(Query.query(Criteria.where("_id").in(ids)), AbstractGroupAttribute.class);
    }

    public AbstractGroupAttribute findRefById(Long id) {
        Query query = Query.query(Criteria.where("_id").is(id));
        query.fields().include("_id", "clazz");
        return this.mongoTemplate.findOne(query, AbstractGroupAttribute.class);
    }

    public PageDTO<AbstractGroupAttributeResponseDTO> findAllDTO(Pageable pageRequest, List<FilterDTO> filters,
                                                                 Long attributeVersionId) {
        List<MongoFilterUtils.FilterHandler> exFilterHandlers = new ArrayList<>();

        Long groupId = null;
        Optional<FilterDTO> groupIdFilterOptional = filters.stream()
                .filter(filter -> "groupId".equalsIgnoreCase(filter.getName()))
                .findFirst();
        if (groupIdFilterOptional.isPresent()) {
            FilterDTO groupIdFilter = groupIdFilterOptional.get();
            groupId = Long.parseLong(groupIdFilter.getValue().toString());
            List<Long> attributeIds = groupOfAttributeManager.findAttributeIdsByGroupId(groupId);

            MongoFilterUtils.FilterHandler attributeVersionFilter = MongoFilterUtils.FilterHandler.of(
                    filter -> "groupId".equalsIgnoreCase(filter.getName()),
                    filter -> "_id",
                    (criteria, filter) -> criteria.in(attributeIds)
            );
            exFilterHandlers.add(attributeVersionFilter);
        }
        Query query = MongoFilterUtils.getQueryEnhance(filters, exFilterHandlers);

        Page<AbstractGroupAttribute> page = this.findAll(query, pageRequest);
        List<AbstractGroupAttribute> content = page.getContent();
        List<AbstractGroupAttributeResponseDTO> groupAttributeDTOs = convert(content, groupId, attributeVersionId);

        PageDTO<AbstractGroupAttributeResponseDTO> result = new PageDTO<>(
                page.getNumber() + 1, page.getSize(), groupAttributeDTOs);

        result.setTotalCount(page.getTotalElements());
        result.setTotalPages(page.getTotalPages());

        return result;
    }

    private List<AbstractGroupAttributeResponseDTO> convert(List<AbstractGroupAttribute> groupAttributes,
                                                            Long groupId, Long attributeVersionId) {
        Set<Long> groupAttributeIds = groupAttributes.stream()
                .map(AbstractGroupAttribute::getId)
                .collect(Collectors.toSet());

        Map<Long, GroupOfAttribute> groupOfAttributeMap;
        if (null == groupId) {
            List<GroupOfAttribute> groupOfAttributes = groupOfAttributeManager.findByAttributeIds(groupAttributeIds);
            groupOfAttributeMap = groupOfAttributes.stream()
                    .collect(Collectors.toMap(groupOfAttribute -> groupOfAttribute.getAttribute().getId(),
                            groupOfAttribute -> groupOfAttribute, (entry1, entry2) -> entry2));
        } else {
            List<GroupOfAttribute> groupOfAttributes = groupOfAttributeManager
                    .findByGroupIdAndAttributeIds(groupId, groupAttributeIds);
            groupOfAttributeMap = groupOfAttributes.stream()
                    .collect(Collectors.toMap(groupOfAttribute -> groupOfAttribute.getAttribute().getId(),
                            groupOfAttribute -> groupOfAttribute, (entry1, entry2) -> entry2));
        }

        Set<Long> attributeIds = new HashSet<>();
        List<AbstractGroupAttributeResponseDTO> responseDTOs = groupAttributes.stream()
                .map(groupAttribute -> {
                    Long attributeId = groupAttribute.getId();
                    GroupOfAttribute groupOfAttribute = groupOfAttributeMap.get(attributeId);
                    Long attributeGroupId = groupOfAttribute.getGroup().getId();
                    AbstractGroupAttributeResponseDTO responseDTO
                            = convert(groupAttribute, attributeGroupId, attributeVersionId, attributeIds);

                    if (null != groupId) {
                        responseDTO.setOrder(groupOfAttribute.getOrder());
                    }
                    return responseDTO;
                })
                .collect(Collectors.toList());

        Set<Long> skuAttributeOptionIds = new HashSet<>();
        responseDTOs.stream()
                .filter(r -> r instanceof DaGroupAttributeResponseDTO)
                .map(r -> (DaGroupAttributeResponseDTO) r)
                .map(DaGroupAttributeResponseDTO::getProductSkuAttribute)
                .filter(Objects::nonNull)
                .forEach(skuAttribute -> {
                    List<AttributeOption> options = skuAttribute.getAttributeOptions();
                    if (null != options) {
                        List<Long> attributeOptionIds = options.stream()
                                .map(AttributeOption::getId)
                                .collect(Collectors.toList());
                        skuAttributeOptionIds.addAll(attributeOptionIds);
                    }
                });

        List<Attribute> attributes = attributeRepository.findByIds(attributeIds);
        Map<Long, Attribute> attributeMap = attributes.stream()
                .collect(Collectors.toMap(Attribute::getId, attribute -> attribute, (entry1, entry2) -> entry2));
        Map<Long, List<AttributeOption>> attributeId2attributeOptionMap = attributeOptionRepository
                .findByAttributeIdsAndStatus(attributeIds, EntityStatus.ACTIVE)
                .stream()
                .collect(Collectors.groupingBy(AttributeOption::getAttributeId));
        Map<Long, AttributeOption> skuAttributeOptionMap = attributeOptionRepository.findByIds(skuAttributeOptionIds);
        responseDTOs.forEach(r -> fillAttribute(r, attributeMap, attributeId2attributeOptionMap, skuAttributeOptionMap));

        return responseDTOs;
    }

    public AbstractGroupAttributeResponseDTO convert(AbstractGroupAttribute groupAttribute, Long groupId,
                                                     Long attributeVersionId, Set<Long> attributeIds) {
        AbstractGroupAttributeResponseDTO groupAttributeDTO;
        if (groupAttribute instanceof DaGroupAttribute) {
            DaGroupAttributeResponseDTO daGroupAttributeDTO = new DaGroupAttributeResponseDTO();
            DaGroupAttribute daGroupAttribute = (DaGroupAttribute) groupAttribute;
            String code = daGroupAttribute.getProductSkuAttribute();
            ConfigurableProductSkuAttribute attribute = skuAttributeRepository
                    .findByCodeAndVersionedAttributeIdAndStatus(code, attributeVersionId, EntityStatus.ACTIVE);

            ConfigurableProductSkuAttributeDTO skuAttributeDTO = convert(attribute);
            if (null != skuAttributeDTO) {
                daGroupAttributeDTO.setProductSkuAttribute(skuAttributeDTO);
                attributeIds.add(skuAttributeDTO.getAttributeId());
            }

            groupAttributeDTO = daGroupAttributeDTO;
        } else if (groupAttribute instanceof CustomGroupAttribute) {
            CustomGroupAttributeResponseDTO customGroupAttributeDTO = new CustomGroupAttributeResponseDTO();
            CustomGroupAttribute customGroupAttribute = (CustomGroupAttribute) groupAttribute;
            Attribute attribute = customGroupAttribute.getAttribute();
            if (null != attribute) {
                Long attributeId = attribute.getId();
                attribute = attributeRepository.getOneByIdAndStatus(attributeId, EntityStatus.ACTIVE);
                customGroupAttributeDTO.setAttribute(attribute);
                attributeIds.add(attributeId);
            }

            groupAttributeDTO = customGroupAttributeDTO;
        } else {
            throw new UnsupportedOperationException();
        }
        BeanUtils.copyProperties(groupAttribute, groupAttributeDTO, "attribute", "productSkuAttribute");

        List<AttributeOption> options = groupAttribute.getOptions();
        groupAttributeDTO.setAttrOptions(options);

        groupAttributeDTO.setGroupId(groupId);
        return groupAttributeDTO;
    }

    private ConfigurableProductSkuAttributeDTO convert(ConfigurableProductSkuAttribute skuAttribute) {
        if (null == skuAttribute) {
            return null;
        }

        ConfigurableProductSkuAttributeDTO skuAttributeDTO = new ConfigurableProductSkuAttributeDTO();
        BeanUtils.copyProperties(skuAttribute, skuAttributeDTO);

        Long attributeId = skuAttribute.getAttributeId();
        Attribute valueTypeAndSelectType = attributeRepository.findValueTypeAndSelectTypeById(attributeId);
        if (null != valueTypeAndSelectType) {
            skuAttributeDTO.setValueType(valueTypeAndSelectType.getValueType());
            skuAttributeDTO.setSelectType(valueTypeAndSelectType.getSelectType());
        }

        return skuAttributeDTO;
    }

    public void fillAttribute(AbstractGroupAttributeResponseDTO groupAttributeResponseDTO,
                              Map<Long, Attribute> attributeMap,
                              Map<Long, List<AttributeOption>> attributeId2attributeOptionMap,
                              Map<Long, AttributeOption> skuAttributeOptionMap) {
        if (groupAttributeResponseDTO instanceof DaGroupAttributeResponseDTO) {
            DaGroupAttributeResponseDTO daGroupAttributeResponseDTO
                    = (DaGroupAttributeResponseDTO) groupAttributeResponseDTO;
            ConfigurableProductSkuAttributeDTO productSkuAttribute
                    = daGroupAttributeResponseDTO.getProductSkuAttribute();
            if(null == productSkuAttribute) {
                return;
            }

            Long attributeId = productSkuAttribute.getAttributeId();
            Attribute attribute = attributeMap.get(attributeId);
            productSkuAttribute.setAttribute(attribute);

            List<AttributeOption> attributeOptions = attributeId2attributeOptionMap.get(attributeId);
            attributeOptions = CollectionUtils.isNotEmpty(attributeOptions)
                    ? attributeOptions : new ArrayList<>(0);
            attribute.setOptions(attributeOptions);

            List<AttributeOption> options = productSkuAttribute.getAttributeOptions();
            if (CollectionUtils.isNotEmpty(options)) {
                options = options.stream()
                        .map(AttributeOption::getId)
                        .map(skuAttributeOptionMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                productSkuAttribute.setAttributeOptions(options);
            }
        } else {
            CustomGroupAttributeResponseDTO customGroupAttributeResponseDTO
                    = (CustomGroupAttributeResponseDTO) groupAttributeResponseDTO;
            Attribute attribute = customGroupAttributeResponseDTO.getAttribute();
            if (null != attribute) {
                Long attributeId = attribute.getId();

                List<AttributeOption> attributeOptions = attributeId2attributeOptionMap.get(attributeId);
                attributeOptions = CollectionUtils.isNotEmpty(attributeOptions)
                        ? attributeOptions : new ArrayList<>(0);
                attribute.setOptions(attributeOptions);
            }
        }
    }

    public Set<Long> getAttributeIds(List<AbstractGroupAttribute> groupAttributes) {
        return groupAttributes.stream()
                .filter(g -> g instanceof CustomGroupAttribute)
                .map(g -> (CustomGroupAttribute) g)
                .map(CustomGroupAttribute::getAttribute)
                .filter(Objects::nonNull)
                .map(Attribute::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    public Set<Long> getAttributeOptionIds(List<AbstractGroupAttribute> groupAttributes) {
        return groupAttributes.stream()
                .map(AbstractGroupAttribute::getOptions)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .map(AttributeOption::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

}
