package com.qpp.cgp.service.shipment.calculate;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qpp.cgp.domain.shipment.WeightBasedPostageRule;
import com.qpp.commons.json.JsonUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.Assert.*;

/**
 * @description:
 * @author: TT-Berg
 * @date: 2023/9/20
 **/
@RunWith(MockitoJUnitRunner.class)
public class ShipmentPriceCalculatorTest {

    private ShipmentPriceCalculator shipmentPriceCalculator;

    private List<WeightBasedPostageRule> weightBasedPostageRules;

    @Before
    public void init() throws JsonProcessingException {
        shipmentPriceCalculator = new ShipmentPriceCalculator();
        //#region json
        String json = "[\n" +
                "    {\n" +
                "        \"startWeight\": 1.0,\n" +
                "        \"endWeight\": 100.0,\n" +
                "        \"plusFee\": 4.6,\n" +
                "        \"plusWeight\": 6.0,\n" +
                "        \"firstWeight\": 10.0,\n" +
                "        \"firstFee\": 6.6,\n" +
                "        \"amountPromotion\": null,\n" +
                "        \"promotion\": null,\n" +
                "        \"extraFeeRate\": null\n" +
                "    },\n" +
                "    {\n" +
                "        \"startWeight\": 100.0,\n" +
                "        \"endWeight\": 1000.0,\n" +
                "        \"plusFee\": 4.6,\n" +
                "        \"plusWeight\": 6.0,\n" +
                "        \"firstWeight\": 200.0,\n" +
                "        \"firstFee\": 66.6,\n" +
                "        \"amountPromotion\": null,\n" +
                "        \"promotion\": null,\n" +
                "        \"extraFeeRate\": null\n" +
                "    },\n" +
                "    {\n" +
                "        \"startWeight\": 1000.0,\n" +
                "        \"endWeight\": 10000.0,\n" +
                "        \"plusFee\": 4.6,\n" +
                "        \"plusWeight\": 6.0,\n" +
                "        \"firstWeight\": 1200.6,\n" +
                "        \"firstFee\": 88.8,\n" +
                "        \"amountPromotion\": null,\n" +
                "        \"promotion\": null,\n" +
                "        \"extraFeeRate\": null\n" +
                "    }\n" +
                "]";
        //#endregion
        List weightBasedPostageRulesTemp = JsonUtils.getJsonObject(json, ArrayList.class);
        weightBasedPostageRules = (List) weightBasedPostageRulesTemp.stream()
                .map(item -> {
                    String jsonString = JsonUtils.toJsonString(item);
                    return JsonUtils.getJsonObject(jsonString, WeightBasedPostageRule.class);
                })
                .collect(Collectors.toList());
    }

    @Test
    public void testCalculateForLessThanFirstWeight() {
        double weight = 140.6d;

        Double shipmentCharges = shipmentPriceCalculator.calculate(weightBasedPostageRules, weight);

        assertNotNull(shipmentCharges);
        assertTrue(shipmentCharges > 0);
        assertTrue(shipmentCharges == 66.6);
    }

    @Test
    public void testCalculateForMoreThanFirstWeight() {
        double weight = 88.8;

        Double shipmentCharges = shipmentPriceCalculator.calculate(weightBasedPostageRules, weight);

        assertNotNull(shipmentCharges);
        assertTrue(shipmentCharges > 0);
        assertTrue(shipmentCharges <= 71 && shipmentCharges > 70);
    }

    @Test
    public void testCalculateForNotInRange() {
        double weight = 111111;

        Double shipmentCharges = shipmentPriceCalculator.calculate(weightBasedPostageRules, weight);

        assertNull(shipmentCharges);
    }
}