package com.qpp.core.utils;

import org.junit.Test;

import static org.junit.Assert.*;

/**
 * @description:
 * @author: TT-Berg
 * @date: 2024/1/8
 **/
public class StringCheckerTest {

    @Test
    public void isLetterDigitSuccess() {
        boolean letterDigit = StringChecker.isLetterDigit("123abcABC");
        assertTrue(letterDigit);
    }

    @Test
    public void isLetterDigitFail() {
        boolean letterDigit = StringChecker.isLetterDigit("123abcABC*");
        assertFalse(letterDigit);
    }

    @Test
    public void isChineseSuccess() {
        boolean isChinese = StringChecker.isChinese("你好");
        assertTrue(isChinese);
    }

    @Test
    public void isChineseFail() {
        boolean isChinese = StringChecker.isChinese("你好hello");
        assertFalse(isChinese);
    }

    @Test
    public void isLetterDigitOrChineseSuccess() {
        boolean isLetterDigitOrChinese = StringChecker.isLetterDigitOrChinese("一二三123abcAbc");
        assertTrue(isLetterDigitOrChinese);
    }

    @Test
    public void isLetterDigitOrChineseFail() {
        boolean isLetterDigitOrChinese = StringChecker.isLetterDigitOrChinese("{一二三123abcAbc");
        assertFalse(isLetterDigitOrChinese);
    }

    @Test
    public void checkChineseLetterSuccess() {
        boolean isChineseLetter = StringChecker.checkChineseLetter("一二三abcAbc");
        assertTrue(isChineseLetter);
    }

    @Test
    public void checkChineseLetterFail() {
        boolean isChineseLetter = StringChecker.checkChineseLetter("一二三abcAbc12");
        assertFalse(isChineseLetter);
    }

    @Test
    public void isDigitSuccess() {
        boolean isDigit = StringChecker.isDigit("123");
        assertTrue(isDigit);
    }

    @Test
    public void isDigitFail() {
        boolean isDigit = StringChecker.isDigit("123abc");
        assertFalse(isDigit);
    }

    @Test
    public void isLetterSuccess() {
        boolean isLetter = StringChecker.isLetter("abcABC");
        assertTrue(isLetter);
    }

    @Test
    public void isLetterFail() {
        boolean isLetter = StringChecker.isLetter("abcABC@");
        assertFalse(isLetter);
    }

    @Test
    public void hasDigitSuccess() {
        boolean hasDigit = StringChecker.hasDigit("123abc");
        assertTrue(hasDigit);
    }

    @Test
    public void hasDigitFail() {
        boolean hasDigit = StringChecker.hasDigit("abc");
        assertFalse(hasDigit);
    }

    @Test
    public void hasLetterSuccess() {
        boolean hasLetter = StringChecker.hasLetter("abc243");
        assertTrue(hasLetter);
    }

    @Test
    public void hasLetterFail() {
        boolean hasLetter = StringChecker.hasLetter("243");
        assertFalse(hasLetter);
    }
}