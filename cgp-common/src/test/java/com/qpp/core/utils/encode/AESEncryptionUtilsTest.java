package com.qpp.core.utils.encode;

import com.qpp.core.exception.BusinessException;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * @Author: zark
 * @Date: 2024/7/3 10:36
 * @Description:
 */

class AESEncryptionUtilsTest {

    @Test
    void encrypt() {
        String password = "Hello World";
        String key = "key12345678";
        String encryptedText = AESEncryptionUtils.encrypt(password, key);
        assertNotNull(encryptedText, "Encrypted text should not be null");
        assertFalse(encryptedText.isEmpty(), "Encrypted text should not be empty");
    }

    @Test
    void decrypt() {
        String encryptedText = "3194C32C20A56734D2E587021453D23F";
        String key = "key12345678";
        String decryptedText = AESEncryptionUtils.decrypt(encryptedText, key);
        assertNotNull(decryptedText, "Decrypted text should not be null");
        assertFalse(decryptedText.isEmpty(), "Decrypted text should not be empty");
    }

    @Test
    void encryptAndDecrypt() {
        String password = "Hello World";
        String key = "key12345678";
        String encryptedText = AESEncryptionUtils.encrypt(password, key);
        String decryptedText = AESEncryptionUtils.decrypt(encryptedText, key);
        assertEquals(password, decryptedText, "Decrypted text should match the original text");
    }

    @Test
    void encryptWithEmptyPassword() {
        String password = "";
        String key = "key12345678";
        assertThrows(BusinessException.class, () -> AESEncryptionUtils.encrypt(password, key), "Should throw BusinessException for empty password");
    }

    @Test
    void decryptWithInvalidHexText() {
        String invalidHexText = "invalidHex";
        String key = "key12345678";
        assertThrows(BusinessException.class, () -> AESEncryptionUtils.decrypt(invalidHexText, key), "Should throw BusinessException for invalid hex text");
    }
}

