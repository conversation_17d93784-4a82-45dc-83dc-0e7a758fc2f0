{
    "_id" : <PERSON><PERSON>ong(4),
    "code" : "HKD",
    "title" : "港币",
    "symbolLeft" : "$",
    "symbolRight" : "",
    "decimalPoint" : ".",
    "thousandsPoint" : ",",
    "decimalPlaces" : "2",
    "value" : 0.462999999523163,
    "website" : {
        "_id" : Number<PERSON><PERSON>(11),
        "code" : "PS",
        "name" : "PS",
        "url" : "PS",
        "showInAdmin" : false,
        "version" : NumberLong(0)
    },
    "modifiedDate" : ISODate("2020-11-20T02:30:45.715Z"),
    "modifiedBy" : "504",
    "clazz" : "com.qpp.cgp.domain.common.Currency"
}
{
    "_id" : NumberLong(7),
    "code" : "CNY",
    "title" : "人民币",
    "symbolLeft" : "CK",
    "symbolRight" : "",
    "decimalPoint" : ".",
    "thousandsPoint" : ",",
    "decimalPlaces" : "2",
    "value" : 0.259999990463257,
    "website" : {
        "_id" : Number<PERSON>ong(11),
        "code" : "PS",
        "name" : "PS",
        "url" : "PS",
        "showInAdmin" : false,
        "version" : NumberLong(0)
    },
    "modifiedDate" : ISODate("2020-11-20T02:15:45.791Z"),
    "modifiedBy" : "504",
    "clazz" : "com.qpp.cgp.domain.common.Currency"
}
{
    "_id" : NumberLong(16),
    "code" : "TWD",
    "title" : "台币",
    "symbolLeft" : "￥$",
    "symbolRight" : "",
    "decimalPoint" : ".",
    "thousandsPoint" : ",",
    "decimalPlaces" : "2",
    "value" : 1.52499997615814,
    "website" : {
        "_id" : NumberLong(11),
        "code" : "PS",
        "name" : "PS",
        "url" : "PS",
        "showInAdmin" : false,
        "version" : NumberLong(0)
    },
    "modifiedDate" : ISODate("2020-11-20T01:59:02.638Z"),
    "modifiedBy" : "420092",
    "clazz" : "com.qpp.cgp.domain.common.Currency"
}
{
    "_id" : NumberLong(20),
    "code" : "USD",
    "title" : "美元",
    "symbolLeft" : "$",
    "symbolRight" : "",
    "decimalPoint" : ".",
    "thousandsPoint" : ",",
    "decimalPlaces" : "2",
    "value" : 1.0,
    "website" : {
        "_id" : NumberLong(11),
        "clazz" : "com.qpp.cgp.domain.common.Website",
        "version" : NumberLong(0)
    },
    "createdDate" : ISODate("2015-07-21T01:19:53.000Z"),
    "modifiedDate" : ISODate("2020-03-24T10:49:22.980Z"),
    "modifiedBy" : "276",
    "clazz" : "com.qpp.cgp.domain.common.Currency"
}
{
    "_id" : NumberLong(224587),
    "code" : "CNY",
    "title" : "人民币",
    "symbolLeft" : "CK",
    "symbolRight" : "",
    "decimalPoint" : ".",
    "thousandsPoint" : ",",
    "decimalPlaces" : "2",
    "value" : 0.259999990463257,
    "website" : {
        "_id" : NumberLong(11),
        "clazz" : "com.qpp.cgp.domain.common.Website",
        "version" : NumberLong(0)
    },
    "createdDate" : ISODate("2015-07-21T08:03:55.000Z"),
    "modifiedDate" : ISODate("2020-03-24T10:49:22.982Z"),
    "modifiedBy" : "276",
    "clazz" : "com.qpp.cgp.domain.common.Currency"
}
{
    "_id" : NumberLong(22),
    "code" : "HKD",
    "title" : "港币",
    "symbolLeft" : "$",
    "symbolRight" : "",
    "decimalPoint" : ".",
    "thousandsPoint" : ",",
    "decimalPlaces" : "2",
    "value" : 0.462999999523163,
    "website" : {
        "_id" : NumberLong(11),
        "clazz" : "com.qpp.cgp.domain.common.Website",
        "version" : NumberLong(0)
    },
    "createdDate" : ISODate("2015-07-21T08:05:23.000Z"),
    "modifiedDate" : ISODate("2020-03-24T10:49:22.984Z"),
    "modifiedBy" : "276",
    "clazz" : "com.qpp.cgp.domain.common.Currency"
}
{
    "_id" : NumberLong(24),
    "code" : "HKD",
    "title" : "港币",
    "symbolLeft" : "##@",
    "symbolRight" : " ",
    "decimalPoint" : ".",
    "thousandsPoint" : ",",
    "decimalPlaces" : "2",
    "value" : 0.215000003576279,
    "website" : {
        "_id" : NumberLong(11),
        "clazz" : "com.qpp.cgp.domain.common.Website",
        "version" : NumberLong(0)
    },
    "createdDate" : ISODate("2015-07-21T08:31:14.000Z"),
    "modifiedDate" : ISODate("2020-03-24T10:49:22.986Z"),
    "modifiedBy" : "276",
    "clazz" : "com.qpp.cgp.domain.common.Currency"
}
{
    "_id" : NumberLong(54),
    "code" : "TWD",
    "title" : "台币",
    "symbolLeft" : "￥$",
    "symbolRight" : "",
    "decimalPoint" : ".",
    "thousandsPoint" : ",",
    "decimalPlaces" : "2",
    "value" : 1.52499997615814,
    "website" : {
        "_id" : NumberLong(11),
        "clazz" : "com.qpp.cgp.domain.common.Website",
        "version" : NumberLong(0)
    },
    "createdDate" : ISODate("2015-07-28T02:37:18.000Z"),
    "modifiedDate" : ISODate("2020-03-24T10:49:22.988Z"),
    "modifiedBy" : "276",
    "clazz" : "com.qpp.cgp.domain.common.Currency"
}
{
    "_id" : NumberLong(34),
    "code" : "CNY",
    "title" : "人民币",
    "symbolLeft" : "￥",
    "symbolRight" : "",
    "decimalPoint" : ".",
    "thousandsPoint" : ",",
    "decimalPlaces" : "2",
    "value" : 0.259999990463257,
    "website" : {
        "_id" : NumberLong(11),
        "clazz" : "com.qpp.cgp.domain.common.Website",
        "version" : NumberLong(0)
    },
    "createdDate" : ISODate("2015-07-28T02:46:07.000Z"),
    "modifiedDate" : ISODate("2020-03-24T10:49:22.990Z"),
    "modifiedBy" : "260",
    "clazz" : "com.qpp.cgp.domain.common.Currency"
}
{
    "_id" : NumberLong(35),
    "code" : "CNY",
    "title" : "人民币",
    "symbolLeft" : "CK",
    "symbolRight" : "",
    "decimalPoint" : ".",
    "thousandsPoint" : ",",
    "decimalPlaces" : "2",
    "value" : 0.259999990463257,
    "website" : {
        "_id" : NumberLong(11),
        "clazz" : "com.qpp.cgp.domain.common.Website",
        "version" : NumberLong(0)
    },
    "modifiedDate" : ISODate("2020-03-24T10:49:22.993Z"),
    "modifiedBy" : "140800",
    "clazz" : "com.qpp.cgp.domain.common.Currency"
}