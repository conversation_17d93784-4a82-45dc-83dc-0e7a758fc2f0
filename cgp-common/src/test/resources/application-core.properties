#project name
project.name=cgp-rest
#oauth server
oauth.server=http://*************/cgp2-oauth/
oauth.tokenservice.clientid=cgpadmin
oauth.tokenservice.password=password
oauth.resource.id=CGP

server.file=http://192.168.26.26:8080/file/file
server.id.generator=http://192.168.7.87:8805/cgp-rest-id-generator/common/key


cgp.admin.website.id=5
#reports director
reports.directory=classpath:reports/


##composing
printing.normal_notify_url=api/orders/ORDERID/printNotification
printing.order_url=api/orders/ORDERID/composing

cgp.frontend.server=http://*************/cgp-rest/
printing.notify_url=api/orders/ORDERID/printNotification?statusId=STATUSID

##order item composing
order_item_printing.normal_notify_url=api/orderItems/ORDERITEMID/printNotification
order_item_printing.order_url=api/orderItems/ORDERITEMID/composing
order_item_printing.notify_url=api/orders/ORDERITEMID/printNotification?statusId=STATUSID
order_item_composing_url=api/composing/orderItem

##appication mode
application.mode=stage
NormalUserAcpId =1370507
##templateUploadRedis时间
templateUploadRedisTime=7

##Google VerifyCode
google.proxy.host=**************
google.proxy.port=3128
##
com.qpp.cgp.token=aW5uZXJhcGk6cGFzc3dvcmQ=
#com.qpp.whitelabel.url=http://127.0.0.1:8080/
com.qpp.whitelabel.url=http://*************/cgp-rest/