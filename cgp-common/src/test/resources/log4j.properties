log4j.rootLogger=INFO, stdout

log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.Target=System.out
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=cgp-rest %d{yyyy-MM-dd HH:mm:ss} %-5p %c{1}:%L - %m%n


log4j.appender.file=org.apache.log4j.RollingFileAppender
log4j.appender.file.File=app.log
log4j.appender.R.MaxFileSize=100KB

log4j.appender.R.MaxBackupIndex=10
log4j.appender.file.layout=org.apache.log4j.PatternLayout
log4j.appender.file.layout.ConversionPattern=%d [%-5p] %c - %m%n

log4j.logger.com.qpp.qris.service=DEBUG
log4j.logger.kafka.coordinator=ERROR
log4j.logger.org.apache.kafka=ERROR


log4j.logger.com.qpp.cgp.rest.core=DEBUG

#log4j.logger.org.springframework.security=DEBUG