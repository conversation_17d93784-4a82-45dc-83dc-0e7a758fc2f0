topic.business.exception=business-exception
topic.system.error=system-error
topic.runtime.exception=runtime-exception
topic.unchecked.exception=unchecked-exception
topic.preprocess.exception=pre-process-exception
#project name
project.name=cgp-rest
#oauth server
oauth.server=http://*************:8888/oauth/
oauth.tokenservice.clientid=cgpadmin
oauth.tokenservice.password=password
oauth.resource.id=CGP
#kafka
kafka.bootstrap.servers=*************:9092,*************:9093,*************:9094

#cgp database connection setting
jdbc.driverClassName=com.mysql.jdbc.Driver
jdbc.url=*********************************************************************************************
#jdbc.url=********************************************************************************
jdbc.user=developer
jdbc.passwd=Dev!123a

hibernate.hbm2ddl.auto=validate
hibernate.show_sql=false
hibernate.dialect=org.hibernate.dialect.MySQL5Dialect

#mongodb database connection setting
mongodb.host=*************
mongodb.port=27017
mongodb.database=cgp2_dev
mongodb.user=developer
mongodb.passwd=Dev!123a

##file-server
server.file=http://*************:8080/file/file
server.id.generator=http://*************:8888/cgp-api-frontend/common/keyGenerate


cgp.admin.website.id=5

##appication mode
application.mode=test
