package com.qpp.cgp.config.mongo;

import com.mongodb.client.MongoDatabase;
import com.qpp.cgp.config.dbsource.MongoDBSource;
import com.qpp.cgp.config.dbsource.MongoDBSourceType;
import com.qpp.core.utils.ReflectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class MongoTemplateFactory {

    /**
     * 默认数据库 mongoTempalte
     */
    @Autowired
    private MongoTemplate defaultMongoTemplate;

    /**
     * 配置数据库 mongoTempalte
     */
    @Autowired
    @Qualifier(MongoTemplateBeanNames.CONFIG)
    private MongoTemplate configMongoTemplate;

    /**
     * 运行数据库 mongoTempalte
     */
    @Autowired
    @Qualifier(MongoTemplateBeanNames.RUNTIME)
    private MongoTemplate runtimeMongoTemplate;

    public MongoTemplate getMongoTemplate(Class clazz) {

        MongoDBSourceType type = Optional.ofNullable(
                ReflectUtils.findAnnotationByType(clazz, MongoDBSource.class)
        ).map(MongoDBSource::value).orElse(null);

        if (MongoDBSourceType.RUNTIME_MONGODB_SOURCE.equals(type)) {
            return runtimeMongoTemplate;
        }

        if (MongoDBSourceType.CONFIG_MONGODB_SOURCE.equals(type)) {
            return configMongoTemplate;
        }

        return defaultMongoTemplate;

    }

    public MongoTemplate getMongoTemplate(String collectionName) {

        if (defaultMongoTemplate.collectionExists(collectionName)) {
            return defaultMongoTemplate;
        } else if (configMongoTemplate.collectionExists(collectionName)) {
            return configMongoTemplate;
        } else if (runtimeMongoTemplate.collectionExists(collectionName)) {
            return runtimeMongoTemplate;
        } else {
            throw new RuntimeException(String.format("该mongo集合在源3个数据库中均不存在, collectionName : %s", collectionName));
        }

    }

    public MongoTemplate getMongoTemplateNotException(String collectionName) {

        if (defaultMongoTemplate.collectionExists(collectionName)) {
            return defaultMongoTemplate;
        } else if (configMongoTemplate.collectionExists(collectionName)) {
            return configMongoTemplate;
        } else if (runtimeMongoTemplate.collectionExists(collectionName)) {
            return runtimeMongoTemplate;
        }
        return null;
    }

    public MongoDatabase getMongoDataBase(String collectionName){
        MongoTemplate mongoTemplate = getMongoTemplate(collectionName);
        return mongoTemplate.getDb();
    }

}
