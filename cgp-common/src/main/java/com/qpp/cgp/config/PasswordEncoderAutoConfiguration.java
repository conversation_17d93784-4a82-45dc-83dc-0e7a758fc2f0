package com.qpp.cgp.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.password.NoOpPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * <AUTHOR>
 * @since 2018/8/2
 */
@Configuration
public class PasswordEncoderAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean(name = "cgpPasswordEncoder", value = PasswordEncoder.class)
    public PasswordEncoder cgpPasswordEncoder() {
        return NoOpPasswordEncoder.getInstance();
    }

}
