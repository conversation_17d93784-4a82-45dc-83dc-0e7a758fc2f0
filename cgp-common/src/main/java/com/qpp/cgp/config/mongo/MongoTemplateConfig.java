package com.qpp.cgp.config.mongo;

import com.qpp.mongo.driver.HybridMongoTemplate;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.core.convert.MongoConverter;

/**
 * <AUTHOR>
 * @date 2022/8/19
 */
@Configuration
@ConditionalOnClass({HybridMongoTemplate.class, AspectIgnoreMongoTemplate.class})
public class MongoTemplateConfig {

    @Bean
    @Primary
    @ConditionalOnMissingBean
    public HybridMongoTemplate hybridMongoTemplate(MongoDatabaseFactory mongoDatabaseFactory, MongoConverter mongoConverter) {
        return new HybridMongoTemplate(mongoDatabaseFactory, mongoConverter);
    }

//    @Bean
//    @ConditionalOnMissingBean
//    public AspectIgnoreMongoTemplate aspectIgnoreMongoTemplate(MongoDatabaseFactory mongoDatabaseFactory,
//                                                               MongoConverter mongoConverter) {
//        return new AspectIgnoreMongoTemplate(mongoDatabaseFactory, mongoConverter);
//    }
}
