package com.qpp.cgp.domain.common.font;

import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version : 1.0
 * Description : 通过Builder字体的DTO
 * Create Date : 2020-03-20 11:05
 **/
@NoArgsConstructor
@Data
public class CommonBuilderFontConfigDTO extends MongoDomain {

    /**
     * 字体的ID引用
     */
    List<FontDTO> fonts;

    /**
     * 默认字体
     */
    private FontDTO defaultFont;

    private String version;

    private List<LanguageFontFilterConfig> filters;
}
