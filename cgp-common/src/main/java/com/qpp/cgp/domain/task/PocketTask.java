package com.qpp.cgp.domain.task;

import com.qpp.cgp.config.domaintype.RuntimeDomain;
import com.qpp.cgp.emmeration.task.TaskStatusEnum;
import com.qpp.mongo.domain.LongMongoDomain;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * @className: PocketTask
 * @description: 定时任务兜底
 * @author: TT-Berg
 * @date: 2023/6/20
 **/
@RuntimeDomain
@Document("pocket_task")
@Data
public abstract class PocketTask extends LongMongoDomain {

    /**
     * 任务状态
     */
    protected TaskStatusEnum taskStatus;

    private String errorMessage;

    private String errorStrace;
}
