package com.qpp.cgp.domain.dto.common;

import com.qpp.cgp.domain.common.platform.Platform;
import com.qpp.cgp.domain.common.rate.ExchangeRate;
import com.qpp.cgp.domain.common.rate.ExchangeRateSetStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * @Author: lw
 * @Date: 2024/12/31/16:00
 * @Description:
 */
@Data
public class ExchangeRateSetDTO {

    @ApiModelProperty(value = "Id")
    private String id;

    /**
     * 默认状态:test
     * release状态不允许修改汇率集合
     */
    @ApiModelProperty(value = "状态")
    private ExchangeRateSetStatus status = ExchangeRateSetStatus.TEST;

    /**
     * 汇率集版本
     * 按照最大版本加一
     */
    @ApiModelProperty(value = "版本")
    private long version;

    private String description;

    /**
     * 此汇率生效的平台代码集合
     */
    @ApiModelProperty(value = "此汇率生效的平台集合--只有汇率集分页查询才填充")
    private List<Platform> usedPlatforms;

    /**
     * 关联的汇率配置
     */
    @ApiModelProperty(value = "汇率配置列表")
    private List<ExchangeRate> exchangeRates;
}
