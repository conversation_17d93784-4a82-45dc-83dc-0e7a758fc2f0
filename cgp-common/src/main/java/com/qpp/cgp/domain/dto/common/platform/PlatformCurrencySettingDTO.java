package com.qpp.cgp.domain.dto.common.platform;

import com.qpp.cgp.domain.common.platform.CurrencyUsageScope;
import com.qpp.cgp.domain.common.platform.EffectiveMode;
import com.qpp.cgp.domain.common.platform.Platform;
import com.qpp.cgp.domain.dto.common.ExchangeRateSetDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.Instant;
import java.util.List;

/**
 * @Author: lw
 * @Date: 2024/12/31/17:01
 * @Description:
 */
@Data
public class PlatformCurrencySettingDTO {

    private String id;

    @ApiModelProperty(value = "平台信息")
    private Platform platform;

    @ApiModelProperty(value = "汇率集")
    private ExchangeRateSetDTO exchangeRateSet;

    @ApiModelProperty(value = "支持的货币配置列表")
    private List<CurrencyUsageScopeDTO> currencyUsageScopes;

    /**
     * 生效时间
     */
    @ApiModelProperty(value = "生效时间毫秒-UTC时间戳13位-不传表示立即生效")
    private Long effectiveTime;

    /**
     * 过期时间
     * nextEffectiveSettingId的生效时间减一秒
     */
    @ApiModelProperty(value = "配置失效时间毫秒-UTC时间戳13位-不传表示立即生效")
    private Long expiredTime;

    private long version = 1;

    @ApiModelProperty(value = "配置状态-只做前端展示使用")
    private Status status;

    @ApiModelProperty(value = "即将生效的配置id引用")
    private String nextEffectiveSettingId;

    //生效模式
    private EffectiveMode effectiveMode;

    private String description;

    public enum Status {
        WAITE_EFFECTIVE,

        EFFECTIVE,

        EXPIRED
    }
}
