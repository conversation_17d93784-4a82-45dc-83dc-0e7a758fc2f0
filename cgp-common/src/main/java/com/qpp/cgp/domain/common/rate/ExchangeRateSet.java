package com.qpp.cgp.domain.common.rate;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * @Author: lw
 * @Date: 2024/12/02/15:35
 * @Description:
 */
@ConfigDomain
@JsonTypeInfo(use = JsonTypeInfo.Id.NONE)
@Document(collection = "exchangeratesets")
@Data
public class ExchangeRateSet extends MongoDomain {

    /**
     * 默认状态:test
     * release状态不允许修改汇率集合
     */
    private ExchangeRateSetStatus status = ExchangeRateSetStatus.TEST;

    /**
     * 在最大version配置基础上加一
     */
    private long version = 1;

    private String description;

    @Transient
    private List<ExchangeRate> exchangeRates;

}
