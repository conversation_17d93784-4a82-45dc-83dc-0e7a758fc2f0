package com.qpp.cgp.domain.dto.management;


import com.qpp.cgp.manager.common.WebsiteDTO;
import com.qpp.core.dto.DTO;

import javax.validation.constraints.NotNull;

public class RefundMailTemplateDTO implements DTO {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private Long id;

	private String subject;
	
	private String fileName;
	
	private String emails;

	public String templateId;
	
	@NotNull
	private WebsiteDTO website;
	
	
	public String getEmails() {
		return emails;
	}

	public void setEmails(String emails) {
		this.emails = emails;
	}

	public WebsiteDTO getWebsite() {
		return website;
	}

	public void setWebsite(WebsiteDTO website) {
		this.website = website;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getSubject() {
		return subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getTemplateId() {
		return templateId;
	}

	public void setTemplateId(String templateId) {
		this.templateId = templateId;
	}

}

