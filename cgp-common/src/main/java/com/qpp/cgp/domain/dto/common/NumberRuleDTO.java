package com.qpp.cgp.domain.dto.common;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.cgp.domain.common.Website;
import com.qpp.cgp.migration.CgpMongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

@ConfigDomain
@NoArgsConstructor
@Data
@JsonTypeInfo(use = JsonTypeInfo.Id.NONE)
@Document(collection = "numberrules")
public class NumberRuleDTO extends CgpMongoDomain {

    private String name;

    private Long smallest;

    private Long largest;

    private Long serial;

    private String format;

    private Long websiteId;

    @Transient
    private Website website;

    private Boolean serialRegenBaseDay;

    public void setWebsite(Website website) {
        this.website = website;

        if (website != null) {
            this.websiteId = website.getId();
        }
    }
}
