package com.qpp.cgp.domain.dto.common.platform;

import com.qpp.cgp.domain.common.platform.CurrencyUsageScope;
import com.qpp.cgp.domain.common.platform.EffectiveMode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.Instant;
import java.util.List;

/**
 * @Author: lw
 * @Date: 2024/12/31/17:15
 * @Description:
 */
@Data
public class PlatformCurrencySettingCreateDTO {

    @ApiModelProperty(value = "平台id")
    private String platformId;

    @ApiModelProperty(value = "汇率集id")
    private String exchangeRateSetId;

    private List<CurrencyUsageScope> currencyUsageScopes;

    //生效模式
    private EffectiveMode effectiveMode;

    /**
     * 生效时间
     */
    @ApiModelProperty(value = "生效时间毫秒-UTC时间戳13位-不传表示立即生效")
    private Long effectiveTime;


    private String description;

}
