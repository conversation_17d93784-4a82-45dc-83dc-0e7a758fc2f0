package com.qpp.cgp.domain.dto.common.platform;

import com.qpp.cgp.domain.common.Currency;
import com.qpp.cgp.domain.common.platform.UsageScope;
import lombok.Data;

import java.util.List;

/**
 * @Author: lw
 * @Date: 2025/01/04/17:05
 * @Description:
 */
@Data
public class CurrencyUsageScopeDTO {

    private String id;

    private String platformCurrencySettingId;

    private UsageScope scope;


    /**
     * 生效的货币code集合
     * com.qpp.cgp.domain.common.Currency
     */
    private List<Currency> currencies;

}
