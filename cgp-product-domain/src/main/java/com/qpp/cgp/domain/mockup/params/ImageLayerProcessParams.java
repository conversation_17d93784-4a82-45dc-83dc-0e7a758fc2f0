package com.qpp.cgp.domain.mockup.params;

import com.qpp.cgp.domain.mockup.MockupLayerTypes;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ImageLayerProcessParams extends MockupLayerProcessParams {

    public ImageLayerProcessParams() {
        type = MockupLayerTypes.IMAGE;
    }

    private String imageUrl;

    private List<Map<String, Object>> imagePipeline;

}
