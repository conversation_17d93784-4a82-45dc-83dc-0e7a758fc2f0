package com.qpp.cgp.domain.pcresource.virtualcontainer;

import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class VirtualContainerContent {

    private String name;

    /**
     * 指向的一定是个Container
     */
    private String selector;

    /**
     * 如果是True的话，就是直接替换Container的items，如果是false的话，在原有的items上追加对应的value
     */
    private Boolean replace = false;

    /**
     * 是否是必要，如果是必要的话，外部一定要传入对应的Item
     */
    private Boolean required = false;



}
