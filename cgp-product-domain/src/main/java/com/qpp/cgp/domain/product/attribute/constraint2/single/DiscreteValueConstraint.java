package com.qpp.cgp.domain.product.attribute.constraint2.single;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 单离散值约束<br>
 * 只作为输入前约束<br>
 * Created by smart on 12/29/2017.
 */
@NoArgsConstructor
@Data

public class DiscreteValueConstraint extends SkuAttributeSingleConstraint {


    /**
     * 所有属性值<br>
     * 最后计算时都会经过筛选<br/>
     */
    private List<DiscreteValueConstraintItem> items = new ArrayList<>();


    /**
     * 返回结果是包含还是排除<br/>
     * true为包含<br/>
     * false为排除<br/>
     */
    private boolean isInclude;


}
