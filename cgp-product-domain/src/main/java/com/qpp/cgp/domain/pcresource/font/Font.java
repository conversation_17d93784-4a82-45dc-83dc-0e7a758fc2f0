package com.qpp.cgp.domain.pcresource.font;

import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.cgp.domain.common.Language;
import com.qpp.cgp.domain.common.font.FontStyleKey;
import com.qpp.cgp.domain.pcresource.ISimplePCResource;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Document(collection = "pcresourcefonts")
@ConfigDomain
@NoArgsConstructor
@Data
public class Font extends ISimplePCResource {

    private String fontFamily;

    private String displayName;

    private String wordRegExp;

    private List<FontStyleKey> supportedStyle;

    private List<Language> languages;
}
