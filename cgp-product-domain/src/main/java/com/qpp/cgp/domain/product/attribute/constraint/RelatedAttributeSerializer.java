package com.qpp.cgp.domain.product.attribute.constraint;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.qpp.cgp.domain.product.ConfigurableProductSkuAttribute;
import org.hibernate.collection.internal.PersistentBag;


import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by smart on 8/12/2017.
 */

public class RelatedAttributeSerializer extends JsonSerializer<PersistentBag> {


    @Override
    public void serialize(PersistentBag value, JsonGenerator gen, SerializerProvider serializers) throws IOException {


        List<String> codes = (List<String>) value.stream()
                .map(obj -> ((ConfigurableProductSkuAttribute) obj).getAttribute().getCode())
                .collect(Collectors.toList());
        if (codes.size() > 0)
            gen.writeObject(codes);
        else
            serializers.defaultSerializeNull(gen);

    }
}
