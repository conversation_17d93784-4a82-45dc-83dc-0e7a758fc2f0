package com.qpp.cgp.domain.pcresource;

import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Document(collection = "businesslibraryofcommons")
@ConfigDomain
@NoArgsConstructor
@Data
public class BusinessLibraryOfCommon extends MongoDomain {

    private EffectType effectType;

    private BusinessType businessType;

    /**
     * IDRef
     */
    private PCResourceLibrary library;

    /**
     * IdRef
     */
    private BusinessLibrary businessLib;

    private String description;

    /**
     * 默认配置
     */
    private List<PCResourceItemInfo> defaultItems;

    private String defaultFillStrategy;
}
