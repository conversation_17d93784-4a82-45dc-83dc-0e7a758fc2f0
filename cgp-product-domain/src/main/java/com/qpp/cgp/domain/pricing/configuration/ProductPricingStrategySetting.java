package com.qpp.cgp.domain.pricing.configuration;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;

@NoArgsConstructor
@Data
public class ProductPricingStrategySetting {

    /**
     * ProductPricingStrategyConfig#_id
     */
    @Id
    private Long _id;

    /**
     * 類型
     */
    private String clazz;

    /**
     * 貨幣代碼
     */
    private String currency;

    /**
     * 序號
     */
    private int index;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 條件表達式配置
     */
    private PricingFilterSetting filterSetting;

    private Boolean isDefault;


    private String strategyType;
}
