package com.qpp.cgp.domain.product.attribute.constraint2.single;

import com.qpp.mongo.domain.LongMongoDomain;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/29
 */
@Getter
@Setter
@Document(collection = "skuattributesingleconstraintgroups")
public class SkuAttributeSingleConstraintGroup extends LongMongoDomain {

    private String name;

    /**
     * 绑定的SkuAttribute
     */
    private List<Long> skuAttributeIds;

    /**
     * 约束的描述
     */
    private String description;
}
