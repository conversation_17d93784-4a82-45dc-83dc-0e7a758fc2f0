package com.qpp.cgp.domain.dto.product;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.qpp.cgp.domain.product.ConfigurableProduct;
import com.qpp.cgp.domain.product.ProductPackage;
import com.qpp.cgp.domain.product.SkuProduct;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CollectionId;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Type;
import org.springframework.data.annotation.Transient;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by smart on 9/25/2017.
 */
@NoArgsConstructor
@Data
public class SkuProductDTO extends ProductDomainDTO {


    //sku编号唯一
    private String sku;

    @org.springframework.data.annotation.Transient
    @JsonIgnore
//    @ManyToOne(cascade = CascadeType.REFRESH, optional = true)
//    @JoinColumn(name = "product_configurable_product_id")
    private ConfigurableProduct configurableProduct;

    @Transient
//    @ManyToMany(cascade = CascadeType.ALL)
//    @JoinTable(name = "cgp_product_to_package", joinColumns = @JoinColumn(name = "product_id"), inverseJoinColumns = @JoinColumn(name = "package_id"))
    // @OrderColumn(name = "sort_order")
//    @CollectionId(type = @Type(type = "long"), columns = @Column(name = "id"), generator = "cgp_id_generator")
    private List<ProductPackage> packages = new ArrayList<>();

}
