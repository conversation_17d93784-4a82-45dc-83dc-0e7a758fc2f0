package com.qpp.cgp.domain.pcresource;

import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.cgp.domain.product.config.ProductConfigDesign;
import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@ConfigDomain
@NoArgsConstructor
@Data
@Document(collection = "businesslibraryofproductconfigdesigns")
public class BusinessLibraryOfProductConfigDesign extends MongoDomain {
    /**
     * businessType
     */
    private BusinessType businessType;

    /**
     * effectType
     */
    private EffectType effectType;

    /**
     * IdRef
     */
    private ProductConfigDesign productConfigDesign;

    /**
     * IdRef
     */
    private PCResourceLibrary library;

    /**
     * IdRef
     */
    private BusinessLibrary businessLib;

    private String description;

    /**
     * 是否继承上一级：如果继承的话需要去查询上一级，然后取并集
     */
    private Boolean isExtend = false;

    /**
     * 默认配置
     */
    private List<PCResourceItemInfo> defaultItems;


    private String defaultFillStrategy;
}
