package com.qpp.cgp.domain.bom;

import com.qpp.cgp.domain.bom.qty.PlaceHolderVdCfg;
import com.qpp.cgp.domain.bom.qty.VariableDataSourceQtyCfg;
import com.qpp.cgp.domain.bom.runtime.pc.PCDisplayName;
import com.qpp.cgp.domain.executecondition.InputCondition;
import com.qpp.cgp.domain.parameterized.IArgumentAssignSource;
import com.qpp.cgp.domain.product.config.material.mapping2dto.MappingRule;
import com.qpp.cgp.domain.theme.AbstractMvtTheme;
import com.qpp.cgp.expression.Expression;
import com.qpp.cgp.value.ValueEx;
import com.qpp.mongo.domain.MongoDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> jerry
 * @version : 1.0
 * Description : 抽象MaterialViewType
 * Create Date : 2019-11-18 15:22
 **/
@NoArgsConstructor
@Data
public class AbstractMaterialViewType extends MongoDomain implements IArgumentAssignSource {


    /**
     * 是否生效的表达式
     */
    private ValueEx condition;

    /**
     * 对应的MaterialViewType
     */
    @NotNull
    private MaterialViewType materialViewType;

    /**
     * 对应的MaterialViewType的模板
     */
    private MaterialViewType materialViewTypeTemplate;

    /**
     * 动态PageContent数量
     */
    private ValueEx pageContentQty;

    /**
     * 模板配置Id 需要改为数组 mongodb怎么跨数据库关联查询
     */
    private List<String> productMaterialViewTemplateConfigIds;

    /**
     * PageContentRange
     */
    private QuantityRange pageContentRange;

    /**
     * pageContentIndexExpression
     */
    private String pageContentIndexExpression;

    /**
     *
     */
    private String userAssign;

    /**
     * 上传数量配置
     */
    private List<VariableDataSourceQtyCfg> variableDataSourceQtyCfgs;

    /**
     * 模板配置
     */
    private List<PlaceHolderVdCfg> placeHolderVdCfgs;


    /**
     * name 字段唯一表示符
     */
    private String name;

    /**
     * 前端配置使用
     */
    private Boolean defaultNode;

    @ApiModelProperty(value = "产品配置版本Id")
    private Long productConfigDesignId;

    @ApiModelProperty(value = "复制的SMVT或PMVT的源的MVT Id")
    private String sourceId;


    /**
     * 实际宽
     */
    private ValueEx actualWidthEx;

    /**
     * 实际高度
     */
    private ValueEx actualHeightEx;

    /**
     * 单位尺寸
     */
    private String actualUnit;

    private InputCondition conditionDTO;

    /**
     * 前端用于标记SimplifyMaterialVieWType或ProductMaterialViewType是否是一组的标记
     */
    private String mvtGroup;

    /**
     * 计算默认主题，优先级高于配置的默认主题，计算返回的结果时主题的配置Id
     */
    private Expression defaultThemeExpression;

    /**
     * 默认主题：优先级低于表达式选中的默认主题
     */
    private AbstractMvtTheme defaultTheme;

    /**
     * 前端
     */
    private List<MappingRule> defaultThemeExpressionDTO;

    /**
     * 产品唯一Code
     */
    @NotBlank
    private String code;


    /**
     * 显示名称
     */
    private String displayName;

    /**
     * 支持的资源类型
     */
    private List<String> supportedResourceTypes;

    /**
     * 是否预览
     */
    private Boolean isPreview;

    @ApiModelProperty(value = "PC的displayName字段的生成配置(ID引用)")
    private PCDisplayName pcDisplayNameConfig;

    private Integer sortOrder = 0;

}
