package com.qpp.cgp.domain.partner.store;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

@JsonTypeInfo(use = JsonTypeInfo.Id.NONE)
@NoArgsConstructor
@Data
@ConfigDomain
@Document(collection = "storeplatforms")
public class StorePlatform extends MongoDomain {

    private String name;

    private String icon;

    private String code;

    private String apiPath;

}
