package com.qpp.cgp.domain.product.config.mockup.layer;

import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.cgp.expression.Expression;
import com.qpp.mongo.domain.MongoDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Map;

@NoArgsConstructor
@Data
@ConfigDomain
@Document(collection = "mockuplayerfillconfigs")
public class MockupLayerFillConfig extends MongoDomain {

    @ApiModelProperty("MockupGenerateConfig 配置 ID，非空")
    private String mockupGenerateConfigId;

    @ApiModelProperty("Layer占位符类型，可选：IMAGE, COLOR, MASK")
    private String type;

    @ApiModelProperty("Layer占位符标识符，非空")
    private String code;

    @ApiModelProperty("生效表达式，可空")
    private Expression condition;

    @ApiModelProperty("生效表达式前端组件数据，可空")
    private Map<String, Object> conditionDTO;

}
