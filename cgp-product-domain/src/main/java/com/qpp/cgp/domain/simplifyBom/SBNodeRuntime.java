package com.qpp.cgp.domain.simplifyBom;

import com.qpp.cgp.config.domaintype.RuntimeDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

/**
 * 运行时SimplifyBomNode
 */
@Getter
@Setter
@RuntimeDomain
@Document(collection = "sbnoderuntime")
public class SBNodeRuntime extends AbstractSBNode {

    /**
     * 配置时id
     */
    private String configId;

    @ApiModelProperty("对应简易的MaterialViewTypes")
    private List<SimplifyMaterialView> simplifyMaterialViews;


    @ApiModelProperty(value = "父简易Bom节点")
    private SBNodeRuntime parent;

    @ApiModelProperty(value = "子节点")
    private List<SBNodeRuntime> child = new ArrayList<>();

    private Long productConfigDesignId;

    @ApiModelProperty(value = "关联的PropertyModelId")
    private String propertyModelId;

    /**
     * 关联的SPU物料全路径
     */
    private String spuMaterialPath;

    /**
     * pci数量
     */
    private Integer pciQty;

    /**
     * 是否包含SMV实体
     */
    private boolean isContainSmvEntity = false;

    /**
     * 是否包含PC实体
     */
    private boolean isContainPCEntity = false;

    /**
     * 关联的产品Id
     */
    private String storeProductId;
}
