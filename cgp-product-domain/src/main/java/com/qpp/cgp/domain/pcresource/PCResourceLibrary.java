package com.qpp.cgp.domain.pcresource;

import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "pcresourcelibraries")
@ConfigDomain
@NoArgsConstructor
@Data
public class PCResourceLibrary extends MongoDomain {
    
    private String name;

    /**
     * 对应的IPCResource子类的Clazz,后端不用专门处理
     * 管理界面存储的时候传入
     * 对于有多个子类的话，对应的值就是基类的Clazz，例如Color
     */
    private String type;

    /**
     * 描述
     */
    private String description;

}
