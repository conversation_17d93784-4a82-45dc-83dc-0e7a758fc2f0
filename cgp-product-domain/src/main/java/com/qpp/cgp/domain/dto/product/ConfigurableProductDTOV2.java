package com.qpp.cgp.domain.dto.product;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qpp.cgp.domain.product.ConfigurableProduct;
import com.qpp.cgp.domain.product.attribute.constraint2.multi.MultiDiscreteValueConstraint;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Transient;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by smart on 1/3/2018.
 */
@NoArgsConstructor
@Data
public class ConfigurableProductDTOV2 extends ProductDomainDTOV2 {


    @org.springframework.data.annotation.Transient
//    @OneToMany(cascade = {CascadeType.ALL}, orphanRemoval = true, mappedBy = "product")
    @OrderBy("sortOrder ASC")
    private List<ConfigurableProductSkuAttributeDTOV2> skuAttributes = new ArrayList<>();

    @Transient
    @JsonProperty
    private List<MultiDiscreteValueConstraint> multiDiscreteValueConstraints;

}
