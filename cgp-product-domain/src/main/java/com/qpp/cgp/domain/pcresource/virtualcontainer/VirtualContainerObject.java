package com.qpp.cgp.domain.pcresource.virtualcontainer;

import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.cgp.domain.bom.runtime.RtObject;
import com.qpp.cgp.domain.theme.IPCResourceBuilderConfig;
import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;
import lombok.NoArgsConstructor;

import java.util.List;

@Document(collection = "virtualcontainerobjects")
@NoArgsConstructor
@ConfigDomain
@Data
public class VirtualContainerObject extends MongoDomain implements IPCResourceBuilderConfig {

    /**
     * IdRef
     */
    private VirtualContainerType containerType;

    private MapRtObjectBuildConfig argumentBuilder;

    private RtObject argument;

    private PCLayout layout;

    private List<ContentMapItem> contentMapItems;

    private String description;

    private Boolean isUseImage;

}
