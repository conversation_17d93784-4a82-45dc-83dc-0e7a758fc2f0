package com.qpp.cgp.domain.simplifyBom;

import com.qpp.cgp.domain.bom.Material;
import com.qpp.cgp.domain.bom.attribute.RtType;
import com.qpp.cgp.domain.bom.bomitem.BOMItem;
import com.qpp.cgp.domain.bom.runtime.ReferenceSource;
import com.qpp.cgp.domain.bom.runtime.RtObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class AbstractSBNode extends ReferenceSource {


    @ApiModelProperty(value = "存储的时候源的SBNodeId")
    private String sourceSBNodeId;

    //TODO 修改id为运行时
    @ApiModelProperty(value = "对应销售Bom中的路径")
    private String sbomPath;

    @ApiModelProperty(value = "对应的RtType")
    private RtType rtType;

    @ApiModelProperty(value = "完全路径")
    private String completePath;

    @ApiModelProperty(value = "给前端用的")
    private Boolean defaultNode;

    @ApiModelProperty(value = "对应的RtObject")
    private RtObject rtObject;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "是否启用")
    private Boolean isEnable;

    @ApiModelProperty(value = "是否是叶子节点：true表示是，false表示否")
    private Boolean leaf;

    @ApiModelProperty(value = "对应的节点的物料")
    private Material material;

    @ApiModelProperty(value = "对应的BomItem")
    private List<BOMItem> bomItem;

    @ApiModelProperty(value = "判断是否有对应SimplifyMaterialViewType")
    private Boolean isContainSimplifyMaterialViewType;

    @ApiModelProperty(value = "复制的SBNode的源的SBNodeId")
    private String sourceId;

    @ApiModelProperty(value = "是否需要预处理")
    private Boolean isNeedPreprocess = false;

    /**
     * Code 唯一
     */
    private String code;
}
