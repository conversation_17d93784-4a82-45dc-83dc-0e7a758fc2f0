package com.qpp.cgp.domain.bom;

import com.qpp.cgp.config.domaintype.ConfigDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotNull;

@Document(collection = "materialmvts")
@ConfigDomain
@Data
public class MaterialMvt extends AbstractMaterialViewType {

    @NotNull
    private Material material;

    @ApiModelProperty("物料路径，管理接口使用，实际运行逻辑用不到")
    @Transient
    private String materialPath;

    @ApiModelProperty("是否是通过viewDisplay配置上绑定的mmvt,管理接口使用，实际运行逻辑用不到")
    @Transient
    private Boolean isViewDisplayConfiguration;

    @ApiModelProperty("是否生效，是否已经被pmvt或smvt复写")
    @Transient
    private Boolean isEffective;
}
