package com.qpp.cgp.domain.attributeconstraint.multi.rule;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2019/10/29
 * @see com.qpp.cgp.domain.product.ConfigurableProductSkuAttribute
 * @see com.qpp.cgp.domain.product.ProductCustomAttributeOption
 */
@NoArgsConstructor
@Data
public class TreeNode {

    private Long skuAttributeId;

    private String attributeName;

    private Long optionId;

    private String optionName;

    private Boolean leaf;

    private List<TreeNode> children = new ArrayList<>();



}
