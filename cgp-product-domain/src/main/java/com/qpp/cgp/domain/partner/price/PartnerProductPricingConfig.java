package com.qpp.cgp.domain.partner.price;

import com.qpp.cgp.domain.pricing.CalculationExpression;
import com.qpp.cgp.domain.pricing.configuration.ProductPricingStrategySetting;
import com.qpp.cgp.domain.product.attribute.VersionedProductAttribute;
import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * Partner零售价格
 */
@Document(collection = "partnerproductpricingconfigs")
@NoArgsConstructor
@Data
public class PartnerProductPricingConfig extends MongoDomain {

    private String partnerId;

    private String productId;

    private List<ProductPricingStrategySetting> strategies;

    private CalculationExpression filter;

    @Transient
    private List<org.bson.Document> strategiesDocuments;

    /**
     * 产品属性版本
     */
    private VersionedProductAttribute versionedAttribute;

}
