package com.qpp.cgp.domain.image.pipeline.operation;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * description:  裁剪操作参数
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS, property = "type")
public class ClipOperation extends FilePipelineOperation{
    private Double x;

    private Double y;

    private Double w;

    private Double h;
}



