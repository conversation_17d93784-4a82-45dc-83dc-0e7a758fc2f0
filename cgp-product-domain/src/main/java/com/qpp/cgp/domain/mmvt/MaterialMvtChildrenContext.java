package com.qpp.cgp.domain.mmvt;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: TT-Berg
 * @date: 2024/1/31
 **/
@Data
public class MaterialMvtChildrenContext implements Serializable {

    private Map<String, Object> attrs;

    private Map<String, Object> bomItems;

    private Integer parentBomItemQty;

    private MaterialContext topMaterial;

    private MaterialContext parentMaterial;

    private MaterialContext currentMaterial;

    private List<MMvtContext> mmvts;

    private List<MvtContext> mvts;
}
