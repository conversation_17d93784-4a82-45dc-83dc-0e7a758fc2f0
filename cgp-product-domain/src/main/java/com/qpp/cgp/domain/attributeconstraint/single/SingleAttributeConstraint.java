package com.qpp.cgp.domain.attributeconstraint.single;

import com.qpp.cgp.domain.executecondition.ExecuteCondition;
import com.qpp.cgp.domain.product.attribute.constraint2.single.SkuAttributeSingleConstraint;
import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @since 2019/10/29
 * @see com.qpp.cgp.domain.product.ConfigurableProductSkuAttribute
 */
@NoArgsConstructor
@Data
@Document(collection = "singleattributeconstraints")
public class SingleAttributeConstraint extends MongoDomain {

    protected SkuAttributeSingleConstraint attributeConstraintDomain;

    protected String description;

    protected String promptTemplate;

    protected String expressionTipTemplate;

    protected Long skuAttributeId;

    protected ExecuteCondition executeCondition;

}
