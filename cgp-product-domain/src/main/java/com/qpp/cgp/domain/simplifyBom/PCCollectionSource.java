package com.qpp.cgp.domain.simplifyBom;

import com.qpp.cgp.domain.bom.runtime.AbstractSource;
import com.qpp.cgp.domain.bom.runtime.PageContent;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class PCCollectionSource extends AbstractSource {
    /**
     * 存储的是IdRef
     */
    private List<PageContent> pcList;

    /**
     * 存储的是IdRef
     */
    private List<SimplifyMaterialView> mvList;
}
