package com.qpp.cgp.domain.attributeconstraint.multi;

import com.qpp.cgp.domain.product.attribute.constraint2.multi.MultiDiscreteValueConstraint;
import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2019/10/29
 * @see com.qpp.cgp.domain.product.ConfigurableProductSkuAttribute
 */
@NoArgsConstructor
@Data
@Document(collection = "multiattributeconstraints")
public class MultiAttributeConstraint extends MongoDomain {

    protected MultiDiscreteValueConstraint attributeConstraintDomain;

    protected Boolean enable;

    protected String description;

    protected List<Long> inSkuAttributeIds = new ArrayList<>();

    protected Long productId;

}
