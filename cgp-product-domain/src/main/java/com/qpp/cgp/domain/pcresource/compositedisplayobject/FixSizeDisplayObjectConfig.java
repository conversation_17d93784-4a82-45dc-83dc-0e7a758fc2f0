package com.qpp.cgp.domain.pcresource.compositedisplayobject;

import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;
import java.util.Map;

@Document(collection = "fixsizedisplayobjectconfigs")
@ConfigDomain
@NoArgsConstructor
@Data
public class FixSizeDisplayObjectConfig extends MongoDomain {

    private Number width;

    private Number height;

    private List<Map<String,Object>> items;

    /**
     * IdRef
     */
    private CompositeDisplayObject compositeDisplayObject;

    private String description;
}
