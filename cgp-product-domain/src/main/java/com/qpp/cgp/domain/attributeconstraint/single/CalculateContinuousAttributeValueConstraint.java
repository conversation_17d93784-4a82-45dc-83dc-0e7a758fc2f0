package com.qpp.cgp.domain.attributeconstraint.single;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2019/10/29
 * @see com.qpp.cgp.domain.product.ConfigurableProductSkuAttribute
 */
@NoArgsConstructor
@Data
public class CalculateContinuousAttributeValueConstraint extends SingleAttributeContinuousValueConstraint {

    private Long minSkuAttributeId;

    private Long maxSkuAttributeId;

    private String operator;

}
