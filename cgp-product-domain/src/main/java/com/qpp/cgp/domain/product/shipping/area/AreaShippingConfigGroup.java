package com.qpp.cgp.domain.product.shipping.area;

import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Document(collection = "areashippingconfiggroups")
@NoArgsConstructor
@Data
public class AreaShippingConfigGroup extends MongoDomain {

    private String name;

    private List<String> tags;

    private String currencyCode;

    private List<AreaShippingConfig> areaShippingConfigs;

}
