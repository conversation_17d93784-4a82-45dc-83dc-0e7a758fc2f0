package com.qpp.cgp.domain.product.shipping;

import com.qpp.cgp.domain.executecondition.InputCondition;
import com.qpp.cgp.expression.Expression;
import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public abstract class ProductShippingConfig extends MongoDomain {

    protected Long productId;

    protected Boolean defaultConfig;

    protected Expression attributePredicate;

    protected InputCondition attributePredicateDto;

}
