package com.qpp.cgp.domain.bom.runtime;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;
import java.util.Map;

@NoArgsConstructor
@Data
@Document(collection = "pagecontents")
public class PageContent extends ReferenceSource {


    /**
     * PageContent的下标，用于预处理指定某个下标的PC
     */
    private String index;

    private String code;

    private String name;

    /**
     * 前端用于展示的
     */
    private String displayName;

    /**
     * 前端自己填充
     */
    private String snapshot;

    private Double width;

    private Double height;

    private List<Map<String, Object>> layers;

    private RtObject rtObject;

    private String pageContentSchemaId;

    private Map<String, Object> clipPath;

    private String templateId;

    private int sortIndex;

    /**
     * 生成模式，是前端自己存储还是用作配置用的
     */
    private String generateMode;

    private String pageContentSchemaRuntimeId;

    /**
     * 是否需要预设内容处理
     */
    private Boolean isNeedPreset;

    /**
     * Builder中是否隐藏
     */
    private Boolean builderHidden;

    /**
     * 结构版本
     */
    private int structVersion = 2;

    private List<Map<String,Object>> constraints;

    /**
     * 取整方式
     */
    private RoundingMode roundingMode;

    /**
     * 可视范围，前端使用，从pcs获取
     */
    private List<Double> bound;

    public PageContent convert2IdRef() {
        PageContent result = new PageContent();
        result.setId(this.getId());
        result.setClazz(this.getClazz());
        return result;
    }
}
