package com.qpp.cgp.domain.bom.datasource;

import com.qpp.cgp.domain.bom.QuantityRange;
import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * Created by smart on 7/21/2017.
 */
@NoArgsConstructor
@Data
@Document(collection = "variabledatasources")
public class VariableDataSource extends MongoDomain {

    private String expression;

    private String selector;

    private QuantityRange quantityRange;

}
