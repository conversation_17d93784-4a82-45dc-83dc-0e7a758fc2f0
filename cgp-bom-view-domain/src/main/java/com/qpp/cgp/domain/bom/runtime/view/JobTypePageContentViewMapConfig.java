package com.qpp.cgp.domain.bom.runtime.view;

import com.qpp.job.domain.JobType;
import com.qpp.mongo.domain.LongMongoDomain;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date 2023/9/20
 */
@Getter
@Setter
@Document(collection = "jobtypepagecontentviewmapconfigs")
public class JobTypePageContentViewMapConfig extends LongMongoDomain {

    private JobType jobType;

    private String pageContentViewName;
}
