package com.qpp.cgp.controller.order.bulk;

import com.qpp.cgp.domain.dto.order.bulk.BulkOrderCheckoutItem;
import com.qpp.cgp.domain.dto.order.bulk.BulkOrderCheckoutResult;
import com.qpp.cgp.domain.dto.order.bulk.BulkOrderCheckoutShippingItem;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/19 18:22
 * @Version 1.0
 */
@RunWith(MockitoJUnitRunner.class)
public class BulkOrderControllerTest {

    @InjectMocks
    private BulkOrderController bulkOrderController;

    @Test
    public void testSortItems(){
        List<BulkOrderCheckoutItem> items = new ArrayList<>();
        BulkOrderCheckoutItem bulkOrderCheckoutItem1 = new BulkOrderCheckoutItem();
        bulkOrderCheckoutItem1.setId("a");
        bulkOrderCheckoutItem1.setQty(10);
        BulkOrderCheckoutItem bulkOrderCheckoutItem2 = new BulkOrderCheckoutItem();
        bulkOrderCheckoutItem2.setId("b");
        BulkOrderCheckoutItem bulkOrderCheckoutItem3 = new BulkOrderCheckoutItem();
        bulkOrderCheckoutItem3.setId("c");
        items.add(bulkOrderCheckoutItem1);
        items.add(bulkOrderCheckoutItem2);
        items.add(bulkOrderCheckoutItem3);

        ArrayList<BulkOrderCheckoutShippingItem> shippings = new ArrayList<>();
        BulkOrderCheckoutShippingItem bulkOrderCheckoutShippingItem1 = new BulkOrderCheckoutShippingItem();
        BulkOrderCheckoutShippingItem bulkOrderCheckoutShippingItem2 = new BulkOrderCheckoutShippingItem();
        bulkOrderCheckoutShippingItem1.setPreBulkOrderItemIds(Arrays.asList("c","b"));
        bulkOrderCheckoutShippingItem2.setPreBulkOrderItemIds(Arrays.asList("a"));
        shippings.add(bulkOrderCheckoutShippingItem1);
        shippings.add(bulkOrderCheckoutShippingItem2);

        BulkOrderCheckoutResult bulkOrderCheckoutResult = new BulkOrderCheckoutResult();
        bulkOrderCheckoutResult.setItems(items);
        bulkOrderCheckoutResult.setShippings(shippings);
        ReflectionTestUtils.invokeMethod(bulkOrderController,"sortItems",bulkOrderCheckoutResult);
        Assert.assertEquals("c",bulkOrderCheckoutResult.getItems().get(0).getId());
        Assert.assertEquals("b",bulkOrderCheckoutResult.getItems().get(1).getId());
        Assert.assertEquals("a",bulkOrderCheckoutResult.getItems().get(2).getId());
    }
}
