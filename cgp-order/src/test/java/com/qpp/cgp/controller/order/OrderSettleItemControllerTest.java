//package com.qpp.cgp.controller.order;
//
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//import org.springframework.test.web.servlet.MockMvc;
//
//
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
//import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
//
///**
// * <AUTHOR>
// * @date 2018/2/28
// */
//@RunWith(SpringRunner.class)
//@SpringBootTest
//@AutoConfigureMockMvc(addFilters = false)
//
//public class OrderSettleItemControllerTest {
//
//    @Autowired
//    private MockMvc mock;
//
//    @Test
//    public void findAll() throws Exception {
//        // language=JSON
//        String json = "[\n" +
//                "  {\"name\":\"hasProducer\",\"value\":true,\"type\":\"number\"}\n" +
//                "]";
//        mock.perform(get("/api/orderSettleItems")
//                .param("page", "1")
//                .param("limit", "20")
//                .param("filter", json)
//        ).andDo(print());
//    }
//}