package com.qpp.cgp.core.moudle.shipping.express;

import com.qpp.cgp.CgpOrderTestApplication;
import com.qpp.cgp.core.module.shipping.express.Express;
import com.qpp.cgp.domain.common.module.ShippingModuleConfig;
import com.qpp.cgp.domain.order.Order;
import com.qpp.cgp.domain.order.ShippingQuote;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR> <PERSON>
 * @Date 2021/2/22 18:07
 * @Description
 * @Version 1.0
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = CgpOrderTestApplication.class)
public class ExpressGetShippingQuoteTests {

    @Test
    public void testByNullOrderOrNullShippingModuleConfigOrNullShippingInfo(){

        Express express = new Express();

        try {
            ShippingQuote shippingQuote = express.getShippingQuote(null, null, null);
        } catch (Exception e) {
            Assert.assertEquals("Parameter：order can not be null!", e.getMessage());
        }

        try {
            ShippingQuote shippingQuote = express.getShippingQuote(new Order(), null, null);
        } catch (Exception e) {
            Assert.assertEquals("Parameter：config can not be null!", e.getMessage());
        }

        try {
            ShippingQuote shippingQuote = express.getShippingQuote(new Order(), new ShippingModuleConfig(), null);
        } catch (Exception e) {
            Assert.assertEquals("Parameter：info can not be null!", e.getMessage());
        }
    }

}
