package com.qpp.cgp.core.moudle.shipping.standard;

import com.qpp.cgp.CgpOrderTestApplication;
import com.qpp.cgp.core.module.shipping.standard.Standard;
import com.qpp.cgp.domain.common.module.ShippingModuleConfig;
import com.qpp.cgp.domain.order.Order;
import com.qpp.cgp.domain.order.ShippingQuote;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR> <PERSON>
 * @Date 2021/2/22 17:23
 * @Description
 * @Version 1.0
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = CgpOrderTestApplication.class)
public class StandardGetShippingQuoteTests {

   @Test
   public void testByNullOrderOrNullShippingModuleConfigOrNullShippingInfo(){

       Standard standard = new Standard();

       try {
           ShippingQuote shippingQuote = standard.getShippingQuote(null, null, null);
       } catch (Exception e) {
           Assert.assertEquals("Parameter：order can not be null!", e.getMessage());
       }

       try {
           ShippingQuote shippingQuote = standard.getShippingQuote(new Order(), null, null);
       } catch (Exception e) {
           Assert.assertEquals("Parameter：config can not be null!", e.getMessage());
       }

       try {
           ShippingQuote shippingQuote = standard.getShippingQuote(new Order(), new ShippingModuleConfig(), null);
       } catch (Exception e) {
           Assert.assertEquals("Parameter：info can not be null!", e.getMessage());
       }
   }

}
