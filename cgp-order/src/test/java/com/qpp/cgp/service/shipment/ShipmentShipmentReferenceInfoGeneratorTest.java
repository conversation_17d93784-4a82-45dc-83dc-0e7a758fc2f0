package com.qpp.cgp.service.shipment;

import com.qpp.cgp.domain.bom.runtime.ProductInstance;
import com.qpp.cgp.domain.customs.CustomsCategory;
import com.qpp.cgp.domain.customs.CustomsOrderLineItemInfo;
import com.qpp.cgp.domain.customs.OrderCustomsInfo;
import com.qpp.cgp.domain.dto.order.OrderLineItemDTO;
import com.qpp.cgp.domain.dto.order.ProductOrderDTO;
import com.qpp.cgp.domain.dto.order.base.OrderBaseDTO;
import com.qpp.cgp.domain.dto.orderitem.OrderItemStatusHistoryDTO;
import com.qpp.cgp.domain.dto.product.ProductDTO;
import com.qpp.cgp.domain.dto.shipment.ShipmentReferenceInfo;
import com.qpp.cgp.domain.dto.shipment.ShipmentRequestDTO;
import com.qpp.cgp.domain.order.Order;
import com.qpp.cgp.domain.order.OrderLineItem;
import com.qpp.cgp.domain.orderitem.OrderItemStatus;
import com.qpp.cgp.domain.shipment.ShipmentOrder;
import com.qpp.cgp.domain.shipment.ShipmentOrderItem;
import com.qpp.cgp.domain.shipment.ShipmentRequirement;
import com.qpp.cgp.domain.user.AddressBook;
import com.qpp.cgp.manager.CustomsCategoryManager;
import com.qpp.cgp.manager.bom.ProductInstanceManager;
import com.qpp.cgp.manager.customs.CustomsElementManager;
import com.qpp.cgp.manager.customs.OrderCustomsInfoManager;
import com.qpp.cgp.manager.order.OrderManager;
import com.qpp.cgp.manager.order.shipping.ShipmentOrderTypeConfigManager;
import com.qpp.cgp.manager.product.ProductManager;
import org.assertj.core.api.Assertions;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;

@ExtendWith(MockitoExtension.class)
class ShipmentShipmentReferenceInfoGeneratorTest {
    @InjectMocks
    @Spy
    private ShipmentReferenceInfoGenerator shipmentReferenceInfoGenerator;

    @Mock
    private OrderManager orderManager;

    @Mock
    private ProductInstanceManager productInstanceManager;

    @Mock
    private ProductManager productManager;

    @Mock
    private CustomsCategoryManager customsCategoryManager;

    @Mock
    private OrderCustomsInfoManager orderCustomsInfoManager;

    @Mock
    private CustomsElementManager customsElementManager;

    @Mock
    private ShipmentOrderTypeConfigManager shipmentOrderTypeConfigManager;

    @Test
    public void testGenerator() {
        ShipmentRequirement shipmentRequirement = new ShipmentRequirement();
        ShipmentOrder shipmentOrder = new ShipmentOrder();
        AddressBook address = new AddressBook();
        address.setFirstName("testFirstName");
        address.setCountryCode2("213");
        shipmentRequirement.setAddress(address);
        shipmentOrder.setId(33L);

        ProductOrderDTO productOrderDTO1 = new ProductOrderDTO();
        ProductOrderDTO productOrderDTO2 = new ProductOrderDTO();
        productOrderDTO1.setId(5L);
        productOrderDTO2.setId(6L);

        OrderLineItem orderLineItem1 = new OrderLineItem();
        OrderLineItem orderLineItem2 = new OrderLineItem();
        ShipmentOrderItem shipmentOrderItem1 = new ShipmentOrderItem();
        ShipmentOrderItem shipmentOrderItem2 = new ShipmentOrderItem();
        OrderLineItemDTO orderLineItemDTO1 = new OrderLineItemDTO();
        OrderLineItemDTO orderLineItemDTO2 = new OrderLineItemDTO();
        shipmentOrder.setItems(Lists.newArrayList(shipmentOrderItem1, shipmentOrderItem2));
        orderLineItemDTO1.setId(1L);
        orderLineItemDTO2.setId(2L);
        orderLineItemDTO1.setQty(3);
        orderLineItemDTO2.setQty(4);
        orderLineItemDTO1.setProduct(productOrderDTO1);
        orderLineItemDTO2.setProduct(productOrderDTO2);

        shipmentOrderItem1.setOrderItem(orderLineItem1);
        shipmentOrderItem2.setOrderItem(orderLineItem2);
        orderLineItem1.setId("1");
        orderLineItem2.setId("2");

        Date deliveredDate = new Date(2022, 1, 1, 1, 1, 1);
        Date createDate = new Date(2022, 2, 1, 1, 1, 1);
        shipmentOrder.setDeliveredDate(deliveredDate);

        Order order = new Order();
        OrderBaseDTO orderBaseDTO = new OrderBaseDTO();
        orderBaseDTO.setId(7L);
        orderLineItemDTO1.setOrder(orderBaseDTO);
        orderLineItemDTO2.setOrder(orderBaseDTO);
        order.setCustomerId(123L);
        order.setCreatedDate(createDate);

        OrderCustomsInfo orderCustomsInfo = new OrderCustomsInfo();
        CustomsOrderLineItemInfo customsOrderLineItemInfo1 = new CustomsOrderLineItemInfo();
        CustomsOrderLineItemInfo customsOrderLineItemInfo2 = new CustomsOrderLineItemInfo();
        customsOrderLineItemInfo1.setOrderLineItemId(1L);
        customsOrderLineItemInfo2.setOrderLineItemId(2L);
        customsOrderLineItemInfo1.setCustomsUnitPrice(new BigDecimal("2.3"));
        customsOrderLineItemInfo2.setCustomsUnitPrice(new BigDecimal("3.4"));
        orderCustomsInfo.setCustomsOrderLineItemInfos(Lists.newArrayList(customsOrderLineItemInfo1, customsOrderLineItemInfo2));

        orderLineItemDTO1.setProductInstanceId("12");
        orderLineItemDTO2.setProductInstanceId("34");
        ProductInstance productInstance1 = new ProductInstance();
        ProductInstance productInstance2 = new ProductInstance();

        productInstance1.setProductId(123123L);
        productInstance2.setProductId(345456L);

        ProductDTO productDTO1 = new ProductDTO();
        ProductDTO productDTO2 = new ProductDTO();
        productDTO1.setName("name1");
        productDTO2.setName("name2");
        productDTO1.setShortDescription("short1");
        productDTO2.setShortDescription("short2");

        OrderItemStatusHistoryDTO orderItemStatusHistoryDTO1 = new OrderItemStatusHistoryDTO();
        OrderItemStatusHistoryDTO orderItemStatusHistoryDTO2 = new OrderItemStatusHistoryDTO();
        OrderItemStatus orderItemStatus = new OrderItemStatus();
        orderItemStatus.setOrderStatusId(OrderItemStatus.PRODUCED_STATUS);
        orderItemStatusHistoryDTO1.setStatus(orderItemStatus);
        orderItemStatusHistoryDTO2.setStatus(orderItemStatus);
        orderItemStatusHistoryDTO1.setCreatedDate(createDate);
        orderItemStatusHistoryDTO2.setCreatedDate(createDate);
        orderItemStatus.setCreatedDate(createDate);

        orderLineItemDTO1.setStatusHistories(Lists.newArrayList(orderItemStatusHistoryDTO1));
        orderLineItemDTO2.setStatusHistories(Lists.newArrayList(orderItemStatusHistoryDTO2));

        orderLineItemDTO1.setCustomsCategoryId("11");
        orderLineItemDTO2.setCustomsCategoryId("22");

        CustomsCategory customsCategory1 = new CustomsCategory();
        CustomsCategory customsCategory2 = new CustomsCategory();
        customsCategory1.setTagKeyCode("tagkeyCode1");
        customsCategory2.setTagKeyCode("tagkeyCode2");

        orderLineItemDTO1.setAlonePacking(false);
        orderLineItemDTO2.setAlonePacking(true);

        Mockito.when(orderManager.findOrderById(any()))
                .thenReturn(Optional.of(order));
        Mockito.when(productInstanceManager.findById(any()))
                .thenReturn(productInstance1)
                .thenReturn(productInstance2);
        Mockito.when(productManager.findById(any()))
                .thenReturn(productDTO1)
                .thenReturn(productDTO2);
        Mockito.when(customsCategoryManager.findById(any()))
                .thenReturn(customsCategory1)
                .thenReturn(customsCategory2);
        Mockito.when(orderCustomsInfoManager.findByOrderId(any()))
                .thenReturn(Optional.of(orderCustomsInfo));
        Mockito.when(shipmentOrderTypeConfigManager.getByOrderType(any()))
                .thenReturn("A");

        ArgumentCaptor<ShipmentRequestDTO> captor = ArgumentCaptor.forClass(ShipmentRequestDTO.class);

        List<ShipmentReferenceInfo> shipmentReferenceInfos = shipmentReferenceInfoGenerator.generate(Lists.newArrayList(orderLineItemDTO1, orderLineItemDTO2), null, null);

        Assertions.assertThat(shipmentReferenceInfos.size()).isEqualTo(1);
        Assertions.assertThat(shipmentReferenceInfos.get(0).getItems().get(0).getProductName()).isEqualTo("name1");
        Assertions.assertThat(shipmentReferenceInfos.get(0).getItems().get(0).getProductDesc()).isEqualTo("short1");
        Assertions.assertThat(shipmentReferenceInfos.get(0).getItems().get(0).getProductCustomsInfo().getTagKeyCode()).isEqualTo("tagkeyCode1");
        Assertions.assertThat(shipmentReferenceInfos.get(0).getItems().get(0).getIsAlonePacking()).isEqualTo(0);
        Assertions.assertThat(shipmentReferenceInfos.get(0).getItems().get(0).getProductItems().size()).isEqualTo(1);
    }
}