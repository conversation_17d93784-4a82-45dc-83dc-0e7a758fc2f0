package com.qpp.cgp.service.order.address;

import com.qpp.cgp.domain.common.Zone;
import com.qpp.cgp.domain.user.AddressBook;
import com.qpp.cgp.repository.common.ZoneRepository;
import com.qpp.cgp.service.order.address.AddressResolveService;
import org.assertj.core.api.Assertions;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Optional;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = AddressResolveService.class)
public class AddressResolveServiceTests {

    @Autowired
    private AddressResolveService addressResolveService;

    @MockBean
    private ZoneRepository zoneRepository;

    @Before
    public void mockZoneRepository() {
        Mockito.doAnswer(invocation -> {
            String code = invocation.getArgument(0);
            if ("GD".equalsIgnoreCase(code)) {
                Zone zone = new Zone();
                zone.setCode("GD");
                zone.setName("GuangDong");
                return zone;
            } else {
                return null;
            }
        }).when(zoneRepository).findByCode(Mockito.any());

        Mockito.doAnswer(invocation -> {
            String code = invocation.getArgument(0);
            if ("GuangDong".equalsIgnoreCase(code)) {
                Zone zone = new Zone();
                zone.setCode("GD");
                zone.setName("GuangDong");
                return zone;
            } else {
                return null;
            }
        }).when(zoneRepository).findByName(Mockito.any());
    }

    @Test
    public void testResolveZoneCodeByNullAddress() {
        AddressBook address = null;

        Optional<String> result = addressResolveService.resolveZoneCode(address);

        Assertions.assertThat(result).isNotPresent();
    }

    @Test
    public void testResolveZoneCodeByNullState() {
        AddressBook address = new AddressBook();
        address.setState(null);

        Optional<String> result = addressResolveService.resolveZoneCode(address);

        Assertions.assertThat(result).isNotPresent();
    }

    @Test
    public void testResolveZoneCodeByExistsZoneOfStateCode() {
        AddressBook address = new AddressBook();
        address.setState("GD");

        Optional<String> result = addressResolveService.resolveZoneCode(address);

        Assertions.assertThat(result).isPresent().hasValue("GD");
    }

    @Test
    public void testResolveZoneCodeByExistsZoneOfStateName() {
        AddressBook address = new AddressBook();
        address.setState("GuangDong");

        Optional<String> result = addressResolveService.resolveZoneCode(address);

        Assertions.assertThat(result).isPresent().hasValue("GD");
    }


}
