package com.qpp.cgp.service.order.salesreturn;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qpp.cgp.advice.CheckedBusinessException;
import com.qpp.cgp.config.mongo.MongoTemplateFactory;
import com.qpp.cgp.domain.stateflow.config.StateNode;
import com.qpp.cgp.domain.stateflow.runtime.FlowInstance;
import com.qpp.cgp.domain.stateflow.runtime.StateInstance;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class RROStatusValidatorTest {

    @Spy
    @InjectMocks
    private RROStatusValidator rroStatusValidator;

    @Mock
    private MongoTemplateFactory mongoTemplateFactory;

    private void mockBehavior(String stateInstanceListStr) throws JsonProcessingException {
        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
        Mockito.when(mongoTemplateFactory.getMongoTemplate(StateInstance.class)).thenReturn(mongoTemplate);
        Mockito.when(mongoTemplateFactory.getMongoTemplate(StateNode.class)).thenReturn(mongoTemplate);
        ObjectMapper objectMapper = new ObjectMapper();
        JavaType javaType = objectMapper.getTypeFactory().constructParametricType(List.class, StateInstance.class);
        List<StateInstance> stateInstanceList = objectMapper.readValue(stateInstanceListStr, javaType);
        StateNode stateNode = stateInstanceList.get(0).getState();
        Mockito.when(mongoTemplate.find(Mockito.any(), Mockito.eq(StateInstance.class))).thenReturn(stateInstanceList);
        Mockito.when(mongoTemplate.findById(Mockito.any(), Mockito.eq(StateNode.class))).thenReturn(stateNode);
    }

    @Test
    void testCanApplyGivenRequestEndStatusThenSuccess() throws JsonProcessingException {
        // input
        FlowInstance flowInstance = new FlowInstance();
        // mock
        mockBehavior("[\n" +
                "  {\n" +
                "    \"state\" : {\n" +
                "      \"_id\" : \"1\",\n" +
                "      \"key\" : \"CANCEL\"\n" +
                "    }\n" +
                "  }\n" +
                "]");
        // invoke
        assertDoesNotThrow(() -> rroStatusValidator.canApply(flowInstance));
        // verify
    }

    @Test
    void testCanApplyGivenOrderStatusThenThrow() throws JsonProcessingException {
        // input
        FlowInstance flowInstance = new FlowInstance();
        // mock
        mockBehavior("[\n" +
                "  {\n" +
                "    \"state\" : {\n" +
                "      \"_id\" : \"1\",\n" +
                "      \"key\" : \"WAIT_FOR_DELIVERY\"\n" +
                "    }\n" +
                "  }\n" +
                "]");
        // invoke
        assertThrows(CheckedBusinessException.class, () -> rroStatusValidator.canApply(flowInstance));
        // verify
    }

    @Test
    void testCanApplyGivenRequestStatusThenThrow() throws JsonProcessingException {
        // input
        FlowInstance flowInstance = new FlowInstance();
        // mock
        mockBehavior("[\n" +
                "  {\n" +
                "    \"state\" : {\n" +
                "      \"_id\" : \"1\",\n" +
                "      \"key\" : \"WAIT_FOR_REVIEW\"\n" +
                "    }\n" +
                "  }\n" +
                "]");
        // invoke
        assertThrows(CheckedBusinessException.class, () -> rroStatusValidator.canApply(flowInstance));
        // verify
    }

    @Test
    void testCanApplyGivenCloseStatusThenSuccess() throws JsonProcessingException {
        // input
        FlowInstance flowInstance = new FlowInstance();
        // mock
        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
        Mockito.when(mongoTemplateFactory.getMongoTemplate(StateInstance.class)).thenReturn(mongoTemplate);
        Mockito.when(mongoTemplateFactory.getMongoTemplate(StateNode.class)).thenReturn(mongoTemplate);
        ObjectMapper objectMapper = new ObjectMapper();
        JavaType javaType = objectMapper.getTypeFactory().constructParametricType(List.class, StateInstance.class);
        List<StateInstance> stateInstanceList = objectMapper.readValue("[\n" +
                "  {\n" +
                "    \"state\": {\n" +
                "      \"_id\": \"0\",\n" +
                "      \"key\": \"CLOSED\"\n" +
                "    }\n" +
                "  }" +
                ",\n" +
                "  {\n" +
                "    \"state\": {\n" +
                "      \"_id\": \"1\",\n" +
                "      \"key\": \"WAIT_FOR_REVIEW\"\n" +
                "    }\n" +
                "  " +
                "}\n" +
                "]", javaType);
        StateNode stateNode = stateInstanceList.get(0).getState();
        StateNode stateNode1 = stateInstanceList.get(1).getState();
        Mockito.when(mongoTemplate.find(Mockito.any(), Mockito.eq(StateInstance.class))).thenReturn(stateInstanceList);
        Mockito.when(mongoTemplate.findById(Mockito.any(), Mockito.eq(StateNode.class))).thenReturn(stateNode, stateNode1);
        // invoke
        assertDoesNotThrow(() -> rroStatusValidator.canApply(flowInstance));
        // verify
    }

    @Test
    void testCanApplyGivenCloseStatusThenThrow() throws JsonProcessingException {
        // input
        FlowInstance flowInstance = new FlowInstance();
        // mock
        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
        Mockito.when(mongoTemplateFactory.getMongoTemplate(StateInstance.class)).thenReturn(mongoTemplate);
        Mockito.when(mongoTemplateFactory.getMongoTemplate(StateNode.class)).thenReturn(mongoTemplate);
        ObjectMapper objectMapper = new ObjectMapper();
        JavaType javaType = objectMapper.getTypeFactory().constructParametricType(List.class, StateInstance.class);
        List<StateInstance> stateInstanceList = objectMapper.readValue("[\n" +
                "  {\n" +
                "    \"state\": {\n" +
                "      \"_id\": \"0\",\n" +
                "      \"key\": \"CLOSED\"\n" +
                "    }\n" +
                "  }" +
                ",\n" +
                "  {\n" +
                "    \"state\": {\n" +
                "      \"_id\": \"1\",\n" +
                "      \"key\": \"WAIT_FOR_DELIVERY\"\n" +
                "    }\n" +
                "  " +
                "}\n" +
                "]", javaType);
        StateNode stateNode = stateInstanceList.get(0).getState();
        StateNode stateNode1 = stateInstanceList.get(1).getState();
        Mockito.when(mongoTemplate.find(Mockito.any(), Mockito.eq(StateInstance.class))).thenReturn(stateInstanceList);
        Mockito.when(mongoTemplate.findById(Mockito.any(), Mockito.eq(StateNode.class))).thenReturn(stateNode, stateNode1);
        // invoke
        assertThrows(CheckedBusinessException.class, () -> rroStatusValidator.canApply(flowInstance));
        // verify
    }
}