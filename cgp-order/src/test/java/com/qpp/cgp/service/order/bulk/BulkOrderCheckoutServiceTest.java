package com.qpp.cgp.service.order.bulk;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.CollectionLikeType;
import com.qpp.cgp.domain.dto.order.bulk.BulkOrderCheckoutItem;
import com.qpp.cgp.domain.dto.order.bulk.BulkOrderCheckoutResult;
import com.qpp.cgp.domain.dto.order.bulk.BulkOrderCheckoutShippingItem;
import com.qpp.cgp.domain.order.bulk.PreBulkOrderItem;
import com.qpp.cgp.domain.product.shipping.ProductShipping;
import com.qpp.cgp.domain.user.AddressBook;
import com.qpp.cgp.dto.pricing.TaxCalculateDTO;
import com.qpp.cgp.dto.pricing.TaxCalculateResult;
import com.qpp.cgp.manager.pricing.ProductPricingManager;
import com.qpp.cgp.service.order.bulk.BulkOrderCheckoutService;
import com.qpp.cgp.service.order.bulk.PreBulkOrderItemService;
import com.qpp.cgp.service.order.address.AddressResolveService;
import com.qpp.cgp.service.product.shipping.ProductAreaShippingCalculateService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;

/**
 * <AUTHOR>
 * @Date 2021/10/15 11:17
 * @Version 1.0
 */

@RunWith(PowerMockRunner.class)
@PrepareForTest(BulkOrderCheckoutService.class)
@PowerMockIgnore({"javax.management.*", "org.w3c.dom.*", "org.apache.log4j.*", "org.xml.sax.*", "javax.xml.*", "javax.script.*"})
public class BulkOrderCheckoutServiceTest {

    @InjectMocks
    private BulkOrderCheckoutService bulkOrderCheckoutService;

    @Mock
    private ProductPricingManager productPricingManager;

    @Mock
    private ProductAreaShippingCalculateService productAreaShippingCalculateService;

    @Mock
    private PreBulkOrderItemService preBulkOrderItemService;

    @Mock
    private AddressResolveService addressResolveService;

    private ObjectMapper objectMapper = new ObjectMapper();

    private List<PreBulkOrderItem> preBulkOrderItems;

    @Before
    public void before() throws IOException {
        bulkOrderCheckoutService = PowerMockito.spy(bulkOrderCheckoutService);

        InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("json/preBulkOrderItems.json");
        CollectionLikeType likeType = objectMapper.getTypeFactory().constructCollectionLikeType(List.class, PreBulkOrderItem.class);
        preBulkOrderItems = objectMapper.readValue(inputStream, likeType);

    }

    /* method: checkout(List<PreBulkOrderItem> preBulkOrderItems)  */
    @Test
    public void testCheckoutCorrect() throws Exception {
        BulkOrderCheckoutItem bulkOrderCheckoutItem = new BulkOrderCheckoutItem();
        bulkOrderCheckoutItem.setAmount(new BigDecimal(10));

        BulkOrderCheckoutShippingItem bulkOrderCheckoutShippingItem = new BulkOrderCheckoutShippingItem();
        bulkOrderCheckoutShippingItem.setShipping(new BigDecimal(12));
        List<BulkOrderCheckoutShippingItem> shippingItems = new ArrayList<>();
        shippingItems.add(bulkOrderCheckoutShippingItem);

        List<TaxCalculateResult> taxCalculateResults = new ArrayList<>();
        TaxCalculateResult taxCalculateResult = new TaxCalculateResult();
        BigDecimal tax = new BigDecimal(11);
        taxCalculateResult.setValue(new BigDecimal(11));
        taxCalculateResults.add(taxCalculateResult);

        PowerMockito.doReturn(bulkOrderCheckoutItem).when(bulkOrderCheckoutService,"createBulkOrderCheckoutItem",any(),any());
        PowerMockito.doReturn(new TaxCalculateDTO()).when(bulkOrderCheckoutService,"createBulkOrderCheckoutTaxItem",any());
        Mockito.when(productPricingManager.productTaxCalculate(any())).thenReturn(taxCalculateResults);
        PowerMockito.doReturn(shippingItems).when(bulkOrderCheckoutService,"createBulkOrderCheckoutShippingItems",any());


        BulkOrderCheckoutResult result = bulkOrderCheckoutService.checkout(preBulkOrderItems);

        Assert.assertEquals(tax,result.getTotalTax());
        Assert.assertEquals(new BigDecimal(12),result.getTotalShipping());
        Assert.assertEquals(new BigDecimal(83),result.getTotal());
    }

    /* method：createBulkOrderCheckoutItem(PreBulkOrderItem preBulkOrderItem, Map<Long, Integer> map)  */
    @Test
    public void testCreateBulkOrderCheckoutItem(){
        PreBulkOrderItem preBulkOrderItem = new PreBulkOrderItem();
        AddressBook addressBook = new AddressBook();
        addressBook.setCity("city");
        addressBook.setCountryCode2("countryCode");
        addressBook.setState("state");
        preBulkOrderItem.setAddress(addressBook);
        preBulkOrderItem.setProductId(111L);
        preBulkOrderItem.setQty(10);
        preBulkOrderItem.setBindOrderId("bindOrderId");

        BigDecimal price = new BigDecimal("101.01");
        BigDecimal bulkPrice = new BigDecimal("11.01");


        Mockito.when(preBulkOrderItemService.calculatePrice(111L,10,addressBook, Optional.ofNullable(0l))).thenReturn(price);
        Mockito.when(preBulkOrderItemService.calculatePrice(111L,30,addressBook, Optional.ofNullable(0l))).thenReturn(bulkPrice);

        HashMap<Long, Integer> map = new HashMap<>();
        map.put(111L,30);

        BulkOrderCheckoutItem result = ReflectionTestUtils.invokeMethod(bulkOrderCheckoutService, "createBulkOrderCheckoutItem", preBulkOrderItem, map);

        assert result != null;
        Assert.assertEquals(preBulkOrderItem.getQty(),result.getQty());
        Assert.assertEquals(preBulkOrderItem.getBindOrderId(),result.getBindOrderId());
        Assert.assertEquals(price,result.getPrice());
        Assert.assertEquals(bulkPrice,result.getBulkPrice());
    }
    /* method: createBulkOrderCheckoutTaxItem(PreBulkOrderItem preBulkOrderItem) 赋值操作 */

    /* method: createBulkOrderCheckoutShippingItems(Map<String, List<PreBulkOrderItem>> map) */
    @Test
    public void testCreateBulkOrderCheckoutShippingItemsCorrect(){
        // 参数准备构建
        ArrayList<PreBulkOrderItem> valueOne = new ArrayList<>();
        ArrayList<PreBulkOrderItem> valueTwo = new ArrayList<>();

        testData(valueOne, valueTwo);

        Map<String, List<PreBulkOrderItem>> map = new HashMap<>();
        map.put("A",valueOne);
        map.put("B",valueTwo);

        ProductShipping productShippingOne = new ProductShipping();
        BigDecimal priceOne = new BigDecimal("101");
        productShippingOne.setValue(priceOne);

        ProductShipping productShippingTwo = new ProductShipping();
        BigDecimal priceTwo = new BigDecimal("202");
        productShippingTwo.setValue(priceTwo);

        Mockito.when(productAreaShippingCalculateService.calculate(anyLong(),eq(60),anyString(),anyString(), Optional.empty())).thenReturn(productShippingOne);
        Mockito.when(productAreaShippingCalculateService.calculate(anyLong(),eq(600),anyString(),anyString(), Optional.empty())).thenReturn(productShippingTwo);

        List<BulkOrderCheckoutShippingItem> result = ReflectionTestUtils.invokeMethod(bulkOrderCheckoutService, "createBulkOrderCheckoutShippingItems", map);

        assert result != null;
        Assert.assertEquals(2,result.size());
        Assert.assertEquals(priceOne,result.get(0).getShipping());
        Assert.assertEquals(priceTwo,result.get(1).getShipping());
    }

    private void testData(ArrayList<PreBulkOrderItem> valueOne, ArrayList<PreBulkOrderItem> valueTwo) {
        AddressBook address = new AddressBook();
        address.setState("state");
        address.setCountryCode2("countryCode");
        PreBulkOrderItem preBulkOrderItemTypeIsOne1 = new PreBulkOrderItem();
        preBulkOrderItemTypeIsOne1.setProductId(123L);
        preBulkOrderItemTypeIsOne1.setId("01");
        preBulkOrderItemTypeIsOne1.setQty(10);
        preBulkOrderItemTypeIsOne1.setAddress(address);
        PreBulkOrderItem preBulkOrderItemTypeIsOne2 = new PreBulkOrderItem();
        preBulkOrderItemTypeIsOne2.setProductId(123L);
        preBulkOrderItemTypeIsOne2.setId("02");
        preBulkOrderItemTypeIsOne2.setQty(20);
        preBulkOrderItemTypeIsOne2.setAddress(address);
        PreBulkOrderItem preBulkOrderItemTypeIsOne3 = new PreBulkOrderItem();
        preBulkOrderItemTypeIsOne3.setProductId(123L);
        preBulkOrderItemTypeIsOne3.setId("03");
        preBulkOrderItemTypeIsOne3.setQty(30);
        preBulkOrderItemTypeIsOne3.setAddress(address);

        valueOne.add(preBulkOrderItemTypeIsOne1);
        valueOne.add(preBulkOrderItemTypeIsOne2);
        valueOne.add(preBulkOrderItemTypeIsOne3);

        PreBulkOrderItem preBulkOrderItemTypeIsTwo1 = new PreBulkOrderItem();
        preBulkOrderItemTypeIsTwo1.setProductId(123L);
        preBulkOrderItemTypeIsTwo1.setId("001");
        preBulkOrderItemTypeIsTwo1.setQty(100);
        preBulkOrderItemTypeIsTwo1.setAddress(address);
        PreBulkOrderItem preBulkOrderItemTypeIsTwo2 = new PreBulkOrderItem();
        preBulkOrderItemTypeIsTwo2.setProductId(123L);
        preBulkOrderItemTypeIsTwo2.setId("002");
        preBulkOrderItemTypeIsTwo2.setQty(200);
        preBulkOrderItemTypeIsTwo2.setAddress(address);
        PreBulkOrderItem preBulkOrderItemTypeIsTwo3 = new PreBulkOrderItem();
        preBulkOrderItemTypeIsTwo3.setProductId(123L);
        preBulkOrderItemTypeIsTwo3.setId("03");
        preBulkOrderItemTypeIsTwo3.setQty(300);
        preBulkOrderItemTypeIsTwo3.setAddress(address);

        valueTwo.add(preBulkOrderItemTypeIsTwo1);
        valueTwo.add(preBulkOrderItemTypeIsTwo2);
        valueTwo.add(preBulkOrderItemTypeIsTwo3);
    }

    /* method: getMapByProductIdAndAddressAll(List<PreBulkOrderItem> preBulkOrderItems) */
    @Test
    public void testMapByProductIdAndAddressAllCorrect(){

        Map<String, List<PreBulkOrderItem>> result = ReflectionTestUtils.invokeMethod(bulkOrderCheckoutService, "getMapByProductIdAndAddressAll", preBulkOrderItems);

        assert result != null;
        Assert.assertEquals(4, result.size());
    }

    /* method: getMapByProductId(List<PreBulkOrderItem> preBulkOrderItems) */
    @Test
    public void testGetMapByProductIdCorrect(){
        Map<Long, Integer> result = ReflectionTestUtils.invokeMethod(bulkOrderCheckoutService, "getMapByProductId", preBulkOrderItems);

        assert result != null;
        Assert.assertEquals(2, result.size());
        Assert.assertEquals(Integer.valueOf(30), result.get(18167042L));
        Assert.assertEquals(Integer.valueOf(250), result.get(20151862L));
    }

}
