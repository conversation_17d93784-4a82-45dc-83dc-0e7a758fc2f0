package com.qpp.cgp.service.shipment;

import com.qpp.cgp.domain.dto.order.OrderLineItemDTO;
import com.qpp.cgp.domain.dto.order.base.OrderBaseDTO;
import com.qpp.cgp.domain.dto.shipment.requirement.ShipmentRequirementCheckResult;
import com.qpp.cgp.domain.order.Order;
import com.qpp.cgp.domain.order.OrderLineItem;
import com.qpp.cgp.domain.shipment.ShipmentOrder;
import com.qpp.cgp.domain.shipment.ShipmentRequirement;
import com.qpp.cgp.manager.order.OrderManager;
import com.qpp.cgp.manager.orderitem.OrderLineItemManager;
import com.qpp.cgp.manager.shipment.ShipmentOrderManager;
import com.qpp.cgp.repository.order.OrderLineItemRepository;
import com.qpp.cgp.service.order.mccs.builder.SaleOrderBuilder;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.commons.util.ReflectionUtils;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ShipmentOrderListenerTest {
    @Mock
    private ShipmentRequirementService shipmentRequirementService;

    @Mock
    private ShipmentOrderService shipmentOrderService;

    @Mock
    private ShipmentOrderManager shipmentOrderManager;

    @Mock
    private OrderLineItemRepository orderLineItemRepository;

    @Mock
    private OrderLineItemManager orderLineItemManager;

    @Mock
    private OrderManager orderManager;

    @Mock
    private SaleOrderBuilder saleOrderBuilder;

    @InjectMocks
    private ShipmentOrderListener shipmentOrderListener;


    @Test
    public void testGenerateShipmentOrdersHaveRequestment() {
        ShipmentRequirement shipmentRequirement1 = new ShipmentRequirement();
        ShipmentRequirement shipmentRequirement2 = new ShipmentRequirement();
        OrderLineItem orderLineItem1 = new OrderLineItem();
        OrderLineItem orderLineItem2 = new OrderLineItem();
        ShipmentRequirementCheckResult shipmentRequirementCheckResult = new ShipmentRequirementCheckResult();
        shipmentRequirementCheckResult.setSuccess(true);

        when(shipmentRequirementService.getAssociatedShipmentRequirementsByOrderItemIds(Mockito.any()))
                .thenReturn(Lists.newArrayList(shipmentRequirement1, shipmentRequirement2));
        when(shipmentRequirementService.getAssociatedOrderItemIdsByShipmentRequirements(Mockito.any()))
                .thenReturn(Lists.newArrayList("12", "213"));
        when(shipmentOrderManager.findByOrderItemIds(any())).thenReturn(null);
        when(orderLineItemRepository.predicateStatus(any(), anyList())).thenReturn(true);
        when(shipmentRequirementService.getAssociatedOrderItemIdsByShipmentRequirements(any()))
                .thenReturn(Lists.newArrayList("12", "213"));
        when(orderLineItemRepository.findRawByIds(any())).thenReturn(Lists.newArrayList(orderLineItem1, orderLineItem2));
        when(shipmentRequirementService.check(anyList(), any())).thenReturn(shipmentRequirementCheckResult);

        Class<ShipmentOrderListener> shipmentOrderListenerClass = ShipmentOrderListener.class;
        Method method = ReflectionUtils.findMethod(shipmentOrderListenerClass, "generateShipmentOrders", List.class, Boolean.class).get();
        ReflectionUtils.invokeMethod(method, shipmentOrderListener, Lists.newArrayList("12", "213"), true);
    }

    @Test
    public void testGenerateShipmentOrdersNoRequirement() {
        ShipmentOrder shipmentOrder = new ShipmentOrder();
        OrderLineItemDTO orderLineItemDTO = new OrderLineItemDTO();
        OrderBaseDTO orderBaseDTO = new OrderBaseDTO();
        orderBaseDTO.setId(123L);
        orderLineItemDTO.setOrder(orderBaseDTO);
        OrderLineItem orderLineItem = new OrderLineItem();
        ShipmentRequirement shipmentRequirement = new ShipmentRequirement();
        Order order = new Order();
        order.setId("2211");

        when(shipmentRequirementService.getAssociatedShipmentRequirementsByOrderItemIds(any()))
                .thenReturn(Lists.newArrayList());
        when(shipmentRequirementService.getAssociatedOrderItemIdsByShipmentRequirements(any()))
                .thenReturn(Lists.newArrayList());
        when(shipmentOrderManager.findByOrderItemIds(any()))
                .thenReturn(Lists.newArrayList());
        when(orderLineItemRepository.predicateStatus(any(), anyList()))
                .thenReturn(false);
        when(shipmentOrderManager.findByOrderItemIds(any()))
                .thenReturn(Lists.newArrayList());
        when(orderLineItemManager.findById(anyString()))
                .thenReturn(orderLineItemDTO);
        when(orderManager.findOrderById(any())).thenReturn(Optional.of(order));
        when(orderLineItemManager.findOrderLineItemByOrderId(any()))
                .thenReturn(Lists.newArrayList(orderLineItem));
        when(saleOrderBuilder.getShipmentRequirements(any(), anyList()))
                .thenReturn(Lists.newArrayList(shipmentRequirement));

        Class<ShipmentOrderListener> shipmentOrderListenerClass = ShipmentOrderListener.class;
        Method method = ReflectionUtils.findMethod(shipmentOrderListenerClass, "generateShipmentOrders", List.class, Boolean.class).get();
        ReflectionUtils.invokeMethod(method, shipmentOrderListener, Lists.newArrayList("12", "213"), true);

        verify(shipmentOrderService, times(1)).create(any(ShipmentRequirement.class));
    }
}