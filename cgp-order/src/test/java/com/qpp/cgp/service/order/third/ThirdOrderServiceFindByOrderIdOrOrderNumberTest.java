package com.qpp.cgp.service.order.third;

import com.qpp.cgp.domain.order.ThirdOrder;
import com.qpp.cgp.manager.order.ThirdOrderManager;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.*;

/**
 * <AUTHOR>
 * @date 2022/7/21
 */
@ExtendWith(MockitoExtension.class)
public class ThirdOrderServiceFindByOrderIdOrOrderNumberTest {

    @InjectMocks
    private ThirdOrderService thirdOrderService;

    @Mock
    private ThirdOrderManager thirdOrderManager;

    @Test
    public void testByOrderIdIsExists() {
        String id = "D22207150001";

        ThirdOrder thirdOrder = new ThirdOrder();
        thirdOrder.setId("29491121");
        thirdOrder.setOrderId("D22207150001");
        thirdOrder.setBindOrderId(29491116L);
        thirdOrder.setClazz(ThirdOrder.class.getName());

        Mockito.when(thirdOrderManager.findByOrderIdOrOrderNumber(anyString())).thenReturn(thirdOrder);

        ThirdOrder result = thirdOrderService.findByOrderIdOrOrderNumber(id);

        Assertions.assertThat(result.getId()).isEqualTo("29491121");
        Assertions.assertThat(result.getOrderId()).isEqualTo("D22207150001");
        Assertions.assertThat(result.getBindOrderId()).isEqualTo(String.valueOf(29491116L));
        Assertions.assertThat(result.getClazz()).isEqualTo("com.qpp.cgp.domain.order.ThirdOrder");
    }

    @Test
    public void testByOrderNumberIsExists() {
        String id = "D22207140001";

        ThirdOrder thirdOrder = new ThirdOrder();
        thirdOrder.setId("29491121");
        thirdOrder.setOrderNumber("D22207150001");
        thirdOrder.setBindOrderId(29465074L);
        thirdOrder.setClazz(ThirdOrder.class.getName());

        Mockito.when(thirdOrderManager.findByOrderIdOrOrderNumber(anyString())).thenReturn(thirdOrder);

        ThirdOrder result = thirdOrderService.findByOrderIdOrOrderNumber(id);

        Assertions.assertThat(result.getId()).isEqualTo("29491121");
        Assertions.assertThat(result.getOrderNumber()).isEqualTo("D22207150001");
        Assertions.assertThat(result.getBindOrderId()).isEqualTo(String.valueOf(29465074L));
        Assertions.assertThat(result.getClazz()).isEqualTo("com.qpp.cgp.domain.order.ThirdOrder");
    }

}
