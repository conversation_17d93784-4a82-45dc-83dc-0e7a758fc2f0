package com.qpp.cgp.service.order.qris;

import com.google.common.collect.ImmutableList;
import com.qpp.cgp.domain.order.ProductSetOrderLineItem;
import com.qpp.cgp.domain.order.QrisProductConfig;
import com.qpp.cgp.domain.productssuit.ConfigurableProductSet;
import com.qpp.cgp.domain.productssuit.SkuProductSet;
import com.qpp.cgp.manager.order.QrisProductConfigManager;
import com.qpp.cgp.manager.prdouctsuit.ProductSetManager;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.anyLong;

/**
 * <AUTHOR>
 * @date 2021/11/16
 */
@RunWith(MockitoJUnitRunner.class)
public class QrisProductServiceTestProductSet {

    @Spy
    @InjectMocks
    private QrisProductService qrisProductService;

    @Mock
    private QrisProductConfigManager qrisProductConfigManager;

    @Mock
    private ProductSetManager productSetManager;

    @Test
    public void testAllProductIsQrisProductTrue() {
        QrisProductConfig qrisProductConfig = new QrisProductConfig();
        qrisProductConfig.setSkuProductIds(ImmutableList.of(1L));
        qrisProductConfig.setConfigurableProductIds(ImmutableList.of(3L));

        Mockito.when(qrisProductConfigManager.findAll())
                .thenReturn(ImmutableList.of(qrisProductConfig));

        SkuProductSet product1 = new SkuProductSet();
        product1.setId(1L);
        ProductSetOrderLineItem orderLineItem1 = new ProductSetOrderLineItem();
        orderLineItem1.setSaleStuff(product1);

        ConfigurableProductSet configurableProductSet2 = new ConfigurableProductSet();
        configurableProductSet2.setId(3L);
        SkuProductSet product2 = new SkuProductSet();
        product2.setId(2L);
        product2.setConfigurableProductSet(configurableProductSet2);
        ProductSetOrderLineItem orderLineItem2 = new ProductSetOrderLineItem();
        orderLineItem2.setSaleStuff(product2);

        Mockito.when(productSetManager.findById(anyLong())).thenAnswer(invocation -> {
            Long productId = invocation.getArgument(0);
            if (1 == productId) {
                return product1;
            } else {
                return product2;
            }
        });

        boolean result = qrisProductService.allProductIsQrisProduct(ImmutableList.of(orderLineItem2, orderLineItem2));
        Assertions.assertThat(result).isTrue();
    }

    @Test
    public void testAllProductIsQrisProductThatSkuProductIsNotConfig() {
        QrisProductConfig qrisProductConfig = new QrisProductConfig();
        qrisProductConfig.setConfigurableProductIds(ImmutableList.of(3L));

        Mockito.when(qrisProductConfigManager.findAll())
                .thenReturn(ImmutableList.of(qrisProductConfig));

        SkuProductSet product1 = new SkuProductSet();
        product1.setId(1L);
        ProductSetOrderLineItem orderLineItem1 = new ProductSetOrderLineItem();
        orderLineItem1.setSaleStuff(product1);

        ConfigurableProductSet configurableProductSet2 = new ConfigurableProductSet();
        configurableProductSet2.setId(3L);
        SkuProductSet product2 = new SkuProductSet();
        product2.setId(2L);
        product2.setConfigurableProductSet(configurableProductSet2);
        ProductSetOrderLineItem orderLineItem2 = new ProductSetOrderLineItem();
        orderLineItem2.setSaleStuff(product2);

        Mockito.when(productSetManager.findById(anyLong())).thenAnswer(invocation -> {
            Long productId = invocation.getArgument(0);
            if (1 == productId) {
                return product1;
            } else {
                return product2;
            }
        });

        boolean result = qrisProductService.allProductIsQrisProduct(ImmutableList.of(orderLineItem1, orderLineItem2));
        Assertions.assertThat(result).isFalse();
    }

    @Test
    public void testAllProductIsQrisProductThatConfigurableProductIsNotConfig() {
        QrisProductConfig qrisProductConfig = new QrisProductConfig();
        qrisProductConfig.setSkuProductIds(ImmutableList.of(1L));

        Mockito.when(qrisProductConfigManager.findAll())
                .thenReturn(ImmutableList.of(qrisProductConfig));

        SkuProductSet product1 = new SkuProductSet();
        product1.setId(1L);
        ProductSetOrderLineItem orderLineItem1 = new ProductSetOrderLineItem();
        orderLineItem1.setSaleStuff(product1);

        ConfigurableProductSet configurableProductSet2 = new ConfigurableProductSet();
        configurableProductSet2.setId(3L);
        SkuProductSet product2 = new SkuProductSet();
        product2.setId(2L);
        product2.setConfigurableProductSet(configurableProductSet2);
        ProductSetOrderLineItem orderLineItem2 = new ProductSetOrderLineItem();
        orderLineItem2.setSaleStuff(product2);

        Mockito.when(productSetManager.findById(anyLong())).thenAnswer(invocation -> {
            Long productId = invocation.getArgument(0);
            if (1 == productId) {
                return product1;
            } else {
                return product2;
            }
        });

        boolean result = qrisProductService.allProductIsQrisProduct(ImmutableList.of(orderLineItem1, orderLineItem2));
        Assertions.assertThat(result).isFalse();
    }
}
