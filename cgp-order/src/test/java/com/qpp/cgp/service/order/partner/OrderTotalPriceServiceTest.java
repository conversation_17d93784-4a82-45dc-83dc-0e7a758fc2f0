package com.qpp.cgp.service.order.partner;

import com.qpp.cgp.domain.common.Currency;
import com.qpp.cgp.domain.order.Order;
import com.qpp.cgp.manager.common.CurrencyService;
import com.qpp.cgp.repository.common.CurrencyPureRepository;
import org.checkerframework.checker.units.qual.C;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
@PrepareForTest({OrderTotalPriceService.class, CurrencyService.class})
@PowerMockIgnore({"javax.management.*", "org.w3c.dom.*", "org.apache.log4j.*", "org.xml.sax.*", "javax.xml.*", "javax.script.*"})
public class OrderTotalPriceServiceTest {

    @InjectMocks
    private OrderTotalPriceService orderTotalPriceService;

    @Mock
    private CurrencyPureRepository currencyPureRepository;

    @Before
    public void setUp() {
        orderTotalPriceService = PowerMockito.spy(orderTotalPriceService);
    }

    @Test
    public void testGetTotalPriceGivenNull() {

        // input
        Order order = null;

        // mock

        // invoke
        String retVal = orderTotalPriceService.getTotalPrice(order);

        // verify
        Mockito.verify(orderTotalPriceService).getTotalPrice(order);
        Mockito.verifyNoInteractions(currencyPureRepository);
        assert retVal == null;
    }

    @Test
    public void testGetTotalPriceGivenNotNull() {

        // input
        Order order = new Order();

        // mock
        PowerMockito.mockStatic(CurrencyService.class);
        Currency currency = new Currency();
        PowerMockito.when(CurrencyService.formatPrice(order.getTotalIncTax(), currency)).thenReturn("test");
        Mockito.when(currencyPureRepository.findById(order.getCurrencyId())).thenReturn(currency);

        // invoke
        String retVal = orderTotalPriceService.getTotalPrice(order);

        // verify
        Mockito.verify(orderTotalPriceService).getTotalPrice(order);
        Mockito.verify(currencyPureRepository).findById(order.getCurrencyId());
        PowerMockito.verifyStatic(CurrencyService.class);
        CurrencyService.formatPrice(order.getTotalIncTax(), currency);
        assert retVal != null && retVal.equals("test");
    }
}