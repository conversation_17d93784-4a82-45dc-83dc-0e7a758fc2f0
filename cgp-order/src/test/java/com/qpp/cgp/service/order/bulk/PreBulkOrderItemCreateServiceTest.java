package com.qpp.cgp.service.order.bulk;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.qpp.cgp.domain.bom.runtime.ProductInstance;
import com.qpp.cgp.domain.dto.order.CreateOrderDTO;
import com.qpp.cgp.domain.dto.order.CreateOrderLineItemDTO;
import com.qpp.cgp.domain.dto.user.ProjectDTO;
import com.qpp.cgp.domain.order.Order;
import com.qpp.cgp.domain.order.OrderLineItem;
import com.qpp.cgp.domain.order.bulk.PreBulkOrderItem;
import com.qpp.cgp.domain.partner.Partner;
import com.qpp.cgp.domain.shipment.ShipmentRequirement;
import com.qpp.cgp.domain.user.AddressBook;
import com.qpp.cgp.domain.user.Customer;
import com.qpp.cgp.manager.bom.ProductInstanceManager;
import com.qpp.cgp.manager.order.bulk.PreBulkOrderItemManager;
import com.qpp.cgp.manager.partner.PartnerManager;
import com.qpp.cgp.manager.shipment.ShipmentRequirementManager;
import com.qpp.cgp.manager.user.ProjectManager;
import com.qpp.cgp.service.order.bulk.PreBulkOrderItemCreateService;
import com.qpp.cgp.service.order.bulk.PreBulkOrderItemService;
import com.qpp.cgp.service.order.address.AddressValidateService;
import com.qpp.cgp.util.UserUtils;
import com.qpp.core.context.SpringApplicationContext;
import com.qpp.core.exception.BusinessException;
import com.qpp.core.utils.SecurityUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;

/**
 * <AUTHOR>
 * @Date 2021/10/12 16:50
 * @Version 1.0
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({PreBulkOrderItemCreateService.class, UserUtils.class, SpringApplicationContext.class, SecurityUtils.class})
@PowerMockIgnore({"javax.management.*", "org.w3c.dom.*", "org.apache.log4j.*", "org.xml.sax.*", "javax.xml.*", "javax.script.*"})
public class PreBulkOrderItemCreateServiceTest {

    @InjectMocks
    private PreBulkOrderItemCreateService preBulkOrderItemCreateService;

    @Mock
    private PartnerManager partnerManager;

    @Mock
    private PreBulkOrderItemManager preBulkOrderItemManager;

    @Mock
    private AddressValidateService addressValidateService;

    @Mock
    private ProductInstanceManager productInstanceManager;

    @Mock
    private ProjectManager projectManager;

    @Mock
    private PreBulkOrderItemService preBulkOrderItemService;

    @Mock
    private ShipmentRequirementManager shipmentRequirementManager;

    private ObjectMapper objectMapper = new ObjectMapper();

    @Before
    public void before() throws Exception {
        preBulkOrderItemCreateService = PowerMockito.spy(preBulkOrderItemCreateService);

        PowerMockito.mockStatic(SpringApplicationContext.class);
        PowerMockito.mockStatic(UserUtils.class);


        Customer customer = new Customer();
        customer.setId(123L);

        // 测试类调用的外部静态方法
        PowerMockito.when(SpringApplicationContext.getBeanStatic(any())).thenReturn(null);
        PowerMockito.when(UserUtils.getCurrentUserDetails()).thenReturn(customer);
    }

    /* method:create(CreateOrderDTO createOrderDTO) */
    @Test
    public void testCreateByNotFoundPartner() throws Exception {
        CreateOrderDTO createOrderDTO = new CreateOrderDTO();

        PowerMockito.doNothing().when(preBulkOrderItemCreateService, "checkCreateOrderDtoObject", any());
        Mockito.when(partnerManager.findByUserId(any())).thenReturn(Optional.empty());

        boolean flag = false;
        try {
            preBulkOrderItemCreateService.create(createOrderDTO);
        } catch (BusinessException e) {
            Assert.assertEquals(2000462, e.getCode());
            flag = true;
        }
        Assert.assertTrue(flag);
    }

    @Test
    public void testCreateIsCorrectCase() throws Exception {
        // 获取外部测试数据
        InputStream createOrderDtoStream = this.getClass().getClassLoader().getResourceAsStream("json/createOrderDTO.json");
        CreateOrderDTO createOrderDTO = objectMapper.readValue(createOrderDtoStream, CreateOrderDTO.class);

        // 构建打桩数据
        Partner partner = new Partner();
        partner.setId(999L);

        BigDecimal price = new BigDecimal("32.1");

        // 打桩
        PowerMockito.doNothing().when(preBulkOrderItemCreateService, "checkCreateOrderDtoObject", any());
        Mockito.when(partnerManager.findByUserId(any())).thenReturn(Optional.ofNullable(partner));
        Mockito.when(preBulkOrderItemService.calculatePrice(anyLong(), anyInt(), any(AddressBook.class), Optional.ofNullable(0l))).thenReturn(price);

        List<PreBulkOrderItem> result = preBulkOrderItemCreateService.create(createOrderDTO);

        // 具体到每一项的数值 断言
        Assert.assertEquals(3, result.size());

        PreBulkOrderItem itemOne = result.get(0);
        Assert.assertEquals(price, itemOne.getPrice());
        Assert.assertEquals(partner.getId(), itemOne.getPartnerId());
        Assert.assertEquals("0001", itemOne.getBindOrderId());
        Assert.assertEquals("#0001", itemOne.getBindOrderNumber());
        Assert.assertEquals(createOrderDTO.getDeliveryAddress(), itemOne.getAddress());

        PreBulkOrderItem itemTwo = result.get(1);
        Assert.assertEquals(itemOne.getPartnerId(), itemTwo.getPartnerId());
        Assert.assertEquals(itemOne.getBindOrderId(), itemTwo.getBindOrderId());
        Assert.assertEquals(itemOne.getBindOrderNumber(), itemTwo.getBindOrderNumber());
        Assert.assertEquals(itemOne.getAddress(), itemTwo.getAddress());
    }

    /* method:create(Order order)*/
    @Test
    public void testCreateByOrderParatemerIsCorrect() throws Exception {
        Order order = new Order();
        Partner partner = new Partner();
        partner.setId(123L);
        order.setPartner(partner);
        order.setLineItems(Arrays.asList(new OrderLineItem(), new OrderLineItem(), new OrderLineItem()));

        PowerMockito.doNothing().when(preBulkOrderItemCreateService, "checkOrderObject", any());
        PowerMockito.doReturn(new AddressBook()).when(preBulkOrderItemCreateService, "getAddressByOrderField", any());
        PowerMockito.doReturn(new PreBulkOrderItem()).when(preBulkOrderItemCreateService, "createPreBulkOrderItem", any(), any());

        List<PreBulkOrderItem> result = preBulkOrderItemCreateService.create(order);

        Assert.assertEquals(3, result.size());
        Assert.assertEquals(Long.valueOf(123L), result.get(0).getPartnerId());
        Assert.assertEquals(Long.valueOf(123L), result.get(1).getPartnerId());
        Assert.assertEquals(Long.valueOf(123L), result.get(2).getPartnerId());
        Assert.assertEquals(result.get(0), result.get(1));
        Assert.assertEquals(result.get(2), result.get(1));
    }

    /* method:checkOrderObject(Order order) */
    @Test
    public void testCheckOrderObjectIsNUll() {
        boolean flag = false;
        try {
            Order order = null;
            ReflectionTestUtils.invokeMethod(preBulkOrderItemCreateService, "checkOrderObject", order);
        } catch (BusinessException e) {
            Assert.assertEquals(2000455, e.getCode());
            flag = true;
        }
        Assert.assertTrue(flag);
    }

    @Test
    public void testCheckOrderObjectByPartnerIsNull() {
        boolean flag = false;
        try {
            Order order = new Order();
            ReflectionTestUtils.invokeMethod(preBulkOrderItemCreateService, "checkOrderObject", order);
        } catch (BusinessException e) {
            Assert.assertEquals(2000456, e.getCode());
            flag = true;
        }
        Assert.assertTrue(flag);
    }

    /* method:checkCreateOrderDtoObject(CreateOrderDTO createOrderDTO) */
    @Test
    public void testCheckOrderObjectDtoObjectByAddressIsNull() {
        boolean flag = false;
        try {
            CreateOrderDTO createOrderDTO = new CreateOrderDTO();
            ReflectionTestUtils.invokeMethod(preBulkOrderItemCreateService, "checkCreateOrderDtoObject", createOrderDTO);
        } catch (BusinessException e) {
            Assert.assertEquals(2000458, e.getCode());
            flag = true;
        }
        Assert.assertTrue(flag);
    }

    @Test
    public void testCheckOrderObjectDtoObjectByLineItemsIsNull() {
        boolean flag = false;
        try {
            CreateOrderDTO createOrderDTO = new CreateOrderDTO();
            createOrderDTO.setDeliveryAddress(new AddressBook());
            ReflectionTestUtils.invokeMethod(preBulkOrderItemCreateService, "checkCreateOrderDtoObject", createOrderDTO);
        } catch (BusinessException e) {
            Assert.assertEquals(2000459, e.getCode());
            flag = true;
        }
        Assert.assertTrue(flag);
    }

    /* method:findProductId(CreateOrderLineItemDTO lineItem) */
    @Test
    public void testFindProductIdByProductId(){
        CreateOrderLineItemDTO createOrderLineItemDTO = new CreateOrderLineItemDTO();
        createOrderLineItemDTO.setProductId(222L);
        Long result = ReflectionTestUtils.invokeMethod(preBulkOrderItemCreateService, "findProductId", createOrderLineItemDTO);
        Assert.assertEquals(Long.valueOf(222),result);
    }

    @Test
    public void testFindProductIdByProductInstancId(){
        CreateOrderLineItemDTO createOrderLineItemDTO = new CreateOrderLineItemDTO();
        createOrderLineItemDTO.setProductInstanceId("333");
        ProductInstance productInstance = new ProductInstance();
        productInstance.setProductId(222L);
        Mockito.when(productInstanceManager.findById("333")).thenReturn(productInstance);
        Long result = ReflectionTestUtils.invokeMethod(preBulkOrderItemCreateService, "findProductId", createOrderLineItemDTO);
        Mockito.verify(productInstanceManager).findById("333");
        Assert.assertEquals(Long.valueOf(222),result);
    }

    @Test
    public void testFindProductIdByProjectId(){
        CreateOrderLineItemDTO createOrderLineItemDTO = new CreateOrderLineItemDTO();
        createOrderLineItemDTO.setProjectId(333L);
        ProductInstance productInstance = new ProductInstance();
        productInstance.setProductId(222L);
        ProjectDTO project = new ProjectDTO();
        project.setProductInstanceId("333");
        Mockito.when(projectManager.getProject(333L)).thenReturn(project);
        Mockito.when(productInstanceManager.findById("333")).thenReturn(productInstance);
        Long result = ReflectionTestUtils.invokeMethod(preBulkOrderItemCreateService, "findProductId", createOrderLineItemDTO);
        Mockito.verify(projectManager).getProject(333L);
        Mockito.verify(productInstanceManager).findById("333");
        Assert.assertEquals(Long.valueOf(222),result);
    }

    @Test
    public void testFindProductIdByProductInstanceOfProjectIdIsNull(){
        CreateOrderLineItemDTO createOrderLineItemDTO = new CreateOrderLineItemDTO();
        createOrderLineItemDTO.setProjectId(333L);
        ProductInstance productInstance = new ProductInstance();
        productInstance.setProductId(222L);
        ProjectDTO project = new ProjectDTO();
        Mockito.when(projectManager.getProject(333L)).thenReturn(project);
        boolean flag = false;
        try{
            ReflectionTestUtils.invokeMethod(preBulkOrderItemCreateService, "findProductId", createOrderLineItemDTO);
        }catch (BusinessException e){
            Assert.assertEquals(2000460,e.getCode());
            flag = true;
        }
        Mockito.verify(projectManager).getProject(333L);
        Assert.assertTrue(flag);
    }

    @Test
    public void testFindProductIdNotFind(){
        boolean flag = false;
        try{
            ReflectionTestUtils.invokeMethod(preBulkOrderItemCreateService, "findProductId", new CreateOrderLineItemDTO());
        }catch (BusinessException e){
            Assert.assertEquals(2000460,e.getCode());
            flag = true;
        }
        Assert.assertTrue(flag);
    }
    /* method:createPreBulkOrderItem(AddressB ook defAddress,OrderLineItem orderLineItem) 賦值逻辑  */

    /* method:findAddressBookByOrderItem(AddressBook defAddress,OrderLineItem orderLineItem) */
    @Test
    public void testFindAddressBookUseDefAddressByOrderItemIdIsValueIsNull() {
        AddressBook address = new AddressBook();
        OrderLineItem orderLineItem = new OrderLineItem();
        orderLineItem.setId("123L");
        Mockito.when(shipmentRequirementManager.findByOrderItemId(anyString())).thenReturn(null);
        AddressBook result = ReflectionTestUtils.invokeMethod(preBulkOrderItemCreateService, "findAddressBookByOrderItem", address, orderLineItem);

        Assert.assertEquals(address, result);
    }

    @Test
    public void testFindAddressBookUseDefAddressByOrderItemIdIsValueIsEmpty() {
        AddressBook address = new AddressBook();
        OrderLineItem orderLineItem = new OrderLineItem();
        orderLineItem.setId("123L");
        Mockito.when(shipmentRequirementManager.findByOrderItemId(anyString())).thenReturn(new ArrayList<>());
        AddressBook result = ReflectionTestUtils.invokeMethod(preBulkOrderItemCreateService, "findAddressBookByOrderItem", address, orderLineItem);

        Assert.assertEquals(address, result);
    }

    @Test
    public void testFindAddressBookFoundSizeOfAddressIsGtOne() {
        AddressBook address = new AddressBook();
        OrderLineItem orderLineItem = new OrderLineItem();
        orderLineItem.setId("123L");
        List<ShipmentRequirement> shipmentRequirements = new ArrayList<>();
        ShipmentRequirement shipmentRequirement = new ShipmentRequirement();
        shipmentRequirements.add(shipmentRequirement);
        shipmentRequirements.add(shipmentRequirement);
        Mockito.when(shipmentRequirementManager.findByOrderItemId(anyString())).thenReturn(shipmentRequirements);
        boolean flag = false;
        try {
            ReflectionTestUtils.invokeMethod(preBulkOrderItemCreateService, "findAddressBookByOrderItem", address, orderLineItem);
        } catch (BusinessException e) {
            Assert.assertEquals(2000461, e.getCode());
            flag = true;
        }
        Assert.assertTrue(flag);

    }

    @Test
    public void testFindAddressBookCorrectCase() {
        AddressBook address = new AddressBook();
        OrderLineItem orderLineItem = new OrderLineItem();
        orderLineItem.setId("123L");
        List<ShipmentRequirement> shipmentRequirements = new ArrayList<>();
        ShipmentRequirement shipmentRequirement = new ShipmentRequirement();
        AddressBook addressBook = new AddressBook();
        shipmentRequirement.setAddress(addressBook);
        shipmentRequirements.add(shipmentRequirement);

        Mockito.when(shipmentRequirementManager.findByOrderItemId(anyString())).thenReturn(shipmentRequirements);

        AddressBook result = ReflectionTestUtils.invokeMethod(preBulkOrderItemCreateService, "findAddressBookByOrderItem", address, orderLineItem);

        Assert.assertEquals(addressBook, result);
    }

    /* method:getAddressByOrderField(Order order) 賦值逻辑 */


}
