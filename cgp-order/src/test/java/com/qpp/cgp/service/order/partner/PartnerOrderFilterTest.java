package com.qpp.cgp.service.order.partner;

import com.qpp.cgp.domain.order.Order;
import org.aspectj.weaver.ast.Or;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class PartnerOrderFilterTest {

    @Spy
    @InjectMocks
    private PartnerOrderFilter partnerOrderFilter;

    @Test
    public void testFilterGivenNull() {
        // input
        Order order = null;
        Long partnerId = null;
        // mock

        // invoke
        Order retVal = partnerOrderFilter.filter(order, partnerId);
        // verify
        Mockito.verify(partnerOrderFilter).filter(order, partnerId);
        assert retVal == null;
    }

    @Test
    public void testFilterGivenNotNull() {
        // input
        Order order = new Order();
        order.setPartnerId(1L);
        Long partnerId = 1L;
        // mock

        // invoke
        Order retVal = partnerOrderFilter.filter(order, partnerId);
        // verify
        Mockito.verify(partnerOrderFilter).filter(order, partnerId);
        assert retVal == order;
    }

    @Test
    public void testFilterListGivenNull() {
        // input
        List<Order> orderList = null;
        Long partnerId = null;
        // mock

        // invoke
        List<Order> retVal = partnerOrderFilter.filter(orderList, partnerId);
        // verify
        Mockito.verify(partnerOrderFilter).filter(orderList, partnerId);
        Mockito.verifyNoMoreInteractions(partnerOrderFilter);
        assert retVal == null;
    }

    @Test
    public void testFilterListGivenNotNull() {
        // input
        List<Order> orderList = new ArrayList<>();
        Order order = new Order();
        order.setPartnerId(1L);
        orderList.add(order);
        Long partnerId = 1L;
        // mock

        // invoke
        List<Order> retVal = partnerOrderFilter.filter(orderList, partnerId);
        // verify
        Mockito.verify(partnerOrderFilter).filter(orderList, partnerId);
        Mockito.verify(partnerOrderFilter).filter(order, partnerId);
        assert retVal != null && retVal.size() == 1 && retVal.get(0) == order;
    }
}