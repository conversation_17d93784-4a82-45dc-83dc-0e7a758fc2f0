package com.qpp.cgp.service.order.partner;

import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.dto.partner.PartnerOrderDTO;
import com.qpp.cgp.domain.order.Order;
import com.qpp.cgp.domain.order.PartnerOrderStatus;
import com.qpp.cgp.domain.user.Customer;
import com.qpp.cgp.domain.user.User;
import com.qpp.cgp.manager.product.config.view.builderurl.MultilingualValueFinder;
import com.qpp.cgp.repository.user.UserPureRepository;
import org.aspectj.weaver.ast.Or;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;

@RunWith(MockitoJUnitRunner.class)
public class PartnerOrderDTOConvertorTest {

    @Spy
    @InjectMocks
    private PartnerOrderDTOConvertor partnerOrderDTOConvertor;

    @Mock
    private UserPureRepository userPureRepository;

    @Mock
    private MultilingualValueFinder multilingualValueFinder;

    @Mock
    private PartnerOrderStatusMapBuilder partnerOrderStatusMapBuilder;

    @Mock
    private OrderTotalPriceService orderTotalPriceService;

    @Test
    public void testConvertGivenNull() {
        // input
        Order order = null;
        Map<Long, PartnerOrderStatus> orderStatusIdMap = null;
        // mock

        // invoke
        PartnerOrderDTO retVal = partnerOrderDTOConvertor.convert(order, orderStatusIdMap);
        // verify
        Mockito.verify(partnerOrderDTOConvertor).convert(order, orderStatusIdMap);
        assert retVal == null;
        Mockito.verifyNoInteractions(userPureRepository, multilingualValueFinder, orderTotalPriceService);
    }

    @Test
    public void testConvertGivenNotNull() {
        // input
        Order order = new Order();
        long userId = 1L;
        order.setCustomerId(userId);
        long orderStatusId = 123L;
        order.setOrderStatusId(orderStatusId);
        Map<Long, PartnerOrderStatus> orderStatusIdMap = new HashMap<>();
        PartnerOrderStatus partnerOrderStatus = new PartnerOrderStatus();
        partnerOrderStatus.setName("test");
        orderStatusIdMap.put(orderStatusId, partnerOrderStatus);
        // mock

        // invoke
        PartnerOrderDTO retVal = partnerOrderDTOConvertor.convert(order, orderStatusIdMap);
        // verify
        Mockito.verify(partnerOrderDTOConvertor).convert(order, orderStatusIdMap);
        Mockito.verify(orderTotalPriceService).getTotalPrice(order);
        Mockito.verify(userPureRepository).findById(userId);
        assert retVal != null;
    }

    @Test
    public void testConvertListGivenNull() {
        // input
        List<Order> orderList = null;
        // mock

        // invoke
        List<PartnerOrderDTO> retVal = partnerOrderDTOConvertor.convert(orderList);
        // verify
        Mockito.verify(partnerOrderDTOConvertor).convert(orderList);
        Mockito.verify(partnerOrderStatusMapBuilder).build();
        assert retVal == null;
        Mockito.verifyNoMoreInteractions(partnerOrderDTOConvertor);
        Mockito.verifyNoInteractions(userPureRepository, multilingualValueFinder);
    }

    @Test
    public void testConvertListGivenNotNull() {
        // input
        List<Order> orderList = new ArrayList<>();
        Map<Long, PartnerOrderStatus> orderStatusIdMap = new HashMap<>();

        Order order = new Order();
        long userId = 1L;
        order.setCustomerId(userId);
        long orderStatusId = 123L;
        order.setOrderStatusId(orderStatusId);
        orderList.add(order);
        PartnerOrderStatus partnerOrderStatus = new PartnerOrderStatus();
        partnerOrderStatus.setName("test");
        orderStatusIdMap.put(orderStatusId, partnerOrderStatus);
        // mock
        Mockito.when(partnerOrderStatusMapBuilder.build()).thenReturn(orderStatusIdMap);
        // invoke
        List<PartnerOrderDTO> retVal = partnerOrderDTOConvertor.convert(orderList);
        // verify
        Mockito.verify(partnerOrderDTOConvertor).convert(orderList);
        Mockito.verify(partnerOrderStatusMapBuilder).build();
        Mockito.verify(partnerOrderDTOConvertor).convert(order, orderStatusIdMap);
        assert retVal != null;
        Mockito.verify(userPureRepository).findById(userId);
        Mockito.verifyNoMoreInteractions(partnerOrderDTOConvertor);
    }

    @Test
    public void testConvertByPartnerOrderStatus() {
        Order order = new Order();
        order.setId("1");
        order.setDatePurchased(new Date());
        order.setOrderNumber("TM1");
        order.setCustomerId(11L);
        order.setOrderStatusId(100L);

        User user = new Customer();
        user.setFirstName("Michael");
        user.setLastName("Chong");

        PartnerOrderStatus partnerOrderStatus = new PartnerOrderStatus();
        partnerOrderStatus.setName("Wait for fulfilment");
        partnerOrderStatus.setCode("Wait for fulfilment code");

        Map<Long, PartnerOrderStatus> orderStatusIdMap = ImmutableMap.of(100L, partnerOrderStatus);

        Mockito.when(orderTotalPriceService.getTotalPrice(any())).thenReturn("100");
        Mockito.when(userPureRepository.findById(any())).thenReturn(Optional.of(user));

        PartnerOrderDTO partnerOrderDTO = partnerOrderDTOConvertor.convert(order, orderStatusIdMap);
        Assertions.assertThat(partnerOrderDTO.getOrderId()).isEqualTo("1");
        Assertions.assertThat(partnerOrderDTO.getDate()).isEqualTo(order.getDatePurchased());
        Assertions.assertThat(partnerOrderDTO.getOrderNumber()).isEqualTo("TM1");
        Assertions.assertThat(partnerOrderDTO.getPrice()).isEqualTo("100");
        Assertions.assertThat(partnerOrderDTO.getName()).isEqualTo("Michael Chong");
        Assertions.assertThat(partnerOrderDTO.getOrderStatus()).isEqualTo("Wait for fulfilment");
        Assertions.assertThat(partnerOrderDTO.getOrderStatusCode()).isEqualTo("Wait for fulfilment code");
        Assertions.assertThat(partnerOrderDTO.getOrderStatusValue()).isEqualTo("Wait for fulfilment");
    }
}