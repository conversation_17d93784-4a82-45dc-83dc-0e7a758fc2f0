//package com.qpp.cgp.service.shipment;
//
//import com.qpp.cgp.CgpOrderTestApplication;
//import com.qpp.cgp.domain.dto.shipment.order.ShipmentOrderDeliverInfo;
//import com.qpp.cgp.domain.shipment.ShipmentOrder;
//import com.qpp.cgp.domain.shipment.ShipmentOrderStatus;
//import com.qpp.cgp.manager.shipment.ShipmentOrderManager;
//import com.qpp.cgp.service.shipment.ShipmentOrderService;
//import com.qpp.cgp.util.EmbeddedTestUtils;
//import com.qpp.core.exception.BusinessException;
//import com.qpp.id.generator.IdGenerator;
//import org.junit.After;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.test.context.junit4.SpringRunner;
//import org.springframework.test.util.ReflectionTestUtils;
//
//import java.io.IOException;
//import java.lang.reflect.Field;
//import java.util.Date;
//
///**
// * <AUTHOR> Chiu
// * @Date 2021/4/1 17:27
// * @Description
// * @Version 1.0
// */
//
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = CgpOrderTestApplication.class)
//public class ShipmentOrderServiceUpdateDeliverInfoTests {
//
//    @Autowired
//    private IdGenerator idGenerator;
//
//    private ShipmentOrderService shipmentOrderService;
//
//    private MongoTemplate mongoTemplate;
//
//
//    @Before
//    public void init() throws IOException, NoSuchFieldException, IllegalAccessException {
//        shipmentOrderService = preProcessEnvironment();
//        Field shipmentOrderManagerField = shipmentOrderService.getClass().getDeclaredField("shipmentOrderManager");
//        shipmentOrderManagerField.setAccessible(true);
//        ShipmentOrderManager shipmentOrderManager = (ShipmentOrderManager) shipmentOrderManagerField.get(shipmentOrderService);
//        Field mongoTemplateField = shipmentOrderManager.getClass().getSuperclass().getDeclaredField("mongoTemplate");
//        mongoTemplateField.setAccessible(true);
//        mongoTemplate = (MongoTemplate) mongoTemplateField.get(shipmentOrderManager);
//    }
//
//    @Test
//    public void testUpdateDeliverInfo() throws NoSuchFieldException, IOException, IllegalAccessException {
//        //0、ShipmentOrderDeliverInfo为空
//        ShipmentOrderDeliverInfo nullShipmentOrderDeliverInfo = null;
//        try {
//            ShipmentOrder shipmentOrderWithNullShipmentOrderSettleInfo = shipmentOrderService.update(nullShipmentOrderDeliverInfo);
//        } catch (Exception e) {
//            Assert.assertEquals("Parameter: updateInfo can not be null!", e.getMessage());
//            return;
//        }
//        throw new BusinessException("方法执行过程中未抛出期待异常");
//
//    }
//
//    @Test
//    public void testByNotExistsShipmentOrderId() {
//        //1、ShipmentOrderDeliverInfo未关联shipmentOrderId
//        ShipmentOrderDeliverInfo shipmentOrderDeliverInfoWithNullOrderId = new ShipmentOrderDeliverInfo();
//        try {
//            ShipmentOrder shipmentOrderWithNullOrderId = shipmentOrderService.update(shipmentOrderDeliverInfoWithNullOrderId);
//        } catch (Exception e) {
//            Assert.assertEquals("The shipmentOrderDeliverInfo is not associated shipmentOrder!", e.getMessage());
//            return;
//        }
//        throw new BusinessException("方法执行过程中未抛出期待异常");
//    }
//
//    @Test
//    public void testByNotExistsOrderId() {
//        //2、ShipmentOrderDeliverInfo关联的shipmentOrderId没有对应订单
//        ShipmentOrderDeliverInfo shipmentOrderDeliverInfoWithNotExistsOrder = new ShipmentOrderDeliverInfo();
//        shipmentOrderDeliverInfoWithNotExistsOrder.setShipmentOrderId(13579L);
//        try {
//            ShipmentOrder shipmentOrderWithNotExistsOrder = shipmentOrderService.update(shipmentOrderDeliverInfoWithNotExistsOrder);
//        } catch (Exception e) {
//            Assert.assertTrue(e.getMessage().contains("is not exists!"));
//            return;
//        }
//        throw new BusinessException("方法执行过程中未抛出期待异常");
//    }
//
//    @Test
//    public void testByErrorStatus() {
//        //3、ShipmentOrderSettleInfo中关联的发货订单未处于已交收（待发货）或已发货状态
//        ShipmentOrderDeliverInfo shipmentOrderDeliverInfoWithErrorStatus = new ShipmentOrderDeliverInfo();
//        shipmentOrderDeliverInfoWithErrorStatus.setShipmentOrderId(12346L);
//        try {
//            ShipmentOrder shipmentOrderWithErrorStatus = shipmentOrderService.update(shipmentOrderDeliverInfoWithErrorStatus);
//        } catch (Exception e) {
//            Assert.assertEquals("The shipmentOrder with ID < 12346 > is not in the state of WAITING_DELIVERY_STATUS or DELIVERED_STATUS. It is forbidden to modify the shipmentOrderDeliverInfo!", e.getMessage());
//            return;
//        }
//        throw new BusinessException("方法执行过程中未抛出期待异常");
//    }
//
//    @Test
//    public void testByNullDeliverDate() {
//        //4、发货信息不完全，禁止修改发货信息
//        ShipmentOrderDeliverInfo inCompleteShipmentOrderDeliverInfo = new ShipmentOrderDeliverInfo();
//        inCompleteShipmentOrderDeliverInfo.setShipmentOrderId(12345L);
//        try {
//            ShipmentOrder shipmentOrderWithInCompleteDeliverInfo = shipmentOrderService.update(inCompleteShipmentOrderDeliverInfo);
//        } catch (Exception e) {
//            Assert.assertEquals("Incomplete shipmentOrderDeliverInfo, no modification!", e.getMessage());
//            return;
//        }
//        throw new BusinessException("方法执行过程中未抛出期待异常");
//    }
//
//    @Test
//    public void testByNormalShipmentOrderSettleInfo() {
//        //5、正常ShipmentOrderSettleInfo
//        ShipmentOrderDeliverInfo normalShipmentOrderDeliverInfo = new ShipmentOrderDeliverInfo();
//        normalShipmentOrderDeliverInfo.setShipmentOrderId(12345L);
//        Date deliverDate = new Date();
//        normalShipmentOrderDeliverInfo.setDeliveredDate(deliverDate);
//        ShipmentOrder shipmentOrderBeforeUpdate = mongoTemplate.findById(12345L, ShipmentOrder.class);
//        Assert.assertTrue(shipmentOrderBeforeUpdate.getStatusId().equals(ShipmentOrderStatus.WAITING_DELIVERY_STATUS) || shipmentOrderBeforeUpdate.getStatusId().equals(ShipmentOrderStatus.DELIVERED_STATUS));
//
//        ShipmentOrder shipmentOrder = shipmentOrderService.update(normalShipmentOrderDeliverInfo);
//
//        ShipmentOrder shipmentOrderAfterUpdate = mongoTemplate.findById(12345L, ShipmentOrder.class);
//        Assert.assertTrue(shipmentOrderAfterUpdate.getStatusId().equals(ShipmentOrderStatus.DELIVERED_STATUS));
//    }
//
//
//    private ShipmentOrderService preProcessEnvironment() throws IOException {
//        MongoTemplate mongoTemplate = EmbeddedTestUtils.startEmbeddedMongo("test", "shipmentorders", "classpath:com/qpp/cgp/manager/shipment/ShipmentOrderServiceTestsData4ShipmentOrder.txt");
//        ShipmentOrderManager shipmentOrderManager = new ShipmentOrderManager(mongoTemplate, idGenerator);
//        ShipmentOrderService shipmentOrderService = new ShipmentOrderService();
//        ReflectionTestUtils.setField(shipmentOrderService, "shipmentOrderManager", shipmentOrderManager);
//        ReflectionTestUtils.setField(shipmentOrderService, "idGenerator", idGenerator);
//        return shipmentOrderService;
//    }
//
//    @After
//    public void destroy(){
//        EmbeddedTestUtils.destroyMongo();
//    }
//
//}
