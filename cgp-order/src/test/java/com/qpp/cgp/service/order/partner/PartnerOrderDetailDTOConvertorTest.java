package com.qpp.cgp.service.order.partner;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.common.Currency;
import com.qpp.cgp.domain.dto.partner.PartnerOrderDTO;
import com.qpp.cgp.domain.dto.partner.PartnerOrderDetailDTO;
import com.qpp.cgp.domain.order.Order;
import com.qpp.cgp.repository.common.CurrencyPureRepository;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Date;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @date 2024/6/6
 */
@ExtendWith(MockitoExtension.class)
public class PartnerOrderDetailDTOConvertorTest {

    @Spy
    @InjectMocks
    private PartnerOrderDetailDTOConvertor partnerOrderDetailDTOConvertor;

    @Mock
    private PartnerOrderDTOConvertor partnerOrderDTOConvertor;

    @Mock
    private CurrencyPureRepository currencyPureRepository;

    @Mock
    private PartnerOrderItemDTOConvertor partnerOrderItemDTOConvertor;

    @Test
    public void testConvert() {
        Date date = new Date();

        Order order = new Order();
        order.setPaymentMethod("payment method");
        order.setPaymentModuleCode("payment method code");
        order.setShippingMethod("shipping method");
        order.setPaidDate(date);

        PartnerOrderDTO partnerOrderDTO = new PartnerOrderDTO();
        partnerOrderDTO.setOrderId("1");
        partnerOrderDTO.setDate(date);
        partnerOrderDTO.setOrderNumber("TM1");
        partnerOrderDTO.setPrice("100");
        partnerOrderDTO.setName("Michael Chong");
        partnerOrderDTO.setOrderStatus("Wait for fulfilment");
        partnerOrderDTO.setOrderStatusValue("Wait for fulfilment");
        partnerOrderDTO.setOrderStatusCode("Wait for fulfilment code");

        Currency currency = new Currency();
        currency.setId(21L);

        Mockito.when(currencyPureRepository.findById(any())).thenReturn(currency);
        Mockito.when(partnerOrderDTOConvertor.convert(any(), any())).thenReturn(partnerOrderDTO);
        Mockito.when(partnerOrderItemDTOConvertor.convert(any())).thenReturn(ImmutableList.of());

        // 调用需要测试的方法
        PartnerOrderDetailDTO partnerOrderDetailDTO = partnerOrderDetailDTOConvertor.convert(order, ImmutableMap.of());

        Assertions.assertThat(partnerOrderDetailDTO.getDatePurchased()).isEqualTo(date);
        Assertions.assertThat(partnerOrderDetailDTO.getOrderNumber()).isEqualTo("TM1");
        Assertions.assertThat(partnerOrderDetailDTO.getOrderId()).isEqualTo("1");
        Assertions.assertThat(partnerOrderDetailDTO.getPrice()).isEqualTo("100");
        Assertions.assertThat(partnerOrderDetailDTO.getName()).isEqualTo("Michael Chong");
        Assertions.assertThat(partnerOrderDetailDTO.getOrderStatus()).isEqualTo("Wait for fulfilment");
        Assertions.assertThat(partnerOrderDetailDTO.getOrderStatusCode()).isEqualTo("Wait for fulfilment code");
        Assertions.assertThat(partnerOrderDetailDTO.getOrderStatusValue()).isEqualTo("Wait for fulfilment");
        Assertions.assertThat(partnerOrderDetailDTO.getPaymentMethod()).isEqualTo("payment method");
        Assertions.assertThat(partnerOrderDetailDTO.getPaymentMethodValue()).isEqualTo("payment method");
        Assertions.assertThat(partnerOrderDetailDTO.getPaymentMethodCode()).isEqualTo("payment method code");
        Assertions.assertThat(partnerOrderDetailDTO.getShippingMethod()).isEqualTo("shipping method");
        Assertions.assertThat(partnerOrderDetailDTO.getPaidDate()).isEqualTo(date);
    }
}
