package com.qpp.cgp.service.shipping;

import com.qpp.cgp.manager.bom.ProductInstanceManager;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(MockitoJUnitRunner.class)
public class ShippingMethodServiceTest {

    @InjectMocks
    @Spy
    private ShippingMethodService shippingMethodService;

    @Mock
    private ProductInstanceManager productInstanceManager;

    /**
     * 方法 ： findProductConfigBomIdByProductInstanceId </br>
     * 测试 ： 传入的productInstanceId为null或空
     * */
    @Test
    public void testFindProductConfigBomIdByProductInstanceIdByNullOrEmptyProductInstanceId(){
        Optional<Long> productConfigBomId = shippingMethodService.findProductConfigBomIdByProductInstanceId(null);
        Assertions.assertFalse(productConfigBomId.isPresent());

        Optional<Long> productConfigBomId1 = shippingMethodService.findProductConfigBomIdByProductInstanceId("");
        Assertions.assertFalse(productConfigBomId1.isPresent());

    }

    /**
     * 方法 ： findProductConfigBomIdByProductInstanceId </br>
     * 测试 ： 传入的productInstanceId不为空
     * */
    @Test
    public void testFindProductConfigBomIdByProductInstanceIdByNotEmptyProductInstanceId(){
        Mockito.when(productInstanceManager.getProductConfigBomIdById("111"))
                .thenReturn(Optional.of(1111L));
        Optional<Long> productConfigBomId = shippingMethodService.findProductConfigBomIdByProductInstanceId("111");
        Assertions.assertEquals(1111L,productConfigBomId.get());
    }
}