package com.qpp.cgp.service.order.third;

import com.qpp.cgp.domain.order.ThirdOrder;
import com.qpp.cgp.manager.order.ThirdOrderManager;
import com.qpp.cgp.manager.shipment.ShipmentRequirementManager;
import com.qpp.core.exception.BusinessException;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

/**
 * <AUTHOR>
 * @date 2022/7/25
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"javax.script.*", "javax.management.*"})
@PrepareForTest(BusinessExceptionBuilder.class)
public class ThirdOrderServiceFeedbackOrderShipmentErrorTest {

    @Spy
    @InjectMocks
    private ThirdOrderService thirdOrderService = new ThirdOrderService();

    @Mock
    private ThirdOrderManager thirdOrderManager;

    @Mock
    private ShipmentRequirementManager shipmentRequirementManager;

    @Test
    public void testByOrderIsNotExists() {
        BusinessException businessException = new BusinessException();
        businessException.setMessage("ThirdOrder '1' is not exists!");

        PowerMockito.mockStatic(BusinessExceptionBuilder.class);
        PowerMockito.when(BusinessExceptionBuilder.of(eq(500112), any()))
                .thenReturn(businessException);

        Mockito.when(thirdOrderManager.findByOrderIdOrOrderNumber(any())).thenReturn(null);

        Assertions.assertThatThrownBy(() ->
                        thirdOrderService.feedbackOrderShipment("1", "11"))
                .hasMessage("ThirdOrder '1' is not exists!");
    }

    @Test
    public void testByShipmentRequirementIsNotExists() {
        BusinessException businessException = new BusinessException();
        businessException.setMessage("ShipmentRequirement '11' is not exists!");

        PowerMockito.mockStatic(BusinessExceptionBuilder.class);
        PowerMockito.when(BusinessExceptionBuilder.of(eq(4400001), any()))
                .thenReturn(businessException);

        ThirdOrder thirdOrder = new ThirdOrder();
        thirdOrder.setOrderId("1");
        thirdOrder.setBindOrderId(101L);
        Mockito.when(thirdOrderManager.findByOrderIdOrOrderNumber(any())).thenReturn(thirdOrder);

        Mockito.when(shipmentRequirementManager.findByThirdShipmentRequirementId(any()))
                .thenReturn(null);

        Assertions.assertThatThrownBy(() ->
                        thirdOrderService.feedbackOrderShipment("1", "11"))
                .hasMessage("ShipmentRequirement '11' is not exists!");
    }
}
