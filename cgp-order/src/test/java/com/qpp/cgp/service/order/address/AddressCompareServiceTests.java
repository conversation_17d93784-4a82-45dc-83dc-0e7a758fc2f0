package com.qpp.cgp.service.order.address;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qpp.cgp.domain.user.AddressBook;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = AddressCompareService.class)
public class AddressCompareServiceTests {

    private static <T> T getResource(String resource, TypeReference<T> typeReference) {
        try {
            return new ObjectMapper().readValue(
                    AddressCompareServiceTests.class.getClassLoader().getResourceAsStream(String.format("bulk_order_test_datas/AddressCompareServiceTests/%s", resource)),
                    typeReference
            );
        } catch (Exception e) {
            throw new RuntimeException(String.format("load %s resource failed, msg: %s", resource, e.getMessage()), e);
        }
    }

    @Autowired
    private AddressCompareService addressCompareService;

    @Test
    public void testCompareEqualsByNullAddressA() {
        IllegalArgumentException expectedException = null;

        try {
            AddressBook a = Mockito.mock(AddressBook.class);
            AddressBook b = null;

            addressCompareService.compareEquals(a, b);
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
            expectedException = e;
        }

        Assertions.assertThat(expectedException).isNotNull();
    }

    @Test
    public void testCompareEqualsByNullAddressB() {
        IllegalArgumentException expectedException = null;

        try {
            AddressBook a = null;
            AddressBook b = Mockito.mock(AddressBook.class);

            addressCompareService.compareEquals(a, b);
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
            expectedException = e;
        }

        Assertions.assertThat(expectedException).isNotNull();
    }

    @Test
    public void testCompareEqualsBySameAddress() {
        AddressBook a = getResource("address_0.json", new TypeReference<AddressBook>() {});
        AddressBook b = getResource("address_0.json", new TypeReference<AddressBook>() {});

        boolean result = addressCompareService.compareEquals(a, b);

        Assertions.assertThat(result).isTrue();
    }

    @Test
    public void testCompareEqualsByUnequalCountryCode2() {
        AddressBook a = getResource("address_0.json", new TypeReference<AddressBook>() {});
        AddressBook b = getResource("address_1.json", new TypeReference<AddressBook>() {});

        boolean result = addressCompareService.compareEquals(a, b);

        Assertions.assertThat(result).isFalse();
    }

    @Test
    public void testCompareEqualsByUnequalCountryName() {
        AddressBook a = getResource("address_0.json", new TypeReference<AddressBook>() {});
        AddressBook b = getResource("address_2.json", new TypeReference<AddressBook>() {});

        boolean result = addressCompareService.compareEquals(a, b);

        Assertions.assertThat(result).isFalse();
    }

    @Test
    public void testCompareEqualsByUnequalState() {
        AddressBook a = getResource("address_0.json", new TypeReference<AddressBook>() {});
        AddressBook b = getResource("address_3.json", new TypeReference<AddressBook>() {});

        boolean result = addressCompareService.compareEquals(a, b);

        Assertions.assertThat(result).isFalse();
    }

    @Test
    public void testCompareEqualsByUnequalCity() {
        AddressBook a = getResource("address_0.json", new TypeReference<AddressBook>() {});
        AddressBook b = getResource("address_4.json", new TypeReference<AddressBook>() {});

        boolean result = addressCompareService.compareEquals(a, b);

        Assertions.assertThat(result).isFalse();
    }

    @Test
    public void testCompareEqualsByUnequalSuburb() {
        AddressBook a = getResource("address_0.json", new TypeReference<AddressBook>() {});
        AddressBook b = getResource("address_5.json", new TypeReference<AddressBook>() {});

        boolean result = addressCompareService.compareEquals(a, b);

        Assertions.assertThat(result).isFalse();
    }

    @Test
    public void testCompareEqualsByUnequalStreetAddress1() {
        AddressBook a = getResource("address_0.json", new TypeReference<AddressBook>() {});
        AddressBook b = getResource("address_6.json", new TypeReference<AddressBook>() {});

        boolean result = addressCompareService.compareEquals(a, b);

        Assertions.assertThat(result).isFalse();
    }

    @Test
    public void testCompareEqualsByUnequalStreetAddress2() {
        AddressBook a = getResource("address_0.json", new TypeReference<AddressBook>() {});
        AddressBook b = getResource("address_7.json", new TypeReference<AddressBook>() {});

        boolean result = addressCompareService.compareEquals(a, b);

        Assertions.assertThat(result).isFalse();
    }

    @Test
    public void testCompareEqualsByUnequalPostCode() {
        AddressBook a = getResource("address_0.json", new TypeReference<AddressBook>() {});
        AddressBook b = getResource("address_8.json", new TypeReference<AddressBook>() {});

        boolean result = addressCompareService.compareEquals(a, b);

        Assertions.assertThat(result).isFalse();
    }

    @Test
    public void testCompareEqualsByUnequalFirstName() {
        AddressBook a = getResource("address_0.json", new TypeReference<AddressBook>() {});
        AddressBook b = getResource("address_9.json", new TypeReference<AddressBook>() {});

        boolean result = addressCompareService.compareEquals(a, b);

        Assertions.assertThat(result).isFalse();
    }

    @Test
    public void testCompareEqualsByUnequalLastName() {
        AddressBook a = getResource("address_0.json", new TypeReference<AddressBook>() {});
        AddressBook b = getResource("address_10.json", new TypeReference<AddressBook>() {});

        boolean result = addressCompareService.compareEquals(a, b);

        Assertions.assertThat(result).isFalse();
    }

    @Test
    public void testCompareEqualsByUnequalGender() {
        AddressBook a = getResource("address_0.json", new TypeReference<AddressBook>() {});
        AddressBook b = getResource("address_11.json", new TypeReference<AddressBook>() {});

        boolean result = addressCompareService.compareEquals(a, b);

        Assertions.assertThat(result).isFalse();
    }

    @Test
    public void testCompareEqualsByUnequalMobile() {
        AddressBook a = getResource("address_0.json", new TypeReference<AddressBook>() {});
        AddressBook b = getResource("address_12.json", new TypeReference<AddressBook>() {});

        boolean result = addressCompareService.compareEquals(a, b);

        Assertions.assertThat(result).isFalse();
    }

    @Test
    public void testCompareEqualsByUnequalTelephone() {
        AddressBook a = getResource("address_0.json", new TypeReference<AddressBook>() {});
        AddressBook b = getResource("address_13.json", new TypeReference<AddressBook>() {});

        boolean result = addressCompareService.compareEquals(a, b);

        Assertions.assertThat(result).isFalse();
    }

    @Test
    public void testCompareEqualsByUnequalEmailAddress() {
        AddressBook a = getResource("address_0.json", new TypeReference<AddressBook>() {});
        AddressBook b = getResource("address_14.json", new TypeReference<AddressBook>() {});

        boolean result = addressCompareService.compareEquals(a, b);

        Assertions.assertThat(result).isFalse();
    }

    @Test
    public void testCompareEqualsByUnequalCompany() {
        AddressBook a = getResource("address_0.json", new TypeReference<AddressBook>() {});
        AddressBook b = getResource("address_15.json", new TypeReference<AddressBook>() {});

        boolean result = addressCompareService.compareEquals(a, b);

        Assertions.assertThat(result).isFalse();
    }

    @Test
    public void testCompareEqualsByUnequalLocationType() {
        AddressBook a = getResource("address_0.json", new TypeReference<AddressBook>() {});
        AddressBook b = getResource("address_16.json", new TypeReference<AddressBook>() {});

        boolean result = addressCompareService.compareEquals(a, b);

        Assertions.assertThat(result).isFalse();
    }

}
