package com.qpp.cgp.service.order.mccs;

import com.google.common.collect.ImmutableList;
import com.qpp.cgp.domain.product.SkuProduct;
import com.qpp.cgp.manager.order.mccs.MccsProductConfigManager;
import com.qpp.cgp.repository.product.ProductRepository;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;

/**
 * <AUTHOR>
 * @date 2024/5/18
 */
@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
public class MccsProductServiceTestIsAnyMccsProduct {

    @Spy
    @InjectMocks
    private MccsProductService mccsProductService;

    @Mock
    private MccsProductConfigManager mccsProductConfigManager;

    @Mock
    private ProductRepository productRepository;

    @Test
    public void testByIsTrue() {
        SkuProduct skuProduct1 = new SkuProduct();
        skuProduct1.setId(11L);
        skuProduct1.setConfigurableProductId(1L);
        SkuProduct skuProduct2 = new SkuProduct();
        skuProduct2.setId(12L);
        skuProduct2.setConfigurableProductId(2L);
        SkuProduct skuProduct3 = new SkuProduct();
        skuProduct3.setId(13L);
        skuProduct3.setConfigurableProductId(3L);

        Mockito.when(productRepository.findById(eq(11L), anyBoolean())).thenReturn(Optional.of(skuProduct1));
        Mockito.when(productRepository.findById(eq(12L), anyBoolean())).thenReturn(Optional.of(skuProduct2));
        Mockito.when(productRepository.findById(eq(13L), anyBoolean())).thenReturn(Optional.of(skuProduct3));

        Mockito.when(mccsProductConfigManager.existsByProductId(eq(1L))).thenReturn(false);
        Mockito.when(mccsProductConfigManager.existsByProductId(eq(2L))).thenReturn(true);
        Mockito.when(mccsProductConfigManager.existsByProductId(eq(3L))).thenReturn(false);
        Mockito.when(mccsProductConfigManager.existsByProductId(eq(11L))).thenReturn(false);
        Mockito.when(mccsProductConfigManager.existsByProductId(eq(12L))).thenReturn(false);
        Mockito.when(mccsProductConfigManager.existsByProductId(eq(13L))).thenReturn(false);

        Assertions.assertThat(mccsProductService.isAnyMccsProduct(ImmutableList.of(11L, 12L, 13L))).isTrue();
    }

    @Test
    public void testByIsFalse() {
        SkuProduct skuProduct1 = new SkuProduct();
        skuProduct1.setId(11L);
        skuProduct1.setConfigurableProductId(1L);
        SkuProduct skuProduct2 = new SkuProduct();
        skuProduct2.setId(12L);
        skuProduct2.setConfigurableProductId(2L);
        SkuProduct skuProduct3 = new SkuProduct();
        skuProduct3.setId(13L);
        skuProduct3.setConfigurableProductId(3L);

        Mockito.when(productRepository.findById(eq(11L), anyBoolean())).thenReturn(Optional.of(skuProduct1));
        Mockito.when(productRepository.findById(eq(12L), anyBoolean())).thenReturn(Optional.of(skuProduct2));
        Mockito.when(productRepository.findById(eq(13L), anyBoolean())).thenReturn(Optional.of(skuProduct3));

        Mockito.when(mccsProductConfigManager.existsByProductId(anyLong())).thenReturn(false);

        Assertions.assertThat(mccsProductService.isAnyMccsProduct(ImmutableList.of(11L, 12L, 13L))).isFalse();
    }
}
