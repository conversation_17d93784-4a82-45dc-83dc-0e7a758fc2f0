package com.qpp.cgp.service.order.third;

import com.qpp.cgp.manager.order.ThirdOrderManager;
import com.qpp.core.exception.BusinessException;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.anyString;

/**
 * <AUTHOR>
 * @date 2022/7/21
 */
@ExtendWith(MockitoExtension.class)
public class ThirdOrderServiceFindByOrderIdOrOrderNumberErrorTest {

    @InjectMocks
    private ThirdOrderService thirdOrderService;

    @Mock
    private ThirdOrderManager thirdOrderManager;

    @Test
    public void testByOrderIsNotExists() {
        String id = "D22207130001";

        Mockito.when(thirdOrderManager.findByOrderIdOrOrderNumber(anyString())).thenReturn(null);

        Assert.assertThrows(BusinessException.class, () -> {
            thirdOrderService.findByOrderIdOrOrderNumber(id);
        });
    }

}
