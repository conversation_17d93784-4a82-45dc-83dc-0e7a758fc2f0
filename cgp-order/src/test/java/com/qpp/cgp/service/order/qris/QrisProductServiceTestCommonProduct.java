package com.qpp.cgp.service.order.qris;

import com.google.common.collect.ImmutableList;
import com.qpp.cgp.domain.order.OrderLineItem;
import com.qpp.cgp.domain.order.ProductSetOrderLineItem;
import com.qpp.cgp.domain.order.QrisProductConfig;
import com.qpp.cgp.domain.product.ConfigurableProduct;
import com.qpp.cgp.domain.product.SkuProduct;
import com.qpp.cgp.manager.order.QrisProductConfigManager;
import com.qpp.cgp.manager.product.ProductManager;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.anyLong;

/**
 * <AUTHOR>
 * @date 2021/11/16
 */
@RunWith(MockitoJUnitRunner.class)
public class QrisProductServiceTestCommonProduct {

    @Spy
    @InjectMocks
    private QrisProductService qrisProductService;

    @Mock
    private QrisProductConfigManager qrisProductConfigManager;

    @Mock
    private ProductManager productManager;

    @Test
    public void testAllProductIsQrisProductTrue() {
        QrisProductConfig qrisProductConfig = new QrisProductConfig();
        qrisProductConfig.setSkuProductIds(ImmutableList.of(1L));
        qrisProductConfig.setConfigurableProductIds(ImmutableList.of(3L));

        Mockito.when(qrisProductConfigManager.findAll())
                .thenReturn(ImmutableList.of(qrisProductConfig));

        SkuProduct product1 = new SkuProduct();
        product1.setId(1L);
        OrderLineItem orderLineItem1 = new OrderLineItem();
        orderLineItem1.setProduct(product1);

        ConfigurableProduct configurableProduct = new ConfigurableProduct();
        configurableProduct.setId(3L);
        SkuProduct product2 = new SkuProduct();
        product2.setId(2L);
        product2.setConfigurableProduct(configurableProduct);
        OrderLineItem orderLineItem2 = new OrderLineItem();
        orderLineItem2.setProduct(product2);

        Mockito.when(productManager.getById(anyLong())).thenAnswer(invocation -> {
            long productId = invocation.getArgument(0);
            if (productId == 1L) {
                return product1;
            } else {
                return product2;
            }
        });

        boolean result = qrisProductService.allProductIsQrisProduct(ImmutableList.of(orderLineItem1, orderLineItem2));
        Assertions.assertThat(result).isTrue();
    }

    @Test
    public void testAllProductIsQrisProductThatSkuProductIsNotConfig() {
        QrisProductConfig qrisProductConfig = new QrisProductConfig();
        qrisProductConfig.setConfigurableProductIds(ImmutableList.of(3L));

        Mockito.when(qrisProductConfigManager.findAll())
                .thenReturn(ImmutableList.of(qrisProductConfig));

        SkuProduct product1 = new SkuProduct();
        product1.setId(1L);
        OrderLineItem orderLineItem1 = new OrderLineItem();
        orderLineItem1.setProduct(product1);

        ConfigurableProduct configurableProduct = new ConfigurableProduct();
        configurableProduct.setId(3L);
        SkuProduct product2 = new SkuProduct();
        product2.setId(2L);
        product2.setConfigurableProduct(configurableProduct);
        OrderLineItem orderLineItem2 = new OrderLineItem();
        orderLineItem2.setProduct(product2);

        Mockito.when(productManager.getById(anyLong())).thenAnswer(invocation -> {
            long productId = invocation.getArgument(0);
            if (productId == 1L) {
                return product1;
            } else {
                return product2;
            }
        });

        boolean result = qrisProductService.allProductIsQrisProduct(ImmutableList.of(orderLineItem1, orderLineItem2));
        Assertions.assertThat(result).isFalse();
    }

    @Test
    public void testAllProductIsQrisProductThatIsConfigurableProductIsNotConfig() {
        QrisProductConfig qrisProductConfig = new QrisProductConfig();
        qrisProductConfig.setSkuProductIds(ImmutableList.of(1L));

        Mockito.when(qrisProductConfigManager.findAll())
                .thenReturn(ImmutableList.of(qrisProductConfig));

        SkuProduct product1 = new SkuProduct();
        product1.setId(1L);
        OrderLineItem orderLineItem1 = new OrderLineItem();
        orderLineItem1.setProduct(product1);

        ConfigurableProduct configurableProduct = new ConfigurableProduct();
        configurableProduct.setId(3L);
        SkuProduct product2 = new SkuProduct();
        product2.setId(2L);
        product2.setConfigurableProduct(configurableProduct);
        OrderLineItem orderLineItem2 = new OrderLineItem();
        orderLineItem2.setProduct(product2);

        Mockito.when(productManager.getById(anyLong())).thenAnswer(invocation -> {
            long productId = invocation.getArgument(0);
            if (productId == 1L) {
                return product1;
            } else {
                return product2;
            }
        });

        boolean result = qrisProductService.allProductIsQrisProduct(ImmutableList.of(orderLineItem1, orderLineItem2));
        Assertions.assertThat(result).isFalse();
    }
}
