package com.qpp.cgp.service.order.partner;

import com.qpp.cgp.domain.dto.partner.PartnerOrderDTO;
import com.qpp.cgp.domain.order.Order;
import com.qpp.cgp.domain.order.PartnerOrderStatus;
import com.qpp.cgp.domain.partner.Partner;
import com.qpp.cgp.manager.order.QPMNOrderManager;
import com.qpp.cgp.manager.partner.qpmn.PartnerFinder;
import com.qpp.core.dto.FilterDTO;
import com.qpp.core.dto.PageDTO;
import com.qpp.core.dto.SortDTO;
import com.qpp.core.exception.BusinessException;
import com.qpp.web.business.sort.SortProcessor;
import org.assertj.core.api.Assertions;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.domain.PageRequest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@RunWith(PowerMockRunner.class)
@PrepareForTest({PartnerOrderDTOFinder.class})
@PowerMockIgnore({"javax.management.*", "org.w3c.dom.*", "org.apache.log4j.*", "org.xml.sax.*", "javax.xml.*", "javax.script.*"})
public class PartnerOrderDTOFinderTest {

    @InjectMocks
    private PartnerOrderDTOFinder partnerOrderDTOFinder;

    @Mock
    private QPMNOrderManager qpmnOrderManager;

    @Mock
    private PartnerFinder partnerFinder;

    @Mock
    private PartnerOrderFilter partnerOrderFilter;

    @Mock
    private PartnerOrderDTOConvertor convertor;

    @Mock
    private PartnerOrderStatusMapBuilder mapBuilder;

    @Before
    public void setUp() {
        partnerOrderDTOFinder = PowerMockito.spy(partnerOrderDTOFinder);
    }

    @Test
    public void testListThenThrow() {
        // input
        int page = 1;
        int limit = 1;
        String sort = null;
        String filter = null;
        // mock

        // invoke
        Assertions.assertThatExceptionOfType(BusinessException.class)
                .isThrownBy(() -> partnerOrderDTOFinder.list(page, limit, sort, filter));
        // verify
        Mockito.verify(partnerOrderDTOFinder).list(page, limit, sort, filter);
        Mockito.verify(partnerFinder).getByCurrentUser();
    }

    @Test
    public void testList() throws Exception {
        // input
        int page = 1;
        int limit = 1;
        String sort = null;
        String filter = null;
        // mock
        Partner partner = new Partner();
        partner.setId(1L);
        Mockito.when(partnerFinder.getByCurrentUser()).thenReturn(partner);
        List<FilterDTO> filterDTOList = new ArrayList<>();
        PowerMockito.when(partnerOrderDTOFinder, "processFilters", filter, partner).thenReturn(filterDTOList);
        Mockito.when(qpmnOrderManager.findAll(Mockito.any(), Mockito.eq(filterDTOList))).thenReturn(new PageDTO<>());
        // invoke
        partnerOrderDTOFinder.list(page, limit, sort, filter);
        // verify
        Mockito.verify(partnerOrderDTOFinder).list(page, limit, sort, filter);
        Mockito.verify(partnerFinder).getByCurrentUser();
        PowerMockito.verifyPrivate(partnerOrderDTOFinder).invoke("processFilters", filter, partner);
        Mockito.verify(qpmnOrderManager).findAll(Mockito.any(), Mockito.eq(filterDTOList));
        Mockito.verify(convertor).convert(null);
    }

    @Test
    public void testFindOrderByIdGivenNull() {

        // input
        String orderId = null;
        // mock

        // invoke
        PartnerOrderDTO retVal = partnerOrderDTOFinder.findOrderById(orderId);
        // verify
        Mockito.verify(partnerOrderDTOFinder).findOrderById(orderId);
        Mockito.verifyNoInteractions(partnerFinder, qpmnOrderManager, partnerOrderFilter, convertor);
        assert retVal == null;
    }

    @Test
    public void testFindOrderByIdGivenNotNull() {

        // input
        String orderId = "1";
        // mock
        Partner partner = new Partner();
        Mockito.when(partnerFinder.getByCurrentUser()).thenReturn(partner);
        Order order = new Order();
        Mockito.when(qpmnOrderManager.findById(orderId)).thenReturn(order);
        Mockito.when(partnerOrderFilter.filter(order, partner.getId())).thenReturn(order);
        HashMap<Long, PartnerOrderStatus> orderStatusIdMap = new HashMap<>();
        Mockito.when(mapBuilder.build()).thenReturn(orderStatusIdMap);
        // invoke
        PartnerOrderDTO retVal = partnerOrderDTOFinder.findOrderById(orderId);
        // verify
        Mockito.verify(partnerOrderDTOFinder).findOrderById(orderId);
        Mockito.verify(partnerFinder).getByCurrentUser();
        Mockito.verify(qpmnOrderManager).findById(orderId);
        Mockito.verify(partnerOrderFilter).filter(order, partner.getId());
        Mockito.verify(mapBuilder).build();
        Mockito.verify(convertor).convert(order, orderStatusIdMap);
        assert retVal == null;
    }
}