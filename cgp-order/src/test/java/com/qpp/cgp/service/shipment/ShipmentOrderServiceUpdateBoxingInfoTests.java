//package com.qpp.cgp.service.shipment;
//
//import com.qpp.cgp.CgpOrderTestApplication;
//import com.qpp.cgp.domain.dto.shipment.order.ShipmentOrderBoxingInfo;
//import com.qpp.cgp.domain.shipment.ShipmentBox;
//import com.qpp.cgp.domain.shipment.ShipmentOrder;
//import com.qpp.cgp.domain.shipment.ShipmentOrderHistory;
//import com.qpp.cgp.domain.shipment.ShipmentOrderStatus;
//import com.qpp.cgp.manager.shipment.ShipmentOrderManager;
//import com.qpp.cgp.service.shipment.ShipmentOrderService;
//import com.qpp.cgp.util.EmbeddedTestUtils;
//import com.qpp.core.exception.BusinessException;
//import com.qpp.id.generator.IdGenerator;
//import org.junit.After;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.context.ApplicationContext;
//import org.springframework.context.ApplicationEvent;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.test.context.junit4.SpringRunner;
//import org.springframework.test.util.ReflectionTestUtils;
//
//import java.io.IOException;
//import java.lang.reflect.Field;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR> Chiu
// * @Date 2021/4/1 16:48
// * @Description
// * @Version 1.0
// */
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = CgpOrderTestApplication.class)
//public class ShipmentOrderServiceUpdateBoxingInfoTests {
//
//    @Autowired
//    private IdGenerator idGenerator;
//
//    @Autowired
//    private ShipmentOrderManager shipmentOrderManager;
//
//    @Autowired
//    private ApplicationContext applicationContext;
//
//    private ShipmentOrderService shipmentOrderService;
//
//    private MongoTemplate mongoTemplate;
//
//    @Before
//    public void init() throws IOException, NoSuchFieldException, IllegalAccessException {
//        shipmentOrderService = preProcessEnvironment();
//        Field shipmentOrderManagerField = shipmentOrderService.getClass().getDeclaredField("shipmentOrderManager");
//        shipmentOrderManagerField.setAccessible(true);
//        ShipmentOrderManager shipmentOrderManager = (ShipmentOrderManager) shipmentOrderManagerField.get(shipmentOrderService);
//        Field mongoTemplateField = shipmentOrderManager.getClass().getSuperclass().getDeclaredField("mongoTemplate");
//        mongoTemplateField.setAccessible(true);
//        mongoTemplate = (MongoTemplate) mongoTemplateField.get(shipmentOrderManager);
//    }
//
//    @Test(expected = BusinessException.class)
//    public void testUpdateBoxingInfo() throws IOException, NoSuchFieldException, IllegalAccessException {
//        //0、boxingInfo为空
//        ShipmentOrderBoxingInfo boxingInfo = null;
//        ShipmentOrder shipmentOrder = shipmentOrderService.update(boxingInfo);
//
//    }
//
//    @Test(expected = BusinessException.class)
//    public void testByNullOrderId() {
//        //1、boxingInfo中未关联发货订单Id
//        ShipmentOrderBoxingInfo emptyShipmentOrderBoxingInfo = new ShipmentOrderBoxingInfo();
//        shipmentOrderService.update(emptyShipmentOrderBoxingInfo);
//    }
//
//    @Test
//    public void testByNotExistsOrderId() {
//        //2、boxingInfo关联的发货订单Id不存在对应的发货订单
//        ShipmentOrderBoxingInfo shipmentOrderBoxingInfoWithNotExistsOrderId = new ShipmentOrderBoxingInfo();
//        shipmentOrderBoxingInfoWithNotExistsOrderId.setShipmentOrderId(13579L);
//        try {
//            ShipmentOrder shipmentOrderWithNotExistsOrderID = shipmentOrderService.update(shipmentOrderBoxingInfoWithNotExistsOrderId);
//        } catch (Exception e) {
//            Assert.assertTrue(e.getMessage().contains("is not exists!"));
//            return;
//        }
//        throw new BusinessException("方法执行过程中未抛出期待异常");
//    }
//
//    @Test
//    public void testByExistsOrderIdAndErrorStatus() {
//        //3、boxing中关联的发货订单未处于待装箱或已装箱状态
//        ShipmentOrderBoxingInfo boxingInfoWithErrorStatusOrder = new ShipmentOrderBoxingInfo();
//        boxingInfoWithErrorStatusOrder.setShipmentOrderId(12345L);
//        try {
//            ShipmentOrder shipmentOrderWithErrorStatusOrder = shipmentOrderService.update(boxingInfoWithErrorStatusOrder);
//        } catch (Exception e) {
//            Assert.assertEquals("The shipmentOrder with ID < 12345 > is not in the state of WAITING_BOXING_STATUS or BOXED_STATUS. It is forbidden to modify the shipmentOrderBoxingInfo!", e.getMessage());
//            return;
//        }
//        throw new BusinessException("方法执行过程中未抛出期待异常");
//
//    }
//
//    @Test
//    public void testByEmptyBoxes() {
//        //4、boxingInfo中未包含有任何装箱信息
//        ShipmentOrderBoxingInfo boxingInfoWithNullBoxes = new ShipmentOrderBoxingInfo();
//        boxingInfoWithNullBoxes.setShipmentOrderId(12346L);
//        try {
//            ShipmentOrder shipmentOrderWithNullBoxes = shipmentOrderService.update(boxingInfoWithNullBoxes);
//        } catch (Exception e) {
//            Assert.assertEquals("The shipmentOrderBoxingInfo does not contain any shipmentBox!", e.getMessage());
//            return;
//        }
//        throw new BusinessException("方法执行过程中未抛出期待异常");
//    }
//
//    @Test
//    public void testByNormalShipmentOrderBoxingInfo() {
//        ShipmentOrder shipmentOrderAfterUpdate = updateShipmentOrderBoxingInfo();
//        Assert.assertTrue(shipmentOrderAfterUpdate.getStatusId().equals(ShipmentOrderStatus.BOXED_STATUS));
//    }
//
//    @Test
//    public void testAfterUpdate() throws NoSuchFieldException, IllegalAccessException {
//
//        ReflectionTestUtils.setField(shipmentOrderService, "applicationContext", applicationContext);
//
//        Class<?> superclass = shipmentOrderManager.getClass().getSuperclass();
//        Field mongoTemplateField = superclass.getDeclaredField("mongoTemplate");
//        mongoTemplateField.setAccessible(true);
//        mongoTemplateField.set(shipmentOrderManager, mongoTemplate);
//
//        ShipmentOrder shipmentOrder = updateShipmentOrderBoxingInfo();
//        List<ShipmentOrderHistory> shipmentOrderHistoryList = mongoTemplate.findAll(ShipmentOrderHistory.class);
//        Assert.assertEquals(3, shipmentOrderHistoryList.size());
//    }
//
//    private ShipmentOrder updateShipmentOrderBoxingInfo() {
//        //5、boxingInfo正常
//        ShipmentOrderBoxingInfo normalBoxingInfo = new ShipmentOrderBoxingInfo();
//        normalBoxingInfo.setShipmentOrderId(12346L);
//        ShipmentBox shipmentBox = new ShipmentBox();
//        shipmentBox.setId(23456L);
//        List<ShipmentBox> boxes = new ArrayList<>(16);
//        boxes.add(shipmentBox);
//        normalBoxingInfo.setBoxes(boxes);
//
//        ShipmentOrder shipmentOrderBeforeUpdate = mongoTemplate.findById(12346L, ShipmentOrder.class);
//        Assert.assertTrue(shipmentOrderBeforeUpdate.getStatusId().equals(ShipmentOrderStatus.WAITING_BOXING_STATUS) || shipmentOrderBeforeUpdate.getStatusId().equals(ShipmentOrderStatus.BOXED_STATUS));
//
//        ShipmentOrder normalShipmentOrder = shipmentOrderService.update(normalBoxingInfo);
//
//        return mongoTemplate.findById(12346L, ShipmentOrder.class);
//    }
//
//    private ShipmentOrderService preProcessEnvironment() throws IOException {
//        MongoTemplate mongoTemplate = EmbeddedTestUtils.startEmbeddedMongo("test", "shipmentorders", "classpath:com/qpp/cgp/manager/shipment/ShipmentOrderServiceTestsData4ShipmentOrder.txt");
//        ShipmentOrderManager shipmentOrderManager = new ShipmentOrderManager(mongoTemplate, idGenerator);
//        ShipmentOrderService shipmentOrderService = new ShipmentOrderService();
//        ReflectionTestUtils.setField(shipmentOrderService, "shipmentOrderManager", shipmentOrderManager);
//        ReflectionTestUtils.setField(shipmentOrderService, "idGenerator", idGenerator);
//        return shipmentOrderService;
//    }
//
//    @After
//    public void destroy(){
//        EmbeddedTestUtils.destroyMongo();
//    }
//
//}
