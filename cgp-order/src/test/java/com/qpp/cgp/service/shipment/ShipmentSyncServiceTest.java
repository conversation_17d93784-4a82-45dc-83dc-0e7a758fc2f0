package com.qpp.cgp.service.shipment;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import com.qpp.cgp.domain.dto.shipment.ShipmentRequestDTO;
import com.qpp.cgp.domain.shipment.ShipmentOrder;
import com.qpp.cgp.dto.shipment.ShipmentUnifiedResult;
import com.qpp.cgp.service.remote.ShipmentUnifiedRemoteService;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

@ExtendWith(MockitoExtension.class)
public class ShipmentSyncServiceTest {
    @Spy
    @InjectMocks
    private ShipmentSyncService shipmentSyncService;

    @Mock
    private ShipmentRequestDTOGenerator shipmentRequestDTOGenerator;

    @Mock
    private ShipmentUnifiedRemoteService shipmentUnifiedRemoteService;

    @Test
    public void testSetShipmentNo() {
        ObjectMapper objectMapper = new ObjectMapper();
        Field objectMapper1 = ReflectionUtils.findField(ShipmentSyncService.class, "objectMapper");
        objectMapper1.setAccessible(true);
        ReflectionUtils.setField(objectMapper1, shipmentSyncService, objectMapper);
        ShipmentOrder shipmentOrder = new ShipmentOrder();
        shipmentOrder.setId(123L);

        JsonNode jsonNode = new TextNode("{\"shipmentNo\":\"102406048372S01\"}");

        ShipmentUnifiedResult shipmentUnifiedResult = new ShipmentUnifiedResult(true, null, jsonNode);

        Mockito.when(shipmentRequestDTOGenerator.generate(eq(shipmentOrder))).thenReturn(new ShipmentRequestDTO());
        Mockito.when(shipmentUnifiedRemoteService.sendShipment(eq(shipmentOrder.getId()), any())).thenReturn(shipmentUnifiedResult);
        shipmentSyncService.transformAndCall(shipmentOrder);
        Assertions.assertThat(shipmentOrder.getShipmentNo()).isEqualTo("102406048372S01");
    }

}