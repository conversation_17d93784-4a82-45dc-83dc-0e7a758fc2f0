package com.qpp.cgp.repository.dto.order;

import com.qpp.cgp.CgpOrderTestApplication;
import com.qpp.cgp.domain.dto.order.detail.DetailOrderDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR> <PERSON>
 * @Date 2021/2/6 11:41
 * @Description
 * @Version 1.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CgpOrderTestApplication.class)
public class OrderDTORepositoryTest {

    @Autowired
    private OrderDTORepository orderDTORepository;

    /**
     * 断言：
     *  1、传入null
     *      期望：程序正常结束，返回一个空对象(非null，但是没有任何数据)
     */
    @Test
    public void getDetailOrderDTOTest(){
        DetailOrderDTO detailOrderDTO = orderDTORepository.getDetailOrderDTO(null);
        Assert.assertNull(detailOrderDTO);
    }

}
