package com.qpp.cgp.repository.order;

import com.google.common.collect.ImmutableList;
import com.qpp.cgp.domain.order.OrderLineItem;
import com.qpp.mongo.driver.HybridMongoTemplate;
import org.mockito.Mockito;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.Set;

import static org.testng.Assert.*;

public class OrderLineItemRepositoryTest {

    @Test
    public void testGetProductIdsByOrderId() {
        HybridMongoTemplate mongoTemplate = Mockito.mock(HybridMongoTemplate.class);
        OrderLineItemRepository orderLineItemRepository = new OrderLineItemRepository(mongoTemplate, null);
        String orderId = "123";
        OrderLineItem orderLineItem = new OrderLineItem();
        orderLineItem.setProductId(11L);
        Query query = Query.query(Criteria.where("order._id").is(orderId));
        query.fields().include("productId");
        Mockito.doReturn(ImmutableList.of(orderLineItem)).when(mongoTemplate).find(query, OrderLineItem.class);
        Set<Long> productIds = orderLineItemRepository.getProductIdsByOrderId(orderId);
        Mockito.verify(mongoTemplate, Mockito.times(1)).find(query, OrderLineItem.class);
        Assert.assertEquals(productIds.size(), 1);
        Assert.assertTrue(productIds.contains(11L));
    }
}