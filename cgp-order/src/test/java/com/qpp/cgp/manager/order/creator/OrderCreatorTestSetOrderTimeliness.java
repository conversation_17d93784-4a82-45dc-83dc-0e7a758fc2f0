package com.qpp.cgp.manager.order.creator;

import com.qpp.cgp.domain.order.Order;
import com.qpp.cgp.service.common.CommonPropertiesService;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @date 2024/6/6
 */
@ExtendWith(MockitoExtension.class)
public class OrderCreatorTestSetOrderTimeliness {

    @Spy
    @InjectMocks
    private OrderCreator orderCreator;

    @Mock
    private CommonPropertiesService commonPropertiesService;

    @Test
    public void testSetOrderTimeliness() {
        Date datePurchased = new Date();
        Order order = new Order();
        order.setDatePurchased(datePurchased);

        Mockito.when(commonPropertiesService.getKeyValueAndApplicationMode(any())).thenReturn("30");

        // 调用需要测试的方法
        orderCreator.setOrderTimeliness(order);

        Instant orderTimeliness = datePurchased.toInstant().plus(30, ChronoUnit.MINUTES);
        Assertions.assertThat(order.getOrderTimeliness()).isEqualTo(orderTimeliness);
    }
}
