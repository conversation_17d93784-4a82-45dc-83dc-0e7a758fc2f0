//package com.qpp.cgp.manager.order;
//
//import com.qpp.cgp.domain.dto.ordeitem.PartnerSettleItemDTO;
//import com.qpp.core.dto.FilterDTO;
//import com.qpp.core.dto.PageDTO;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.data.domain.PageRequest;
//import org.springframework.data.domain.Sort;
//import org.springframework.test.context.junit4.SpringRunner;
//
//
//import java.text.SimpleDateFormat;
//import java.time.Instant;
//import java.time.YearMonth;
//import java.time.temporal.ChronoUnit;
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//
//@RunWith(SpringRunner.class)
//@SpringBootTest
//
//public class PartnerSettleItemManagerTest {
//
//    @Autowired
//    private PartnerSettleItemManager partnerSettleItemManager;
//
//    @Test
//    public void getWaitingSettleOrders() {
//        YearMonth yearMonth = YearMonth.of(2018, 8);
//        yearMonth = yearMonth.plusMonths(1);
//        String mon = String.valueOf(yearMonth.getMonthValue());
//        if (mon.length() < 2) {
//            mon = "0" + mon;
//        }
//        Instant lastInstant = Instant.parse(yearMonth.getYear() + "-" + mon + "-" + "01" + "T00:00:00Z");
//        List<FilterDTO> filters = new ArrayList<>();
//        FilterDTO filter = new FilterDTO();
//        filter.setName("order.receivedDate@to");
//        filter.setType("date");
//        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        // partnerSettleItemManager.getWaitingSettleOrders(filters, pageRequest)查询会包括查询的当天，所以config.getDateInterval()需要在加1
//        filter.setValue(format.format(new Date(lastInstant.minus(8 + 1, ChronoUnit.DAYS).toEpochMilli())));
//        filters.add(filter);
//        filter = new FilterDTO();
//        filter.setType("boolean");
//        filter.setValue("false");
//        filter.setName("order.isTest");
//        filters.add(filter);
//
//        filter = new FilterDTO();
//        filter.setName("order.partner.id");
//        filter.setValue(200784);
//        filter.setType("number");
//        filters.add(filter);
//
//        Sort sort = Sort.by(Sort.Direction.ASC, "order.receivedDate");
//        PageRequest pageRequest = PageRequest.of(0, Integer.MAX_VALUE, sort);
//
//        PageDTO<PartnerSettleItemDTO> page = partnerSettleItemManager.getWaitingSettleOrders(filters, pageRequest);
//        List<PartnerSettleItemDTO> list = (List<PartnerSettleItemDTO>) page.getContent();
//        list.forEach(System.out::println);
//        list.forEach(elem -> {
//            //List<OrderStatusHistory> orderStatusHistories = elem.getStatusHistories();
//            //System.out.println(orderStatusHistories);
//            System.out.println(elem.getPrice());
//            System.out.println(elem.getQty());
//            System.out.println("=================================");
//        });
//    }
//}