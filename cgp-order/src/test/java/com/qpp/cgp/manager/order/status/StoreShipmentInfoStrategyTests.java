package com.qpp.cgp.manager.order.status;

import com.qpp.cgp.CgpOrderTestApplication;
import com.qpp.cgp.domain.dto.order.OrderStatusUpdateDTO;
import com.qpp.cgp.domain.order.Order;
import com.qpp.cgp.domain.order.OrderStatus;
import com.qpp.cgp.domain.order.ShipmentInfo;
import com.qpp.cgp.manager.order.OrderManager;
import com.qpp.cgp.repository.common.WebsiteRepository;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/4/7 18:17
 * @Description
 * @Version 1.0
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = CgpOrderTestApplication.class)
public class StoreShipmentInfoStrategyTests {

    @Autowired
    private StoreShipmentInfoStrategy storeShipmentInfoStrategy;

    @Test
    public void testIsMatchWithNewStatus4DELIVERED_STATUS(){
        OrderStatus newStatus = new OrderStatus();
        newStatus.setId(OrderStatus.DELIVERED_STATUS);
        Boolean match = storeShipmentInfoStrategy.isMatch(new OrderStatus(), newStatus, new Order());
        Assert.assertTrue(match);
    }

    @Test
    public void testIsMatchWithNewStatus4Other(){
        OrderStatus newStatus = new OrderStatus();
        newStatus.setId(OrderStatus.REDO_APPLY_STATUS);
        Boolean match = storeShipmentInfoStrategy.isMatch(new OrderStatus(), newStatus, new Order());
        Assert.assertFalse(match);
    }

    @Test
    public void testUpdateWithRightNewStatus() throws IOException {
        Date deliveryDate = new Date();
        String deliveryNo = "1314159";
        OrderManager orderManager = Mockito.mock(OrderManager.class);
        WebsiteRepository websiteRepository = Mockito.mock(WebsiteRepository.class);
        ReflectionTestUtils.setField(storeShipmentInfoStrategy, "websiteRepository", websiteRepository);
        ReflectionTestUtils.setField(storeShipmentInfoStrategy, "orderManager", orderManager);
        OrderStatusUpdateDTO dto = new OrderStatusUpdateDTO();
        ShipmentInfo shipmentInfo = new ShipmentInfo();
        shipmentInfo.setDeliveryDate(deliveryDate);
        shipmentInfo.setDeliveryNo(deliveryNo);
        dto.setShipmentInfo(shipmentInfo);
        Order order = new Order();
        storeShipmentInfoStrategy.update(dto, order, false, false, new Date());
        Assert.assertTrue(order.getDeliveryNo().equals(deliveryNo) && order.getDeliveryDate().equals(deliveryDate));
    }

    @Test
    public void testUpdateByShipmentInfoWithinOrder() throws IOException {
        Date deliveryDate = new Date(132465498798L);
        String deliveryNo = "1314159";
        OrderManager orderManager = Mockito.mock(OrderManager.class);
        WebsiteRepository websiteRepository = Mockito.mock(WebsiteRepository.class);
        ReflectionTestUtils.setField(storeShipmentInfoStrategy, "websiteRepository", websiteRepository);
        OrderStatusUpdateDTO dto = new OrderStatusUpdateDTO();
        Order order = new Order();
        Date updateDate = new Date(1254643464673L);
        ShipmentInfo shipmentInfo = new ShipmentInfo();
        shipmentInfo.setDeliveryNo(deliveryNo);
        shipmentInfo.setDeliveryDate(updateDate);
        order.setShipmentInfo(shipmentInfo);
        Mockito.when(orderManager.saveShipmentInfo(order, updateDate)).thenReturn(shipmentInfo);
        ReflectionTestUtils.setField(storeShipmentInfoStrategy, "orderManager", orderManager);
        storeShipmentInfoStrategy.update(dto, order, false, false, updateDate);
        Assert.assertTrue(order.getDeliveryNo().equals(deliveryNo) && order.getDeliveryDate().equals(updateDate));
    }

    @Test
    public void testUpdateByNullShipmentInfo() throws IOException {
        OrderManager orderManager = Mockito.mock(OrderManager.class);
        WebsiteRepository websiteRepository = Mockito.mock(WebsiteRepository.class);
        ReflectionTestUtils.setField(storeShipmentInfoStrategy, "websiteRepository", websiteRepository);
        ReflectionTestUtils.setField(storeShipmentInfoStrategy, "orderManager", orderManager);
        OrderStatusUpdateDTO dto = new OrderStatusUpdateDTO();
        Order order = new Order();
        Date updateDate = new Date(1254643464673L);
        storeShipmentInfoStrategy.update(dto, order, false, false, updateDate);
        Assert.assertTrue(order.getDeliveryDate().equals(updateDate));
    }
}
