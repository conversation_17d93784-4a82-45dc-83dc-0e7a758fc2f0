package com.qpp.cgp.manager.listener.order;

import com.qpp.cgp.domain.order.Order;
import com.qpp.cgp.domain.order.OrderStatus;
import com.qpp.cgp.event.order.OrderStatusChangedEvent;
import com.qpp.cgp.event.order.OrderStatusChangedEventContext;
import com.qpp.cgp.manager.shipment.ShipmentRequirementManager;
import com.qpp.cgp.repository.order.OrderLineItemRepository;
import com.qpp.cgp.service.order.mccs.MccsShipmentService;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @date 2022/7/14
 */
@RunWith(MockitoJUnitRunner.Silent.class)
public class OrderDeliveredListenerTest {

    @Spy
    @InjectMocks
    private OrderDeliveredListener orderDeliveredListener;

    @Mock
    private MccsShipmentService mccsShipmentService;

    @Test
    public void testDeliveredByOrderIsNotDeliveredStatus() {
        ArgumentCaptor<String> orderIdCaptor = ArgumentCaptor.forClass(String.class);

        Mockito.doNothing().when(mccsShipmentService).delivered(orderIdCaptor.capture());

        OrderStatus status = new OrderStatus();
        status.setId(OrderStatus.CHECKED_PRINT_STATUS);
        Order order = new Order();
        order.setId("1");
        order.setStatus(status);
        OrderStatusChangedEventContext context = new OrderStatusChangedEventContext();
        context.setOrder(order);
        OrderStatusChangedEvent<OrderStatusChangedEventContext> event =  new OrderStatusChangedEvent<>(context);

        orderDeliveredListener.delivered(event);

        Assertions.assertThat(orderIdCaptor.getAllValues().size()).isEqualTo(0);
    }

    @Test
    public void testDeliveredByOrderIsDeliveredStatus() {
        ArgumentCaptor<String> orderIdCaptor = ArgumentCaptor.forClass(String.class);

        Mockito.doNothing().when(mccsShipmentService).delivered(orderIdCaptor.capture());

        OrderStatus status = new OrderStatus();
        status.setId(OrderStatus.DELIVERED_STATUS);
        Order order = new Order();
        order.setId("1");
        order.setStatus(status);
        OrderStatusChangedEventContext context = new OrderStatusChangedEventContext();
        context.setOrder(order);
        OrderStatusChangedEvent<OrderStatusChangedEventContext> event =  new OrderStatusChangedEvent<>(context);

        orderDeliveredListener.delivered(event);

        Assertions.assertThat(orderIdCaptor.getAllValues().size()).isEqualTo(1);
    }
}
