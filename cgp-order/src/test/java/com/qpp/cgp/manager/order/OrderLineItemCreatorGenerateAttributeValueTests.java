package com.qpp.cgp.manager.order;

import com.qpp.cgp.CgpOrderTestApplication;
import com.qpp.cgp.domain.order.OrderLineItemProductAttributeValue;
import com.qpp.cgp.manager.order.creator.OrderLineItemCreator;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> Chiu
 * @Date 2021/3/4 14:31
 * @Description
 * @Version 1.0
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = CgpOrderTestApplication.class)
public class OrderLineItemCreatorGenerateAttributeValueTests {

    @Autowired
    private OrderLineItemCreator orderLineItemCreator;

    @Test
    public void testByNullProduct(){
        try {
            List<OrderLineItemProductAttributeValue> list = orderLineItemCreator.generateAttributeValue(null, Optional.empty());
        } catch (Exception e) {
            Assert.assertEquals("Parameter：product can not be null!", e.getMessage());
        }
    }

}
