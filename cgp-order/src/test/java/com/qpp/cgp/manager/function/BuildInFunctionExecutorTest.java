package com.qpp.cgp.manager.function;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableList;
import com.qpp.cgp.domain.bom.runtime.RtObject;
import com.qpp.cgp.domain.funtion.CalculateValue;
import com.qpp.cgp.domain.funtion.StaticValue;
import com.qpp.cgp.domain.funtion.calender.*;
import com.qpp.cgp.manager.bom.RtObjectManager;
import com.qpp.cgp.value.ConstantValue;
import com.qpp.cgp.value.calculator.ValueExCalculateService;
import com.qpp.core.context.SpringApplicationContext;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.HashMap;
import java.util.Map;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"javax.management.*", "org.w3c.dom.*", "org.apache.log4j.*", "org.xml.sax.*", "javax.xml.*", "javax.script.*"})
@PrepareForTest(SpringApplicationContext.class)
public class BuildInFunctionExecutorTest {

    BuildInFunctionExecutor buildInFunctionExecutor = Mockito.spy(new BuildInFunctionExecutor());

    @Test
    public void testExecutor() {
        GetMonthCalendarInput getMonthCalendarInput = new GetMonthCalendarInput();
        StaticValue staticValue = new StaticValue();
        Map<String, Object> context = new HashMap<>();
        GetMonthCalendarOutput getMonthCalendarOutput = new GetMonthCalendarOutput();
        GetMonthCalendarExecutor getMonthCalendarExecutor = Mockito.mock(GetMonthCalendarExecutor.class);
        Mockito.doReturn(getMonthCalendarExecutor).when(buildInFunctionExecutor).getFunctionByName("getMonthCalendarMethod");
        Mockito.doReturn(getMonthCalendarOutput).when(getMonthCalendarExecutor).execute(getMonthCalendarInput);
        Mockito.doReturn(getMonthCalendarInput).when(buildInFunctionExecutor).convertInputValue(staticValue, context, "getMonthCalendarMethod");
        Object result = buildInFunctionExecutor.executor("getMonthCalendarMethod", staticValue, context);
        Assert.assertNotNull(result);
        Assert.assertEquals(getMonthCalendarOutput, result);
        Mockito.verify(buildInFunctionExecutor).convertInputValue(staticValue, context, "getMonthCalendarMethod");
        Mockito.verify(buildInFunctionExecutor).getFunctionByName("getMonthCalendarMethod");
        Mockito.verify(getMonthCalendarExecutor).execute(getMonthCalendarInput);
    }

    @Test
    public void testGetFunctionByName() {
        String functionName = "getMonthCalendarMethod";
        PowerMockito.mockStatic(SpringApplicationContext.class);
        GetMonthCalendarExecutor getMonthCalendarExecutor = Mockito.mock(GetMonthCalendarExecutor.class);
        PowerMockito.when(SpringApplicationContext.getBean("getMonthCalendarExecutor")).thenReturn(getMonthCalendarExecutor);
        FunctionExecutor<BuiltInInputValue, BuiltInOutPut> functionExecutor = buildInFunctionExecutor.getFunctionByName(functionName);
        Assert.assertEquals(getMonthCalendarExecutor, functionExecutor);
        Assert.assertNotNull(functionExecutor);
    }

    @Test
    public void testConvertInputValue() {
        ObjectMapper objectMapper = new ObjectMapper();
        RtObjectManager rtObjectManager = Mockito.mock(RtObjectManager.class);
        ValueExCalculateService valueExCalculateService = Mockito.mock(ValueExCalculateService.class);
        Whitebox.setInternalState(buildInFunctionExecutor, "valueExCalculateService", valueExCalculateService);
        Whitebox.setInternalState(buildInFunctionExecutor, "objectMapper", objectMapper);
        Whitebox.setInternalState(buildInFunctionExecutor, "rtObjectManager", rtObjectManager);
        StaticValue staticValue = new StaticValue();
        RtObject rtObject = new RtObject();
        Map<String, Object> objectJson = new HashMap<>();
        objectJson.put("month", 12);
        objectJson.put("format", NameFormat.Full.toString());
        objectJson.put("languageId", 1213L);
        rtObject.setId("123");
        rtObject.setObjectJSON(objectJson);
        staticValue.setValue(rtObject);
        Map<String, Object> context = new HashMap<>();
        String functionName = "getMonthNameMethod";
        ConstantValue constantValue = new ConstantValue();
        constantValue.setValue("123");
        Mockito.when(rtObjectManager.findById(rtObject.getId())).thenReturn(rtObject);
        BuiltInInputValue builtInInputValue = buildInFunctionExecutor.convertInputValue(staticValue, context, functionName);
        Assert.assertNotNull(builtInInputValue);
        Assert.assertTrue(builtInInputValue instanceof GetMonthNameInput);
        Assert.assertEquals(12, ((GetMonthNameInput) builtInInputValue).getMonth());
        Assert.assertEquals(NameFormat.Full, ((GetMonthNameInput) builtInInputValue).getFormat());
        Assert.assertEquals(1213L, ((GetMonthNameInput) builtInInputValue).getLanguageId());
        Mockito.verify(rtObjectManager).findById(rtObject.getId());
        CalculateValue calculateValue = new CalculateValue();
        calculateValue.setValue(constantValue);
        Map<String, Object> getMonthCalendarInput = new HashMap<>();
        getMonthCalendarInput.put("year", 2021);
        getMonthCalendarInput.put("month", 10);
        getMonthCalendarInput.put("startOfWeek", 1);
        getMonthCalendarInput.put("columns", 6);
        getMonthCalendarInput.put("rows", 7);
        getMonthCalendarInput.put("languageId", 1233);
        getMonthCalendarInput.put("countryIds", ImmutableList.of(1111L));
        getMonthCalendarInput.put("extraCalendarTypes", ImmutableList.of(CalendarType.Gregorian.toString()));
        Mockito.when(valueExCalculateService.calculate(constantValue, context)).thenReturn(getMonthCalendarInput);
        BuiltInInputValue builtInInputValue1 = buildInFunctionExecutor.convertInputValue(calculateValue, context, "getMonthCalendarMethod");
        Mockito.verify(valueExCalculateService).calculate(constantValue, context);
        Assert.assertNotNull(builtInInputValue1);
        Assert.assertTrue(builtInInputValue1 instanceof GetMonthCalendarInput);
        Assert.assertEquals(2021, ((GetMonthCalendarInput) builtInInputValue1).getYear());
        Assert.assertEquals(10, ((GetMonthCalendarInput) builtInInputValue1).getMonth());
        Assert.assertEquals(1, ((GetMonthCalendarInput) builtInInputValue1).getStartOfWeek());
        Assert.assertEquals(1233L, ((GetMonthCalendarInput) builtInInputValue1).getLanguageId());
        Assert.assertEquals(6, ((GetMonthCalendarInput) builtInInputValue1).getColumns());
        Assert.assertEquals(7, ((GetMonthCalendarInput) builtInInputValue1).getRows());
        Assert.assertEquals(ImmutableList.of(1111L), ((GetMonthCalendarInput) builtInInputValue1).getCountryIds());
        Assert.assertEquals(ImmutableList.of(CalendarType.Gregorian), ((GetMonthCalendarInput) builtInInputValue1).getExtraCalendarTypes());
    }

    @Test
    public void getInputClazz() {
        Assert.assertEquals(GetMonthCalendarInput.class.getName(), buildInFunctionExecutor.getInputClazz("getMonthCalendarMethod"));
        Assert.assertEquals(GetMonthNameInput.class.getName(), buildInFunctionExecutor.getInputClazz("getMonthNameMethod"));
        Assert.assertEquals(GetWeekNameInput.class.getName(), buildInFunctionExecutor.getInputClazz("getWeekNameMethod"));
        Assert.assertEquals(GetAllCatalogInput.class.getName(),buildInFunctionExecutor.getInputClazz("getAllCatalogMethod"));
        Assert.assertEquals(GetAllProductCatalogInput.class.getName(),buildInFunctionExecutor.getInputClazz("getAllProductCatalogMethod"));
        Assert.assertEquals(GetProductDetailInput.class.getName(),buildInFunctionExecutor.getInputClazz("getProductDetailMethod"));
        Assert.assertEquals(GetCatalogInput.class.getName(),buildInFunctionExecutor.getInputClazz("getCatalogMethod"));
        Assert.assertEquals(GetProductsOfCatalogInput.class.getName(),buildInFunctionExecutor.getInputClazz("getProductsOfCatalogMethod"));
        Assert.assertEquals(GetProductDetailInput.class.getName(),buildInFunctionExecutor.getInputClazz("getProductDetailMethod"));
    }
}