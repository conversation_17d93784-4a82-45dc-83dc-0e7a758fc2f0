package com.qpp.cgp.manager.order;

import com.qpp.cgp.CgpOrderTestApplication;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/3/5 11:25
 * @Description
 * @Version 1.0
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = CgpOrderTestApplication.class)
public class OrderNotifyServiceTests {

    @Autowired
    private OrderNotifyService orderNotifyService;

    @Test
    public void testGetDefaultImageUrlByNullProduct(){
        try {
            String defaultImageUrl = orderNotifyService.getDefaultImageUrl(null);
        } catch (Exception e) {
            Assert.assertEquals("Parameter：product can not be null!", e.getMessage());
        }
    }

    @Test
    public void testGetMailVariableByNullOrder(){
        try {
            Map<String, Object> mailVariable = orderNotifyService.getMailVariable(null);
        } catch (Exception e) {
            Assert.assertEquals("Parameter：order can not be null!", e.getMessage());
        }
    }

    @Test
    public void testGetManagerMailVariable(){
        try {
            Map<String, Object> map = orderNotifyService.getManagerMailVariable(null);
        } catch (Exception e) {
            Assert.assertEquals("Parameter：order can not be null!", e.getMessage());
        }
    }

}
