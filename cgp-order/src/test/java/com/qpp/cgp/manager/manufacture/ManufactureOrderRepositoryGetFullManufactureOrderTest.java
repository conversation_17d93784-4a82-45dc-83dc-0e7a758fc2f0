package com.qpp.cgp.manager.manufacture;

import com.qpp.cgp.CgpOrderTestApplication;
import com.qpp.cgp.domain.manufacture.ManufactureOrder;
import com.qpp.cgp.repository.manufacture.ManufactureOrderRepository;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR> Chiu
 * @Date 2021/2/25 16:44
 * @Description
 * @Version 1.0
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = CgpOrderTestApplication.class)
public class ManufactureOrderRepositoryGetFullManufactureOrderTest {

    @Autowired
    private ManufactureOrderRepository manufactureOrderRepository;

    @Test
    public void testByNullManufactureOrder(){
        try {
            ManufactureOrder manufactureOrder = manufactureOrderRepository.getFullManufactureOrder(null);
        } catch (Exception e) {
            Assert.assertEquals("Parameter：manufactureOrder can not be null", e.getMessage());
        }
    }

}
