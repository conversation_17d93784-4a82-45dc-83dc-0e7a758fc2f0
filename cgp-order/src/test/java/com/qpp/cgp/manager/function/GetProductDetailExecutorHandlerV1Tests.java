package com.qpp.cgp.manager.function;

import com.qpp.cgp.domain.cms.record.EnvMode;
import com.qpp.cgp.domain.funtion.calender.BaseProductInfo;
import com.qpp.cgp.domain.funtion.calender.GetProductDetailInput;
import com.qpp.cgp.domain.funtion.calender.ProductDetailInfoOutput;
import com.qpp.cgp.domain.product.Product;
import com.qpp.cgp.domain.product.SkuProduct;
import com.qpp.cgp.domain.product.config.ImageIntegrationConfig;
import com.qpp.cgp.domain.product.config.ProductConfig;
import com.qpp.cgp.domain.product.config.ProductConfigDesign;
import com.qpp.cgp.manager.cms.CmsConfigManager;
import com.qpp.cgp.manager.function.handler.GetProductDetailExecutorHandlerV1;
import com.qpp.cgp.manager.function.service.BaseProductInfoService;
import com.qpp.cgp.manager.pricing.PartnerSalesPriceService;
import com.qpp.cgp.manager.product.attributecalculate.AttributeCalculateManager;
import com.qpp.cgp.manager.product.config.ImageIntegrationConfigManager;
import com.qpp.cgp.manager.product.price.ProductPriceCalculator;
import com.qpp.cgp.repository.common.CurrencyRepository;
import com.qpp.cgp.repository.product.ProductRepository;
import com.qpp.cgp.repository.product.config.ProductConfigDesignRepository;
import com.qpp.cgp.repository.product.config.ProductConfigRepository;
import com.qpp.core.exception.BusinessException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/4/29 14:31
 * @Version 1.0
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({GetProductDetailExecutor.class, ProductRepository.class, BaseProductInfoService.class
        , ProductConfigDesignRepository.class,ProductConfigRepository.class, ImageIntegrationConfigManager.class
        , AttributeCalculateManager.class,CmsConfigManager.class, PartnerSalesPriceService.class, ProductPriceCalculator.class, CurrencyRepository.class})
@PowerMockIgnore({"javax.management.*", "org.w3c.dom.*", "org.apache.log4j.*", "org.xml.sax.*", "javax.xml.*", "javax.script.*"})
public class GetProductDetailExecutorHandlerV1Tests {
    @Test
    public void findImageIntegrationInfoByProductIdTest(){
        Long productId=1001L;
        GetProductDetailExecutorHandlerV1 getProductDetailExecutor=new GetProductDetailExecutorHandlerV1();
        ProductConfigRepository productConfigRepository = PowerMockito.mock(ProductConfigRepository.class);
        ProductConfigDesignRepository productConfigDesignRepository = PowerMockito.mock(ProductConfigDesignRepository.class);
        ImageIntegrationConfigManager imageIntegrationConfigManager = PowerMockito.mock(ImageIntegrationConfigManager.class);
        ReflectionTestUtils.setField(getProductDetailExecutor,"productConfigRepository",productConfigRepository);
        ReflectionTestUtils.setField(getProductDetailExecutor,"productConfigDesignRepository",productConfigDesignRepository);
        ReflectionTestUtils.setField(getProductDetailExecutor,"imageIntegrationConfigManager",imageIntegrationConfigManager);

        ProductConfig productConfig=new ProductConfig();
        productConfig.setId(1111L);
        Optional<ProductConfig> optional = Optional.of(productConfig);
        PowerMockito.when(productConfigRepository.findByProductId(productId)).thenReturn(optional);

        List<ProductConfigDesign> productConfigDesigns=new ArrayList<>();
        ProductConfigDesign productConfigDesign1 = new ProductConfigDesign();
        ProductConfigDesign productConfigDesign2 = new ProductConfigDesign();
        productConfigDesign1.setId(222L);
        productConfigDesign2.setId(333L);
        productConfigDesigns.add(productConfigDesign1);
        productConfigDesigns.add(productConfigDesign2);

        PowerMockito.when(productConfigDesignRepository.findByProductConfigIdOrderByConfigVersionAsc(productConfig.getId()))
                .thenReturn(productConfigDesigns);

        List<ImageIntegrationConfig> imageIntegrationConfigs=new ArrayList<>();
        ImageIntegrationConfig imageIntegrationConfig1 = new ImageIntegrationConfig();
        ImageIntegrationConfig imageIntegrationConfig2 = new ImageIntegrationConfig();
        String materialPath1="/afasdf001";
        String side1="f001";
        imageIntegrationConfig1.setMaterialPath(materialPath1);
        imageIntegrationConfig1.setSide(side1);

        String materialPath2="/afasdf002";
        String side2="f002";
        imageIntegrationConfig2.setMaterialPath(materialPath2);
        imageIntegrationConfig2.setSide(side2);

        imageIntegrationConfigs.add(imageIntegrationConfig1);
        imageIntegrationConfigs.add(imageIntegrationConfig2);


        PowerMockito.when(imageIntegrationConfigManager.findImageIntegrationConfigByProductConfigDesignIds(productConfigDesigns.stream()
                .map(ProductConfigDesign::getId).collect(Collectors.toList()))).thenReturn(imageIntegrationConfigs);

        List<ImageIntegrationInfo> imageIntegrationInfos = getProductDetailExecutor.findImageIntegrationInfoByProductId(productId);
        ImageIntegrationInfo imageIntegrationInfo1 = imageIntegrationInfos.get(0);
        ImageIntegrationInfo imageIntegrationInfo2 = imageIntegrationInfos.get(1);

        Assert.assertEquals(materialPath1,imageIntegrationInfo1.getMaterialPath());
        Assert.assertEquals(materialPath2,imageIntegrationInfo2.getMaterialPath());
    }


    @Test
    public void findImageIntegrationInfoByProductIdOneTest(){
        Long productId=1001L;
        GetProductDetailExecutorHandlerV1 getProductDetailExecutor=new GetProductDetailExecutorHandlerV1();
        ProductConfigRepository productConfigRepository = PowerMockito.mock(ProductConfigRepository.class);
        ProductConfigDesignRepository productConfigDesignRepository = PowerMockito.mock(ProductConfigDesignRepository.class);
        ImageIntegrationConfigManager imageIntegrationConfigManager = PowerMockito.mock(ImageIntegrationConfigManager.class);
        ReflectionTestUtils.setField(getProductDetailExecutor,"productConfigRepository",productConfigRepository);
        ReflectionTestUtils.setField(getProductDetailExecutor,"productConfigDesignRepository",productConfigDesignRepository);
        ReflectionTestUtils.setField(getProductDetailExecutor,"imageIntegrationConfigManager",imageIntegrationConfigManager);

        ProductConfig productConfig=new ProductConfig();
        productConfig.setId(1111L);
        Optional<ProductConfig> optional = Optional.of(productConfig);
        PowerMockito.when(productConfigRepository.findByProductId(productId)).thenReturn(optional);

        List<ProductConfigDesign> productConfigDesigns=new ArrayList<>();
        ProductConfigDesign productConfigDesign1 = new ProductConfigDesign();
        ProductConfigDesign productConfigDesign2 = new ProductConfigDesign();
        productConfigDesign1.setId(222L);
        productConfigDesign2.setId(333L);
        productConfigDesigns.add(productConfigDesign1);
        productConfigDesigns.add(productConfigDesign2);



        PowerMockito.when(productConfigDesignRepository.findByProductConfigIdOrderByConfigVersionAsc(productConfig.getId()))
                .thenReturn(productConfigDesigns);

        List<ImageIntegrationConfig> imageIntegrationConfigs=new ArrayList<>();
        ImageIntegrationConfig imageIntegrationConfig1 = new ImageIntegrationConfig();
        String materialPath1="/afasdf001";
        String side1="f001";
        imageIntegrationConfig1.setMaterialPath(materialPath1);
        imageIntegrationConfig1.setSide(side1);
        imageIntegrationConfigs.add(imageIntegrationConfig1);


        PowerMockito.when(imageIntegrationConfigManager.findImageIntegrationConfigByProductConfigDesignIds(productConfigDesigns.stream()
                .map(ProductConfigDesign::getId).collect(Collectors.toList()))).thenReturn(imageIntegrationConfigs);

        List<ImageIntegrationInfo> imageIntegrationInfos = getProductDetailExecutor.findImageIntegrationInfoByProductId(productId);
        ImageIntegrationInfo imageIntegrationInfo1 = imageIntegrationInfos.get(0);

        Assert.assertEquals(materialPath1,imageIntegrationInfo1.getMaterialPath());
    }

    @Test
    public void executeTest(){

        GetProductDetailExecutorHandlerV1 getProductDetailExecutor=new GetProductDetailExecutorHandlerV1();
        ProductRepository productRepository = PowerMockito.mock(ProductRepository.class);
        BaseProductInfoService baseProductInfoService = PowerMockito.mock(BaseProductInfoService.class);
        ProductConfigDesignRepository productConfigDesignRepository = PowerMockito.mock(ProductConfigDesignRepository.class);
        ProductConfigRepository productConfigRepository = PowerMockito.mock(ProductConfigRepository.class);
        ImageIntegrationConfigManager imageIntegrationConfigManager = PowerMockito.mock(ImageIntegrationConfigManager.class);
        AttributeCalculateManager attributeCalculateManager = PowerMockito.mock(AttributeCalculateManager.class);
        CmsConfigManager cmsConfigManager = PowerMockito.mock(CmsConfigManager.class);
        PartnerSalesPriceService partnerSalesPriceService = PowerMockito.mock(PartnerSalesPriceService.class);
        ProductPriceCalculator productPriceCalculator = PowerMockito.mock(ProductPriceCalculator.class);
        CurrencyRepository currencyRepository = PowerMockito.mock(CurrencyRepository.class);

        ReflectionTestUtils.setField(getProductDetailExecutor,"productRepository",productRepository);
        ReflectionTestUtils.setField(getProductDetailExecutor,"baseProductInfoService",baseProductInfoService);
        ReflectionTestUtils.setField(getProductDetailExecutor,"productConfigDesignRepository",productConfigDesignRepository);
        ReflectionTestUtils.setField(getProductDetailExecutor,"productConfigRepository",productConfigRepository);
        ReflectionTestUtils.setField(getProductDetailExecutor,"imageIntegrationConfigManager",imageIntegrationConfigManager);
        ReflectionTestUtils.setField(getProductDetailExecutor,"attributeCalculateManager",attributeCalculateManager);
        ReflectionTestUtils.setField(getProductDetailExecutor,"partnerSalesPriceService",partnerSalesPriceService);
        ReflectionTestUtils.setField(getProductDetailExecutor,"cmsConfigManager",cmsConfigManager);
        ReflectionTestUtils.setField(getProductDetailExecutor,"currencyRepository",currencyRepository);

        String productId="1001";
        GetProductDetailInput inputValue=new GetProductDetailInput();
        inputValue.setId(productId);
        Product product=new SkuProduct();
        Optional<Product> optional = Optional.of(product);
        PowerMockito.when(productRepository.findById(Long.parseLong(productId))).thenReturn(optional);

        BaseProductInfo baseProductInfo=new BaseProductInfo();
        int mustOrderQuantity=5;
        baseProductInfo.setMustOrderQuantity(mustOrderQuantity);
        PowerMockito.when(baseProductInfoService.getProductDetailInfoOutputByProduct(product,null, null, EnvMode.TEST))
                .thenReturn(baseProductInfo);

        ProductConfig productConfig=new ProductConfig();
        productConfig.setId(1111L);
        Optional<ProductConfig> optionalProductConfig = Optional.of(productConfig);
        PowerMockito.when(productConfigRepository.findByProductId(Long.parseLong(productId))).thenReturn(optionalProductConfig);

        List<ProductConfigDesign> productConfigDesigns=new ArrayList<>();
        ProductConfigDesign productConfigDesign1 = new ProductConfigDesign();
        ProductConfigDesign productConfigDesign2 = new ProductConfigDesign();
        productConfigDesign1.setId(222L);
        productConfigDesign2.setId(333L);
        productConfigDesigns.add(productConfigDesign1);
        productConfigDesigns.add(productConfigDesign2);



        PowerMockito.when(productConfigDesignRepository.findByProductConfigIdOrderByConfigVersionAsc(productConfig.getId()))
                .thenReturn(productConfigDesigns);

        List<ImageIntegrationConfig> imageIntegrationConfigs=new ArrayList<>();
        ImageIntegrationConfig imageIntegrationConfig1 = new ImageIntegrationConfig();
        ImageIntegrationConfig imageIntegrationConfig2 = new ImageIntegrationConfig();
        String materialPath1="/afasdf001";
        String side1="f001";
        imageIntegrationConfig1.setMaterialPath(materialPath1);
        imageIntegrationConfig1.setSide(side1);

        String materialPath2="/afasdf002";
        String side2="f002";
        imageIntegrationConfig2.setMaterialPath(materialPath2);
        imageIntegrationConfig2.setSide(side2);

        imageIntegrationConfigs.add(imageIntegrationConfig1);
        imageIntegrationConfigs.add(imageIntegrationConfig2);


        PowerMockito.when(imageIntegrationConfigManager.findImageIntegrationConfigByProductConfigDesignIds(productConfigDesigns.stream()
                .map(ProductConfigDesign::getId).collect(Collectors.toList()))).thenReturn(imageIntegrationConfigs);
        Exception exception=null;
        try {
            ProductDetailInfoOutput productDetailInfoOutput = getProductDetailExecutor.execute(inputValue);
        }catch (Exception e){
            exception=e;
        }
        Assert.assertNotNull(exception);
        BusinessException businessException=(BusinessException) exception;
        Assert.assertEquals(15011,businessException.getCode());

    }

    @Test
    public void ImageIntegrationInfoEqualsTest(){
        ImageIntegrationInfo imageIntegrationInfo1 = new ImageIntegrationInfo();
        imageIntegrationInfo1.setSide("aaa");
        imageIntegrationInfo1.setMaterialPath("/user/a");
        ImageIntegrationInfo imageIntegrationInfo2 = new ImageIntegrationInfo();
        imageIntegrationInfo2.setSide("aaa");
        imageIntegrationInfo2.setMaterialPath("/user/a");
        Assert.assertEquals(true,imageIntegrationInfo1.equals(imageIntegrationInfo2));


        ImageIntegrationInfo imageIntegrationInfo3 = new ImageIntegrationInfo();
        imageIntegrationInfo3.setSide("aaa");
        ImageIntegrationInfo imageIntegrationInfo4 = new ImageIntegrationInfo();
        imageIntegrationInfo4.setSide("aaa");
        imageIntegrationInfo4.setMaterialPath("/user/a");
        Assert.assertEquals(false,imageIntegrationInfo3.equals(imageIntegrationInfo4));



        ImageIntegrationInfo imageIntegrationInfo5 = new ImageIntegrationInfo();
        imageIntegrationInfo5.setSide("aaa");
        ImageIntegrationInfo imageIntegrationInfo6 = new ImageIntegrationInfo();
        imageIntegrationInfo6.setSide("aaa");
        Assert.assertEquals(true,imageIntegrationInfo5.equals(imageIntegrationInfo6));

        ImageIntegrationInfo imageIntegrationInfo7 = new ImageIntegrationInfo();
        imageIntegrationInfo7.setSide("aaa");
        ImageIntegrationInfo imageIntegrationInfo8 = new ImageIntegrationInfo();
        imageIntegrationInfo8.setSide("aaa");
        imageIntegrationInfo8.setMaterialPath("");
        Assert.assertEquals(true,imageIntegrationInfo7.equals(imageIntegrationInfo8));

        ImageIntegrationInfo imageIntegrationInfo9 = new ImageIntegrationInfo();
        imageIntegrationInfo9.setSide("aaa");
        ImageIntegrationInfo imageIntegrationInfo10 = new ImageIntegrationInfo();
        imageIntegrationInfo10.setSide("aaa");
        imageIntegrationInfo10.setMaterialPath("  ");
        Assert.assertEquals(true,imageIntegrationInfo9.equals(imageIntegrationInfo10));





    }
}
