package com.qpp.cgp.manager.order.creator;

import com.qpp.cgp.domain.dto.order.CreateOrderLineItemDTO;
import com.qpp.cgp.domain.suit.SkuProductSetInstance;
import com.qpp.mongo.driver.HybridMongoTemplate;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.Assert.*;

//@RunWith(SpringRunner.class)
//@SpringBootTest
@RunWith(MockitoJUnitRunner.class)
public class OrderLineItemCreatorCheckIsProductSetOrderItemTests {

    @Mock
    private HybridMongoTemplate mongoTemplate;

    @InjectMocks
    private OrderLineItemCreator orderLineItemCreator;

    private final String methodName = "checkIsProductSetOrderItem";

    @Test
    public void testCheckIsProductSetOrderItemByProductInstanceIdNull() {

        // build params
        CreateOrderLineItemDTO model = new CreateOrderLineItemDTO();
        model.setProductInstanceId(null);

        // invoke & assert
        boolean retVal = ReflectionTestUtils.invokeMethod(orderLineItemCreator, methodName, model);
        assertFalse(retVal);

    }

    @Test
    public void testCheckIsProductSetOrderItemByProductIdNull() {

        // build params
        CreateOrderLineItemDTO model = new CreateOrderLineItemDTO();
        model.setProductId(null);

        // invoke & assert
        boolean retVal = ReflectionTestUtils.invokeMethod(orderLineItemCreator, methodName, model);
        assertFalse(retVal);

    }

    @Test
    public void testCheckIsProductSetOrderItem() {

        // mock behavior
        Mockito.when(mongoTemplate.findById(Mockito.any(), Mockito.eq(SkuProductSetInstance.class))).thenReturn(new SkuProductSetInstance());

        // build params
        CreateOrderLineItemDTO model = new CreateOrderLineItemDTO();
        model.setProductInstanceId("123");

        // invoke & assert
        boolean retVal = ReflectionTestUtils.invokeMethod(orderLineItemCreator, methodName, model);
        assertTrue(retVal);

    }
}