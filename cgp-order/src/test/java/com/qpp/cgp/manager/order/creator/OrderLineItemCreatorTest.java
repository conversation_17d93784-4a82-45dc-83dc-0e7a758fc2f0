package com.qpp.cgp.manager.order.creator;

import com.qpp.cgp.domain.bom.runtime.ProductInstance;
import com.qpp.cgp.domain.customs.CustomsElement;
import com.qpp.cgp.domain.order.OrderLineItem;
import com.qpp.cgp.domain.product.SkuProduct;
import com.qpp.cgp.manager.bom.ProductInstanceManager;
import com.qpp.cgp.manager.customs.CustomsElementManager;
import com.qpp.cgp.repository.product.ProductRepository;
import com.qpp.cgp.service.common.CommonPropertiesService;
import com.qpp.cgp.service.product.attribute.VersionedProductAttributeService;
import com.qpp.cgp.value.ExpressionValueEx;
import com.qpp.cgp.value.ValueEx;
import com.qpp.cgp.value.ValueType;
import com.qpp.cgp.value.calculator.ValueExCalculateService;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Optional;


/**
 * <AUTHOR>
 * @Package com.qpp.cgp.manager.order.creator
 * @Date 2024/6/4 16:13
 */
@ExtendWith(MockitoExtension.class)
public class OrderLineItemCreatorTest {

    @Spy
    @InjectMocks
    private OrderLineItemCreator orderLineItemCreator;

    @Mock
    private CommonPropertiesService commonPropertiesService;

    @Mock
    private CustomsElementManager customsElementManager;

    @Mock
    private ProductRepository productRepository;

    @Mock
    private ProductInstanceManager productInstanceManager;

    @Mock
    private VersionedProductAttributeService versionedProductAttributeService;

    @Mock
    private ValueExCalculateService valueExCalculateService;

    @Test
    public void testCheckCustomProductInfo() {
        Mockito.when(commonPropertiesService
                .getKeyValueAndApplicationModeForNoException("versionedAttribute.customsElement.original")).thenReturn("false");

        Mockito.when(commonPropertiesService.getKeyValueAndApplicationModeForNoException("configurableProductIdCustomsElementSwitch")).thenReturn("true");

        Mockito.when(productRepository.getProductId(84624906L)).thenReturn(84609936L);
        Mockito.when(productInstanceManager.findVersionedAttributeIdById("8913145689")).thenReturn(Optional.ofNullable(12345678L));

        CustomsElement customsElement = new CustomsElement();
        customsElement.setOutCustoms(true);
        ValueEx valueEx = new ExpressionValueEx();
        valueEx.setType(ValueType.String);
        customsElement.setCustomsCategory(valueEx);

        Mockito.when(versionedProductAttributeService.getLatestCustomsElement(84609936L, Optional.ofNullable(12345678L)))
                .thenReturn(customsElement);

        OrderLineItem orderLineItem = new OrderLineItem();
        SkuProduct skuProduct = new SkuProduct();
        skuProduct.setId(84624906L);
        orderLineItem.setProduct(skuProduct);
        orderLineItem.setProductInstanceId("8913145689");

        Assertions.assertThatThrownBy(() -> orderLineItemCreator.checkCustomProductInfo(orderLineItem))
                .hasMessage("The customsElement CustomsCategory ValueEx type error! ");
    }

    /**
     * 方法 ： findVersionIdAndProductConfigBomIdByProductInstanceId
     * 测试 ： 当查询到的versionId和ProductConfigBomId都存在
     * */
    @Test
    public void testFindVersionIdAndProductConfigBomIdByProductInstanceIdByExistsVersionIdAndProductBomId(){
        HashSet<String> fields = new HashSet<>(Arrays.asList("versionedProductAttributeId", "productConfigBomId"));
        Mockito.when(productInstanceManager.findByIdProjectFields("1",fields))
                .thenReturn(new ProductInstance(){{
                    setVersionedProductAttributeId(111L);
                    setProductConfigBomId(222L);
                }});

        OrderLineItemCreator.VersionIdAndProductConfigBomId versionIdAndProductConfigBomId =
                orderLineItemCreator.findVersionIdAndProductConfigBomIdByProductInstanceId("1");

        Assertions.assertThat(versionIdAndProductConfigBomId.getVersionedAttributeId().get()).isEqualTo(111L);
        Assertions.assertThat(versionIdAndProductConfigBomId.getProductConfigBomId().get()).isEqualTo(222L);
    }

    /**
     * 方法 ： findVersionIdAndProductConfigBomIdByProductInstanceId
     * 测试 ： 当查询到的versionId不存在，ProductConfigBomId存在
     * */
    @Test
    public void testFindVersionIdAndProductConfigBomIdByProductInstanceIdByNotExistsVersionIdButExistsProductBomId(){
        HashSet<String> fields = new HashSet<>(Arrays.asList("versionedProductAttributeId", "productConfigBomId"));
        Mockito.when(productInstanceManager.findByIdProjectFields("1",fields))
                .thenReturn(new ProductInstance(){{
                    setProductConfigBomId(222L);
                }});

        OrderLineItemCreator.VersionIdAndProductConfigBomId versionIdAndProductConfigBomId =
                orderLineItemCreator.findVersionIdAndProductConfigBomIdByProductInstanceId("1");

        Assertions.assertThat(versionIdAndProductConfigBomId.getVersionedAttributeId()).isEmpty();
        Assertions.assertThat(versionIdAndProductConfigBomId.getProductConfigBomId().get()).isEqualTo(222L);
    }

    /**
     * 方法 ： findVersionIdAndProductConfigBomIdByProductInstanceId
     * 测试 ： 当查询到的versionId存在，ProductConfigBomId不存在
     * */
    @Test
    public void testFindVersionIdAndProductConfigBomIdByProductInstanceIdByExistsVersionIdButNotExistsProductBomId(){
        HashSet<String> fields = new HashSet<>(Arrays.asList("versionedProductAttributeId", "productConfigBomId"));
        Mockito.when(productInstanceManager.findByIdProjectFields("1",fields))
                .thenReturn(new ProductInstance(){{
                    setVersionedProductAttributeId(111L);
                }});

        OrderLineItemCreator.VersionIdAndProductConfigBomId versionIdAndProductConfigBomId =
                orderLineItemCreator.findVersionIdAndProductConfigBomIdByProductInstanceId("1");

        Assertions.assertThat(versionIdAndProductConfigBomId.getVersionedAttributeId().get()).isEqualTo(111L);
        Assertions.assertThat(versionIdAndProductConfigBomId.getProductConfigBomId()).isEmpty();
    }

    /**
     * 方法 ： findVersionIdAndProductConfigBomIdByProductInstanceId
     * 测试 ： 当查询到的versionId和ProductConfigBomId都不存在
     * */
    @Test
    public void testFindVersionIdAndProductConfigBomIdByProductInstanceIdByNotExistsVersionIdAndProductBomId(){
        HashSet<String> fields = new HashSet<>(Arrays.asList("versionedProductAttributeId", "productConfigBomId"));
        Mockito.when(productInstanceManager.findByIdProjectFields("1",fields))
                .thenReturn(new ProductInstance());

        OrderLineItemCreator.VersionIdAndProductConfigBomId versionIdAndProductConfigBomId =
                orderLineItemCreator.findVersionIdAndProductConfigBomIdByProductInstanceId("1");

        Assertions.assertThat(versionIdAndProductConfigBomId.getVersionedAttributeId()).isEmpty();
        Assertions.assertThat(versionIdAndProductConfigBomId.getProductConfigBomId()).isEmpty();
    }

}
