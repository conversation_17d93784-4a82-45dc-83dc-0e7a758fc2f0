//package com.qpp.cgp.manager.customs;
//
//import com.qpp.cgp.CgpOrderTestApplication;
//import com.qpp.cgp.CustomsCategoryTestApplication;
//import com.qpp.web.core.exception.BusinessExceptionInfo;
//import com.qpp.web.core.exception.manager.DefaultBusinessExceptionMessageFinder;
//import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
//import org.assertj.core.api.Assertions;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.Mockito;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.boot.test.mock.mockito.MockBean;
//import org.springframework.core.io.Resource;
//import org.springframework.mock.web.MockMultipartFile;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.io.IOException;
//
///**
// * <AUTHOR>
// * @date 2021/1/12
// */
//@SpringBootTest(classes = CgpOrderTestApplication.class)
//@RunWith(SpringRunner.class)
//public class CustomsCategoryServiceExcelErrorTest {
//
//    @Autowired
//    private CustomsCategoryService customsCategoryService;
//
//    @MockBean(name = "businessExceptionMessageFinder")
//    private DefaultBusinessExceptionMessageFinder exceptionMessageFinder;
//
//    @Value("classpath:customs/excel/error/customs_category.txt")
//    private Resource customsCategoryTextFile;
//
//    @Value("classpath:customs/excel/error/customs_category_false.xlsx")
//    private Resource customsCategoryFalseExcelFile;
//
//    @Test
//    public void testCheckIsExcelFileTextFile() throws IOException, InvalidFormatException {
//        String errorMessage = "The customs category file 'customs_category.txt' is not excel file!";
//
//        BusinessExceptionInfo businessExceptionInfo = new BusinessExceptionInfo();
//        businessExceptionInfo.setCode(600001);
//        businessExceptionInfo.setMessage(errorMessage);
//
//        Mockito.when(exceptionMessageFinder.getBusinessExceptionInfo(600001))
//                .thenReturn(businessExceptionInfo);
//
//        // 创建MultipartFile对象
//        MockMultipartFile mockMultipartFile = new MockMultipartFile("customs_category.txt",
//                "customs_category.txt", "application/xls;charset=utf-8",
//                customsCategoryTextFile.getInputStream());
//
//        Assertions.assertThatThrownBy(() -> customsCategoryService.checkIsExcelFile(mockMultipartFile))
//                .hasMessage(errorMessage);
//    }
//
//    @Test
//    public void testCheckIsExcelFileFalseExcelFile() throws IOException, InvalidFormatException {
//        String errorMessage = "The customs category file 'customs_category_false.xlsx' is not excel file!";
//
//        BusinessExceptionInfo businessExceptionInfo = new BusinessExceptionInfo();
//        businessExceptionInfo.setCode(600001);
//        businessExceptionInfo.setMessage(errorMessage);
//
//        Mockito.when(exceptionMessageFinder.getBusinessExceptionInfo(600001))
//                .thenReturn(businessExceptionInfo);
//
//        // 创建MultipartFile对象
//        MockMultipartFile mockMultipartFile = new MockMultipartFile("customs_category_false.xlsx",
//                "customs_category_false.xlsx", "application/xls;charset=utf-8",
//                customsCategoryTextFile.getInputStream());
//
//        Assertions.assertThatThrownBy(() -> customsCategoryService.checkIsExcelFile(mockMultipartFile))
//                .hasMessage(errorMessage);
//    }
//}
