package com.qpp.cgp.manager.orderitem;

import com.qpp.cgp.CgpOrderTestApplication;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @Date 2021/3/4 14:53
 * @Description
 * @Version 1.0
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = CgpOrderTestApplication.class)
public class OrderLineItemManagerUpdateTests {

    @Autowired
    private OrderLineItemManager orderLineItemManager;

    @Test
    public void testByNullOrderItemStatusUpdateDTO(){
        try {
             orderLineItemManager.updateStatus(123L, null, true);
        } catch (Exception e) {
            Assert.assertEquals("Parameter：dto can not be null!", e.getMessage());
        }
    }

}
