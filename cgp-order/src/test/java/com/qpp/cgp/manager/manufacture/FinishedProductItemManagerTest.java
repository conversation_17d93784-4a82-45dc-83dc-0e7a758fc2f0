package com.qpp.cgp.manager.manufacture;

import com.qpp.cgp.CgpOrderTestApplication;
import com.qpp.cgp.domain.manufacture.FinishedProductItemCurrentInfo;
import com.qpp.core.exception.BusinessException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Optional;

/**
 * <AUTHOR> Chiu
 * @Date 2021/2/6 17:21
 * @Description
 * @Version 1.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CgpOrderTestApplication.class)
public class FinishedProductItemManagerTest {

    @Autowired
    private FinishedProductItemManager finishedProductItemManager;

    /**
     * 断言：
     *  1、传入null的item和null的statusId
     *      期望：方法抛出业务异常
     *  2、传入null的item和值为156201L的statusId
     *      期望：方法抛出业务异常
     */
    @Test
    public void findCurrentInfoByStatusIdTest() {
        try {
            Optional<FinishedProductItemCurrentInfo> currentInfoByStatusId = finishedProductItemManager.findCurrentInfoByStatusId(null, null);
        } catch (Exception e) {
            Assert.assertEquals("Parameter：item can not be null", e.getMessage());
        }
    }

}
