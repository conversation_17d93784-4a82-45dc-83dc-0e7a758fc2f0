package com.qpp.cgp.manager.order.status;

import com.qpp.cgp.CgpOrderTestApplication;
import com.qpp.cgp.domain.dto.order.OrderStatusUpdateDTO;
import com.qpp.cgp.domain.order.Order;
import com.qpp.cgp.domain.order.OrderStatus;
import com.qpp.cgp.manager.order.OrderManager;
import com.qpp.core.exception.BusinessException;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.mapping.TextScore;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Date;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/4/7 16:20
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = CgpOrderTestApplication.class)
public class PageContentFileCheckStrategyTests {

    @Test
    public void testIsMatchIdByExist(){
        PageContentFileCheckStrategy pageContentFileCheckStrategy = new PageContentFileCheckStrategy();
//        OrderStatus newStatus = mock(OrderStatus.class);
//        newStatus.setId(OrderStatus.CONFIRMED_STATUS);
        OrderStatus newStatus = new OrderStatus();
        newStatus.setId(OrderStatus.CONFIRMED_STATUS);
//        ReflectionTestUtils.setField(pageContentFileCheckStrategy,"newStatus",newStatus);
        Boolean match = pageContentFileCheckStrategy.isMatch(null, newStatus, null);
        Assert.assertTrue(match);
    }
    @Test
    public void testUpdateFileByExist(){
        PageContentFileCheckStrategy pageContentFileCheckStrategy =  new PageContentFileCheckStrategy();
        OrderManager orderManager = mock(OrderManager.class);
        Mockito.when(orderManager.checkExistsPSDOrAIFile(null)).thenReturn(true);
        ReflectionTestUtils.setField(pageContentFileCheckStrategy,"orderManager",orderManager);
        try{
            pageContentFileCheckStrategy.update(null,null,true,true,new Date());
        }catch (BusinessException e){
            Assert.assertEquals(500099,e.getCode());
        }
    }
    @Test
    public void testUpdateFileByNotExist(){
        PageContentFileCheckStrategy pageContentFileCheckStrategy = new PageContentFileCheckStrategy();
        OrderManager orderManager = mock(OrderManager.class);
        Order order = new Order();
        order.setId("123");
        when(orderManager.checkExistsPSDOrAIFile(order)).thenReturn(false);
        ReflectionTestUtils.setField(pageContentFileCheckStrategy,"orderManager",orderManager);
        Order neworder = pageContentFileCheckStrategy.update(null, order, false, false,null);

        Assert.assertEquals("123",neworder.getId());
    }
}
