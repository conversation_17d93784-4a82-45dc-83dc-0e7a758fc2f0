package com.qpp.cgp.manager.order.creator;

import com.qpp.cgp.config.mongo.MongoTemplateFactory;
import com.qpp.cgp.domain.order.Order;
import com.qpp.cgp.domain.order.WhitelabelOrderType;
import com.qpp.cgp.domain.partner.Partner;
import com.qpp.cgp.domain.stateflow.config.StateFlow;
import com.qpp.cgp.manager.partner.order.PartnerOrderActionManager;
import com.qpp.cgp.service.order.OrderCreateAction;
import com.qpp.cgp.service.order.partner.PartnerOrderAction;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class OrderCreatorActionTest {

    @InjectMocks
    private OrderCreator orderCreator;

    @Mock
    private PartnerOrderActionManager partnerOrderActionManager;

    @Mock
    private MongoTemplate mongoTemplate;

    @Mock
    private MongoTemplateFactory mongoTemplateFactory;

    @Mock
    private WhitelabelOrderType whitelabelOrderType;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void getOrderCreateAction_PartnerOrderActionExists_ReturnsOrderCreateAction() {

        PartnerOrderAction partnerOrderAction = new PartnerOrderAction();
        Long partnerId = 1L;
        Partner partner = new Partner();
        partner.setId(1L);
        partnerOrderAction.setPartner(partner);
        OrderCreateAction expectedOrderCreateAction = new OrderCreateAction();
        expectedOrderCreateAction.setOrderType(WhitelabelOrderType.NORMAL);
        StateFlow stateFlow = new StateFlow();
        stateFlow.setId("123456");
        stateFlow.setModule(StateFlow.Module.return_request_order);
        stateFlow.setStatus(2);
        stateFlow.setVersion(1);
        expectedOrderCreateAction.setOrderStatusFlow(stateFlow);
        expectedOrderCreateAction.setComment("test_Comment");
        partnerOrderAction.setOrderCreateAction(expectedOrderCreateAction);

        when(partnerOrderActionManager.findByPartnerId(partnerId)).thenReturn(partnerOrderAction);

        OrderCreateAction result = orderCreator.getOrderCreateAction(partnerId);

        assertNotNull(result);
        assertEquals(expectedOrderCreateAction, result);
    }

    @Test
    public void getOrderCreateAction_PartnerOrderActionNotExists_ReturnsNull() {
        Long partnerId = 1L;

        when(partnerOrderActionManager.findByPartnerId(partnerId)).thenReturn(null);

        OrderCreateAction result = orderCreator.getOrderCreateAction(partnerId);

        assertNull(result);
    }

    @Test
    public void getStateFlow_OrderCreateActionIsNull_ReturnsNull() {
        StateFlow result = orderCreator.getStateFlow(null);

        assertNull(result);
    }

    @Test
    public void getStateFlow_OrderStatusFlowIsNull_ReturnsNull() {
        OrderCreateAction orderCreateAction = new OrderCreateAction();

        StateFlow result = orderCreator.getStateFlow(orderCreateAction);

        assertNull(result);
    }

    @Test
    public void getStateFlow_OrderStatusFlowExists_ReturnsStateFlow() {
        OrderCreateAction orderCreateAction = new OrderCreateAction();
        StateFlow expectedStateFlow = new StateFlow();
        orderCreateAction.setOrderStatusFlow(new StateFlow());

        when(mongoTemplateFactory.getMongoTemplate(StateFlow.class)).thenReturn(mongoTemplate);
        when(mongoTemplate.findOne(any(Query.class), eq(StateFlow.class))).thenReturn(expectedStateFlow);

        StateFlow result = orderCreator.getStateFlow(orderCreateAction);

        assertNotNull(result);
        assertEquals(expectedStateFlow, result);
    }

    @Test
    public void setOrderRemarkAndType_OrderCreateActionIsNull_DoesNothing() {
        Order order = new Order();

        orderCreator.setOrderRemarkAndType(order, null);

        assertNull(order.getRemark());
        assertNull(order.getType());
    }

    @Test
    public void setOrderRemarkAndType_OrderCreateActionNotNull_SetsRemarkAndType() {
        Order order = new Order();
        order.setType(WhitelabelOrderType.ONE_DRAGON);
        order.setRemark("Order Remark");

        OrderCreateAction orderCreateAction = new OrderCreateAction();
        orderCreateAction.setComment("IT Test");
        orderCreateAction.setDesc("Config Desc");
        orderCreateAction.setOrderType(WhitelabelOrderType.NORMAL);

        orderCreator.setOrderRemarkAndType(order, orderCreateAction);

        assertEquals("Config Desc-Order Remark", order.getRemark());
        assertEquals(WhitelabelOrderType.NORMAL, order.getType());
    }

    @Test
    public void setOrderRemarkAndType_OrderIsTest_SetsRemarkWithITPrefix() {
        Order order = new Order();
        order.setType(WhitelabelOrderType.ONE_DRAGON);
//        order.setRemark("Order Remark");

        OrderCreateAction orderCreateAction = new OrderCreateAction();
        orderCreateAction.setComment("IT Test");
        orderCreateAction.setDesc("Config Desc");
        orderCreateAction.setOrderType(WhitelabelOrderType.NORMAL);

        orderCreator.setOrderRemarkAndType(order, orderCreateAction);

        assertEquals("Config Desc ", order.getRemark());
    }
}
