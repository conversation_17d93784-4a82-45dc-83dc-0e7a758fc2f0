package com.qpp.cgp.manager.order;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.bom.attribute.AttributesToRtType;
import com.qpp.cgp.domain.bom.attribute.RtAttributeDef;
import com.qpp.cgp.domain.bom.attribute.RtType;
import com.qpp.cgp.domain.bom.runtime.RtObject;
import com.qpp.cgp.domain.dto.order.OrderItemUserImpositionParams;
import com.qpp.cgp.domain.order.OrderLineItem;
import com.qpp.cgp.domain.product.config.ProductConfigImposition;
import com.qpp.cgp.manager.bom.RtObjectManager;
import com.qpp.cgp.manager.bom.attribute.RtTypeManager;
import com.qpp.cgp.manager.orderitem.OrderLineItemManager;
import com.qpp.cgp.manager.product.config.ProductConfigImpositionManager;
import com.qpp.cgp.repository.order.OrderLineItemRepository;
import org.assertj.core.api.Assertions;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * <AUTHOR>
 * @date 2021/4/22
 */
@RunWith(MockitoJUnitRunner.class)
public class OrderManagerUserImpositionParamsTest {

    @Spy
    @InjectMocks
    private OrderManager orderManager;

    @Spy
    private OrderLineItemManager orderLineItemManager;

    @Mock
    private OrderLineItemRepository orderLineItemRepository;

    @Mock
    private ProductConfigImpositionManager productConfigImpositionManager;

    @Mock
    private RtTypeManager rtTypeManager;

    @Mock
    private RtObjectManager rtObjectManager;

    @Before
    public void init() {
        ReflectionTestUtils.setField(orderLineItemManager, "orderLineItemRepository", orderLineItemRepository);
        ReflectionTestUtils.setField(orderLineItemManager,
                "productConfigImpositionManager", productConfigImpositionManager);
        ReflectionTestUtils.setField(orderLineItemManager, "rtTypeManager", rtTypeManager);
        ReflectionTestUtils.setField(orderLineItemManager, "rtObjectManager", rtObjectManager);
    }

    private RtType createUserParams() {
        RtAttributeDef def1 = new RtAttributeDef();
        def1.setName("machine");
        AttributesToRtType attributes1 = new AttributesToRtType();
        attributes1.setRtAttributeDef(def1);

        RtAttributeDef def2 = new RtAttributeDef();
        def2.setName("material");
        AttributesToRtType attributes2 = new AttributesToRtType();
        attributes2.setRtAttributeDef(def2);

        RtAttributeDef def3 = new RtAttributeDef();
        def3.setName("remark");
        AttributesToRtType attributes3 = new AttributesToRtType();
        attributes3.setRtAttributeDef(def3);

        RtType rtType = new RtType();
        rtType.setId("1");
        rtType.setAttributesToRtTypes(ImmutableList.of(attributes1, attributes2, attributes3));

        return rtType;
    }

    private RtObject createUserParamDefaultValues() {
        Map<String, Object> objectJSON = new HashMap<>();
        objectJSON.put("machine", "HP10000");
        objectJSON.put("material", "wood");
        objectJSON.put("test", "test");
        RtObject userParamDefaultValues = new RtObject();
        userParamDefaultValues.setId("1");
        userParamDefaultValues.setObjectJSON(objectJSON);
        return userParamDefaultValues;
    }

    private ProductConfigImposition createProductConfigImposition() {
        ProductConfigImposition imposition = new ProductConfigImposition();
        imposition.setId(1L);

        RtType userParams = new RtType();
        userParams.setId("1");
        userParams.setClazz(RtType.class.getName());
        imposition.setUserParams(userParams);

        RtObject userParamDefaultValues = new RtObject();
        userParamDefaultValues.setId("1");
        userParamDefaultValues.setClazz(RtObject.class.getName());
        imposition.setUserParamDefaultValues(userParamDefaultValues);

        return imposition;
    }

    @Test
    public void testGetItemsUserImpositionParams() {
        OrderLineItem item1 = new OrderLineItem();
        item1.setId("1");
        item1.setProductId(1L);
        item1.setUserImpositionParams(ImmutableMap.of("machine", "HP5500", "test", "123"));
        OrderLineItem item2 = new OrderLineItem();
        item2.setId("2");
        item2.setProductId(1L);
        item2.setUserImpositionParams(ImmutableMap.of("machine", "HP10000", "material", "wood"));

        Mockito.when(orderLineItemRepository.findByOrderId("1")).thenReturn(ImmutableList.of(item1, item2));
        Mockito.when(orderLineItemRepository.findById("1")).thenReturn(item1);
        Mockito.when(orderLineItemRepository.findById("2")).thenReturn(item2);

        RtType userParams = createUserParams();
        Mockito.when(rtTypeManager.findById("1")).thenReturn(userParams);

        RtObject userParamDefaultValues = createUserParamDefaultValues();
        Mockito.when(rtObjectManager.findById("1")).thenReturn(userParamDefaultValues);

        ProductConfigImposition productConfigImposition = createProductConfigImposition();
        Mockito.when(productConfigImpositionManager.findByProductId(1L)).thenReturn(Optional.of(productConfigImposition));

        List<OrderItemUserImpositionParams> result = orderManager.getItemsUserImpositionParams("1");

        Assertions.assertThat(result.size()).isEqualTo(2);
        for (OrderItemUserImpositionParams params : result) {
            if ("1".equalsIgnoreCase(params.getOrderItemId())) {
                Map<String, Object> userImpositionParams = params.getUserImpositionParams();
                Assertions.assertThat(userImpositionParams.size()).isEqualTo(2);
                Assertions.assertThat(userImpositionParams.get("machine")).isEqualTo("HP5500");
                Assertions.assertThat(userImpositionParams.get("test")).isEqualTo("123");

                Assertions.assertThat(params.getUserParams()).isEqualTo(userParams);
                Assertions.assertThat(params.getUserParamDefaultValues()).isEqualTo(userParamDefaultValues);
            } else if ("2".equalsIgnoreCase(params.getOrderItemId())) {
                Map<String, Object> userImpositionParams = params.getUserImpositionParams();
                Assertions.assertThat(userImpositionParams.size()).isEqualTo(2);
                Assertions.assertThat(userImpositionParams.get("machine")).isEqualTo("HP10000");
                Assertions.assertThat(userImpositionParams.get("material")).isEqualTo("wood");

                Assertions.assertThat(params.getUserParams()).isEqualTo(userParams);
                Assertions.assertThat(params.getUserParamDefaultValues()).isEqualTo(userParamDefaultValues);
            }
        }
    }

    @Test
    public void testSetItemsUserImpositionParams() {
        OrderLineItem item1 = new OrderLineItem();
        item1.setId("1");
        OrderLineItem item2 = new OrderLineItem();
        item2.setId("2");

        Mockito.when(orderLineItemRepository.findByOrderId("1")).thenReturn(ImmutableList.of(item1, item2));

        OrderItemUserImpositionParams param1 = new OrderItemUserImpositionParams();
        param1.setOrderItemId("1");
        param1.setUserImpositionParams(ImmutableMap.of("machine", "HP5500", "test", "123"));

        OrderItemUserImpositionParams param2 = new OrderItemUserImpositionParams();
        param2.setOrderItemId("2");
        param2.setUserImpositionParams(ImmutableMap.of("machine", "HP10000", "material", "wood"));

        Mockito.doAnswer(invocation -> {
            String id = invocation.getArgument(0);
            Map<String, Object> params = invocation.getArgument(1);
            if ("1".equalsIgnoreCase(id)) {
                item1.setUserImpositionParams(params);
                return item1;
            } else if ("2".equalsIgnoreCase(id)) {
                item2.setUserImpositionParams(params);
                return item2;
            }
            return null;
        }).when(orderLineItemManager).setUserImpositionParams(anyString(), any());

        List<OrderLineItem> result = orderManager
                .setItemsUserImpositionParams("1", ImmutableList.of(param1, param2));

        Assertions.assertThat(result.size()).isEqualTo(2);
        for (OrderLineItem orderLineItem : result) {
            Map<String, Object> userImpositionParams = orderLineItem.getUserImpositionParams();
            if ("1".equalsIgnoreCase(orderLineItem.getId())) {
                Assertions.assertThat(userImpositionParams.size()).isEqualTo(2);
                Assertions.assertThat(userImpositionParams.get("machine")).isEqualTo("HP5500");
                Assertions.assertThat(userImpositionParams.get("test")).isEqualTo("123");
            } else if ("2".equalsIgnoreCase(orderLineItem.getId())) {
                Assertions.assertThat(userImpositionParams.size()).isEqualTo(2);
                Assertions.assertThat(userImpositionParams.get("machine")).isEqualTo("HP10000");
                Assertions.assertThat(userImpositionParams.get("material")).isEqualTo("wood");
            }
        }
    }
}
