package com.qpp.cgp.manager.composing;

import com.qpp.cgp.domain.material.desc.CustomDescInstance;
import com.qpp.cgp.domain.material.desc.MaterialDescInstance;
import com.qpp.cgp.domain.order.OrderLineItem;
import com.qpp.cgp.repository.order.OrderLineItemRepository;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class OrderlineItemProcessTest {
    @Mock
    private OrderLineItemRepository orderLineItemRepository;

    @InjectMocks
    private TextParameterOrderComposingInfoFiller textParameterOrderComposingInfoFiller;

    @Test
    public void testNormal() {
        String materialPath1 = "1.2.3";
        String materialPath2 = "5.6.8";
        String description1 = "test1";
        String description2 = "test2";
        List<MaterialDescInstance> re = new ArrayList<>();
        MaterialDescInstance m1 = new MaterialDescInstance();
        m1.setDescription(description1);
        m1.setMaterialPath(materialPath1);
        CustomDescInstance cu1 = new CustomDescInstance();
        cu1.setKey("testKey1");
        cu1.setValue("testValue1");
        CustomDescInstance cu2 = new CustomDescInstance();
        cu2.setKey("testKey2");
        cu2.setValue("testValue2");
        List<CustomDescInstance> cuList = new ArrayList<>();
        cuList.add(cu1);
        cuList.add(cu2);
        m1.setCustomDesc(cuList);

        MaterialDescInstance m2 = new MaterialDescInstance();
        m2.setDescription("test2");
        m2.setMaterialPath(materialPath2);
        CustomDescInstance cu3 = new CustomDescInstance();
        cu3.setKey("testKey3");
        cu3.setValue("testValue3");
        CustomDescInstance cu4 = new CustomDescInstance();
        cu4.setKey("testKey4");
        cu4.setValue("testValue4");
        List<CustomDescInstance> cuList1 = new ArrayList<>();
        cuList1.add(cu3);
        cuList1.add(cu4);
        m2.setCustomDesc(cuList1);

        re.add(m1);
        re.add(m2);
//        Map<String, Object> stringObjectMap = OrderlineItemProcess(re);
        OrderLineItem orderLineItem = new OrderLineItem();
        orderLineItem.setMaterialDesc(re);
        Mockito.when(orderLineItemRepository.findById(Mockito.anyString())).thenReturn(orderLineItem);
        Map<String, Object> stringObjectMap = textParameterOrderComposingInfoFiller.orderlineItemProcess("1");
        System.out.println(stringObjectMap);

        Map<String, Object> materialDesc = (Map<String, Object>) stringObjectMap.get(materialPath1 + "_desc");
        String desc1 = (String) materialDesc.get("description");
        List<CustomDescInstance> customDesc = (List<CustomDescInstance>) materialDesc.get("customDesc");
        Assert.assertSame(desc1, description1);
        Assert.assertSame(customDesc, cuList);

        Map<String, Object> materialDesc2 = (Map<String, Object>) stringObjectMap.get(materialPath2 + "_desc");
        String des2 = (String) materialDesc2.get("description");
        List<CustomDescInstance> customDesc2 = (List<CustomDescInstance>) materialDesc2.get("customDesc");
        Assert.assertSame(des2, description2);
        Assert.assertSame(customDesc2, cuList1);
    }

    //测试OrderLineItem的List为null
    @Test
    public void testListNull() {
        OrderLineItem orderLineItem = new OrderLineItem();
        orderLineItem.setMaterialDesc(null);
        Mockito.when(orderLineItemRepository.findById(Mockito.anyString())).thenReturn(orderLineItem);
        Exception exception = null;
        try {
            Map<String, Object> stringObjectMap = textParameterOrderComposingInfoFiller.orderlineItemProcess("1");
        } catch (Exception e) {
            exception = e;
        }
        Assert.assertNull(exception);
    }

    //MaterialDescInstance中的字段为null
    @Test
    public void testMaterialDescInstanceItemNull() {
        List<MaterialDescInstance> re = new ArrayList<>();
        MaterialDescInstance m1 = new MaterialDescInstance();
        m1.setDescription(null);
        m1.setMaterialPath(null);
        m1.setCustomDesc(null);

        MaterialDescInstance m2 = new MaterialDescInstance();
        m2.setDescription(null);
        m2.setMaterialPath(null);
        m2.setCustomDesc(null);

        re.add(m1);
        re.add(m2);
//        Map<String, Object> stringObjectMap = OrderlineItemProcess(re);
        OrderLineItem orderLineItem = new OrderLineItem();
        orderLineItem.setMaterialDesc(re);
        Mockito.when(orderLineItemRepository.findById(Mockito.anyString())).thenReturn(orderLineItem);
        Exception exception = null;
        try {
            Map<String, Object> stringObjectMap = textParameterOrderComposingInfoFiller.orderlineItemProcess("1");
        } catch (Exception e) {
            exception = e;
        }
        Assert.assertNull(exception);

//        Map<String, Object> materialDesc = (Map<String, Object>) stringObjectMap.get(materialPath1 + "_desc");
//        String desc1 = (String)materialDesc.get("description");
//        List<CustomDescInstance> customDesc = (List<CustomDescInstance>) materialDesc.get("customDesc");
//
//        Map<String, Object> materialDesc2 = (Map<String, Object>) stringObjectMap.get(materialPath2 + "_desc");
//        String des2= (String)materialDesc2.get("description");
//        List<CustomDescInstance> customDesc2 = (List<CustomDescInstance>) materialDesc2.get("customDesc");
    }

    //List中的MaterialDescInstance为null
    @Test
    public void testMaterialDescInstanceListIsNull() {
        List<MaterialDescInstance> re = new ArrayList<>();
        re.add(null);
        re.add(null);
//        Map<String, Object> stringObjectMap = OrderlineItemProcess(re);
        OrderLineItem orderLineItem = new OrderLineItem();
        orderLineItem.setMaterialDesc(re);
        Mockito.when(orderLineItemRepository.findById(Mockito.anyString())).thenReturn(orderLineItem);
        Exception exception = null;
        try {
            Map<String, Object> stringObjectMap = textParameterOrderComposingInfoFiller.orderlineItemProcess("1");
        } catch (Exception e) {
            exception = e;
        }
        Assert.assertNull(exception);
    }

    @Test
    public void testSamePath() {
        String materialPath1 = "1";
        String materialPath2 = "1";
        String description1 = "test1";
        String description2 = "test2";
        List<MaterialDescInstance> re = new ArrayList<>();
        MaterialDescInstance m1 = new MaterialDescInstance();
        m1.setDescription(description1);
        m1.setMaterialPath(materialPath1);
        CustomDescInstance cu1 = new CustomDescInstance();
        cu1.setKey("testKey1");
        cu1.setValue("testValue1");
        CustomDescInstance cu2 = new CustomDescInstance();
        cu2.setKey("testKey2");
        cu2.setValue("testValue2");
        List<CustomDescInstance> cuList = new ArrayList<>();
        cuList.add(cu1);
        cuList.add(cu2);
        m1.setCustomDesc(cuList);

        MaterialDescInstance m2 = new MaterialDescInstance();
        m2.setDescription("test2");
        m2.setMaterialPath(materialPath2);
        CustomDescInstance cu3 = new CustomDescInstance();
        cu3.setKey("testKey3");
        cu3.setValue("testValue3");
        CustomDescInstance cu4 = new CustomDescInstance();
        cu4.setKey("testKey4");
        cu4.setValue("testValue4");
        List<CustomDescInstance> cuList1 = new ArrayList<>();
        cuList1.add(cu3);
        cuList1.add(cu4);
        m2.setCustomDesc(cuList1);
        re.add(m1);
        re.add(m2);
        OrderLineItem orderLineItem = new OrderLineItem();
        orderLineItem.setMaterialDesc(re);
        Mockito.when(orderLineItemRepository.findById(Mockito.anyString())).thenReturn(orderLineItem);
        Exception e = null;
        try {
            textParameterOrderComposingInfoFiller.orderlineItemProcess("1");
        } catch (Exception exception) {
            e = exception;
        }
        Assert.assertNull(e);
    }
}
