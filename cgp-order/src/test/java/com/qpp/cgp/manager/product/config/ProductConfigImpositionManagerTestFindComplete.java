package com.qpp.cgp.manager.product.config;

import com.google.common.collect.ImmutableList;
import com.qpp.cgp.domain.bom.attribute.AttributesToRtType;
import com.qpp.cgp.domain.bom.attribute.RtAttributeDef;
import com.qpp.cgp.domain.bom.attribute.RtType;
import com.qpp.cgp.domain.bom.runtime.RtObject;
import com.qpp.cgp.domain.product.config.ProductConfigImposition;
import com.qpp.cgp.manager.bom.RtObjectManager;
import com.qpp.cgp.manager.bom.attribute.RtAttributeDefManager;
import com.qpp.cgp.manager.bom.attribute.RtTypeManager;
import org.assertj.core.api.Assertions;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;
import java.util.stream.Collectors;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;

@RunWith(MockitoJUnitRunner.class)
public class ProductConfigImpositionManagerTestFindComplete {

    @Spy
    @InjectMocks
    private ProductConfigImpositionManager productConfigImpositionManager;

    @Mock
    private RtTypeManager rtTypeManager;

    @Mock
    private RtAttributeDefManager rtAttributeDefManager;

    @Mock
    private RtObjectManager rtObjectManager;

    private ProductConfigImposition createProductConfigImposition() {
        ProductConfigImposition imposition = new ProductConfigImposition();
        imposition.setId(1L);

        RtType rtType = new RtType();
        rtType.setId("1");

        imposition.setUserParams(rtType);

        RtObject userParamDefaultValues = new RtObject();
        userParamDefaultValues.setId("1");

        imposition.setUserParamDefaultValues(userParamDefaultValues);

        return imposition;
    }

    private RtType createUserParams() {
        RtAttributeDef def1 = new RtAttributeDef();
        def1.setId("1");
        def1.setName("machine");
        AttributesToRtType attributes1 = new AttributesToRtType();
        attributes1.setRtAttributeDef(def1);

        RtAttributeDef def2 = new RtAttributeDef();
        def2.setId("2");
        def2.setName("material");
        AttributesToRtType attributes2 = new AttributesToRtType();
        attributes2.setRtAttributeDef(def2);

        RtAttributeDef def3 = new RtAttributeDef();
        def3.setId("3");
        def3.setName("remark");
        AttributesToRtType attributes3 = new AttributesToRtType();
        attributes3.setRtAttributeDef(def3);

        List<AttributesToRtType> attributesToRtTypes = new ArrayList<>();
        attributesToRtTypes.add(attributes1);
        attributesToRtTypes.add(attributes2);
        attributesToRtTypes.add(attributes3);

        RtType rtType = new RtType();
        rtType.setId("1");
        rtType.setAttributesToRtTypes(attributesToRtTypes);

        return rtType;
    }

    private List<RtAttributeDef> createRtAttributeDefs() {
        RtAttributeDef def1 = new RtAttributeDef();
        def1.setId("1");
        def1.setName("machine");
        AttributesToRtType attributes1 = new AttributesToRtType();
        attributes1.setRtAttributeDef(def1);

        RtAttributeDef def2 = new RtAttributeDef();
        def2.setId("2");
        def2.setName("material");
        AttributesToRtType attributes2 = new AttributesToRtType();
        attributes2.setRtAttributeDef(def2);

        RtAttributeDef def3 = new RtAttributeDef();
        def3.setId("3");
        def3.setName("remark");
        AttributesToRtType attributes3 = new AttributesToRtType();
        attributes3.setRtAttributeDef(def3);

        return ImmutableList.of(def1, def2, def3);
    }

    private RtObject createUserParamDefaultValues() {
        Map<String, Object> objectJSON = new HashMap<>();
        objectJSON.put("machine", "HP10000");
        objectJSON.put("material", "wood");
        objectJSON.put("remark", "test");
        RtObject userParamDefaultValues = new RtObject();
        userParamDefaultValues.setId("1");
        userParamDefaultValues.setObjectJSON(objectJSON);

        return userParamDefaultValues;
    }

    @Before
    public void init() {
        Mockito.when(rtTypeManager.findById(any())).thenReturn(createUserParams());

        List<RtAttributeDef> rtAttributeDefs = createRtAttributeDefs();
        Mockito.when(rtAttributeDefManager.findById(any())).thenAnswer(invocation -> {
            String id = invocation.getArgument(0);
            return rtAttributeDefs.stream()
                    .filter(def -> id.equalsIgnoreCase(def.getId()))
                    .findFirst()
                    .get();
        });

        Mockito.when(rtObjectManager.findById(any())).thenReturn(createUserParamDefaultValues());
    }

    @Test
    public void testFindComplete() {
        Mockito.doReturn(Optional.of(createProductConfigImposition()))
                .when(productConfigImpositionManager).findByProductId(anyLong());

        Optional<ProductConfigImposition> productConfigImpositionOptional
                = productConfigImpositionManager.findComplete(1L);
        ProductConfigImposition productConfigImposition = productConfigImpositionOptional.get();

        RtType userParams = productConfigImposition.getUserParams();
        List<AttributesToRtType> attributesToRtTypes = userParams.getAttributesToRtTypes();
        List<String> rtAttributeDefNames = attributesToRtTypes.stream()
                .map(AttributesToRtType::getRtAttributeDef)
                .map(RtAttributeDef::getName)
                .collect(Collectors.toList());
        Assertions.assertThat(rtAttributeDefNames).contains("machine", "material", "remark");

        RtObject userParamDefaultValues = productConfigImposition.getUserParamDefaultValues();
        List<String> objectJSONValues = userParamDefaultValues.getObjectJSON().entrySet()
                .stream()
                .map(Map.Entry::getValue)
                .map(value -> (String)value)
                .collect(Collectors.toList());
        Assertions.assertThat(objectJSONValues).contains("HP10000", "wood", "test");
    }

    @Test
    public void testFindCompleteUserParamsIsNull() {
        ProductConfigImposition productConfigImposition1 = createProductConfigImposition();
        productConfigImposition1.setUserParams(null);

        Mockito.doReturn(Optional.of(productConfigImposition1))
                .when(productConfigImpositionManager).findByProductId(anyLong());

        Optional<ProductConfigImposition> productConfigImpositionOptional
                = productConfigImpositionManager.findComplete(1L);
        ProductConfigImposition result = productConfigImpositionOptional.get();

        RtType userParams = result.getUserParams();
        Assertions.assertThat(userParams).isNull();

        RtObject userParamDefaultValues = result.getUserParamDefaultValues();
        List<String> objectJSONValues = userParamDefaultValues.getObjectJSON().entrySet()
                .stream()
                .map(Map.Entry::getValue)
                .map(value -> (String)value)
                .collect(Collectors.toList());
        Assertions.assertThat(objectJSONValues).contains("HP10000", "wood", "test");
    }

    @Test
    public void testFindCompleteUserParamsIdIsNull() {
        ProductConfigImposition productConfigImposition1 = createProductConfigImposition();
        productConfigImposition1.getUserParams().setId(null);

        Mockito.doReturn(Optional.of(productConfigImposition1))
                .when(productConfigImpositionManager).findByProductId(anyLong());

        Optional<ProductConfigImposition> productConfigImpositionOptional
                = productConfigImpositionManager.findComplete(1L);
        ProductConfigImposition result = productConfigImpositionOptional.get();

        RtType userParams = result.getUserParams();
        Assertions.assertThat(userParams).isNull();

        RtObject userParamDefaultValues = result.getUserParamDefaultValues();
        List<String> objectJSONValues = userParamDefaultValues.getObjectJSON().entrySet()
                .stream()
                .map(Map.Entry::getValue)
                .map(value -> (String)value)
                .collect(Collectors.toList());
        Assertions.assertThat(objectJSONValues).contains("HP10000", "wood", "test");
    }

    @Test
    public void testFindCompleteUserParamsAttributeIsNull() {
        RtType userParams = createUserParams();
        List<AttributesToRtType> attributesToRtTypes = userParams.getAttributesToRtTypes();
        attributesToRtTypes.set(0, null);
        attributesToRtTypes.get(1).setRtAttributeDef(null);
        attributesToRtTypes.get(2).getRtAttributeDef().setId(null);

        Mockito.when(rtTypeManager.findById(any())).thenReturn(userParams);

        Mockito.doReturn(Optional.of(createProductConfigImposition()))
                .when(productConfigImpositionManager).findByProductId(anyLong());

        Optional<ProductConfigImposition> productConfigImpositionOptional
                = productConfigImpositionManager.findComplete(1L);
        ProductConfigImposition result = productConfigImpositionOptional.get();

        RtType userParamsResult = result.getUserParams();
        List<AttributesToRtType> attributesToRtTypesResult = userParamsResult.getAttributesToRtTypes();
        Assertions.assertThat(attributesToRtTypesResult.get(0)).isNull();
        Assertions.assertThat(attributesToRtTypesResult.get(1).getRtAttributeDef()).isNull();
        Assertions.assertThat(attributesToRtTypesResult.get(2).getRtAttributeDef()).isNull();

        RtObject userParamDefaultValues = result.getUserParamDefaultValues();
        List<String> objectJSONValues = userParamDefaultValues.getObjectJSON().entrySet()
                .stream()
                .map(Map.Entry::getValue)
                .map(value -> (String)value)
                .collect(Collectors.toList());
        Assertions.assertThat(objectJSONValues).contains("HP10000", "wood", "test");
    }

    @Test
    public void testFindCompleteUserParamDefaultValuesIsNull() {
        ProductConfigImposition productConfigImposition = createProductConfigImposition();
        productConfigImposition.setUserParamDefaultValues(null);

        Mockito.doReturn(Optional.of(productConfigImposition))
                .when(productConfigImpositionManager).findByProductId(anyLong());

        Optional<ProductConfigImposition> productConfigImpositionOptional
                = productConfigImpositionManager.findComplete(1L);
        ProductConfigImposition result = productConfigImpositionOptional.get();

        RtType userParams = result.getUserParams();
        List<AttributesToRtType> attributesToRtTypes = userParams.getAttributesToRtTypes();
        List<String> rtAttributeDefNames = attributesToRtTypes.stream()
                .map(AttributesToRtType::getRtAttributeDef)
                .map(RtAttributeDef::getName)
                .collect(Collectors.toList());
        Assertions.assertThat(rtAttributeDefNames).contains("machine", "material", "remark");

        RtObject userParamDefaultValues = result.getUserParamDefaultValues();
        Assertions.assertThat(userParamDefaultValues).isNull();
    }

    @Test
    public void testFindCompleteUserParamDefaultValuesIdIsNull() {
        ProductConfigImposition productConfigImposition = createProductConfigImposition();
        productConfigImposition.getUserParamDefaultValues().setId(null);

        Mockito.doReturn(Optional.of(productConfigImposition))
                .when(productConfigImpositionManager).findByProductId(anyLong());

        Optional<ProductConfigImposition> productConfigImpositionOptional
                = productConfigImpositionManager.findComplete(1L);
        ProductConfigImposition result = productConfigImpositionOptional.get();

        RtType userParams = result.getUserParams();
        List<AttributesToRtType> attributesToRtTypes = userParams.getAttributesToRtTypes();
        List<String> rtAttributeDefNames = attributesToRtTypes.stream()
                .map(AttributesToRtType::getRtAttributeDef)
                .map(RtAttributeDef::getName)
                .collect(Collectors.toList());
        Assertions.assertThat(rtAttributeDefNames).contains("machine", "material", "remark");

        RtObject userParamDefaultValues = result.getUserParamDefaultValues();
        Assertions.assertThat(userParamDefaultValues).isNull();
    }
}
