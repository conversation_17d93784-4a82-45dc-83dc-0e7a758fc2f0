//package com.qpp.cgp.manager.partner.shipping;
//
//import com.google.common.collect.ImmutableList;
//import com.google.common.collect.ImmutableSet;
//import com.qpp.cgp.config.mongo.MongoTemplateFactory;
//import com.qpp.cgp.domain.bom.runtime.ProductInstance;
//import com.qpp.cgp.domain.common.Country;
//import com.qpp.cgp.domain.common.Currency;
//import com.qpp.cgp.domain.common.Zone;
//import com.qpp.cgp.domain.product.Product;
//import com.qpp.cgp.domain.product.SkuProduct;
//import com.qpp.cgp.domain.product.shipping.ProductShipping;
//import com.qpp.cgp.domain.shipment.*;
//import com.qpp.cgp.domain.shoppingcart.BaseShoppingCartItem;
//import com.qpp.cgp.domain.user.AddressBook;
//import com.qpp.cgp.dto.checkout.ProductQty;
//import com.qpp.cgp.dto.freight.FreightPriceCalculateDTO;
//import com.qpp.cgp.dto.freight.FreightPriceDTO;
//import com.qpp.cgp.dto.ps.ShippingMethodDTO;
//import com.qpp.cgp.manager.pricing.ProductPricingManager;
//import com.qpp.cgp.manager.shipment.PostageConfigManager;
//import com.qpp.cgp.manager.shipment.ShipmentConfigManager;
//import com.qpp.cgp.manager.shipment.WeightBasedPostageRulesCalculateService;
//import com.qpp.cgp.repository.user.AddressBookRepository;
//import com.qpp.cgp.service.product.shipping.ProductAreaShippingCalculateService;
//import com.qpp.core.utils.Platform;
//import org.mockito.Mockito;
//import org.powermock.reflect.Whitebox;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.data.mongodb.core.query.Criteria;
//import org.springframework.data.mongodb.core.query.Query;
//import org.testng.Assert;
//import org.testng.annotations.Test;
//
//import java.math.BigDecimal;
//import java.util.*;
//import java.util.stream.Collectors;
//
//public class PartnerShippingServiceTest {
//
//
//
//    @Test
//    public void testFindAvailableAllShipping() {
//        PartnerShippingService partnerShippingService = new PartnerShippingService();
//        MongoTemplateFactory mongoTemplateFactory = Mockito.mock(MongoTemplateFactory.class);
//        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
//        Mockito.when(mongoTemplateFactory.getMongoTemplate(ShipmentConfig.class)).thenReturn(mongoTemplate);
//        Whitebox.setInternalState(partnerShippingService, "mongoTemplateFactory", mongoTemplateFactory);
//        Query query = Query.query(Criteria.where("available").is(true).and("platform").is(Platform.QPMN));
//        List<ShipmentConfig> availableAllShipping = new ArrayList<>();
//        ShipmentConfig shipmentconfig = new ShipmentConfig();
//        shipmentconfig.setCode("Standard");
//        shipmentconfig.setDescription("StandardDescription");
//        shipmentconfig.setTitle("StandardTitle");
//        shipmentconfig.setSortOrder(1);
//        shipmentconfig.setId("1289321");
//        availableAllShipping.add(shipmentconfig);
//        Mockito.when(mongoTemplate.find(query, ShipmentConfig.class)).thenReturn(availableAllShipping);
//        List<ShippingMethodDTO> result = partnerShippingService.findAvailableAllShipping();
//        Assert.assertNotNull(result);
//        Assert.assertEquals(result.size(), 1);
//        ShippingMethodDTO shippingMethodDTO = result.get(0);
//        Assert.assertEquals(shippingMethodDTO.getCode(), shipmentconfig.getCode());
//        Assert.assertEquals(shippingMethodDTO.getId(), shipmentconfig.getId().toString());
//        Assert.assertEquals(shippingMethodDTO.getTitle(), shipmentconfig.getTitle());
//        Assert.assertEquals(shippingMethodDTO.getDescription(), shipmentconfig.getDescription());
//        Assert.assertEquals(shippingMethodDTO.getSortOrder(), shipmentconfig.getSortOrder());
//        Mockito.verify(mongoTemplateFactory).getMongoTemplate(ShipmentConfig.class);
//        Mockito.verify(mongoTemplate).find(query, ShipmentConfig.class);
//    }
//
//    @Test
//    public void testFreightPriceByQty() {
//        PartnerShippingService spy = Mockito.spy(new PartnerShippingService());
//        FreightPriceCalculateDTO freightPriceCalculateDTO = new FreightPriceCalculateDTO();
//        freightPriceCalculateDTO.setShippingMethodId("12389132");
//        freightPriceCalculateDTO.setShippingMethodCode("Standard");
//        freightPriceCalculateDTO.setShoppingCartItemIds(ImmutableList.of("12389"));
//        freightPriceCalculateDTO.setDeliveryAddressId("12839");
//        ShipmentConfig shipmentConfig = new ShipmentConfig();
//        shipmentConfig.setPostageCountType(PostageCountType.QTY_BASED);
//        ShipmentConfigManager shipmentConfigManager = Mockito.mock(ShipmentConfigManager.class);
//        Mockito.when(shipmentConfigManager.findById(freightPriceCalculateDTO.getShippingMethodId())).thenReturn(shipmentConfig);
//        AddressBookRepository addressBookRepository = Mockito.mock(AddressBookRepository.class);
//        AddressBook addressBook = new AddressBook();
//        addressBook.setCountryCode2("US");
//        addressBook.setState("NY");
//        Mockito.when(addressBookRepository.findById(Long.valueOf(freightPriceCalculateDTO.getDeliveryAddressId()))).thenReturn(Optional.of(addressBook));
//        Whitebox.setInternalState(spy, "addressBookRepository", addressBookRepository);
//        Whitebox.setInternalState(spy, "shipmentConfigManager", shipmentConfigManager);
//        List<ProductQty> productQtyList = new ArrayList<>();
//        Mockito.doReturn(productQtyList).when(spy).convertProductIdAndQtyMap(freightPriceCalculateDTO.getShoppingCartItemIds());
//        Long countryId = 123831789L;
//        Long zoneId = 123891293L;
//        Mockito.doReturn(countryId).when(spy).findByCountryCode(addressBook.getCountryCode2());
//        Mockito.doReturn(zoneId).when(spy).findZoneIdByState(addressBook.getState());
//        Mockito.doReturn(20.0).when(spy).qtyShipmentPrice(productQtyList, countryId, zoneId);
//        ProductPricingManager productPricingManager = Mockito.mock(ProductPricingManager.class);
//        Whitebox.setInternalState(spy, "productPricingManager", productPricingManager);
//        Currency currency = new Currency();
//        Mockito.when(productPricingManager.getQPMNCurrency()).thenReturn(currency);
//        FreightPriceDTO freightPriceDTO = spy.freightPrice(freightPriceCalculateDTO);
//        Assert.assertNotNull(freightPriceDTO);
//        Assert.assertNotNull(freightPriceDTO.getPrice());
//        Assert.assertEquals(freightPriceDTO.getPrice().doubleValue(), 20.0);
//        Assert.assertEquals(freightPriceDTO.getCurrency(), currency);
//        Mockito.verify(productPricingManager).getQPMNCurrency();
//        Mockito.verify(addressBookRepository).findById(Long.valueOf(freightPriceCalculateDTO.getDeliveryAddressId()));
//        Mockito.verify(shipmentConfigManager).findById(freightPriceCalculateDTO.getShippingMethodId());
//        Mockito.verify(spy).convertProductIdAndQtyMap(freightPriceCalculateDTO.getShoppingCartItemIds());
//        Mockito.verify(spy).findByCountryCode(addressBook.getCountryCode2());
//        Mockito.verify(spy).findZoneIdByState(addressBook.getState());
//        Mockito.verify(spy).qtyShipmentPrice(productQtyList, countryId, zoneId);
//    }
//
//    @Test
//    public void testFreightPriceByWeight() {
//        PartnerShippingService spy = Mockito.spy(new PartnerShippingService());
//        FreightPriceCalculateDTO freightPriceCalculateDTO = new FreightPriceCalculateDTO();
//        freightPriceCalculateDTO.setShippingMethodId("12389132");
//        freightPriceCalculateDTO.setShippingMethodCode("Standard");
//        freightPriceCalculateDTO.setShoppingCartItemIds(ImmutableList.of("12389"));
//        freightPriceCalculateDTO.setDeliveryAddressId("12839");
//        ShipmentConfig shipmentConfig = new ShipmentConfig();
//        shipmentConfig.setPostageCountType(PostageCountType.WEIGHT_BASED);
//        PostageConfig postageConfig = new PostageConfig();
//        postageConfig.setId("12389312");
//        shipmentConfig.setPostageConfig(postageConfig);
//        ShipmentConfigManager shipmentConfigManager = Mockito.mock(ShipmentConfigManager.class);
//        Mockito.when(shipmentConfigManager.findById(freightPriceCalculateDTO.getShippingMethodId())).thenReturn(shipmentConfig);
//        AddressBookRepository addressBookRepository = Mockito.mock(AddressBookRepository.class);
//        AddressBook addressBook = new AddressBook();
//        addressBook.setCountryCode2("US");
//        addressBook.setState("NY");
//        Mockito.when(addressBookRepository.findById(Long.valueOf(freightPriceCalculateDTO.getDeliveryAddressId()))).thenReturn(Optional.of(addressBook));
//        Whitebox.setInternalState(spy, "addressBookRepository", addressBookRepository);
//        Whitebox.setInternalState(spy, "shipmentConfigManager", shipmentConfigManager);
//        List<ProductQty> productQtyList = new ArrayList<>();
//        productQtyList.add(new ProductQty(1238988L, 20, Optional.empty()));
//        Mockito.doReturn(productQtyList).when(spy).convertProductIdAndQtyMap(freightPriceCalculateDTO.getShoppingCartItemIds());
//        Long countryId = 123831789L;
//        Long zoneId = 123891293L;
//        Mockito.doReturn(countryId).when(spy).findByCountryCode(addressBook.getCountryCode2());
//        Mockito.doReturn(zoneId).when(spy).findZoneIdByState(addressBook.getState());
//        Mockito.doReturn(204.0).when(spy).calculateProductListTotalWeight(Mockito.argThat(e -> e.containsAll(productQtyList.stream().map(ProductQty::getProductId).collect(Collectors.toSet()))));
//        Mockito.doReturn(20.0).when(spy).calculateWeightShipmentPrice(shipmentConfig.getPostageConfig(), countryId, 204.0, 0);
//        ProductPricingManager productPricingManager = Mockito.mock(ProductPricingManager.class);
//        Whitebox.setInternalState(spy, "productPricingManager", productPricingManager);
//        Currency currency = new Currency();
//        Mockito.when(productPricingManager.getQPMNCurrency()).thenReturn(currency);
//        FreightPriceDTO freightPriceDTO = spy.freightPrice(freightPriceCalculateDTO);
//        Assert.assertNotNull(freightPriceDTO);
//        Assert.assertNotNull(freightPriceDTO.getPrice());
//        Assert.assertEquals(freightPriceDTO.getPrice().doubleValue(), 20.0);
//        Assert.assertEquals(freightPriceDTO.getCurrency(), currency);
//        Mockito.verify(productPricingManager).getQPMNCurrency();
//        Mockito.verify(addressBookRepository).findById(Long.valueOf(freightPriceCalculateDTO.getDeliveryAddressId()));
//        Mockito.verify(shipmentConfigManager).findById(freightPriceCalculateDTO.getShippingMethodId());
//        Mockito.verify(spy).convertProductIdAndQtyMap(freightPriceCalculateDTO.getShoppingCartItemIds());
//        Mockito.verify(spy).findByCountryCode(addressBook.getCountryCode2());
//        Mockito.verify(spy).findZoneIdByState(addressBook.getState());
//        Mockito.verify(spy).calculateProductListTotalWeight(Mockito.argThat(e -> e.containsAll(productQtyList.stream().map(ProductQty::getProductId).collect(Collectors.toSet()))));
//        Mockito.verify(spy).calculateWeightShipmentPrice(shipmentConfig.getPostageConfig(), countryId, 204.0, 0);
//    }
//
//    @Test
//    public void testCalculateProductListTotalWeight() {
//        PartnerShippingService partnerShippingService = new PartnerShippingService();
//        MongoTemplateFactory mongoTemplateFactory = Mockito.mock(MongoTemplateFactory.class);
//        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
//        Whitebox.setInternalState(partnerShippingService, "mongoTemplateFactory", mongoTemplateFactory);
//        Mockito.when(mongoTemplateFactory.getMongoTemplate(Product.class)).thenReturn(mongoTemplate);
//        ImmutableList<Long> productIds = ImmutableList.of(123L);
//        Query query = Query.query(Criteria.where("_id").in(productIds));
//        SkuProduct skuProduct = new SkuProduct();
//        skuProduct.setId(123L);
//        skuProduct.setWeight(20.0);
//        Mockito.when(mongoTemplate.find(query, Product.class)).thenReturn(ImmutableList.of(skuProduct));
//        double result = partnerShippingService.calculateProductListTotalWeight(productIds);
//        Assert.assertEquals(20.0, result);
//        Mockito.verify(mongoTemplateFactory).getMongoTemplate(Product.class);
//        Mockito.verify(mongoTemplate).find(query, Product.class);
//    }
//
//    @Test
//    public void testConvertProductIdAndQtyMap() {
//        PartnerShippingService partnerShippingService = new PartnerShippingService();
//        MongoTemplateFactory mongoTemplateFactory = Mockito.mock(MongoTemplateFactory.class);
//        Whitebox.setInternalState(partnerShippingService, "mongoTemplateFactory", mongoTemplateFactory);
//        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
//        Mockito.when(mongoTemplateFactory.getMongoTemplate(ProductInstance.class)).thenReturn(mongoTemplate);
//        MongoTemplate mongoTemplate1 = Mockito.mock(MongoTemplate.class);
//        Mockito.when(mongoTemplateFactory.getMongoTemplate(BaseShoppingCartItem.class)).thenReturn(mongoTemplate1);
//        BaseShoppingCartItem baseShoppingCartItem = new BaseShoppingCartItem();
//        baseShoppingCartItem.setProductId(123L);
//        baseShoppingCartItem.setQty(12);
//        BaseShoppingCartItem baseShoppingCartItem1 = new BaseShoppingCartItem();
//        baseShoppingCartItem1.setQty(1238);
//        baseShoppingCartItem1.setProductInstanceId("132189");
//        Query query = Query.query(Criteria.where("_id").in(ImmutableSet.of(baseShoppingCartItem1.getProductInstanceId())));
//        query.fields().include("productId");
//        ProductInstance productInstance = new ProductInstance();
//        productInstance.setId(baseShoppingCartItem1.getProductInstanceId());
//        productInstance.setProductId(1238999L);
//        Mockito.when(mongoTemplate.find(query, ProductInstance.class)).thenReturn(ImmutableList.of(productInstance));
//        Query query1 = Query.query(Criteria.where("_id").in(ImmutableList.of("12389123", "1238921893")));
//        Mockito.when(mongoTemplate1.find(query1, BaseShoppingCartItem.class)).thenReturn(ImmutableList.of(baseShoppingCartItem, baseShoppingCartItem1));
//        List<ProductQty> result = partnerShippingService.convertProductIdAndQtyMap(ImmutableList.of("12389123", "1238921893"));
//        Assert.assertNotNull(result);
//        Assert.assertEquals(result.size(), 2);
//        Assert.assertNotNull(result.stream().collect(Collectors.toMap(ProductQty::getProductId, ProductQty::getQty)).get(baseShoppingCartItem.getProductId()));
//        Assert.assertEquals(result.stream().collect(Collectors.toMap(ProductQty::getProductId, ProductQty::getQty)).get(baseShoppingCartItem.getProductId()), baseShoppingCartItem.getQty());
//        Assert.assertNotNull(result.stream().collect(Collectors.toMap(ProductQty::getProductId, ProductQty::getQty)).get(productInstance.getProductId()));
//        Assert.assertEquals(result.stream().collect(Collectors.toMap(ProductQty::getProductId, ProductQty::getQty)).get(productInstance.getProductId()), baseShoppingCartItem1.getQty());
//        Mockito.verify(mongoTemplateFactory).getMongoTemplate(BaseShoppingCartItem.class);
//        Mockito.verify(mongoTemplateFactory).getMongoTemplate(ProductInstance.class);
//        Mockito.verify(mongoTemplate).find(query, ProductInstance.class);
//        Mockito.verify(mongoTemplate1).find(query1, BaseShoppingCartItem.class);
//    }
//
//    @Test
//    public void testQtyShipmentPrice() {
//        PartnerShippingService partnerShippingService = new PartnerShippingService();
//        MongoTemplateFactory mongoTemplateFactory = Mockito.mock(MongoTemplateFactory.class);
//        ProductAreaShippingCalculateService productAreaShippingCalculateService = Mockito.mock(ProductAreaShippingCalculateService.class);
//        Whitebox.setInternalState(partnerShippingService, "productAreaShippingCalculateService", productAreaShippingCalculateService);
//        List<ProductQty> productQtyList = new ArrayList<>();
//        productQtyList.add(new ProductQty(333L, 20, Optional.empty()));
//        Long countryId = 123L;
//        Long zoneId = 111L;
//        ProductShipping productShipping = new ProductShipping();
//        productShipping.setValue(BigDecimal.valueOf(20.34));
//        Mockito.when(productAreaShippingCalculateService.calculate(333L, 20, countryId, zoneId, Optional.empty())).thenReturn(productShipping);
//        double result = partnerShippingService.qtyShipmentPrice(productQtyList, countryId, zoneId);
//        Assert.assertEquals(result, 20.34);
//        Mockito.verify(productAreaShippingCalculateService).calculate(333L, 20, countryId, zoneId, Optional.empty());
//    }
//
//    @Test
//    public void testFindZoneIdByStateByMatchCode() {
//        //
//        PartnerShippingService partnerShippingService = new PartnerShippingService();
//        MongoTemplateFactory mongoTemplateFactory = Mockito.mock(MongoTemplateFactory.class);
//        String state = "NY";
//        Whitebox.setInternalState(partnerShippingService, "mongoTemplateFactory", mongoTemplateFactory);
//        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
//        Mockito.when(mongoTemplateFactory.getMongoTemplate(Zone.class)).thenReturn(mongoTemplate);
//        Query query = Query.query(Criteria.where("code").is(state));
//        Zone zone = new Zone();
//        zone.setId(12389L);
//        Mockito.when(mongoTemplate.findOne(query, Zone.class)).thenReturn(zone);
//        Long zoneIdByState = partnerShippingService.findZoneIdByState(state);
//        Assert.assertNotNull(zoneIdByState);
//        Assert.assertEquals(zoneIdByState, zone.getId());
//        Mockito.verify(mongoTemplateFactory).getMongoTemplate(Zone.class);
//        Mockito.verify(mongoTemplate).findOne(query, Zone.class);
//    }
//
//    @Test
//    public void testFindZoneIdByStateByMatchName() {
//        PartnerShippingService partnerShippingService = new PartnerShippingService();
//        MongoTemplateFactory mongoTemplateFactory = Mockito.mock(MongoTemplateFactory.class);
//        //
//        String state = "NY";
//        Whitebox.setInternalState(partnerShippingService, "mongoTemplateFactory", mongoTemplateFactory);
//        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
//        Mockito.when(mongoTemplateFactory.getMongoTemplate(Zone.class)).thenReturn(mongoTemplate);
//        Query query = Query.query(Criteria.where("name").is(state));
//        Query queryByCode = Query.query(Criteria.where("code").is(state));
//        Zone zone = new Zone();
//        zone.setId(12389L);
//        Mockito.when(mongoTemplate.findOne(query, Zone.class)).thenReturn(zone);
//        Mockito.when(mongoTemplate.findOne(queryByCode, Zone.class)).thenReturn(null);
//        Long zoneIdByState = partnerShippingService.findZoneIdByState(state);
//        Assert.assertNotNull(zoneIdByState);
//        Assert.assertEquals(zoneIdByState, zone.getId());
//        Mockito.verify(mongoTemplateFactory).getMongoTemplate(Zone.class);
//        Mockito.verify(mongoTemplate).findOne(query, Zone.class);
//        Mockito.verify(mongoTemplate).findOne(queryByCode, Zone.class);
//    }
//
//    @Test
//    public void testFindZoneIdByStateByNotMatch() {
//        //
//        PartnerShippingService partnerShippingService = new PartnerShippingService();
//        MongoTemplateFactory mongoTemplateFactory = Mockito.mock(MongoTemplateFactory.class);
//        String state = "NY";
//        Whitebox.setInternalState(partnerShippingService, "mongoTemplateFactory", mongoTemplateFactory);
//        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
//        Mockito.when(mongoTemplateFactory.getMongoTemplate(Zone.class)).thenReturn(mongoTemplate);
//        Query query = Query.query(Criteria.where("name").is(state));
//        Query queryByCode = Query.query(Criteria.where("code").is(state));
//        Mockito.when(mongoTemplate.findOne(query, Zone.class)).thenReturn(null);
//        Mockito.when(mongoTemplate.findOne(queryByCode, Zone.class)).thenReturn(null);
//        Long zoneIdByState = partnerShippingService.findZoneIdByState(state);
//        Assert.assertNull(zoneIdByState);
//        Mockito.verify(mongoTemplateFactory).getMongoTemplate(Zone.class);
//        Mockito.verify(mongoTemplate).findOne(query, Zone.class);
//        Mockito.verify(mongoTemplate).findOne(queryByCode, Zone.class);
//    }
//
//    @Test
//    public void testFindByCountryCode() {
//        PartnerShippingService partnerShippingService = new PartnerShippingService();
//        MongoTemplateFactory mongoTemplateFactory = Mockito.mock(MongoTemplateFactory.class);
//        String countryCode = "US";
//        Whitebox.setInternalState(partnerShippingService, "mongoTemplateFactory", mongoTemplateFactory);
//        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
//        Mockito.when(mongoTemplateFactory.getMongoTemplate(Country.class)).thenReturn(mongoTemplate);
//        Query query = Query.query(Criteria.where("isoCode2").is(countryCode));
//        Country country = new Country();
//        country.setId(12389L);
//        Mockito.when(mongoTemplate.findOne(query, Country.class)).thenReturn(country);
//        Long result = partnerShippingService.findByCountryCode(countryCode);
//        Assert.assertNotNull(result);
//        Assert.assertEquals(result, country.getId());
//        Mockito.verify(mongoTemplateFactory).getMongoTemplate(Country.class);
//        Mockito.verify(mongoTemplate).findOne(query, Country.class);
//    }
//
//    @Test
//    public void testCalculateWeightShipmentPrice() {
//        PartnerShippingService partnerShippingService = new PartnerShippingService();
//        MongoTemplateFactory mongoTemplateFactory = Mockito.mock(MongoTemplateFactory.class);
//        PostageConfig postageConfig = new PostageConfig();
//        postageConfig.setId("12389");
//        Long countryId = 1238899L;
//        double totalWeight = 20.39;
//        double totalTax = 0;
//        PostageConfigManager postageConfigManager = Mockito.mock(PostageConfigManager.class);
//        WeightBasedPostageConfig postageConfigEntity = new WeightBasedPostageConfig();
//        List<AreaWeightBasedPostageConfig> areaWeightBasedPostageConfigs = new ArrayList<>();
//        AreaWeightBasedPostageConfig areaWeightBasedPostageConfig = new AreaWeightBasedPostageConfig();
//        Area area = new Area();
//        Country country = new Country();
//        country.setId(countryId);
//        area.setCountry(country);
//        List<WeightBasedPostageRule> weightBasedPostageRules = new ArrayList<>();
//        areaWeightBasedPostageConfig.setWeightBasedPostageRules(weightBasedPostageRules);
//        areaWeightBasedPostageConfig.setArea(area);
//        areaWeightBasedPostageConfigs.add(areaWeightBasedPostageConfig);
//        postageConfigEntity.setAreaWeightBasedPostageConfigs(areaWeightBasedPostageConfigs);
//        WeightBasedPostageRulesCalculateService weightBasedPostageRulesCalculateService = Mockito.mock(WeightBasedPostageRulesCalculateService.class);
//        Whitebox.setInternalState(partnerShippingService, "weightBasedPostageRulesCalculateService", weightBasedPostageRulesCalculateService);
//        Mockito.when(weightBasedPostageRulesCalculateService.calculate(weightBasedPostageRules, totalWeight, totalTax)).thenReturn(300.0);
//        Whitebox.setInternalState(partnerShippingService, "postageConfigManager", postageConfigManager);
//        Mockito.when(postageConfigManager.findById(postageConfig.getId())).thenReturn(postageConfigEntity);
//        double result = partnerShippingService.calculateWeightShipmentPrice(postageConfig, countryId, totalWeight, totalTax);
//        Assert.assertEquals(result, 300.0);
//        Mockito.verify(weightBasedPostageRulesCalculateService).calculate(weightBasedPostageRules, totalWeight, totalTax);
//        Mockito.verify(postageConfigManager).findById(postageConfig.getId());
//    }
//}