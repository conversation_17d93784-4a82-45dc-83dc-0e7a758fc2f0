package com.qpp.cgp.manager.listener.order;

import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.qpp.cgp.domain.order.Order;
import com.qpp.cgp.domain.order.design.UserDesignDistributeInfoDTO;
import com.qpp.cgp.event.order.OrderCreatedEvent;
import com.qpp.cgp.service.common.CommonPropertiesService;
import com.qpp.cgp.service.order.design.UserDesignDistributeInfoGenerateService;
import com.qpp.cgp.service.order.design.UserDesignDistributeRemoteService;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import java.io.IOException;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @Package com.qpp.cgp.manager.listener.order
 * @Date 2024/5/28 16:39
 */
@ExtendWith(MockitoExtension.class)
public class UserDesignDistributeListenerTest {

    @InjectMocks
    @Spy
    private UserDesignDistributeListener userDesignDistributeListener;

    @Mock
    private UserDesignDistributeRemoteService userDesignDistributeRemoteService;

    @Mock
    private CommonPropertiesService commonPropertiesService;

    @Mock
    private UserDesignDistributeInfoGenerateService userDesignDistributeInfoGenerateService;

    @Test
    public void testDistributeUserDesign() {
        ArgumentCaptor<List> dtoInfosCaptor = ArgumentCaptor.forClass(List.class);

        List<UserDesignDistributeInfoDTO> userDesignDistributeInfoDTOS = generateDTO();
        Mockito.when(userDesignDistributeInfoGenerateService.generate(any())).thenReturn(userDesignDistributeInfoDTOS);

        Mockito.when(commonPropertiesService.getKeyValueAndApplicationMode(any())).thenReturn("http://localhost:8080");

        Mockito.doNothing().when(userDesignDistributeRemoteService).distributeUserDesign(any(), dtoInfosCaptor.capture());

        Order order = new Order();
        order.setId("84552164");
        OrderCreatedEvent event =  new OrderCreatedEvent(order);

        userDesignDistributeListener.distributeUserDesign(event);

        Assertions.assertThat(dtoInfosCaptor.getAllValues().size()).isEqualTo(1);
    }

    private List<UserDesignDistributeInfoDTO> generateDTO() {
        String json = "[{\"orderNumber\":\"TM2405270026\",\"seqNo\":\"01\",\"materialName\":\"Box lid\",\"originalFileName\":null,\"fileHashValue\":null,\"productMaterialViewId\":\"60367368\",\"pageContentDistributeInfo\":{\"id\":\"84552131\",\"layers\":[{\"idPath\":\"84551957,1\",\"readOnly\":false,\"_id\":\"44916619\",\"clazz\":\"Layer\",\"items\":[{\"_id\":\"44937600\",\"uniqueId\":\"2\",\"idPath\":\"84551957,1,2\",\"clazz\":\"Container\",\"tags\":[],\"readOnly\":false,\"x\":0,\"y\":0,\"width\":471.23149606299216,\"height\":400.3653543307087,\"clipPath\":{\"_id\":\"48853422\",\"clazz\":\"Path\",\"d\":\"M97.0016 0.1417 L374.2299 0.1417 L374.2299 37.2756 L411.9307 37.2756 L411.9307 96.1512 L418.4901 96.1512 L423.0255 97.852 L471.0898 97.852 L471.0898 302.5134 L423.0255 302.5134 L418.4901 304.2142 L411.9307 304.2142 L411.9307 363.0898 L374.2299 363.0898 L374.2299 400.2236 L97.0016 400.2236 L97.0016 363.0898 L59.3008 363.0898 L59.3008 304.2142 L52.7414 304.2142 L48.206 302.5134 L0.1417 302.5134 L0.1417 97.852 L48.206 97.852 L52.7414 96.1512 L59.3008 96.1512 L59.3008 37.2756 L97.0016 37.2756 L97.0016 0.1417\"},\"items\":[{\"_id\":\"44916623\",\"uniqueId\":\"3\",\"idPath\":\"84551957,1,2,3\",\"clazz\":\"UploadPicture\",\"tags\":[],\"readOnly\":false,\"x\":0,\"y\":0,\"width\":471.23149606299216,\"height\":400.3653543307087,\"printFile\":\"f832acc27a6eb4317abf47558da56141.pdf\",\"imageName\":\"f832acc27a6eb4317abf47558da56141.pdf\",\"displayName\":\"PRINT_TYPE\",\"originalFileName\":\"2024-05-27_length_90_width_65_height_32.14_box_top_lux_gamebox.pdf\",\"templateConfigGroupId\":\"60389850\",\"url\":\"https://sz-nginx-test1.qppdev.com/file/file/f832acc27a6eb4317abf47558da56141.pdf\"}]}],\"uniqueId\":\"1\",\"tags\":[\"4c\"]}]}}]";
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JavaType dtoType = objectMapper.getTypeFactory().constructCollectionType(List.class, UserDesignDistributeInfoDTO.class);
            List<UserDesignDistributeInfoDTO> dtoInfos = objectMapper.readValue(json, dtoType);
            return dtoInfos;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
