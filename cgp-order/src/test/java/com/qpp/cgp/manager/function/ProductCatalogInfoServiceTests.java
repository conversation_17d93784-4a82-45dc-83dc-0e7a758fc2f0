package com.qpp.cgp.manager.function;

import com.qpp.cgp.domain.cms.ProductsOfCatalogCMSConfig;
import com.qpp.cgp.domain.funtion.calender.ProductCatalogInfoOutput;
import com.qpp.cgp.domain.product.category.ProductCategory;
import com.qpp.cgp.domain.product.category.SubProductCategory;
import com.qpp.cgp.manager.cms.CmsConfigManager;
import com.qpp.cgp.manager.function.service.ProductCatalogInfoService;
import com.qpp.cgp.repository.product.category.ProductCategoryRepository;
import com.qpp.cgp.service.cms.CmsSaasService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2022/4/29 17:25
 * @Version 1.0
 */

@RunWith(PowerMockRunner.class)
@PrepareForTest({CmsSaasService.class, ProductCategoryRepository.class, CmsConfigManager.class})
@PowerMockIgnore({"javax.management.*", "org.w3c.dom.*", "org.apache.log4j.*", "org.xml.sax.*", "javax.xml.*", "javax.script.*"})
public class ProductCatalogInfoServiceTests {
    @Test
    public void getProductCatalogInfoByProductCategoryIdTestProductCategoryIsCanFind(){
        ProductCatalogInfoService productCatalogInfoService=new ProductCatalogInfoService();
        CmsSaasService cmsSaasService = PowerMockito.mock(CmsSaasService.class);
        ProductCategoryRepository productCategoryRepository = PowerMockito.mock(ProductCategoryRepository.class);
        CmsConfigManager cmsConfigManager = PowerMockito.mock(CmsConfigManager.class);
        ReflectionTestUtils.setField(productCatalogInfoService,"cmsSaasService",cmsSaasService);
        ReflectionTestUtils.setField(productCatalogInfoService,"productCategoryRepository",productCategoryRepository);
        ReflectionTestUtils.setField(productCatalogInfoService,"cmsConfigManager",cmsConfigManager);

        Long productCategoryId=100L;
        ProductCategory productCategory=new SubProductCategory();
        Long parentId=200L;
        productCategory.setParentId(parentId);
        productCategory.setName("abc");
        productCategory.setIsLeaf(true);
        productCategory.setStatus(1);
        Optional<ProductCategory> optionalProductCategory = Optional.of(productCategory);
        PowerMockito.when(productCategoryRepository.findById(productCategoryId)).thenReturn(optionalProductCategory);

        ProductsOfCatalogCMSConfig productsOfCatalogCMSConfig=new ProductsOfCatalogCMSConfig();
        String CmsPageId="111";
        productsOfCatalogCMSConfig.setCmsPageId(CmsPageId);
        PowerMockito.when(cmsConfigManager.getProductsOfCatalogCMSConfigBySubProductCategoryId(productCategoryId)).thenReturn(productsOfCatalogCMSConfig);
        String cmsPageOutputDir="/avc";
        PowerMockito.when(cmsSaasService.getCmsPageOutputDir(CmsPageId)).thenReturn(cmsPageOutputDir);

        ProductCatalogInfoOutput productCatalogInfoOutput = productCatalogInfoService.getProductCatalogInfoByProductCategoryId(productCategoryId);
        Assert.assertEquals(productCategoryId,productCatalogInfoOutput.getId());
        Assert.assertEquals(parentId,productCatalogInfoOutput.getParent().getId());
    }
}
