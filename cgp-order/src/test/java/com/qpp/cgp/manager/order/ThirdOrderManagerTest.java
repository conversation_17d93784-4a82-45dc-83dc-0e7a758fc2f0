package com.qpp.cgp.manager.order;

import com.google.common.collect.ImmutableList;
import com.qpp.cgp.advice.CheckedBusinessException;
import com.qpp.cgp.config.mongo.MongoTemplateFactory;
import com.qpp.cgp.domain.attributecalculate.PropertyModel;
import com.qpp.cgp.domain.bom.runtime.ProductInstance;
import com.qpp.cgp.domain.dto.order.ProjectOrderDTO;
import com.qpp.cgp.domain.dto.order.ProjectOrderLineItemDTO;
import com.qpp.cgp.domain.partner.store.*;
import com.qpp.cgp.domain.product.SkuProduct;
import com.qpp.cgp.domain.project.CustomizeProjectDTO;
import com.qpp.cgp.manager.partner.qpmn.ShopOwnerMatcher;
import com.qpp.cgp.repository.project.dto.ImageProjectDTO;
import com.qpp.core.exception.BusinessException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.ArrayList;
import java.util.List;

public class ThirdOrderManagerTest {

    ThirdOrderManager thirdOrderManager;
    MongoTemplateFactory mongoTemplateFactory;
    ThirdOrderManager spy;
    @Before
    public void before(){
        spy = Mockito.spy(ThirdOrderManager.class);
        thirdOrderManager= new ThirdOrderManager();
        mongoTemplateFactory = Mockito.mock(MongoTemplateFactory.class);
        Whitebox.setInternalState(thirdOrderManager, "mongoTemplateFactory", mongoTemplateFactory);
        Whitebox.setInternalState(spy, "mongoTemplateFactory", mongoTemplateFactory);

    }



    @Test
    public void checkStoreProductIsBelongCurrentPartnerStore() {
        ProjectOrderDTO projectOrderDTO = new ProjectOrderDTO();
        Exception exception = null;
        try {
            thirdOrderManager.checkStoreProductIsBelongCurrentPartnerStore(projectOrderDTO);
        } catch (Exception e) {
            exception = e;
        }
        Assert.assertNotNull(exception);
        Assert.assertEquals(2700412, ((BusinessException) exception).getCode());
        ProjectOrderDTO projectOrderDTO1 = new ProjectOrderDTO();
        List<ProjectOrderLineItemDTO> projectOrderLineItemDTOList = new ArrayList<>();
        ProjectOrderLineItemDTO projectOrderLineItemDTO = new ProjectOrderLineItemDTO();
        projectOrderLineItemDTO.setProductInstanceId("123");
        projectOrderLineItemDTOList.add(projectOrderLineItemDTO);
        projectOrderDTO1.setLineItems(projectOrderLineItemDTOList);
        Mockito.doNothing().when(spy).checkProductInstanceStoreProductIsBelongPartner(projectOrderLineItemDTO.getProductInstanceId());
        spy.checkStoreProductIsBelongCurrentPartnerStore(projectOrderDTO1);
        Mockito.verify(spy, Mockito.times(1)).checkProductInstanceStoreProductIsBelongPartner(projectOrderLineItemDTO.getProductInstanceId());
        ProjectOrderLineItemDTO projectOrderLineItemDTO1 = new ProjectOrderLineItemDTO();
        ConfigurableSkuStoreProduct configurableSkuStoreProduct = new ConfigurableSkuStoreProduct();
        configurableSkuStoreProduct.setId("12311");
        ConfigurableStoreProduct configurableStoreProduct = new ConfigurableStoreProduct();
        configurableStoreProduct.setId("465");
        configurableSkuStoreProduct.setConfigurableStoreProduct(configurableStoreProduct);
        SkuProduct skuProduct = new SkuProduct();
        skuProduct.setId(123L);
        configurableSkuStoreProduct.setSkuProduct(skuProduct);
        projectOrderLineItemDTO1.setConfigurableSkuStoreProductId("12311");
        ProjectOrderDTO projectOrderDTO2 = new ProjectOrderDTO();
        projectOrderDTO2.setLineItems(ImmutableList.of(projectOrderLineItemDTO1));
        Mockito.doNothing().when(spy).checkStoreProductProductIsBelongPartner(configurableStoreProduct.getId());
        Mockito.doReturn(configurableSkuStoreProduct).when(spy).getConfigurableSkuStoreProductBytId(projectOrderLineItemDTO1.getConfigurableSkuStoreProductId());
        spy.checkStoreProductIsBelongCurrentPartnerStore(projectOrderDTO2);
        Assert.assertEquals(skuProduct.getId(), projectOrderLineItemDTO1.getProductId());
        Assert.assertEquals(configurableStoreProduct.getId(), projectOrderLineItemDTO1.getStoreProductId());
        Mockito.verify(spy, Mockito.times(1)).checkStoreProductProductIsBelongPartner(configurableStoreProduct.getId());
        Mockito.verify(spy, Mockito.times(1)).getConfigurableSkuStoreProductBytId(projectOrderLineItemDTO1.getConfigurableSkuStoreProductId());

        ProjectOrderDTO projectOrderDTO3 = new ProjectOrderDTO();
        ProjectOrderLineItemDTO projectOrderLineItemDTO2 = new ProjectOrderLineItemDTO();
        projectOrderLineItemDTO2.setStoreProductId("12389");
        projectOrderDTO3.setLineItems(ImmutableList.of(projectOrderLineItemDTO2));
        SkuStoreProduct skuStoreProduct = new SkuStoreProduct();
        skuStoreProduct.setId(projectOrderLineItemDTO2.getStoreProductId());
        SkuProduct skuProduct1 = new SkuProduct();
        skuProduct1.setId(12839L);
        skuStoreProduct.setProduct(skuProduct1);
        Mockito.doReturn(skuStoreProduct).when(spy).getSkuStoreProductById(projectOrderLineItemDTO2.getStoreProductId());
        Mockito.doNothing().when(spy).checkStoreProductProductIsBelongPartner(projectOrderLineItemDTO2.getStoreProductId());
        spy.checkStoreProductIsBelongCurrentPartnerStore(projectOrderDTO3);
        Assert.assertEquals(skuProduct1.getId(), projectOrderLineItemDTO2.getProductId());
        Mockito.verify(spy, Mockito.times(1)).getSkuStoreProductById(projectOrderLineItemDTO2.getStoreProductId());
        Mockito.verify(spy, Mockito.times(1)).checkStoreProductProductIsBelongPartner(projectOrderLineItemDTO2.getStoreProductId());
    }

    @Test
    public void checkProductInstanceStoreProductIsBelongPartner() {
        String productInstanceId = "123";
        MongoTemplate productInstanceMongoTemplate = Mockito.mock(MongoTemplate.class);
        Mockito.when(mongoTemplateFactory.getMongoTemplate(ProductInstance.class)).thenReturn(productInstanceMongoTemplate);
        ProductInstance productInstance = new ProductInstance();
        Query queryByProductInstance = Query.query(Criteria.where("_id").is(productInstanceId));
        queryByProductInstance.fields().include("propertyModelId");
        Exception exception = null;
        try {
            Mockito.when(productInstanceMongoTemplate.findOne(queryByProductInstance, ProductInstance.class)).thenReturn(productInstance);
            spy.checkProductInstanceStoreProductIsBelongPartner(productInstanceId);
        } catch (Exception e) {
            exception = e;
        }
        Assert.assertNotNull(exception);
        Assert.assertEquals(2700412, ((BusinessException) exception).getCode());

        String productInstanceId1 = "1234";
        ProductInstance productInstance1 = new ProductInstance();
        String propertyModelId = "12389";
        productInstance1.setPropertyModelId(propertyModelId);
        Query queryByProductInstance1 = Query.query(Criteria.where("_id").is(productInstanceId1));
        queryByProductInstance1.fields().include("propertyModelId");
        MongoTemplate mongoTemplateByPropertyModel = Mockito.mock(MongoTemplate.class);
        Mockito.when(mongoTemplateFactory.getMongoTemplate(PropertyModel.class)).thenReturn(mongoTemplateByPropertyModel);
        Exception exception1 = null;
        Query queryModel = Query.query(Criteria.where("_id").is(propertyModelId));
        queryModel.fields().include("storeProductId");
        try {
            Mockito.when(mongoTemplateByPropertyModel.findOne(queryModel, PropertyModel.class)).thenReturn(null);
            Mockito.when(productInstanceMongoTemplate.findOne(queryByProductInstance1, ProductInstance.class)).thenReturn(productInstance1);
            spy.checkProductInstanceStoreProductIsBelongPartner(productInstanceId1);
        } catch (Exception e) {
            exception1 = e;
        }
        Assert.assertNotNull(exception1);
        Assert.assertEquals(2700412, ((BusinessException) exception1).getCode());

        String productInstanceId3 = "12389";
        Query query = Query.query(Criteria.where("_id").is(productInstanceId3));
        query.fields().include("propertyModelId");
        ProductInstance productInstance2 = new ProductInstance();
        String propertyModelId2 = "12389";
        productInstance2.setPropertyModelId(propertyModelId2);
        Mockito.when(productInstanceMongoTemplate.findOne(query, ProductInstance.class)).thenReturn(productInstance2);
        PropertyModel propertyModel = new PropertyModel();
        propertyModel.setStoreProductId("89123");
        Query queryPropertyModel = Query.query(Criteria.where("_id").is(propertyModelId2));
        queryPropertyModel.fields().include("storeProductId");
        Mockito.when(mongoTemplateByPropertyModel.findOne(queryPropertyModel, PropertyModel.class)).thenReturn(propertyModel);
        Mockito.doNothing().when(spy).checkStoreProductProductIsBelongPartner(propertyModel.getStoreProductId());
        spy.checkProductInstanceStoreProductIsBelongPartner(productInstanceId3);
        Mockito.verify(spy, Mockito.times(1)).checkStoreProductProductIsBelongPartner(propertyModel.getStoreProductId());
    }

    @Test
    public void checkStoreProductProductIsBelongPartner() {
        ShopOwnerMatcher shopOwnerMatcher = Mockito.mock(ShopOwnerMatcher.class);
        Whitebox.setInternalState(thirdOrderManager, "shopOwnerMatcher", shopOwnerMatcher);
        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
        Mockito.when(mongoTemplateFactory.getMongoTemplate(StoreProduct.class)).thenReturn(mongoTemplate);
        String storeProductId = "123";
        Exception exception = null;
        try {
            Mockito.when(mongoTemplate.findById(storeProductId, StoreProduct.class)).thenReturn(null);
            thirdOrderManager.checkStoreProductProductIsBelongPartner(storeProductId);
        } catch (Exception e) {
            exception = e;
        }
        Assert.assertNotNull(exception);
        Assert.assertEquals(2700412, ((BusinessException) exception).getCode());
        Mockito.verify(mongoTemplate, Mockito.times(1)).findById(storeProductId, StoreProduct.class);
        Mockito.verify(mongoTemplateFactory, Mockito.times(1)).getMongoTemplate(StoreProduct.class);
        String storeProductId1 = "1234";
        Exception exception2 = null;
        try {
            StoreProduct storeProduct = new StoreProduct();
            Mockito.when(mongoTemplate.findById(storeProductId1, StoreProduct.class)).thenReturn(storeProduct);
            thirdOrderManager.checkStoreProductProductIsBelongPartner(storeProductId1);
        } catch (Exception e) {
            exception2 = e;
        }
        Assert.assertNotNull(exception2);
        Assert.assertEquals(2700412, ((BusinessException) exception2).getCode());
        Mockito.verify(mongoTemplate, Mockito.times(1)).findById(storeProductId1, StoreProduct.class);
        Mockito.verify(mongoTemplateFactory, Mockito.times(2)).getMongoTemplate(StoreProduct.class);

        String storeProductId2 = "12134";
        Exception exception3 = null;
        try {
            StoreProduct storeProduct = new StoreProduct();
            storeProduct.setStore(new Store());
            Mockito.when(mongoTemplate.findById(storeProductId2, StoreProduct.class)).thenReturn(storeProduct);
            thirdOrderManager.checkStoreProductProductIsBelongPartner(storeProductId2);
        } catch (Exception e) {
            exception3 = e;
        }
        Assert.assertNotNull(exception3);
        Assert.assertEquals(2700412, ((BusinessException) exception3).getCode());
        Mockito.verify(mongoTemplate, Mockito.times(1)).findById(storeProductId2, StoreProduct.class);
        Mockito.verify(mongoTemplateFactory, Mockito.times(3)).getMongoTemplate(StoreProduct.class);

        String storeProductId3 = "112134";
        Exception exception4 = null;
        Store store = new Store();
        store.setId("1223");
        try {
            StoreProduct storeProduct = new StoreProduct();

            storeProduct.setStore(store);
            Mockito.when(mongoTemplate.findById(storeProductId3, StoreProduct.class)).thenReturn(storeProduct);
            Mockito.doNothing().when(shopOwnerMatcher).matchByStoreId(store.getId());
            thirdOrderManager.checkStoreProductProductIsBelongPartner(storeProductId3);
        } catch (Exception e) {
            exception4 = e;
        }
        Assert.assertNull(exception4);
        Mockito.verify(mongoTemplate, Mockito.times(1)).findById(storeProductId3, StoreProduct.class);
        Mockito.verify(mongoTemplateFactory, Mockito.times(4)).getMongoTemplate(StoreProduct.class);
        Exception exception5 = null;
        try {
            Mockito.verify(shopOwnerMatcher, Mockito.times(1)).matchByStoreId(store.getId());
        } catch (CheckedBusinessException e) {
            exception5 = e;
        }
        Assert.assertNull(exception5);
    }

    @Test
    public void getSkuProductIdByStoreProductId(){
        String id = "123";
        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
        ConfigurableSkuStoreProduct configurableStoreProduct = new ConfigurableSkuStoreProduct();
        Mockito.when(mongoTemplate.findById(id, ConfigurableSkuStoreProduct.class)).thenReturn(configurableStoreProduct);
        Mockito.when(mongoTemplateFactory.getMongoTemplate(ConfigurableSkuStoreProduct.class)).thenReturn(mongoTemplate);
        ConfigurableSkuStoreProduct result = thirdOrderManager.getConfigurableSkuStoreProductBytId(id);
        Assert.assertEquals(configurableStoreProduct, result);
    }

    @Test
    public void setProjectItemDTOStoreProductId(){
        ProjectOrderLineItemDTO projectOrderLineItemDTO = new ProjectOrderLineItemDTO();
        projectOrderLineItemDTO.setStoreProductId("123");
        projectOrderLineItemDTO.setImageProject(new ImageProjectDTO());
        Long productId = 123L;
        thirdOrderManager.setProjectItemDTOStoreProductId(projectOrderLineItemDTO, productId, projectOrderLineItemDTO.getVersionedAttributeId());
        Assert.assertEquals(projectOrderLineItemDTO.getProductId(), productId);

        ProjectOrderLineItemDTO projectOrderLineItemDTO1 = new ProjectOrderLineItemDTO();
        ImageProjectDTO imageProjectDTO = new ImageProjectDTO();
        imageProjectDTO.setStoreProductId("1231");
        CustomizeProjectDTO customizeProjectDTO = new CustomizeProjectDTO();
        customizeProjectDTO.setStoreProductId("123");
        projectOrderLineItemDTO1.setImageProject(imageProjectDTO);
        projectOrderLineItemDTO1.setCustomizeProject(customizeProjectDTO);

        thirdOrderManager.setProjectItemDTOStoreProductId(projectOrderLineItemDTO1,productId, projectOrderLineItemDTO.getVersionedAttributeId());
        Assert.assertEquals(projectOrderLineItemDTO1.getImageProject().getProductId(), productId);
        Assert.assertNull(projectOrderLineItemDTO1.getProductId());

        ProjectOrderLineItemDTO projectOrderLineItemDTO2 = new ProjectOrderLineItemDTO();
        projectOrderLineItemDTO2.setCustomizeProject(customizeProjectDTO);
        thirdOrderManager.setProjectItemDTOStoreProductId(projectOrderLineItemDTO2, productId, projectOrderLineItemDTO.getVersionedAttributeId());
        Assert.assertEquals(projectOrderLineItemDTO2.getCustomizeProject().getProductId(), productId);
        Assert.assertNull(projectOrderLineItemDTO2.getProductId());

    }
    @Test
    public void setProjectItemDTOStoreProductIdExistsConfigurableSkuStoreProductId(){
        ProjectOrderLineItemDTO projectOrderLineItemDTO = new ProjectOrderLineItemDTO();
        projectOrderLineItemDTO.setConfigurableSkuStoreProductId("123");
        projectOrderLineItemDTO.setImageProject(new ImageProjectDTO());
        Long productId = 123L;
        thirdOrderManager.setProjectItemDTOStoreProductId(projectOrderLineItemDTO, productId, projectOrderLineItemDTO.getVersionedAttributeId());
        Assert.assertEquals(projectOrderLineItemDTO.getProductId(), productId);

        ProjectOrderLineItemDTO projectOrderLineItemDTO1 = new ProjectOrderLineItemDTO();
        ImageProjectDTO imageProjectDTO = new ImageProjectDTO();
        imageProjectDTO.setConfigurableSkuStoreProductId("1231");
        CustomizeProjectDTO customizeProjectDTO = new CustomizeProjectDTO();
        customizeProjectDTO.setConfigurableSkuStoreProductId("123");
        projectOrderLineItemDTO1.setImageProject(imageProjectDTO);
        projectOrderLineItemDTO1.setCustomizeProject(customizeProjectDTO);

        thirdOrderManager.setProjectItemDTOStoreProductId(projectOrderLineItemDTO1,productId, projectOrderLineItemDTO.getVersionedAttributeId());
        Assert.assertEquals(projectOrderLineItemDTO1.getImageProject().getProductId(), productId);
        Assert.assertNull(projectOrderLineItemDTO1.getProductId());

        ProjectOrderLineItemDTO projectOrderLineItemDTO2 = new ProjectOrderLineItemDTO();
        projectOrderLineItemDTO2.setCustomizeProject(customizeProjectDTO);
        thirdOrderManager.setProjectItemDTOStoreProductId(projectOrderLineItemDTO2, productId, projectOrderLineItemDTO.getVersionedAttributeId());
        Assert.assertEquals(projectOrderLineItemDTO2.getCustomizeProject().getProductId(), productId);
        Assert.assertNull(projectOrderLineItemDTO2.getProductId());

    }


    @Test
    public void getConfigurableSkuStoreProductIdByProjectOrderLineItemDTO(){
        ProjectOrderLineItemDTO projectOrderLineItemDTO = new ProjectOrderLineItemDTO();
        ImageProjectDTO imageProjectDTO = new ImageProjectDTO();
        imageProjectDTO.setConfigurableSkuStoreProductId("12389");
        projectOrderLineItemDTO.setImageProject(imageProjectDTO);
        projectOrderLineItemDTO.setConfigurableSkuStoreProductId("123");
        String result = thirdOrderManager.getConfigurableSkuStoreProductIdByProjectOrderLineItemDTO(projectOrderLineItemDTO);
        Assert.assertEquals(projectOrderLineItemDTO.getConfigurableSkuStoreProductId(), result);
        CustomizeProjectDTO customizeProjectDTO = new CustomizeProjectDTO();
        customizeProjectDTO.setConfigurableSkuStoreProductId("12389");
        ProjectOrderLineItemDTO projectOrderLineItemDTO1 = new ProjectOrderLineItemDTO();
        projectOrderLineItemDTO1.setImageProject(imageProjectDTO);
        projectOrderLineItemDTO1.setCustomizeProject(customizeProjectDTO);
        String result2 = thirdOrderManager.getConfigurableSkuStoreProductIdByProjectOrderLineItemDTO(projectOrderLineItemDTO1);
        Assert.assertEquals(imageProjectDTO.getConfigurableSkuStoreProductId(), result2);
        ProjectOrderLineItemDTO projectOrderLineItemDTO2 = new ProjectOrderLineItemDTO();
        projectOrderLineItemDTO2.setCustomizeProject(customizeProjectDTO);
        String result3 = thirdOrderManager.getConfigurableSkuStoreProductIdByProjectOrderLineItemDTO(projectOrderLineItemDTO2);
        Assert.assertEquals(customizeProjectDTO.getConfigurableSkuStoreProductId(), result3);
    }

    @Test
    public void getStoreSkuProductIdByProjectOrderLineItemDTO(){
        ProjectOrderLineItemDTO projectOrderLineItemDTO = new ProjectOrderLineItemDTO();
        ImageProjectDTO imageProjectDTO = new ImageProjectDTO();
        imageProjectDTO.setStoreProductId("12389");
        projectOrderLineItemDTO.setImageProject(imageProjectDTO);
        projectOrderLineItemDTO.setStoreProductId("123");
        String result = thirdOrderManager.getStoreSkuProductIdByProjectOrderLineItemDTO(projectOrderLineItemDTO);
        Assert.assertEquals(projectOrderLineItemDTO.getStoreProductId(), result);
        CustomizeProjectDTO customizeProjectDTO = new CustomizeProjectDTO();
        customizeProjectDTO.setStoreProductId("12389");
        ProjectOrderLineItemDTO projectOrderLineItemDTO1 = new ProjectOrderLineItemDTO();
        projectOrderLineItemDTO1.setImageProject(imageProjectDTO);
        projectOrderLineItemDTO1.setCustomizeProject(customizeProjectDTO);
        String result2 = thirdOrderManager.getStoreSkuProductIdByProjectOrderLineItemDTO(projectOrderLineItemDTO1);
        Assert.assertEquals(imageProjectDTO.getStoreProductId(), result2);
        ProjectOrderLineItemDTO projectOrderLineItemDTO2 = new ProjectOrderLineItemDTO();
        projectOrderLineItemDTO2.setCustomizeProject(customizeProjectDTO);
        String result3 = thirdOrderManager.getStoreSkuProductIdByProjectOrderLineItemDTO(projectOrderLineItemDTO2);
        Assert.assertEquals(customizeProjectDTO.getStoreProductId(), result3);
    }

    @Test
    public void getSkuStoreProductById(){
        String id = "123";
        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
        SkuStoreProduct skuStoreProduct = new SkuStoreProduct();
        Mockito.when(mongoTemplateFactory.getMongoTemplate(SkuStoreProduct.class)).thenReturn(mongoTemplate);
        Mockito.when(mongoTemplate.findById(id, SkuStoreProduct.class)).thenReturn(skuStoreProduct);
        SkuStoreProduct skuStoreProductById = thirdOrderManager.getSkuStoreProductById(id);
        Assert.assertEquals(skuStoreProduct, skuStoreProductById);
        Mockito.verify(mongoTemplateFactory, Mockito.times(1)).getMongoTemplate(SkuStoreProduct.class);
        Mockito.verify(mongoTemplate, Mockito.times(1)).findById(id, SkuStoreProduct.class);
    }
}