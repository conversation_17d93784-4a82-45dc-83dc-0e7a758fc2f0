package com.qpp.cgp.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.type.ArrayType;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since  2020/6/24
 */
public class JsonResourceUtils {

    static ObjectMapper objectMapper = new ObjectMapper();

    static {
        objectMapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false)
                .configure(SerializationFeature.WRITE_DATE_TIMESTAMPS_AS_NANOSECONDS, false)
                .configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, true)
                .setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    /**
     * @param filePath classPath 下的相对路径
     * @param tClass   返回值类型
     */
    public static <T> T getObject(String filePath, Class<T> tClass) {
        try {
            return objectMapper.readValue(Thread.currentThread().getContextClassLoader().getSystemResourceAsStream(filePath), tClass);
        } catch (IOException e) {
            throw new RuntimeException("fail to create " + tClass.getSimpleName() +  "object from classPath file:" + filePath, e);
        }
    }

    /**
     * @param filePath classPath 下的相对路径
     * @param tClass   列表元素的类型
     */
    public static <T> List<T> getList(String filePath, Class<T> tClass) {
        try {
            ArrayType testDataArrType = objectMapper.getTypeFactory().constructArrayType(tClass);
            FileInputStream fileInputStream = new FileInputStream(filePath);
            return Arrays.asList((objectMapper.readValue(fileInputStream, testDataArrType)));
        } catch (IOException e) {
            throw new RuntimeException("fail to create " + tClass.getSimpleName() +  "object list from classPath file:" + filePath, e);
        }
    }

}
