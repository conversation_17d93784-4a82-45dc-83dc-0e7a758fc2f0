package com.qpp.cgp.util;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.qpp.cgp.domain.sale.refund.RefundRequest;
import com.qpp.core.utils.QueryForIdUtils;
import org.checkerframework.checker.index.qual.PolyUpperBound;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.mongodb.core.query.Query;

import java.util.Objects;

/**
 * @className: QueryForIdUtilsTest
 * @description:
 * @author: TT-Berg
 * @date: 2023/1/9
 **/
@RunWith(MockitoJUnitRunner.class)
public class QueryForIdUtilsTest {

    @Test
    public void testGetIdQuery() {
        RefundRequest refundRequest = new RefundRequest();
        refundRequest.setId(1l);
        Query query = QueryForIdUtils.getIdQuery(refundRequest);
        Assert.assertNotNull(query);
    }

    @Test
    public void testGetIdQuery2() {
        RefundRequest refundRequest = new RefundRequest();
        refundRequest.setId(1l);
        Query query = QueryForIdUtils.getIdQuery(refundRequest, "test");
        Assert.assertNotNull(query);
    }

    @Test
    public void testGetQuery() {
        Query query = QueryForIdUtils.getQuery(ImmutableMap.of("int", 1, "string", "1", "nice._id", 11111l));
        Assert.assertNotNull(query);
    }
}
