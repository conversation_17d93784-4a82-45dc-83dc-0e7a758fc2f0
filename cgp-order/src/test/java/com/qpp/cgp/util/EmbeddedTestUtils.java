//package com.qpp.cgp.util;
//
//import com.mongodb.MongoClient;
//import com.qpp.core.context.SpringApplicationContext;
//import com.qpp.mongo.driver.HybridMongoTemplate;
//import de.flapdoodle.embed.mongo.*;
//import de.flapdoodle.embed.mongo.config.*;
//import de.flapdoodle.embed.mongo.distribution.Version;
//import de.flapdoodle.embed.process.runtime.Network;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.data.mongodb.core.SimpleMongoDbFactory;
//import org.springframework.data.mongodb.core.convert.MongoConverter;
//import org.springframework.util.ResourceUtils;
//
//import java.io.File;
//import java.io.IOException;
//
///**
// * <AUTHOR> Chiu
// * @Date 2021/2/25 9:25
// * @Description
// * @Version 1.0
// */
//public class EmbeddedTestUtils {
//
//    private static MongodExecutable mongodExecutable;
//    private static MongoClient mongoClient;
//    private static IMongodConfig iMongodConfig;
//
//    public static MongoTemplate startEmbeddedMongo(String database, String collection, String jsonFile) throws IOException {
//        int port = 27017;
//        MongodConfigBuilder mongodConfigBuilder = new MongodConfigBuilder();
//        iMongodConfig = mongodConfigBuilder
//                .version(Version.Main.V3_4)
//                .net(new Net(port, Network.localhostIsIPv6()))
//                .build();
//
//        MongodStarter starter = MongodStarter.getDefaultInstance();
//
//        mongodExecutable = starter.prepare(iMongodConfig);
//        mongodExecutable.start();
//        mongoClient = new MongoClient("localhost", port);
//
//        // 从json文件中导入数据（注意json文件的格式与规范json文件有区别）
//        mongoImport(database, collection, jsonFile);
//
//        // 修改Manager中的mongoTemplate为该测试类中的MongoTemplate，即是操作嵌入式数据库的mongoTemplate
//        SimpleMongoDbFactory mongoDbFactory = new SimpleMongoDbFactory(mongoClient, database);
//        MongoConverter mongoConverter = SpringApplicationContext.getBeanStatic(MongoConverter.class);
//        MongoTemplate mongoTemplate = new HybridMongoTemplate(mongoDbFactory, mongoConverter);
//        return mongoTemplate;
//    }
//
//    public static void mongoImport(String database, String collection, String jsonFile) throws IOException {
//        if (jsonFile == null || jsonFile.equals("")){
//            return;
//        }
//        File file = ResourceUtils.getFile(jsonFile);
//        MongoImportConfigBuilder mongoImportConfigBuilder = new MongoImportConfigBuilder();
//        IMongoImportConfig mongoImportConfig = mongoImportConfigBuilder
//                .version(iMongodConfig.version())
//                .net(iMongodConfig.net())
//                .db(database)
//                .collection(collection)
//                .upsert(true)
//                .dropCollection(true)
//                .jsonArray(false)
//                .importFile(file.getAbsolutePath())
//                .build();
//
//        // 准备数据导入操作的执行对象
//        MongoImportExecutable mongoImportExecutable = MongoImportStarter.getDefaultInstance().prepare(mongoImportConfig);
//        // 开启导入数据操作
//        MongoImportProcess mongoImport = mongoImportExecutable.start();
//
//        // 关闭操作
//        mongoImport.stop();
//    }
//
//    public static void destroyMongo(){
//        if (mongodExecutable != null){
//            mongodExecutable.stop();
//        }
//    }
//
//}
