//package com.qpp.cgp.domain.orderitem;
//
//import com.qpp.cgp.CgpOrderTestApplication;
//import com.qpp.cgp.config.mongo.MongoTemplateBeanNames;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.data.mongodb.core.query.Criteria;
//import org.springframework.data.mongodb.core.query.Query;
//
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.List;
//import java.util.Map;
//import java.util.Optional;
//import java.util.stream.Collectors;
//
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = CgpOrderTestApplication.class)
//public class OrderItemStatusTest {
//
//    @Qualifier(MongoTemplateBeanNames.RUNTIME)
//    @Autowired
//    private MongoTemplate mongoTemplate;
//
//    @Autowired
//    private JdbcTemplate jdbcTemplate;
//    /**
//     * 新旧数据对比
//     */
////    @Test
////    public void newAndOldDataIsSameCheckTest()  {
////        //对比新旧数据，对比一下对应的数据数量
////        List<Map<String, Object>> order_item_status_maps = jdbcTemplate.queryForList("select * from cgp_order_item_status;");
////        List<OrderItemStatus> orderStatuses = mongoTemplate.findAll(OrderItemStatus.class);
////        for (Map<String, Object> order_item_status_map : order_item_status_maps) {
////            Object order_status_id = order_item_status_map.get("id");
////            Optional<OrderItemStatus> first = orderStatuses.stream().filter(orderFlow -> Long.valueOf(order_status_id.toString()).equals(orderFlow.getId())).findFirst();
////            Assert.assertTrue(first.isPresent());
////            OrderItemStatus orderItemStatus = first.get();
////            Assert.assertEquals(order_item_status_map.get("status_name").toString(), orderItemStatus.getName());
////            Assert.assertEquals(order_item_status_map.get("make_sort_order").toString(), String.valueOf(orderItemStatus.getMakeSortOrder()));
////            Assert.assertEquals(order_item_status_map.get("order_status_id").toString(), String.valueOf(orderItemStatus.getOrderStatusId()));
////        }
////    }
//
//    /**
//     * 测试新的数据是否包含必有字段
//     */
////    @Test
////    public void newGeneratedDateCheckTest() {
////        //测试下新生成的数据是否包含必有字段
////        List<Map<String, Object>> old_order_flows_Maps = jdbcTemplate.queryForList("select * from cgp_order_item_status;");
////        Assert.assertNotNull(old_order_flows_Maps);
////        Assert.assertFalse(old_order_flows_Maps.isEmpty());
////        List<Long> order_flow_ids = old_order_flows_Maps.stream().map(e -> e.get("id").toString()).collect(Collectors.toList()).stream().map(Long::valueOf).collect(Collectors.toList());
////        List<OrderItemStatus> new_order_item_status = mongoTemplate.find(Query.query(Criteria.where("id").nin(order_flow_ids)), OrderItemStatus.class);
////        for (OrderItemStatus newGeneratedOrderItemStatus : new_order_item_status) {
////            Assert.assertNotNull(newGeneratedOrderItemStatus.getName());
////            Assert.assertNotNull(newGeneratedOrderItemStatus.getOrderStatusId());
////        }
////    }
//}