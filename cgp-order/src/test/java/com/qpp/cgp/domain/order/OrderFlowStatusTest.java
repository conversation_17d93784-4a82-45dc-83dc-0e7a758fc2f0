//package com.qpp.cgp.domain.order;
//
//
//import com.qpp.cgp.CgpOrderTestApplication;
//import com.qpp.cgp.config.mongo.MongoTemplateBeanNames;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.data.mongodb.core.query.Criteria;
//import org.springframework.data.mongodb.core.query.Query;
//
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.List;
//import java.util.Map;
//import java.util.Optional;
//import java.util.stream.Collectors;
//
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = CgpOrderTestApplication.class)
//public class OrderFlowStatusTest {
//
//    @Qualifier(MongoTemplateBeanNames.RUNTIME)
//    @Autowired
//    private MongoTemplate mongoTemplate;
//
//    @Autowired
//    private JdbcTemplate jdbcTemplate;
//
//    /**
//     * 新旧数据对比
//     */
//    @Test
//    public void newAndOldDataIsSameCheckTest()  {
//        //对比新旧数据，对比一下对应的数据数量
//        List<Map<String, Object>> old_order_flow_status_Maps = jdbcTemplate.queryForList("select * from cgp_order_flow_status;");
//        List<OrderFlowStatus> orderFlowStatuses = mongoTemplate.findAll(OrderFlowStatus.class);
//        for (Map<String, Object> old_order_flows_map : old_order_flow_status_Maps) {
//            Object ofs_id = old_order_flows_map.get("ofs_id");
//            Optional<OrderFlowStatus> first = orderFlowStatuses.stream().filter(orderFlow -> Long.valueOf(ofs_id.toString()).equals(orderFlow.getId())).findFirst();
//            Assert.assertTrue(first.isPresent());
//            OrderFlowStatus orderFlowStatus = first.get();
//            Assert.assertEquals(old_order_flows_map.get("ofs_sort_order").toString(), orderFlowStatus.getSortOrder().toString());
//            Assert.assertNotNull(orderFlowStatus.getOrderFlow());
//            Assert.assertNotNull(orderFlowStatus.getOrderFlow().getId());
//            Assert.assertEquals(old_order_flows_map.get("ofs_order_flow_id").toString(), orderFlowStatus.getOrderFlow().getId().toString());
//            Assert.assertNotNull(orderFlowStatus.getStatus());
//            Assert.assertNotNull(orderFlowStatus.getStatus().getId());
//            Assert.assertEquals(old_order_flows_map.get("ofs_order_status_id").toString(), orderFlowStatus.getStatus().getId().toString());
//        }
//    }
//
//    /**
//     * 测试新的数据是否包含必有字段
//     */
//    @Test
//    public void newGeneratedDateCheckTest() {
//        //测试下新生成的数据是否包含必有字段
//        List<Map<String, Object>> old_order_flows_Maps = jdbcTemplate.queryForList("select * from cgp_order_flow_status;");
//        Assert.assertNotNull(old_order_flows_Maps);
//        Assert.assertFalse(old_order_flows_Maps.isEmpty());
//        List<Long> order_flow_ids = old_order_flows_Maps.stream().map(e -> e.get("ofs_id").toString()).collect(Collectors.toList()).stream().map(Long::valueOf).collect(Collectors.toList());
//        List<OrderFlowStatus> new_order_flow_status = mongoTemplate.find(Query.query(Criteria.where("id").nin(order_flow_ids)), OrderFlowStatus.class);
//        for (OrderFlowStatus newGeneratedOrderFlowStatus : new_order_flow_status) {
//           Assert.assertNotNull(newGeneratedOrderFlowStatus.getStatus());
//           Assert.assertNotNull(newGeneratedOrderFlowStatus.getStatus().getId());
//           Assert.assertNotNull(newGeneratedOrderFlowStatus.getOrderFlow());
//           Assert.assertNotNull(newGeneratedOrderFlowStatus.getOrderFlow().getId());
//        }
//    }
//}