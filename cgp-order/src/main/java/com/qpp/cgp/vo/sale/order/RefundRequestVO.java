package com.qpp.cgp.vo.sale.order;

import com.qpp.cgp.domain.dto.order.list.ThirdOrderDTO;
import com.qpp.cgp.domain.order.AbstractOrder;
import com.qpp.cgp.domain.sale.refund.RefundOrderType;
import com.qpp.cgp.domain.sale.refund.RefundType;
import com.qpp.cgp.domain.user.User;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class RefundRequestVO {


    /**
     * 唯一Id
     */
    private Long _id;

    /**
     * WhiteLabelOrder的IdRef
     */
    private AbstractOrder order;

    /**
     * 来源，配置在系统的configuration表中
     */
    private String from;

    /**
     * 退货请求单号
     */
    private String requestNo;

    /**
     * 销售单号
     */
    private String salesOrderNo;

    /**
     * 退单类型
     */
    private RefundOrderType refundOrderType;

    /**
     * 退款类型
     */
    private RefundType type;

    /**
     * 支付金额
     */
    private BigDecimal paymentAmount;

    /**
     * 当前货币
     */
    private String currencyCode;

    /**
     * 付款方式
     */
    private String paymentMethod;

    /**
     * 付款记录Id
     */
    private String paymentTranId;

    /**
     * 退款记录
     */
    private String refundTranId;

    /**
     * 退产品金额
     */
    private BigDecimal productsAmount;


    /**
     * 退运费金额
     */
    private BigDecimal shippingAmount;

    /**
     * 退税费金额
     */
    private BigDecimal salesTaxAmount;

    /**
     * 原因
     */
    private String reason;

    /**
     * 退款状态的stateKey
     */
    private String state;

    private List<RefundItemVO> refundItems;

    protected Date createdDate;

    protected User createdUser;

    /**
     * 取消时间
     */
    private Date cancelDate;

    /**
     * 取消用户
     */
    private User cancelUser;

    /**
     * 退款时间
     */
    private Date refundDate;

    /**
     * 退款用户
     */
    private User refundUser;

    private ThirdOrderDTO bindOrder;

    public void setBindOrderRef(ThirdOrderDTO thirdOrderDTORef) {
        ThirdOrderDTO thirdOrderDTO = new ThirdOrderDTO();
        thirdOrderDTO.setId(thirdOrderDTORef.getId());
        thirdOrderDTO.setOrderNumber(thirdOrderDTORef.getOrderNumber());
        thirdOrderDTO.setClazz(thirdOrderDTORef.getClazz());
        this.bindOrder = thirdOrderDTO;
    }
}
