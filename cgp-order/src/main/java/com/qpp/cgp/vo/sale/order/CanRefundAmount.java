package com.qpp.cgp.vo.sale.order;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @className: CanRefundAmount
 * @description:
 * @author: TT-Berg
 * @date: 2023/1/13
 **/
@Data
public class CanRefundAmount {

    /**
     * 可退产品金额(productsPrice-已退产品金额-折扣金额)
     */
    private BigDecimal canRefundProductsPrice;

    /**
     * 可退销售税（总可退-所有已退）
     */
    private BigDecimal canRefundSaleTax;

    /**
     * 可退运费金额(总可退-所有已退)
     */
    private BigDecimal canRefundShippingPrice;
}
