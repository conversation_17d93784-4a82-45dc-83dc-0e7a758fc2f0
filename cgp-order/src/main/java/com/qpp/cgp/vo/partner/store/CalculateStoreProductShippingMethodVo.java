package com.qpp.cgp.vo.partner.store;

import com.qpp.cgp.dto.ps.ProductionInfo;
import com.qpp.cgp.exception.ExceptionInfo;
import lombok.Data;

import java.util.List;

/**
 * @className: CalculateStoreProductShippingMethodVo
 * @description:
 * @author: TT-Berg
 * @date: 2023/3/30
 **/
@Data
public class CalculateStoreProductShippingMethodVo {

    private List<StoreShippingMethodVo> shippingMethods;

    private ProductionInfo productionTime;

    /**
     * 错误信息
     */
    private ExceptionInfo exceptionInfo;

}
