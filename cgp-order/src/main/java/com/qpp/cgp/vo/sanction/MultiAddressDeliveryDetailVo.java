package com.qpp.cgp.vo.sanction;

import com.qpp.cgp.core.module.shipping.ShippingInfo;
import com.qpp.cgp.domain.dto.order.OrderLineItemDTO;
import com.qpp.cgp.domain.dto.order.OrderLineItemListDTO;
import com.qpp.cgp.domain.order.ShipmentInfo;
import com.qpp.cgp.domain.shipment.ShipmentOrder;
import com.qpp.cgp.domain.shipment.ShipmentRequirement;
import com.qpp.cgp.domain.user.AddressBook;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: TT-Berg
 * @date: 2023/8/30
 **/
@Data
@Builder
public class MultiAddressDeliveryDetailVo {

    @ApiModelProperty("账单地址")
    private AddressBook billingAddress;

    @ApiModelProperty("发货地址")
    private AddressBook deliveryAddress;

    @ApiModelProperty("订单项数量")
    private Integer orderItemNum;

    @ApiModelProperty("订单项列表")
    private List<OrderLineItemDTO> orderItems;

    @ApiModelProperty("发货要求")
    private ShipmentRequirement shipmentRequirement;

    @ApiModelProperty("发货单")
    private ShipmentOrder shipmentOrder;

    @ApiModelProperty("制裁详细信息")
    private List<SingleAddressDeliveryDetailVo> singleAddressDeliveryDetails;

    @ApiModelProperty("发货信息")
    private ShipmentInfo shipmentInfo;


}
