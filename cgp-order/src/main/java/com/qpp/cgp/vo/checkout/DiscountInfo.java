package com.qpp.cgp.vo.checkout;

import com.qpp.cgp.dto.v2.arg.ProductItem;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description:
 * @author: TT-Berg
 * @date: 2024/5/6
 **/
@Data
@Builder
public class DiscountInfo {

    private BigDecimal priceAfterDiscount;

    private BigDecimal bulkPrice;

    private List<ProductItem> productItems;
}
