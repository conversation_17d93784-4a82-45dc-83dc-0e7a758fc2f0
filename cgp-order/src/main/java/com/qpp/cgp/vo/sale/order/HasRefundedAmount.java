package com.qpp.cgp.vo.sale.order;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @className: HasRefunedAmount
 * @description:
 * @author: TT-Berg
 * @date: 2023/1/18
 **/
@Data
public class HasRefundedAmount {

    /**
     * 销售订单总金额-退款金额
     */
    private BigDecimal netReceipt;

    /**
     * 已退销售税
     */
    private BigDecimal hasRefundedSalesTax;

    /**
     * 已退运费
     */
    private BigDecimal hasRefundedShippingPrice;

    /**
     * 已退产品
     */
    private BigDecimal hasRefundedProductsPrice;

}
