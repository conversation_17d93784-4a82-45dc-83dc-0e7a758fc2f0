package com.qpp.cgp.domain.partner.client;

import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.cgp.domain.partner.Partner;
import com.qpp.cgp.domain.partner.store.Store;
import com.qpp.cgp.domain.user.AddressBook;
import com.qpp.mongo.domain.MongoDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@ApiModel("客户")
@Data
@NoArgsConstructor
@ConfigDomain
@Document(collection = "clients")
public class Client extends MongoDomain {

    @ApiModelProperty("所属的合作商")
    private Partner partner;

    @ApiModelProperty("所属的店铺")
    private Store store;

    @ApiModelProperty(value = "第三方系统ID", required = true)
    @NotBlank(message = "clientId cannot be blank!")
    private String clientId;

    @ApiModelProperty(value = "客户名称", required = true)
    @NotBlank(message = "clientName cannot be blank!")
    private String clientName;

    @ApiModelProperty("是否是临时Client")
    private Boolean isTemp = false;

    private String gender;

    private Date birthday;

    private String emailAddress;

    @ApiModelProperty("non-idRef, complete instance")
    private AddressBook address;

    @Transient
    private Boolean createClientRoot;

}
