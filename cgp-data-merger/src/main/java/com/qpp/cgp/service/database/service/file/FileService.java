package com.qpp.cgp.service.database.service.file;

import com.google.common.collect.ImmutableMap;
import com.qpp.core.utils.http.RestTemplateBuildUtils;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.apache.commons.io.FilenameUtils;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;

/**
 * 文件服务
 *
 * <AUTHOR>
 * @since 2020/11/11
 */
public class FileService {

    public static RestTemplate restTemplate = RestTemplateBuildUtils.buildRestTemplate();

    public static FileRepository fileRepository = new FileRepository();

    /**
     * 同步文件
     */
    public static String syncFile(String originFileUrl, String uploadFileUrl) {

        String fileName = FilenameUtils.getName(originFileUrl);
        File file = null;
        byte[] bytes=null;
        try {
            bytes = downloadFile(originFileUrl);
            file = fileRepository.createCacheFile(fileName);
            fileRepository.write(bytes, file.getAbsolutePath());
            return uploadFile(file, uploadFileUrl);
        } catch (IOException e) {
            throw BusinessExceptionBuilder.of(789484, ImmutableMap.of("message", "下载源文件或上传文件至目标服务器的请求失败，请求下载originFileUrl："+originFileUrl+"请求上传uploadFileUrl:"+uploadFileUrl));
        } finally {
            fileRepository.deleteCacheFile(file);
        }
    }

    /**
     * 同步静态文件
     * @param originFileUrl
     * @param uploadFileUrl
     * @return
     */
    public static void syncStaticFile(String originFileUrl, String uploadFileUrl,String fileDir) {

        String fileName = FilenameUtils.getName(originFileUrl);
        File file = null;
        byte[] bytes =null;
        try {
            bytes= downloadFile(originFileUrl);
            file = fileRepository.createCacheFile(fileName);
            fileRepository.write(bytes, file.getAbsolutePath());
            uploadFile(file, uploadFileUrl+"?dirName="+fileDir.substring(0,fileDir.length()-1));
        }catch (Exception e){
            throw BusinessExceptionBuilder.of(789474, ImmutableMap.of("message", "下载源文件或上传文件至目标服务器的请求失败，请求url："+originFileUrl));
        }finally {
            fileRepository.deleteCacheFile(file);
        }
    }

    public static String syncStaticFileV2(String originFileUrl, String uploadFileUrl,String fileDir) {

        String fileName = FilenameUtils.getName(originFileUrl);
        File file = null;
        byte[] bytes =null;
        try {
            bytes= downloadFile(originFileUrl);
            file = fileRepository.createCacheFile(fileName);
            fileRepository.write(bytes, file.getAbsolutePath());
            return uploadFile(file, uploadFileUrl+"?dirName="+fileDir.substring(0,fileDir.length()-1));
        }catch (Exception e){
            throw BusinessExceptionBuilder.of(789474, ImmutableMap.of("message", "下载源文件或上传文件至目标服务器的请求失败，请求url："+originFileUrl));
        }finally {
            fileRepository.deleteCacheFile(file);
        }
    }



    /**
     * 下载文件
     */
    public static byte[] downloadFile(String url) {
        return restTemplate.getForEntity(url, byte[].class, new HashMap<>()).getBody();
    }

    /**
     * 上传文件
     */
    public static String uploadFile(File file, String url) {
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        params.add("file", new FileSystemResource(file));

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(params, headers);

        return restTemplate.postForEntity(url,requestEntity, String.class).getBody();
    }

}
