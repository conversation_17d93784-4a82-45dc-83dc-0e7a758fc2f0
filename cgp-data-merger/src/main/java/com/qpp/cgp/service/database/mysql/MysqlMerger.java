package com.qpp.cgp.service.database.mysql;

import com.qpp.cgp.domain.BaseSyncProgress;
import com.qpp.cgp.domain.SyncProgressStatus;
import com.qpp.cgp.service.database.DatabaseMerger;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

import java.sql.*;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Created by smart on 9/28/2017.
 */
@Component
public class MysqlMerger implements DatabaseMerger<MysqlConfig, MysqlMergeQuery> {


    private Logger logger = LoggerFactory.getLogger(this.getClass());



    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public void mergeData(MysqlConfig targetConfig, MysqlMergeQuery query) {

        logger.debug("start meger " + query.getTableQueries());


//        Connection sourceConnection = this.getConnection(sourceConfig);
        Connection sourceConnection = this.getSourceConnection();

        Connection targetConnection = this.getConnection(targetConfig);

        query.getTableQueries().stream().forEach(tableQuery -> {
            try {
                this.mergeTable(sourceConnection, targetConnection, tableQuery);
            } catch (SQLException e) {

                logger.error("merge data error!", e);
                logger.error("merge table " + tableQuery.getTableName() + " data  to " + targetConfig.getUrl() + " error:" + e.getMessage());
            }
        });


    }

    public void mergeData(MysqlConfig targetConfig, MysqlMergeQuery query, String progressId, Class<? extends BaseSyncProgress> entityType) {

        logger.debug("start merge " + query.getTableQueries());

        Connection sourceConnection = this.getSourceConnection();

        Connection targetConnection = this.getConnection(targetConfig);

        query.getTableQueries().stream().forEach(tableQuery -> {
            try {
                this.mergeTable(sourceConnection, targetConnection, tableQuery);
                // update progress
                updateProgress(progressId, tableQuery.getTableName(), entityType);
            } catch (SQLException e) {

                logger.error("merge data error!", e);
                logger.error("merge table " + tableQuery.getTableName() + " data  to " + targetConfig.getUrl() + " error:" + e.getMessage());
            }
        });

    }

    private void updateProgress(String progressId, String tableName, Class<? extends BaseSyncProgress> entityType) {
        BaseSyncProgress progress = mongoTemplate.findById(progressId, entityType);
        progress.setStatus(SyncProgressStatus.syncing);
        progress.getTableNames().add(tableName);
        mongoTemplate.save(progress);
    }

    private Connection getSourceConnection() {
        Connection sourceConnection = null;
        return sourceConnection;
    }

    /**
     * @param source     源连接
     * @param target     目标连接
     * @param tableQuery table查询
     */
    public void mergeTable(Connection source, Connection target, TableQuery tableQuery) throws SQLException {

        logger.debug("meger table " + tableQuery.getTableName());

        String tableName = tableQuery.getTableName();

        PreparedStatement preparedStatement = source.prepareStatement("select * from " + tableName + tableQuery.getFilter().map(filter -> " " + filter).orElse(""));
        ResultSet         resultSet         = preparedStatement.executeQuery();


        String primaryKey = this.getTablePrimaryKey(source, tableName);

        if (!resultSet.next()) {
            return;
        }
        List<String> columnNames = this.getColumnNames(resultSet);

        resultSet.previous();

        //遍历所有record
        while (resultSet.next()) {
            this.mergeRecord(resultSet, primaryKey, columnNames, tableName, target);
        }

    }


    /**
     * 同步单条记录
     *
     * @param resultSet
     * @param primaryKey
     * @param columnNames
     */
    public void mergeRecord(ResultSet resultSet, String primaryKey, List<String> columnNames, String tableName, Connection target) throws SQLException {


        //先获取id
        Long id = resultSet.getLong(primaryKey);

        boolean isExist = this.checkIdExists(id, target, tableName, primaryKey);

        String sql = null;

        if (!isExist) {
            //需要insert
            sql = this.buildInsertSql(columnNames, resultSet, tableName, primaryKey);
        } else {
            sql = this.buildUpdateSql(columnNames, resultSet, tableName, primaryKey);
        }

        PreparedStatement preparedStatement = target.prepareStatement(sql);

        IntStream.range(0, columnNames.size()).forEach(index -> {
            try {
                String columnName = columnNames.get(index);
                Object value      = resultSet.getObject(columnName);
                if (value == null) {
                    preparedStatement.setNull(index + 1, resultSet.getMetaData().getColumnType(index));
                } else
                    preparedStatement.setObject(index + 1, value);

            } catch (SQLException e) {
                e.printStackTrace();
            }

        });

        preparedStatement.executeUpdate();


    }


    /**
     * 构建Insert语句
     *
     * @param columns
     * @param resultSet
     * @param tableName
     * @param primaryKey
     * @return
     */
    public String buildInsertSql(List<String> columns, ResultSet resultSet, String tableName, String primaryKey) {


        String valuePlaceHolder = StringUtils.join(IntStream.range(0, columns.size()).mapToObj((index) -> "?").collect(Collectors.toList()), ",");
        String sql              = "insert into %s (%s) values (%s) ";
        sql = String.format(sql, tableName, StringUtils.join(columns, ","), valuePlaceHolder);
        return sql;

    }

    /**
     * 构建Update语句
     *
     * @param columns
     * @param resultSet
     * @param tableName
     * @param primaryKey
     * @return
     */
    public String buildUpdateSql(List<String> columns, ResultSet resultSet, String tableName, String primaryKey) throws SQLException {

        String updatePlaceHolder = StringUtils.join(columns.stream().map(columnName -> columnName + "=?").collect(Collectors.toList()), ",");

        String sql = "update %s set %s where %s";

        return String.format(sql, tableName, updatePlaceHolder, primaryKey + "=" + resultSet.getObject(primaryKey));

    }


    /**
     * 获取所有的Column名称
     *
     * @param resultSet
     * @return
     */
    public List<String> getColumnNames(ResultSet resultSet) throws SQLException {


        ResultSetMetaData metaData = resultSet.getMetaData();

        int columnCount = metaData.getColumnCount();

        return IntStream.rangeClosed(1, columnCount)
                .mapToObj(index -> {
                    try {
                        return metaData.getColumnName(index);
                    } catch (SQLException e) {
                        return null;
                    }

                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    /**
     * 检查指定id的记录是否已经存在
     *
     * @param id
     * @param target
     * @param tableName
     * @param primaryKey
     * @return
     */
    public boolean checkIdExists(Object id, Connection target, String tableName, String primaryKey) throws SQLException {

        String    sql    = "select count(*) count from " + tableName + " where " + primaryKey + " = " + id;
        ResultSet result = target.prepareStatement(sql).executeQuery();
        result.first();
        int count = result.getInt("count");
        return count == 1;
    }


    /**
     * 获取指定表的主键
     *
     * @param connection
     * @param tableName
     * @return
     */
    public String getTablePrimaryKey(Connection connection, String tableName) throws SQLException {


        String targetKeyName = "primaryKey";

        ResultSet resultSet = connection.prepareStatement("SELECT k.column_name " + targetKeyName + "\n" +
                "FROM information_schema.table_constraints t\n" +
                "JOIN information_schema.key_column_usage k\n" +
                "USING(constraint_name,table_schema,table_name)\n" +
                "WHERE t.constraint_type='PRIMARY KEY'\n" +
                "  AND t.table_schema='" + connection.getCatalog() + "'\n" +
                "  AND t.table_name='" + tableName + "';").executeQuery();

        resultSet.first();

        return resultSet.getString(targetKeyName);

    }


    /**
     * 根据配置获取驱动
     *
     * @param mySqlDatabaseConfig
     * @return
     */
    public Connection getConnection(MysqlConfig mySqlDatabaseConfig) {

        String driver = mySqlDatabaseConfig.getDriver();

        Connection conn = null;
        try {
            Class.forName(driver); //classLoader,加载对应驱动
            conn = DriverManager.getConnection(mySqlDatabaseConfig.getUrl(), mySqlDatabaseConfig.getUsername(), mySqlDatabaseConfig.getPassword());
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return conn;

    }


}
