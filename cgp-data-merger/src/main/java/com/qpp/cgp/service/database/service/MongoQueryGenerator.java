package com.qpp.cgp.service.database.service;

import com.mongodb.DBObject;
import com.qpp.cgp.service.database.dto.BaseMongoMergeInfo;
import com.qpp.cgp.service.database.dto.MongoMergeInfo;
import com.qpp.cgp.service.database.dto.ProductMongoMergeInfo;
import com.qpp.cgp.service.database.mongo.CollectionIncreaseQuery;
import com.qpp.cgp.service.database.mongo.CollectionQuery;
import com.qpp.cgp.service.database.mongo.MongoIncreaseMergeQuery;
import com.qpp.cgp.service.database.mongo.MongoMergeQuery;
import org.bson.Document;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;

/**
 * <AUTHOR> Lee 2019/4/3 16:18
 */
@Component
public class MongoQueryGenerator {

    public MongoMergeQuery generateQuery(BaseMongoMergeInfo info) {

        MongoMergeQuery mongoMergeQuery = new MongoMergeQuery();
        final List<CollectionQuery> collectionQueries = mongoMergeQuery.getCollectionQueries();

        final Class<? extends BaseMongoMergeInfo> aClass = info.getClass();
        final Field[] fields = aClass.getDeclaredFields();

        for (Field field : fields) {

            final MongoMergeInfo annotation = field.getAnnotation(MongoMergeInfo.class);

            if (null == annotation) {
                continue;
            }

            final String collection = annotation.value();

            field.setAccessible(true);
            Set value = null;
            try {
                Object idsObj = field.get(info);
                if (idsObj instanceof Set) {
                    value = (Set) idsObj;
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
            if (value != null && value.size() > 0) {
                this.add(collectionQueries, collection, value);
            }

        }

        return mongoMergeQuery;

    }

    public MongoIncreaseMergeQuery generateQueryByIncrease(BaseMongoMergeInfo info) {

        MongoIncreaseMergeQuery mongoMergeQuery = new MongoIncreaseMergeQuery();
        List<CollectionIncreaseQuery> collectionQueries = mongoMergeQuery.getCollectionQueries();

        Class<? extends BaseMongoMergeInfo> aClass = info.getClass();
        Field[] fields = aClass.getDeclaredFields();

        for (Field field : fields) {

            final MongoMergeInfo annotation = field.getAnnotation(MongoMergeInfo.class);

            if (null == annotation) {
                continue;
            }

            String collection = annotation.value();

            field.setAccessible(true);
            Set value = null;
            try {
                Object idsObj = field.get(info);
                if (idsObj instanceof Set) {
                    value = (Set) idsObj;
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
            if (value != null && value.size() > 0) {
                this.addWithIncrease(collectionQueries, collection, value);
            }

        }

        return mongoMergeQuery;

    }

    /**
     * ids 支持 String 和 Long 类型的 id
     *
     * @param collectionQueries Mongo 查询列表
     * @param collection        Mongo Collection 名称
     * @param ids               id 集合
     */
    public void add(List<CollectionQuery> collectionQueries, String collection, Collection ids) {

        if (ids.size() == 0) {
            return;
        }

        CollectionQuery collectionQuery = new CollectionQuery();
        collectionQuery.setCollectionName(collection);

        Document dbObject = Criteria.where("_id").in(ids).getCriteriaObject();

        String filter = dbObject.toJson();

        collectionQuery.setFilter(Optional.of(filter));

        collectionQueries.add(collectionQuery);
    }

    public void addWithIncrease(List<CollectionIncreaseQuery> collectionQueries, String collection, Set ids) {

        if (ids.size() == 0) {
            return;
        }

        CollectionIncreaseQuery collectionQuery = new CollectionIncreaseQuery();
        collectionQuery.setCollectionName(collection);
        collectionQuery.setIds(ids);

        collectionQueries.add(collectionQuery);
    }

//    public void add(List<CollectionQuery> collectionQueries, String collection, Collection<String> ids) {
//
//        if (ids.size() == 0) {
//            return;
//        }
//
//        CollectionQuery collectionQuery = new CollectionQuery();
//        collectionQuery.setCollectionName(collection);
//
//        final String idString = ids.stream().map(id -> "\"" + id + "\"").collect(Collectors.joining(","));
//
//        String filter = "{\"_id\": {\n" +
//                "  \"$in\": [" + idString + "]\n" +
//                "}}";
//
//
//        collectionQuery.setFilter(Optional.of(filter));
//
//        collectionQueries.add(collectionQuery);
//    }

}
