package com.qpp.cgp.service.database.service;

import com.google.common.collect.ImmutableMap;
import com.mongodb.client.MongoDatabase;
import com.qpp.cgp.config.mongo.MongoTemplateBeanNames;
import com.qpp.cgp.domain.product.sync.ProjectDeployEnv;
import com.qpp.cgp.service.database.dto.PartnerMergeInfo;
import com.qpp.cgp.service.database.dto.UserMergeInfo;
import com.qpp.cgp.service.database.mongo.CollectionQuery;
import com.qpp.cgp.service.database.mongo.MongoDatabaseFactory;
import com.qpp.cgp.service.database.mongo.MongoMergeQuery;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
public class PartnerMergeVerifier {


    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    @Qualifier(MongoTemplateBeanNames.CONFIG)
    private MongoTemplate configMongoTemplate;

    @Autowired
    @Qualifier(MongoTemplateBeanNames.RUNTIME)
    private MongoTemplate runtimeMongoTemplate;

    private void verifyMongo(ProjectDeployEnv env, CollectionQuery collectionQuery) {
        String collectionName = collectionQuery.getCollectionName();
        Optional<String> filter = collectionQuery.getFilter();

        MongoDatabase sourceMongoDatabase = mongoTemplate.getDb();
        String targetDatabaseName = "default";
        if (!mongoTemplate.collectionExists(collectionName)) {
            sourceMongoDatabase = configMongoTemplate.getDb();
            targetDatabaseName = "config";
            if (!configMongoTemplate.collectionExists(collectionName)) {
                sourceMongoDatabase = runtimeMongoTemplate.getDb();
                targetDatabaseName = "runtime";
                if (!runtimeMongoTemplate.collectionExists(collectionName)) {
                    throw new RuntimeException(String.format("该mongo集合在源3个数据库中均不存在, collectionName : %s", collectionName));
                }
            }
        }
        MongoDatabase targetMongoDatabase = MongoDatabaseFactory.getMongoDatabase(env, targetDatabaseName);

        long count = sourceMongoDatabase.getCollection(collectionName).countDocuments(Document.parse(filter.get()));
        if (collectionName.equals("configurations")) {
            System.out.println("configurations count: " + count);
        }
        long targetCount = targetMongoDatabase.getCollection(collectionName).countDocuments(Document.parse(filter.get()));
        if (count > targetCount)
            throw BusinessExceptionBuilder.of(200317, ImmutableMap.of("message", "partner同步校验错误", "collectionName", collectionName));
    }

    public void checkPartnerMergeResult(PartnerMergeInfo partnerMergeInfo, ProjectDeployEnv env) {

        MongoMergeQuery mongoMergeQuery = partnerMergeInfo.getMongoMergeQuery();
        List<UserMergeInfo> userMergeInfoList = partnerMergeInfo.getUserMergeInfo();

        // partner check
        // > mongo compare
        for (CollectionQuery collectionQuery : mongoMergeQuery.getCollectionQueries())
            verifyMongo(env, collectionQuery);

        // user check
        checkUserMergeResult(userMergeInfoList, env);
    }


    private void checkUserMergeResult(List<UserMergeInfo> userMergeInfoList, ProjectDeployEnv env) {

        if (userMergeInfoList != null) {
            for (UserMergeInfo userMergeInfo : userMergeInfoList) {
                MongoMergeQuery mongoMergeQuery = userMergeInfo.getMongoMergeQuery();
                if (mongoMergeQuery != null) {
                    List<CollectionQuery> collectionQueries = mongoMergeQuery.getCollectionQueries();
                    if (collectionQueries != null) {
                        for (CollectionQuery collectionQuery : collectionQueries) {
                            verifyMongo(env, collectionQuery);
                        }
                    }
                }
            }
        }
    }
}
