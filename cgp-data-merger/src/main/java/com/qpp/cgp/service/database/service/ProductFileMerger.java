package com.qpp.cgp.service.database.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.config.mongo.MongoTemplateFactory;
import com.qpp.cgp.constant.ThreadLocalContainerKey;
import com.qpp.cgp.domain.bom.PageContentSchema;
import com.qpp.cgp.domain.common.Website;
import com.qpp.cgp.domain.pcspreprocess.config.PageContentSchemaPreprocessConfig;
import com.qpp.cgp.domain.preprocess.template.PropertyCalculateProductMaterialViewTypeTemplateConfig;
import com.qpp.cgp.domain.preprocess.template.StaticProductMaterialViewTypeTemplateConfig;
import com.qpp.cgp.domain.product.AbstractProduct;
import com.qpp.cgp.domain.product.category.ProductCategory;
import com.qpp.cgp.domain.product.config.model.ThreeDView;
import com.qpp.cgp.domain.product.config.model.ThreeJSVariableConfig;
import com.qpp.cgp.domain.product.media.ProductMedia;
import com.qpp.cgp.domain.product.sync.ProjectDeployEnv;
import com.qpp.cgp.manager.cms.CmsConfigManager;
import com.qpp.cgp.manager.common.FileServerService;
import com.qpp.cgp.repository.product.ProductRepository;
import com.qpp.cgp.repository.product.category.ProductCategoryRepository;
import com.qpp.cgp.repository.product.media.ProductMediaRepository;
import com.qpp.cgp.service.database.dto.ProductMergeInfo;
import com.qpp.cgp.service.database.dto.ProductMigratedMongoMergeInfo;
import com.qpp.cgp.service.database.dto.ProductMongoMergeInfo;
import com.qpp.cgp.service.database.service.file.FileRepository;
import com.qpp.cgp.service.database.service.file.FileService;
import com.qpp.cgp.value.ValueEx;
import com.qpp.core.context.SpringApplicationContext;
import com.qpp.core.utils.SentryUtils;
import com.qpp.core.utils.context.ThreadLocalContainUtils;
import com.qpp.core.utils.http.RestTemplateBuildUtils;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import lombok.extern.log4j.Log4j2;
import net.sf.json.JSONObject;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 产品文件同步器
 *
 * <AUTHOR> Kwok
 * @since 2020/11/11
 */
@Log4j2
@Component
public class ProductFileMerger {

    private Pattern pattern = Pattern.compile("\"imageName\"\\s*:\\s*\"([^\"]*)\"");
    private Pattern pattern2 = Pattern.compile("\"printFile\"\\s*:\\s*\"([^\"]*)\"");

    private Pattern imagePattern = Pattern.compile("(([0-9]|[a-z])+).(png|svg|jpg|pdf)");

    @Autowired
    private FileServerService fileServerService;

    @Autowired
    private ProductMediaRepository productMediaRepository;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${com.qpp.data.merger.file.originFileServer}")
    private String originFileServer;

    @Autowired
    private MongoTemplateFactory mongoTemplateFactory;

    @Autowired
    private CmsConfigManager cmsConfigManager;

    @Autowired
    private ProductRepository productRepository;


    @Autowired
    private ProductCategoryRepository productCategoryRepository;

    public FileRepository fileRepository = new FileRepository();

    // 同步产品相关文件
    public void mergeFile(ProductMergeInfo productMergeInfo, ProjectDeployEnv deployEnv) {
        String targetFileURL = gettargetFileService(deployEnv);
        fetchFileUrlFromProductMergeInfo(productMergeInfo).forEach(e -> {
            syncFile(e, targetFileURL);
        });
        //同步静态文件
        fetchStaticFileUrlFromProductMergeInfo(productMergeInfo).forEach(staticFileUrl -> {
            String fileDir = FilenameUtils.getFullPath(staticFileUrl);

            String originFileUrl = originFileServer + "static/" + staticFileUrl;//FilenameUtils.concat(FilenameUtils.concat(originFileServer, "static"), staticFileUrl);
            String uploadUrl = targetFileURL + "file/upload/static";//FilenameUtils.concat(targetFileURL, "file/upload/static");
            String uploadFileUrl = uploadUrl + "/" + staticFileUrl;//FilenameUtils.concat(uploadUrl, staticFileUrl);
            String targetFileUrlExists = targetFileURL + "files/static/" + staticFileUrl + "/fileInfo/exists";//FilenameUtils.concat(FilenameUtils.concat(FilenameUtils.concat(targetFileURL, "files/static"), staticFileUrl), "fileInfo/exists");

            try {
                syncStaticFile(originFileUrl, uploadUrl, fileDir, targetFileUrlExists);
                log.info("SUCCESS! syncFile({}, {})", originFileUrl, uploadFileUrl);
            } catch (Exception e) {
                log.info("FAILURE! syncFile({}, {}) cause {}", originFileUrl, uploadFileUrl, e.getMessage());
            }
        });
    }

    public Set<String> fetchStaticFileUrlFromProductMergeInfo(ProductMergeInfo productMergeInfo) {
        Set<String> staticFileUrls = new HashSet<>();
        Set<String> threedmodelvariableconfigIds = productMergeInfo.getProductMongoMergeInfo().getThreedmodelvariableconfigIds();
        if (!CollectionUtils.isEmpty(threedmodelvariableconfigIds)) {
            List<ThreeJSVariableConfig> threeJSVariableConfigs = mongoTemplateFactory.getMongoTemplate(ThreeJSVariableConfig.class).find(Query.query(Criteria.where("_id").in(threedmodelvariableconfigIds)), ThreeJSVariableConfig.class);
            for (ThreeJSVariableConfig threeJSVariableConfig : threeJSVariableConfigs) {
                String modelFileName = threeJSVariableConfig.getModelFileName();
                if (StringUtils.isNotBlank(modelFileName)) {
                    staticFileUrls.add(modelFileName);
                }
                List<ThreeDView> views = threeJSVariableConfig.getViews();
                if (views != null && !views.isEmpty()) {
                    views.forEach(view -> {
                        String icon = view.getIcon();
                        if (StringUtils.isNotBlank(icon)) {
                            staticFileUrls.add(icon);
                        }
                    });
                }
            }
        }
        if (null != ThreadLocalContainUtils.get(ThreadLocalContainerKey.SYNC_PRODUCT_STATIC_IMAGE_FILES)) {
            HashSet<String> staticFileContext = ThreadLocalContainUtils.get(ThreadLocalContainerKey.SYNC_PRODUCT_STATIC_IMAGE_FILES);
            if (!CollectionUtils.isEmpty(staticFileContext)) {
                staticFileUrls.addAll(staticFileContext);
            }
        }
        return staticFileUrls;
    }

    public Set<String> fetchFileUrlFromProductMergeInfo(ProductMergeInfo productMergeInfo) {

        Set<String> urls = new HashSet<>();

        Set<Long> productIds = Optional.ofNullable(productMergeInfo)
                .map(ProductMergeInfo::getProductIds)
                .orElse(new HashSet<>());

        Set<Long> productMediaIds = Optional.ofNullable(productMergeInfo)
                .map(ProductMergeInfo::getProductMigratedMongoMergeInfo)
                .map(ProductMigratedMongoMergeInfo::getProductMediaIds)
                .orElse(new HashSet<>());

        Set<String> pageContentSchemaIds = Optional.ofNullable(productMergeInfo)
                .map(ProductMergeInfo::getProductMongoMergeInfo)
                .map(ProductMongoMergeInfo::getPageContentSchemaIds)
                .orElse(new HashSet<>());
        Set<String> productMediaFileUrls = fetchFileUrlFromProductMediaIds(productMediaIds);
        Optional.ofNullable(productMergeInfo).flatMap(e -> Optional.ofNullable(e.getProductMongoMergeInfo())).ifPresent(productMongoMergeInfo -> {
            Set<String> pageContentSchemaPreprocessConfigIds = productMongoMergeInfo.getPageContentSchemaPreprocessConfigIds();
            List<PageContentSchemaPreprocessConfig> pageContentSchemaPreprocessConfigs = mongoTemplateFactory.getMongoTemplate(PageContentSchemaPreprocessConfig.class).find(Query.query(Criteria.where("_id").in(pageContentSchemaPreprocessConfigIds)), PageContentSchemaPreprocessConfig.class);
            Set<String> pcsPreprocessConfigImageUrls = new HashSet<>();
            for (PageContentSchemaPreprocessConfig pageContentSchemaPreprocessConfig : pageContentSchemaPreprocessConfigs) {
                String json = "";
                try {
                    json = objectMapper.writeValueAsString(pageContentSchemaPreprocessConfig);
                } catch (JsonProcessingException ex) {
                    log.info("The pageContentSchemaPreprocessConfig:" + pageContentSchemaPreprocessConfig.getId() + " serialization fail! message:" + ex.getMessage());
                }
                Matcher matcher = imagePattern.matcher(json);
                while (matcher.find()) {
                    String imageUrl = matcher.group();
                    if (StringUtils.isNotBlank(imageUrl)) {
                        log.info("The pageContentSchemaPreprocessConfig:" + pageContentSchemaPreprocessConfig.getId() + " need sync imageUrl:" + imageUrl);
                        if (!imageUrl.contains("http")) {
                            imageUrl = concatUrl(originFileServer, "file", imageUrl);
                            pcsPreprocessConfigImageUrls.add(imageUrl);
                        } else {
                            pcsPreprocessConfigImageUrls.add(imageUrl);
                        }
                    }
                }
            }
            if (!pcsPreprocessConfigImageUrls.isEmpty()) {
                urls.addAll(pcsPreprocessConfigImageUrls);
            }
        });
        Set<String> productContentSchemaFileUrls = fetchFileUrlFromPageContentSchemaIds(pageContentSchemaIds);
        Set<String> templateConfigIds = getTemplateConfigIds(productMergeInfo);
        Set<String> fileURLsByProductMaterialViewTypeTemplateIds = getFileURLsByProductMaterialViewTypeTemplateIds(templateConfigIds);
        urls.addAll(productMediaFileUrls);
        urls.addAll(fileURLsByProductMaterialViewTypeTemplateIds);
        urls.addAll(productContentSchemaFileUrls);
        Set<String> cmsFileNames = cmsConfigManager.getFileNameByProductIds(productMergeInfo.getProductIds());
        if (cmsFileNames != null && !cmsFileNames.isEmpty()) {
            Set<String> replaceCmsFileUrl = new HashSet<>();
            for (String cmsFileName : cmsFileNames) {
                if (!cmsFileName.contains("http")) {
                    cmsFileName = concatUrl(originFileServer, "file", cmsFileName);
                }
                replaceCmsFileUrl.add(cmsFileName);
            }
            urls.addAll(replaceCmsFileUrl);
        }
        log.info(
                "SUCCESS! fetchFileUrlFromProductMergeInfo(productIds={}) return {}",
                productIds.stream().map(Objects::toString).collect(Collectors.joining(" , ", "[", "]")),
                urls.stream().collect(Collectors.joining(" , ", "[", "]"))
        );

        if (null != ThreadLocalContainUtils.get(ThreadLocalContainerKey.SYNC_PRODUCT_IMAGE_FILES)) {
            HashSet<String> urlContext = ThreadLocalContainUtils.get(ThreadLocalContainerKey.SYNC_PRODUCT_IMAGE_FILES);
            if (!CollectionUtils.isEmpty(urlContext)) {
                urls.addAll(urlContext);
            }
        }

        return urls;
    }

    private Set<String> getTemplateConfigIds(ProductMergeInfo productMergeInfo) {
        Set<String> templateConfigIds = new HashSet<>();
        ProductMongoMergeInfo productMongoMergeInfo = productMergeInfo.getProductMongoMergeInfo();
        if (productMongoMergeInfo == null) {
            return templateConfigIds;
        }
        if (productMongoMergeInfo.getProductMaterialViewTypeTemplateConfigIds() == null) {
            return templateConfigIds;
        }
        return productMongoMergeInfo.getProductMaterialViewTypeTemplateConfigIds();
    }

    private String gettargetFileService(ProjectDeployEnv deployEnv) {

        String filePath = String.format("data-merge-env.%s.targetFileServer", deployEnv);
        String fileService = SpringApplicationContext.getApplicationContextStatic().getEnvironment().getProperty(filePath);

        if (StringUtils.isBlank(fileService)) {
            throw new RuntimeException(String.format("获取目标数文件服务器地址失败，配置文件属性路径为：%s", filePath));
        }
        return fileService;
    }

    private Set<String> getFileURLsByProductMaterialViewTypeTemplateIds(Set<String> templateConfigIds) {
        Set<String> fileURLs = new HashSet<>();
        if (templateConfigIds != null && !templateConfigIds.isEmpty()) {
            MongoTemplate mongoTemplate = mongoTemplateFactory.getMongoTemplate(StaticProductMaterialViewTypeTemplateConfig.class);
            List<StaticProductMaterialViewTypeTemplateConfig> templateConfigs = mongoTemplate.find(Query.query(Criteria.where("_id").in(templateConfigIds)), StaticProductMaterialViewTypeTemplateConfig.class);
            if (!templateConfigs.isEmpty()) {
                Set<String> files = templateConfigs.stream().map(StaticProductMaterialViewTypeTemplateConfig::getFileUrl).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
                if (!files.isEmpty()) {
                    for (String file : files) {
                        if (file.contains("http")) {
                            fileURLs.add(file);
                        } else {
                            file = concatUrl(originFileServer, "file", file);
                            fileURLs.add(file);
                        }
                    }
                }
            }
            List<PropertyCalculateProductMaterialViewTypeTemplateConfig> templateConfigList = mongoTemplateFactory.getMongoTemplate(PropertyCalculateProductMaterialViewTypeTemplateConfig.class).find(Query.query(Criteria.where("_id").in(templateConfigIds).and("clazz").is(PropertyCalculateProductMaterialViewTypeTemplateConfig.class.getName())), PropertyCalculateProductMaterialViewTypeTemplateConfig.class);
            if (!templateConfigList.isEmpty()) {
                for (PropertyCalculateProductMaterialViewTypeTemplateConfig propertyCalculateProductMaterialViewTypeTemplateConfig : templateConfigList) {
                    ValueEx fileUrl = propertyCalculateProductMaterialViewTypeTemplateConfig.getFileUrl();
                    String fileJson;
                    try {
                        fileJson = objectMapper.writeValueAsString(fileUrl);
                    } catch (JsonProcessingException e) {
                        SentryUtils.recordEx(e, ImmutableMap.of("message", "The propertyCalculateProductMaterialViewTypeTemplateConfigId:" + propertyCalculateProductMaterialViewTypeTemplateConfig.getId() + " get pdf fail!"));
                        continue;
                    }
                    Matcher matcher = imagePattern.matcher(fileJson);
                    while (matcher.find()) {
                        String imageUrl = matcher.group();
                        if (StringUtils.isNotBlank(imageUrl)) {
                            if (!imageUrl.contains("http")) {
                                imageUrl = concatUrl(originFileServer, "file", imageUrl);
                                fileURLs.add(imageUrl);
                            } else {
                                fileURLs.add(imageUrl);
                            }
                        }
                    }
                }
            }
        }
        return fileURLs;
    }

    public Set<String> fetchFileUrlFromProductMediaIds(Set<Long> productMediaIds) {
        return productMediaIds.stream()
                .map(productMediaRepository::findById)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .map(productMedia -> {
                    Set<String> urls = new HashSet<>();
                    try {
                        urls = fetchFileUrlFromProductMedia(productMedia);
                        log.info("SUCCESS! fetchFileUrlFromProductMedia(ProductMedia({})) return {}", productMedia.getId(), urls.stream().collect(Collectors.joining(" , ", "[", "]")));
                    } catch (Exception e) {
                        log.error("FAILURE! fetchFileUrlFromProductMedia(ProductMedia({})) cause {}", productMedia.getId(), e.getMessage());
                    }
                    return urls;
                })
                .flatMap(Collection::stream)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
    }

    public Set<String> fetchFileUrlFromProductMedia(ProductMedia productMedia) {
        Set<String> fileUrls = new HashSet<>();

        // 获取 websiteId
        Long websiteId = Optional.ofNullable(productMedia)
                .map(ProductMedia::getProductId)
                .map(e -> productRepository.getOne(e, false))
                .map(AbstractProduct::getMainCategoryId)
                .map(e -> productCategoryRepository.getOne(e, false))
                .map(ProductCategory::getWebsite)
                .map(Website::getId)
                .orElse(null);

        if (websiteId == null) {
            return fileUrls;
        }

        // 获取文件名称
        String name = productMedia.getName();
        if (StringUtils.isBlank(name)) {
            return fileUrls;
        }

        // 获取文件格式
        String format = productMedia.getFormat();
        if (StringUtils.isBlank(format)) {
            format = "";
        }

        // 拼接完整的 Url
        String fileUrl = fileServerService.getFileUrl(name, format, websiteId);
        if (StringUtils.isNotBlank(fileUrl)) {
            fileUrls.add(fileUrl);
        }


        return fileUrls;
    }

    public Set<String> fetchFileUrlFromPageContentSchemaIds(Set<String> pageContentSchemaIds) {
        return pageContentSchemaIds.stream()
                .map(pcsId -> mongoTemplate.findById(pcsId, PageContentSchema.class))
                .filter(Objects::nonNull)
                .map(pcs -> {
                    Set<String> urls = new HashSet<>();
                    try {
                        urls = fetchFileUrlFromPageContentSchema(pcs);
                        log.info("SUCCESS! fetchFileUrlFromPageContentSchema(PageContentSchema({})) return {}", pcs.getId(), urls.stream().collect(Collectors.joining(" , ", "[", "]")));
                    } catch (Exception e) {
                        log.error("FAILURE! fetchFileUrlFromPageContentSchema(PageContentSchema({})) cause {}", pcs.getId(), e.getMessage());
                    }
                    return urls;
                })
                .flatMap(Collection::stream)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

    }

    public Set<String> fetchFileUrlFromPageContentSchema(PageContentSchema pageContentSchema) {
        Set<String> fileUrls = new HashSet<>();

        String pscJSON = null;
        try {
            pscJSON = objectMapper.writeValueAsString(pageContentSchema);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            return fileUrls;
        }
        findFileURL(pattern, fileUrls, pscJSON);
        findFileURL(pattern2, fileUrls, pscJSON);
        return fileUrls;
    }

    private void findFileURL(Pattern pattern, Set<String> fileUrls, String pscJSON) {
        Matcher matcher = pattern.matcher(pscJSON);
        while (matcher.find()) {
            String fileName = matcher.group(1);
            if (StringUtils.isBlank(fileName)) {
                continue;
            }
            String fileUrl = concatUrl(originFileServer, "file", fileName);
            if (StringUtils.isNotBlank(fileUrl)) {
                fileUrls.add(fileUrl);
            }
        }
    }

    public void syncFile(String originFileUrl, String targetFileUrl) {

        // 保证文件名称不变, 使用 ~/file/upload/{specName} API 上传文件
        String uploadFileUrl = "";
        try {
            String baseName = FilenameUtils.getBaseName(originFileUrl);
            uploadFileUrl= concatUrl(targetFileUrl, "file/upload", baseName);
            String result = FileService.syncFile(originFileUrl, uploadFileUrl);
            log.info("SUCCESS! syncFile({}, {}) return {}", originFileUrl, uploadFileUrl, result);
        } catch (Exception e) {
            log.info("FAILURE! syncFile({}, {}) cause {}", originFileUrl, uploadFileUrl, e.getMessage());
        }
    }

    /**
     * 同步文件和检查
     *
     * @param originFileUrl
     * @param targetFileUrl
     */
    public void syncFileAndCheck(String originFileUrl, String targetFileUrl, String targetFileUrlExists) {
        // 目标服务器已存在该文件：return
        if (targetFileUrlExists(targetFileUrlExists)) {
            return;
        }
        this.syncFile(originFileUrl, targetFileUrl);
    }

    /**
     * 同步静态文件
     *
     * @param originFileUrl
     * @param uploadUrl
     * @param fileDir
     * @param targetFileUrlExists
     */
    public void syncStaticFile(String originFileUrl, String uploadUrl, String fileDir, String targetFileUrlExists) {
        if (fileDir == null || !fileDir.matches("^(/?[^\\/:*?*<>|]+)+/$")) {
            // 同步静态文件目录Dir路径不符合要求
            Map<String, Object> errorParams = new HashMap<>();
            errorParams.put("fileDir", fileDir);
            throw BusinessExceptionBuilder.of(789477, "前端传入的静态文件目录字段值不否符合要求，staticFileDir值为：" + fileDir, errorParams);
        }
        // 目标服务器已存在该文件：return
        if (targetFileUrlExists(targetFileUrlExists)) {
            return;
        }
        FileService.syncStaticFile(originFileUrl, uploadUrl, fileDir);
    }


    /**
     * 判断目标服务器是否已存在该文件 已存在：true   不存在：false
     *
     * @param originFileUrl
     * @return
     */
    public boolean targetFileUrlExists(String originFileUrl) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.63 Safari/537.36");
        RestTemplate restTemplate = RestTemplateBuildUtils.buildRestTemplate();
        JSONObject body = null;
        try {
            body = restTemplate.getForObject(originFileUrl, JSONObject.class);
        } catch (Exception e) {
            Map<String, Object> errorParams = new HashMap<>();
            errorParams.put("originFileUrl", originFileUrl);
            throw BusinessExceptionBuilder.of(56201, "请求检查文件存在目标服务器的远程接口调用失败，请求url：" + originFileUrl, errorParams);
        }
        if (body == null) {
            Map<String, Object> errorParams = new HashMap<>();
            errorParams.put("originFileUrl", originFileUrl);
            throw BusinessExceptionBuilder.of(56202, "请求检查文件存在目标服务器的远程接口失败，未返回请求body数据,请求url：" + originFileUrl, errorParams);
        }
        return body.get("data").equals("true");
    }


    public static String concatUrl(String... items) {

        String PATH_SEPARATOR = "/";

        StringBuilder sb = new StringBuilder();

        for (int idx = 0; idx < items.length; idx++) {

            String pathItem = items[idx];

            String formatPathItem = pathItem;

            if (idx == 0) {
                sb.append(formatPathItem);
            } else {
                if (sb.toString().endsWith(PATH_SEPARATOR) && formatPathItem.startsWith(PATH_SEPARATOR)) {
                    sb.append(formatPathItem, 1, formatPathItem.length());
                } else if (!sb.toString().endsWith(PATH_SEPARATOR) && !formatPathItem.startsWith(PATH_SEPARATOR)) {
                    sb.append(PATH_SEPARATOR).append(pathItem);
                } else {
                    sb.append(pathItem);
                }
            }

        }

        return sb.toString();
    }

    public String getOriginFileServer() {
        return originFileServer;
    }
}
