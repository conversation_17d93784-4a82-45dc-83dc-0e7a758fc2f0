package com.qpp.cgp.service.database.service;

import com.qpp.cgp.domain.dynamicsize.configuration.DsTemplateGenerateConfig;
import com.qpp.cgp.domain.product.sync.ProjectDeployEnv;
import com.qpp.cgp.manager.dynamicsize.configuration.DsTemplateGenerateConfigManager;
import com.qpp.cgp.service.database.mongo.CollectionQuery;
import com.qpp.cgp.service.database.mongo.MongoMergeQuery;
import com.qpp.cgp.service.database.mongo.MongoMerger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class DsTemplateGenerateConfigSyncService {
    @Autowired
    private DsTemplateGenerateConfigManager dsTemplateGenerateConfigManager;

    @Autowired
    private MongoMerger mongoMerger;

    @Autowired
    private MongoTemplate mongoTemplate;


    public Set<Long> sync(String templateName, ProjectDeployEnv projectDeployEnv, String userName, String password) {
        Set<Long> ids = dsTemplateGenerateConfigManager.findByTemplateName(templateName)
                .stream()
                .map(DsTemplateGenerateConfig::getId)
                .collect(Collectors.toSet());
        // 开始同步
        MongoMergeQuery mongoMergeQuery = new MongoMergeQuery();
        CollectionQuery collectionQuery = new CollectionQuery();
        collectionQuery.setCollectionName("dstemplategenerateconfigs");
        collectionQuery.setFilter(Optional.of("{_id : {$in : " + ids + "}}"));
        mongoMergeQuery.setCollectionQueries(Collections.singletonList(collectionQuery));

        mongoMerger.mergeData(mongoMergeQuery, projectDeployEnv, userName, password);

        return ids;
    }

}
