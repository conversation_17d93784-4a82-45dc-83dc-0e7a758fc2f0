package com.qpp.cgp.service.database.service.file;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.StringUtils;

import java.util.HashSet;
import java.util.Set;

/**
 * 文件路径工具类
 */
public class FilePathUtils {

    private static final String WINDOWS_PATH_SEPARATOR = "\\";

    private static final String UNIX_PATH_SEPARATOR = "/";

    private static final Set<String> OTHER_SEPARATORS = new HashSet<>();

    public static final String PATH_SEPARATOR;

    static {
        // 统一使用 UNIX 文件分隔符
        PATH_SEPARATOR = UNIX_PATH_SEPARATOR;
        // 其他分隔符
        OTHER_SEPARATORS.add(WINDOWS_PATH_SEPARATOR);
    }

    /**
     * 格式化文件路径, 替换统一的文件分隔符 {@link FilePathUtils#PATH_SEPARATOR}, 去除 ./ 和 ../
     */
    public static String normalize(String filePath) {

        String normalizedFilePath = FilenameUtils.normalize(filePath);

        for (String otherPathSeparator : OTHER_SEPARATORS) {
            normalizedFilePath = normalizedFilePath.replace(otherPathSeparator, PATH_SEPARATOR);
        }

        return normalizedFilePath;
    }

    /**
     * 拼接文件路径
     */
    public static String concat(String ...pathItems) {

        StringBuilder sb = new StringBuilder();

        for (int idx = 0; idx < pathItems.length; idx ++) {

            String pathItem = pathItems[idx];

            String formatPathItem = normalize(pathItem);

            if (idx == 0) {
                sb.append(formatPathItem);
            } else {
                if (sb.toString().endsWith(PATH_SEPARATOR) && formatPathItem.startsWith(PATH_SEPARATOR)) {
                    sb.append(formatPathItem, 1, formatPathItem.length());
                } else if (!sb.toString().endsWith(PATH_SEPARATOR) && !formatPathItem.startsWith(PATH_SEPARATOR)) {
                    sb.append(PATH_SEPARATOR).append(pathItem);
                } else {
                    sb.append(pathItem);
                }
            }

        }

        return normalize(sb.toString());
    }

    /**
     * 替换文件的基本名称（不替换文件格式）
     */
    public static String replaceBasename(String filePath, String newBasename) {

        String basePath = getBasePath(filePath);

        String format = getFormat(filePath);

        String suffix = StringUtils.isNotBlank(filePath) ? "." + format : "";

        String basename = StringUtils.isNotBlank(newBasename) ? newBasename : "";

        return concat(basePath, basename + suffix);
    }

    /**
     * 替换文件的格式
     */
    public static String replaceFormat(String filePath, String newFormat) {

        String basePath = getBasePath(filePath);

        String basename = getBasename(filePath);

        String suffix = StringUtils.isNotBlank(newFormat) ? "." + newFormat : "";

        return concat(basePath, basename + suffix);

    }

    /**
     * 获取文件目录
     */
    public static String getBasePath(String filePath) {
        return normalize(FilenameUtils.getFullPathNoEndSeparator(filePath));
    }

    /**
     * 获取文件名称（附带文件格式）
     */
    public static String getFilename(String filePath) {
        return FilenameUtils.getName(filePath);
    }

    /**
     * 获取文件名称 (不带文件格式)
     */
    public static String getBasename(String filePath) {
        return FilenameUtils.getBaseName(filePath);
    }

    /**
     * 获取文件格式
     */
    public static String getFormat(String filePath) {
        return FilenameUtils.getExtension(filePath);
    }



}
