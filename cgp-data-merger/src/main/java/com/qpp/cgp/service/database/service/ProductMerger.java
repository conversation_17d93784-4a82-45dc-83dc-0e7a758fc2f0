package com.qpp.cgp.service.database.service;

import com.qpp.cgp.domain.product.sync.ProjectDeployEnv;
import com.qpp.cgp.service.database.dto.ProductMergeInfo;
import com.qpp.cgp.service.database.mongo.*;
import com.qpp.cgp.service.database.mysql.MysqlMerger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> Lee 2019/4/3 17:07
 */
@Component
public class ProductMerger {

    @Autowired
    private ProductMergeInfoGenerator productMergeInfoGenerator;

    @Autowired
    private MongoQueryGenerator mongoQueryGenerator;

    @Autowired
    private ProductMysqlQueryGenerator productMysqlQueryGenerator;

    @Autowired
    private MongoMerger mongoMerger;

    @Autowired
    private MysqlMerger mysqlMerger;

    @Autowired
    private ProductMergerLockService productMergerLockService;


    public ProductMergeInfo mergeProduct(List<Long> productIds, ProjectDeployEnv env) {

        final ProductMergeInfo productMergeInfo = productMergeInfoGenerator.generateProductMergeInfo(productIds);
        final MongoMergeQuery migratedMongoQuery = mongoQueryGenerator.generateQuery(productMergeInfo.getProductMigratedMongoMergeInfo());
        final MongoMergeQuery mongoMergeQuery = mongoQueryGenerator.generateQuery(productMergeInfo.getProductMongoMergeInfo());
        final MongoMergeQuery mongoSupplementMergeQuery = mongoQueryGenerator.generateQuery(productMergeInfo.getProductMongoSupplementMergeInfo());


        mongoMerger.mergeData(migratedMongoQuery, env);
        mongoMerger.mergeData(mongoMergeQuery, env);
        mongoMerger.mergeData(mongoSupplementMergeQuery, env);


        productMergerLockService.batchProductConfigLock(productIds, env);

        return productMergeInfo;
    }

}
