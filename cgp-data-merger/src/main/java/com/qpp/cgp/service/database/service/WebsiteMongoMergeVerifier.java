package com.qpp.cgp.service.database.service;

import com.google.common.collect.ImmutableMap;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.qpp.cgp.domain.product.sync.ProjectDeployEnv;
import com.qpp.cgp.service.database.dto.WebsiteMongoMergeInfo;
import com.qpp.cgp.service.database.mongo.MongoDatabaseFactory;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.bson.Document;

import java.util.Set;

public class WebsiteMongoMergeVerifier {

    public static void checkContainsIds(WebsiteMongoMergeInfo info, ProjectDeployEnv env) {
        // 判断 目标环境数据表中 是否存在 原环境数据表中数据id
        MongoDatabase configMongoDatabase = MongoDatabaseFactory.getMongoDatabase(env, "config");

        Set<Long> configurationIds = info.getConfigurationIds();
        MongoCollection<Document> configurations = configMongoDatabase.getCollection("configurations");
        if (configurations.countDocuments(Document.parse(String.format("{_id : {$in : %s}}", configurationIds))) != configurationIds.size())
            throw BusinessExceptionBuilder.of(200317, ImmutableMap.of("message", "website同步校验错误", "collectionName", "configurations"));

        Set<Long> currencyIds = info.getCurrencyIds();
        MongoCollection<Document> currencies = configMongoDatabase.getCollection("currencies");
        if (currencies.countDocuments(Document.parse(String.format("{_id : {$in : %s}}", currencyIds))) != currencyIds.size())
            throw BusinessExceptionBuilder.of(200317, ImmutableMap.of("message", "website同步校验错误", "collectionName", "currencies"));

        Set<Long> numberRuleIds = info.getNumberRuleIds();
        MongoCollection<Document> numberRules = configMongoDatabase.getCollection("numberrules");
        if (numberRules.countDocuments(Document.parse(String.format("{_id : {$in : %s}}", numberRuleIds))) != numberRuleIds.size())
            throw BusinessExceptionBuilder.of(200317, ImmutableMap.of("message", "website同步校验错误", "collectionName", "numberrules"));

        Set<Long> paymentModuleConfigIds = info.getPaymentModuleConfigIds();
        MongoCollection<Document> paymentModuleConfigs = configMongoDatabase.getCollection("paymentmoduleconfigs");
        if(paymentModuleConfigs.countDocuments(Document.parse(String.format("{_id : {$in : %s}}", paymentModuleConfigIds))) != paymentModuleConfigIds.size())
            throw BusinessExceptionBuilder.of(200317, ImmutableMap.of("message", "website同步校验错误", "collectionName", "paymentmoduleconfigs"));

        Set<Long> shippingModuleConfigIds = info.getShippingModuleConfigIds();
        MongoCollection<Document> shippingModuleConfigs = configMongoDatabase.getCollection("shippingmoduleconfigs");
        if(shippingModuleConfigs.countDocuments(Document.parse(String.format("{_id : {$in : %s}}", shippingModuleConfigIds))) != shippingModuleConfigIds.size())
            throw BusinessExceptionBuilder.of(200317, ImmutableMap.of("message", "website同步校验错误", "collectionName", "shippingmoduleconfigs"));

        Set<Long> orderTotalModuleConfigIds = info.getOrderTotalModuleConfigIds();
        MongoCollection<Document> orderTotalModuleConfigs = configMongoDatabase.getCollection("ordertotalmoduleconfigs");
        if(orderTotalModuleConfigs.countDocuments(Document.parse(String.format("{_id : {$in : %s}}", orderTotalModuleConfigIds))) != orderTotalModuleConfigIds.size())
            throw BusinessExceptionBuilder.of(200317, ImmutableMap.of("message", "website同步校验错误", "collectionName", "ordertotalmoduleconfigs"));

        Set<Long> postageEmsIds = info.getPostageEmsIds();
        MongoCollection<Document> postageEms = configMongoDatabase.getCollection("postageems");
        if(postageEms.countDocuments(Document.parse(String.format("{_id : {$in : %s}}", postageEmsIds))) != postageEmsIds.size())
            throw BusinessExceptionBuilder.of(200317, ImmutableMap.of("message", "website同步校验错误", "collectionName", "postageems"));

        Set<Long> postageExpressIds = info.getPostageExpressIds();
        MongoCollection<Document> postageExpress = configMongoDatabase.getCollection("postageexpress");
        if(postageExpress.countDocuments(Document.parse(String.format("{_id : {$in : %s}}", postageExpressIds))) != postageExpressIds.size())
            throw BusinessExceptionBuilder.of(200317, ImmutableMap.of("message", "website同步校验错误", "collectionName", "postageexpress"));

        Set<Long> postageSfIds = info.getPostageSfIds();
        MongoCollection<Document> postageSf = configMongoDatabase.getCollection("postagesf");
        if(postageSf.countDocuments(Document.parse(String.format("{_id : {$in : %s}}", postageSfIds))) != postageSfIds.size())
            throw BusinessExceptionBuilder.of(200317, ImmutableMap.of("message", "website同步校验错误", "collectionName", "postagesf"));

        Set<Long> postageStandardIds = info.getPostageStandardIds();
        MongoCollection<Document> postageStandard = configMongoDatabase.getCollection("postagestandard");
        if(postageStandard.countDocuments(Document.parse(String.format("{_id : {$in : %s}}", postageStandardIds))) != postageStandardIds.size())
            throw BusinessExceptionBuilder.of(200317, ImmutableMap.of("message", "website同步校验错误", "collectionName", "postagestandard"));

        Set<Long> postageZtIds = info.getPostageZtIds();
        MongoCollection<Document> postageZt = configMongoDatabase.getCollection("postagezt");
        if(postageZt.countDocuments(Document.parse(String.format("{_id : {$in : %s}}", postageZtIds))) != postageZtIds.size())
            throw BusinessExceptionBuilder.of(200317, ImmutableMap.of("message", "website同步校验错误", "collectionName", "postagezt"));
    }

}
