package com.qpp.cgp.service.database.service;

import com.qpp.cgp.domain.PartnerSyncProgress;
import com.qpp.cgp.domain.product.sync.ProjectDeployEnv;
import com.qpp.cgp.service.database.dto.UserMergeInfo;
import com.qpp.cgp.service.database.mongo.CollectionQuery;
import com.qpp.cgp.service.database.mongo.MongoMergeQuery;
import com.qpp.cgp.service.database.mongo.MongoMerger;
import com.qpp.cgp.service.database.mysql.MysqlConfig;
import com.qpp.cgp.service.database.mysql.MysqlMergeQuery;
import com.qpp.cgp.service.database.mysql.MysqlMerger;
import com.qpp.cgp.service.database.mysql.TableQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;

/**
 * 相较于V1版，拆分原merge方法为两部分，提供给partner同步使用
 */
@Component
public class UserMergerV2 {

    @Autowired
    private MongoMerger mongoMerger;

    public UserMergeInfo getUserMergeInfo(List<Long> userIds) {

        UserMergeInfo result = new UserMergeInfo();

        MongoMergeQuery mongoMergeQuery = new MongoMergeQuery();
        List<CollectionQuery> collectionQueries = mongoMergeQuery.getCollectionQueries();

        //用户
        CollectionQuery userColl = new CollectionQuery();
        userColl.setCollectionName("users");
        userColl.setFilter(Optional.of("{_id : {$in : " + userIds + "}}"));

        //地址
        CollectionQuery addressBookColl = new CollectionQuery();
        addressBookColl.setCollectionName("addressbooks");
        addressBookColl.setFilter(Optional.of("{userId : {$in : " + userIds + "}}"));

        collectionQueries.add(userColl);
        collectionQueries.add(addressBookColl);

        result.setMongoMergeQuery(mongoMergeQuery);

        return result;
    }

    public void merge(ProjectDeployEnv projectDeployEnv, UserMergeInfo userMergeInfo, String progressId) {

        MongoMergeQuery mongoMergeQuery = userMergeInfo.getMongoMergeQuery();
        mongoMerger.mergeData(mongoMergeQuery, projectDeployEnv, progressId, PartnerSyncProgress.class);
    }
}
