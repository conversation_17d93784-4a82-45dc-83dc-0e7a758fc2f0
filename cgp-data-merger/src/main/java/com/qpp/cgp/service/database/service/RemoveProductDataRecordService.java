package com.qpp.cgp.service.database.service;

import com.qpp.cgp.domain.reset.RemoveProductDataRecord;
import com.qpp.cgp.manager.product.reset.RemoveProductDataRecordManager;
import com.qpp.cgp.manager.reset.NeedResetProductData;
import com.qpp.core.utils.SecurityUtils;
import com.qpp.id.generator.IdGenerator;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * @description:
 * @author: TT-Berg
 * @date: 2023/10/11
 **/
@Service
public class RemoveProductDataRecordService {

    @Autowired
    private RemoveProductDataRecordManager removeProductDataRecordManager;
    @Autowired
    private IdGenerator idGenerator;

    public RemoveProductDataRecord initRecord(List<Long> productIds) {
        RemoveProductDataRecord record = new RemoveProductDataRecord();

        record.setId(idGenerator.generateId());
        record.setTotalRemoveData(new HashMap<>());
        record.setHasRemovedData(new HashMap<>());
        record.setOperateDate(new Date());
        record.setOperateUser(SecurityUtils.getLoginedInUserId());
        record.setProductIds(productIds);

        return record;
    }

    public RemoveProductDataRecord fillTotalRemoveData(RemoveProductDataRecord record,
                                                       List<NeedResetProductData> needResetProductDataList) {
        if (CollectionUtils.isEmpty(needResetProductDataList)) {
            return record;
        }
        if (null == record.getTotalRemoveData()) {
            record.setTotalRemoveData(new HashMap<>());
        }
        needResetProductDataList.forEach(data -> {
            String collectionName = data.getCollectionName();
            List<Object> ids = data.getIds();
            record.getTotalRemoveData().put(collectionName, ids);
        });
        return record;
    }

    public RemoveProductDataRecord saveNew(RemoveProductDataRecord record){
        return removeProductDataRecordManager.saveNew(record);
    }
}
