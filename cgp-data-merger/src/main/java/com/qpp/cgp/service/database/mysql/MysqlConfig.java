package com.qpp.cgp.service.database.mysql;

import com.qpp.cgp.service.database.DatabaseConfig;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * Created by smart on 9/28/2017.
 */
@NoArgsConstructor
@Data
@AllArgsConstructor
public class MysqlConfig implements DatabaseConfig {

    private String url;

    private String username;

    private String password;

    private String driver = "com.mysql.jdbc.Driver";
}
