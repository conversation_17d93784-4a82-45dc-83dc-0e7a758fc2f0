package com.qpp.cgp.service.database.service;

import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.MongoDatabase;
import com.qpp.cgp.domain.product.lock.LockHistory;
import com.qpp.cgp.domain.product.lock.ProductLockConfig;
import com.qpp.cgp.manager.product.lock.ProductLockConfigManager;
import com.qpp.cgp.service.database.mongo.MongoConfig;
import com.qpp.cgp.service.database.mongo.MongoDatabaseFactory;
import com.qpp.cgp.domain.product.sync.ProjectDeployEnv;
import com.qpp.id.generator.IdGenerator;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class ProductMergerLockService {


    @Autowired
    private ProductLockConfigManager productLockConfigManager;

    @Autowired
    private IdGenerator idGenerator;


    /**
     * 同步产品的时候同步锁定Source的产品配置和解锁Target的产品配置
     *
     * @param targetMongoConfig target
     * @param productIds
     */
    public void batchProductConfigLock(MongoConfig targetMongoConfig, List<Long> productIds) {

        MongoDatabase targetMongoDataBase = MongoDatabaseFactory.getMongoDatabase(targetMongoConfig);

        batchProductConfigLock(productIds, targetMongoDataBase, 0L, "产品同步");

    }

    public void batchProductConfigLock(List<Long> productIds, MongoDatabase targetMongoDataBase, long userId, String comment) {
        for (Long productId : productIds) {
            Document document = null;

            FindIterable<Document> documentFindIterable = targetMongoDataBase.getCollection("productlockconfigs").find(new Document("productId", productId));
            MongoCursor<Document> iterator = documentFindIterable.iterator();
            boolean exists = false;
            if (iterator.hasNext()) {
                document = iterator.next();
                exists = true;
            }
            if (document == null) {
                document = new Document();
                document.put("_id", idGenerator.generateId().toString());
                document.put("clazz", ProductLockConfig.class.getName());
                document.put("createdDate", new Date());
                document.put("productId", productId);
                document.put("isLock", false);
            } else {
                document.replace("isLock", false);
            }
            Object value = document.get("lockHistories");
            if (value == null) {
                List<Document> lockHistories = new ArrayList<>();
                addHistory(document, lockHistories, false, userId, comment);
            } else {
                List<Document> lockHistories = (List<Document>) value;
                addHistory(document, lockHistories, true, userId, comment);
            }
            document.put("modifiedDate", new Date());
            if (exists) {
                targetMongoDataBase.getCollection("productlockconfigs").updateOne(new Document("_id", document.get("_id").toString()), new Document("$set", document));
            } else {
                targetMongoDataBase.getCollection("productlockconfigs").insertOne(document);
            }
            productLockConfigManager.lock(productId, userId, comment);
        }
    }

    public void batchProductConfigLock(List<Long> productIds, ProjectDeployEnv env) {

        MongoDatabase targetMongoDataBase = MongoDatabaseFactory.getMongoDatabase(env, "default");

        batchProductConfigLock(productIds, targetMongoDataBase, 0L, "产品同步");

    }

    public void batchProductConfigLock(List<Long> productIds, ProjectDeployEnv env, long userId, String comment) {

        MongoDatabase targetMongoDataBase = MongoDatabaseFactory.getMongoDatabase(env, "default");

        batchProductConfigLock(productIds, targetMongoDataBase, userId, comment);

    }

    private void addHistory(Document document, List<Document> lockHistories, Boolean isExists, long userId, String comment) {
        Document lockHistory = new Document();
        lockHistory.put("operatorDate", new Date());
        lockHistory.put("userId", userId);
        lockHistory.put("lockStatus", false);
        lockHistory.put("comment", comment);
        lockHistory.put("clazz", LockHistory.class.getName());
        lockHistories.add(lockHistory);
        if (isExists) {
            document.replace("lockHistories", lockHistories);
        } else {
            document.put("lockHistories", lockHistories);
        }
    }

    private MongoConfig buildMongoConfig() {
        String host = "*************";
        int port = 27017;
        String username = "developer";
        String password = "Dev!123a";

        String targetDatabase = "cgp2_test";

        MongoConfig targetMongoConfig = new MongoConfig(host, port, username, password, targetDatabase);
        return targetMongoConfig;
    }
}
