package com.qpp.cgp.service.database.service;

import com.qpp.cgp.service.database.mysql.MysqlConfig;
import com.qpp.cgp.service.database.mysql.MysqlMergeQuery;
import com.qpp.cgp.service.database.mysql.MysqlMerger;
import com.qpp.cgp.service.database.mysql.TableQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> Lee 2020/1/15 16:46
 */
@Service
public class WebsiteMerger {

    private MysqlMerger mysqlMerger;

    @Autowired
    public WebsiteMerger(MysqlMerger mysqlMerger) {
        this.mysqlMerger = mysqlMerger;
    }

    public void merger(Long websiteId, MysqlConfig targetMysqlConfig) {

        MysqlMergeQuery        mysqlQuery   = new MysqlMergeQuery();
        final List<TableQuery> tableQueries = mysqlQuery.getTableQueries();


        //网站配置
        TableQuery configuration = new TableQuery();
        configuration.setTableName("cgp_configuration");
        configuration.setFilter(Optional.of("where config_website_id = " + websiteId));

        //运送方式
        TableQuery shipping = new TableQuery();
        shipping.setTableName("cgp_module_shipping");
        shipping.setFilter(Optional.of("where website_id = " + websiteId));

        //付款方式
        TableQuery payment = new TableQuery();
        payment.setTableName("cgp_module_payment");
        payment.setFilter(Optional.of("where website_id = " + websiteId));

        //货币
        TableQuery currency = new TableQuery();
        currency.setTableName("cgp_currency");
        currency.setFilter(Optional.of("where cur_website_id = " + websiteId));

        //订单号规则
        TableQuery numberRule = new TableQuery();
        numberRule.setTableName("cgp_number_rule");
        numberRule.setFilter(Optional.of("where website_id = " + websiteId));

        tableQueries.add(configuration);
        tableQueries.add(shipping);
        tableQueries.add(payment);
        tableQueries.add(currency);
        tableQueries.add(numberRule);

        final MysqlMergeQuery mysqlMergeQuery = new MysqlMergeQuery();
        mysqlMergeQuery.setTableQueries(tableQueries);

        mysqlMerger.mergeData(targetMysqlConfig, mysqlMergeQuery);

    }

}
