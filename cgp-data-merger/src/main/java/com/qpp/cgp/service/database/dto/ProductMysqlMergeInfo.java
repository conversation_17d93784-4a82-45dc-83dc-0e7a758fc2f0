package com.qpp.cgp.service.database.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR> Lee 2019/4/3 11:20
 */
@Getter
@Setter
public class ProductMysqlMergeInfo {

    @MysqlMergeInfo(table = "cgp_product",idName = "product_id")
    private Set<Long> productIds = new HashSet<>();

    @MysqlMergeInfo(table = "cgp_product_attribute_value",idName = "pav_id")
    private Set<Long> productAttributeValueIds = new HashSet<>();

    @MysqlMergeInfo(table = "cgp_product_template",idName = "pt_id")
    private Set<Long> productTemplateIds = new HashSet<>();

    @MysqlMergeInfo(table = "cgp_product_category",idName = "category_id")
    private Set<Long> categoryIds = new HashSet<>();

    @MysqlMergeInfo(table = "cgp_product_category_template",idName = "pct_id")
    private Set<Long> categoryTemplateIds = new HashSet<>();

    @MysqlMergeInfo(table = "cgp_product_media",idName = "media_id")
    private Set<Long> productMediaIds = new HashSet<>();

    @MysqlMergeInfo(table = "cgp_builder_config",idName = "bc_id")
    private Set<Long> productConfigIds = new HashSet<>();

    @MysqlMergeInfo(table = "cgp_builder_config_bom",idName = "bcb_id")
    private Set<Long> productConfigBomIds = new HashSet<>();

    @MysqlMergeInfo(table = "cgp_builder_config_imposition",idName = "bci_id")
    private Set<Long> productConfigImpositionIds = new HashSet<>();

    @MysqlMergeInfo(table = "cgp_builder_config_imposition_compatibility_to_bom",idName = "id")
    private Set<Long> productConfigImpositionCompatibilityToBomIds = new HashSet<>();

    @MysqlMergeInfo(table = "cgp_builder_config_design",idName = "bcd_id")
    private Set<Long> productConfigDesignIds = new HashSet<>();

    @MysqlMergeInfo(table = "cgp_builder_config_design_compatibility_to_bom",idName = "id")
    private Set<Long> productConfigDesignCompatibilityToBomIds = new HashSet<>();

    @MysqlMergeInfo(table = "cgp_builder_config_view",idName = "bcv_id")
    private Set<Long> productConfigViewIds = new HashSet<>();

    @MysqlMergeInfo(table = "cgp_builder_config_view_compatibility_to_bom",idName = "id")
    private Set<Long> productConfigViewCompatibilityToBomIds = new HashSet<>();

    @MysqlMergeInfo(table = "cgp_builder_config_view_compatibility_to_view",idName = "id")
    private Set<Long> productConfigViewCompatibilityToViewIds = new HashSet<>();

    @MysqlMergeInfo(table = "cgp_attribute",idName = "attr_id")
    private Set<Long> attributeIds = new HashSet<>();

    @MysqlMergeInfo(table = "cgp_attribute_option",idName = "ao_id")
    private Set<Long> attributeOptionIds = new HashSet<>();

    @MysqlMergeInfo(table = "cgp_configurable_product_sku_attribute",idName = "cpsa_id")
    private Set<Long> configurableProductSkuAttributeIds = new HashSet<>();

    @MysqlMergeInfo(table = "cgp_main_product_category_attribute",idName = "mpca_id")
    private Set<Long> mainProductCategoryAttributeIds = new HashSet<>();

    @MysqlMergeInfo(table = "cgp_product_to_sub_category",idName = "id")
    private Set<Long> productToSubCategoryIds = new HashSet<>();

}
