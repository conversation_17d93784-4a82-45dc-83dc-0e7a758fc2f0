package com.qpp.cgp.service.database.mongo;

import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.ReplaceOptions;
import com.qpp.cgp.config.mongo.MongoTemplateBeanNames;
import com.qpp.cgp.config.mongo.MongoTemplateFactory;
import com.qpp.cgp.domain.BaseSyncProgress;
import com.qpp.cgp.domain.SyncProgressStatus;
import com.qpp.cgp.domain.product.sync.ProductSyncProgress;
import com.qpp.cgp.domain.product.sync.ProjectDeployEnv;
import com.qpp.cgp.repository.product.ProductSyncProgressRepository;
import com.qpp.cgp.service.database.DatabaseMerger;
import com.qpp.mongo.domain.MongoDomain;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Set;

/**
 * Created by smart on 9/29/2017.
 */
@Component
public class MongoMerger implements DatabaseMerger<MongoConfig, MongoMergeQuery> {


    private final String idProperty = "_id";

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    @Qualifier(MongoTemplateBeanNames.CONFIG)
    private MongoTemplate configMongoTemplate;

    @Autowired
    @Qualifier(MongoTemplateBeanNames.RUNTIME)
    private MongoTemplate runtimeMongoTemplate;

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private MongoTemplateFactory mongoTemplateFactory;

    @Autowired
    private MongoDatabaseNameFactory mongoDatabaseNameFactory;

    @Autowired
    private ProductSyncProgressRepository productSyncProgressRepository;

    @Deprecated
    @Override
    public void mergeData(MongoConfig targetConfig, MongoMergeQuery query) {

        MongoDatabase targetMongoDatabase = MongoDatabaseFactory.getMongoDatabase(targetConfig);

        final MongoDatabase sourceMongoDatabase = mongoTemplate.getDb();

        query.getCollectionQueries().stream()
                .forEach(collectionQuery -> {
                    this.mergeCollection(sourceMongoDatabase, targetMongoDatabase, collectionQuery);
                });


    }

    public void mergeData(MongoMergeQuery query, ProjectDeployEnv env) {

        query.getCollectionQueries().stream()
                .forEach(collectionQuery -> {
                    String collectionName = collectionQuery.getCollectionName();
                    MongoDatabase sourceMongoDatabase = mongoTemplate.getDb();
                    String targetDatabaseName = "default";
                    if (!mongoTemplate.collectionExists(collectionName)) {
                        sourceMongoDatabase = configMongoTemplate.getDb();
                        targetDatabaseName = "config";
                        if (!configMongoTemplate.collectionExists(collectionName)) {
                            sourceMongoDatabase = runtimeMongoTemplate.getDb();
                            targetDatabaseName = "runtime";
                            if (!runtimeMongoTemplate.collectionExists(collectionName)) {
                                throw new RuntimeException(String.format("该mongo集合在源3个数据库中均不存在, collectionName : %s", collectionName));
                            }
                        }
                    }
                    MongoDatabase targetMongoDatabase = MongoDatabaseFactory.getMongoDatabase(env, targetDatabaseName);
                    this.mergeCollection(sourceMongoDatabase, targetMongoDatabase, collectionQuery);
                });


    }

    public void mergeData(MongoMergeQuery query, ProjectDeployEnv env, String userName, String password) {

        query.getCollectionQueries().stream()
                .forEach(collectionQuery -> {
                    String collectionName = collectionQuery.getCollectionName();
                    MongoDatabase sourceMongoDatabase = mongoTemplate.getDb();
                    String targetDatabaseName = "default";
                    if (!mongoTemplate.collectionExists(collectionName)) {
                        sourceMongoDatabase = configMongoTemplate.getDb();
                        targetDatabaseName = "config";
                        if (!configMongoTemplate.collectionExists(collectionName)) {
                            sourceMongoDatabase = runtimeMongoTemplate.getDb();
                            targetDatabaseName = "runtime";
                            if (!runtimeMongoTemplate.collectionExists(collectionName)) {
                                throw new RuntimeException(String.format("该mongo集合在源3个数据库中均不存在, collectionName : %s", collectionName));
                            }
                        }
                    }
                    MongoDatabase targetMongoDatabase = MongoDatabaseFactory.getMongoDatabase(env, targetDatabaseName, userName, password);
                    this.mergeCollection(sourceMongoDatabase, targetMongoDatabase, collectionQuery);
                });


    }

    public void mergeData(MongoIncreaseMergeQuery query, ProjectDeployEnv env, String progressId) {

        query.getCollectionQueries()
                .forEach(collectionQuery -> {
                    String collectionName = collectionQuery.getCollectionName();
                    MongoTemplate sourceMongoTemplate = mongoTemplateFactory.getMongoTemplate(collectionName);
                    MongoDatabase sourceMongoDatabase = sourceMongoTemplate.getDb();

                    String targetDatabaseName = mongoDatabaseNameFactory.getMongoDatabaseName(sourceMongoTemplate);
                    MongoDatabase targetMongoDatabase = MongoDatabaseFactory.getMongoDatabase(env, targetDatabaseName);

                    this.mergeCollection(sourceMongoDatabase, targetMongoDatabase, collectionQuery);

                    updateProgress(progressId, collectionName);
                });

    }

    public void mergeData(MongoMergeQuery query, ProjectDeployEnv env, String progressId, Class<? extends BaseSyncProgress> entityType) {

        query.getCollectionQueries()
                .forEach(collectionQuery -> {
                    String collectionName = collectionQuery.getCollectionName();

                    MongoTemplate sourceMongoTemplate = mongoTemplateFactory.getMongoTemplate(collectionName);
                    MongoDatabase sourceMongoDatabase = sourceMongoTemplate.getDb();
                    String targetDatabaseName = mongoDatabaseNameFactory.getMongoDatabaseName(sourceMongoTemplate);
                    MongoDatabase targetMongoDatabase = MongoDatabaseFactory.getMongoDatabase(env, targetDatabaseName);

                    this.mergeCollection(sourceMongoDatabase, targetMongoDatabase, collectionQuery);

                    updateProgress(progressId, collectionName, entityType);
                });

    }

    private void updateProgress(String progressId, String collectionName) {
        ProductSyncProgress progress = productSyncProgressRepository.findById(progressId);
        progress.setStatus(ProductSyncProgress.ProductSyncProgressStatus.syncing);
        progress.getCollectionNames().add(collectionName);
        productSyncProgressRepository.getMongoTemplate().save(progress);
    }

    private void updateProgress(String progressId, String collectionName, Class<? extends BaseSyncProgress> entityType) {
        BaseSyncProgress progress = mongoTemplate.findById(progressId, entityType);
        progress.setStatus(SyncProgressStatus.syncing);
        progress.getCollectionNames().add(collectionName);
        mongoTemplate.save(progress);
    }

    /**
     * 合并单个Collection的数据
     *
     * @param sourceMongoDatabase
     * @param targetMongoDatabase
     * @param collectionQuery
     */
    private void mergeCollection(MongoDatabase sourceMongoDatabase, MongoDatabase targetMongoDatabase, CollectionQuery collectionQuery) {

        String collectionName = collectionQuery.getCollectionName();

        FindIterable<Document> documents = this.findDocuments(sourceMongoDatabase, collectionQuery);
        this.mergeDocuments(targetMongoDatabase, documents, collectionName);

    }

    private void mergeCollection(MongoDatabase sourceMongoDatabase, MongoDatabase targetMongoDatabase, CollectionIncreaseQuery collectionQuery) {

        String collectionName = collectionQuery.getCollectionName();
        Set<Object> ids = collectionQuery.getIds();

        if (!CollectionUtils.isEmpty(ids)) {
            for (Object id : ids) {
                FindIterable<Document> documents = this.findDocuments(sourceMongoDatabase, collectionName, id);
                Document first = documents.first();
                if (null != first) {
                    logger.warn("products" + "==" + first.get("_id") + ":" + first.get("clazz"));
                } else {
                    logger.warn("products == not fund data, collectionName:" + collectionName + ",id:" + id);
                }
                this.mergeDocuments(targetMongoDatabase, documents, collectionName);
            }
        }

    }


    /**
     * 根据查询条件查询出需要合并的数据s
     *
     * @param mongoDatabase
     * @param collectionQuery
     * @return
     */
    private FindIterable<Document> findDocuments(MongoDatabase mongoDatabase, CollectionQuery collectionQuery) {

        String collectionName = collectionQuery.getCollectionName();

        MongoCollection<Document> mongoCollection = mongoDatabase.getCollection(collectionName);

        return collectionQuery.getFilter().map(filter -> mongoCollection.find(Document.parse(filter))).orElse(mongoCollection.find());

    }

    private FindIterable<Document> findDocuments(MongoDatabase mongoDatabase, String collectionName, Object id) {

        Document criteriaObject = Criteria.where(MongoDomain.idProperty).is(id).getCriteriaObject();

        MongoCollection<Document> mongoCollection = mongoDatabase.getCollection(collectionName);

        return mongoCollection.find(criteriaObject);

    }


    /**
     * 合并数据
     *
     * @param mongoDatabase
     * @param documents
     * @param collectionName
     */
    private void mergeDocuments(MongoDatabase mongoDatabase, Iterable<Document> documents, String collectionName) {

        MongoCollection<Document> mongoCollection = mongoDatabase.getCollection(collectionName);

        documents.forEach(document -> this.insertOrUpdateOneDocument(mongoCollection, document));

    }

    /**
     * 更新或者创建一个Document
     *
     * @param mongoCollection
     * @param document
     */
    private void insertOrUpdateOneDocument(MongoCollection<Document> mongoCollection, Document document) {

        mongoCollection.replaceOne(new Document().append(idProperty, document.get(idProperty)), document, new ReplaceOptions().upsert(true));

    }


}
