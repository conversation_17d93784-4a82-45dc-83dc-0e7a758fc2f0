package com.qpp.cgp.repository.website;

import com.qpp.cgp.domain.WebsiteLockConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;


import java.util.List;

@Service
public class WebsiteLockConfigRepository {

    @Autowired
    private MongoTemplate mongoTemplate;

    public List<WebsiteLockConfig> findByWebsiteIdIn(List<Long> websiteIds) {
        List<WebsiteLockConfig> result =

        mongoTemplate.find(Query.query(Criteria.where("websiteId").in(websiteIds)), WebsiteLockConfig.class);

        return result;
    }

    public MongoTemplate getMongoTemplate() {
        return mongoTemplate;
    }
}
