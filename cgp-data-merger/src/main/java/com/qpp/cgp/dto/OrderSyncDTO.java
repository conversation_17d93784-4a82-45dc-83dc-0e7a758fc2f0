package com.qpp.cgp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.qpp.cgp.domain.product.sync.ProjectDeployEnv;
import lombok.Data;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * @className: OrderSyncDTO
 * @description:
 * @author: TT-Berg
 * @date: 2023/1/9
 **/
@Data
public class OrderSyncDTO {

    List<String> orderIds;

    ProjectDeployEnv targetEnv;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSz")
    Date lastSyncProductInstanceTime;

}
