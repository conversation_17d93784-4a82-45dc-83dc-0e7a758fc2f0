package com.qpp.cgp.dto;

import com.qpp.cgp.controller.sync.WebsiteSyncController;
import com.qpp.cgp.domain.WebsiteLockConfig;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 用于表示{@link WebsiteSyncController#preCheck(List) 接口的响应结果}
 */
@Getter@Setter
public class WebsiteLockInfoDTO {

    /**
     * 该次同步是否适合进行
     */
    private boolean canSync;

    /**
     * 该website的锁历史
     */
    private List<WebsiteLockConfig> lockConfigList;

}
