package com.qpp.cgp.domain;

import com.qpp.cgp.domain.product.sync.ProjectDeployEnv;
import com.qpp.mongo.domain.MongoDomain;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Transient;

import java.util.List;

@Getter@Setter
public class BaseSyncProgress extends MongoDomain {

    /** 同步目标环境 */
    private ProjectDeployEnv targetEnv;

    /** 已同步表数量，用于前端展示 */
    @Transient
    private int count;

    /** 总需同步表数量，用于前端展示 */
    @Transient
    private int total;

    /** 已同步mongo表名数组 */
    private List<String> collectionNames;

    /** 已同步mysql表名数组 */
    private List<String> tableNames;

    /** 总需同步mongo表名数组 */
    private List<String> totalCollectionNames;

    /** 总需同步mysql表名数组 */
    private List<String> totalTableNames;

    /** 本次同步实时状态 */
    private SyncProgressStatus status;

    /** 同步失败异常Message */
    private String failureDesc;

    /** 同步失败的异常堆栈 */
    private String stackTraceDesc;
}
