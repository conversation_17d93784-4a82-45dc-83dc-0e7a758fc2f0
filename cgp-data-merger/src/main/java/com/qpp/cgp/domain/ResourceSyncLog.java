package com.qpp.cgp.domain;

import com.qpp.cgp.config.domaintype.RuntimeDomain;
import com.qpp.mongo.domain.LongMongoDomain;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Set;

@RuntimeDomain
@Document(collection = "resourcesynclogs")
public class ResourceSyncLog extends LongMongoDomain {

    /**
     * 操作用户Id
     */
    private String operatorUserId;

    /**
     * 操作用户名称
     */
    private String operatorUserName;

    /**
     * 操作用户邮箱
     */
    private String operatorEmail;

    /**
     * 操作原因
     */
    private String remark;
    /**
     * 资源名
     */
    private String resourceName;

    /**
     * 资源的类型：必填
     */
    private String resourceClazz;

    /**
     * 资源的id列表
     */
    private Set<String> resourceIds;
}
