package com.qpp.cgp.domain;

import com.qpp.cgp.config.domaintype.RuntimeDomain;
import com.qpp.cgp.domain.product.sync.ProjectDeployEnv;
import com.qpp.mongo.domain.LongMongoDomain;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@RuntimeDomain
@Document("filesyncprogresslogs")
@Data
public class FileSyncProgressLog extends LongMongoDomain {

    /**
     * 需要同步的非静态文件的名称
     */
    private List<String> fileNames;

    /**
     * 静态文件
     */
    private List<StaticFile> staticFiles;

    /** 本次同步实时状态 */
    private SyncProgressStatus status;

    /** 同步失败异常Message */
    private String failureDesc;

    /**
     * source的路径
     */
    private ProjectDeployEnv sourceEnvironment;

    /**
     * 目标的环境
     */
    private ProjectDeployEnv targetEnvironment;
}
