package com.qpp.cgp.controller.sync;

import com.qpp.cgp.domain.PartnerLockConfig;
import com.qpp.cgp.domain.PartnerSyncProgress;
import com.qpp.cgp.dto.LockInfoDTO;
import com.qpp.cgp.dto.PartnerSyncDTO;
import com.qpp.cgp.manager.partner.sync.PartnerLockConfigManager;
import com.qpp.cgp.manager.partner.sync.PartnerSyncManager;
import com.qpp.cgp.manager.partner.sync.PartnerSyncProgressManager;
import com.qpp.core.utils.SecurityUtils;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "partner同步接口集")
@RequestMapping("api")
@RestController
public class PartnerSyncController {

    @Autowired
    private PartnerSyncProgressManager partnerSyncProgressManager;

    @Autowired
    private PartnerLockConfigManager partnerLockConfigManager;

    @Autowired
    private PartnerSyncManager partnerSyncManager;

    @PostMapping("partnerSync")
    public String startSync(@RequestBody PartnerSyncDTO partnerSyncDTO) {
        String result = "";

        Long userId = SecurityUtils.getLoginedInUserId();
        result = partnerSyncManager.startSync(userId, partnerSyncDTO.getTargetEnv(), partnerSyncDTO.getComment(), partnerSyncDTO.getPartnerIds());

        return result;
    }

    @GetMapping("partnerSyncPreCheck")
    public LockInfoDTO<PartnerLockConfig> preCheck(@RequestParam List<Long> partnerIds) {
        LockInfoDTO<PartnerLockConfig> result;

        result = partnerLockConfigManager.syncPreCheck(partnerIds);

        return result;
    }

    @GetMapping("partnerSyncProgresses/{id}")
    public PartnerSyncProgress retrieveProgress(@PathVariable String id) {
        PartnerSyncProgress result;

        result = partnerSyncProgressManager.findById(id);

        return result;
    }
}
