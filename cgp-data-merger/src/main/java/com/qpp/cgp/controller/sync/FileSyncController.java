package com.qpp.cgp.controller.sync;

import com.qpp.cgp.domain.FileSyncProgressLog;
import com.qpp.cgp.manager.file.FileSyncProgressLogManager;
import com.qpp.cgp.manager.file.FileSyncService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RequestMapping(value = "api/files")
@RestController
public class FileSyncController {
    @Autowired
    FileSyncService fileSyncService;
    @Autowired
    FileSyncProgressLogManager fileSyncProgressLogManager;
    /**
     * 启动文件同步
     * @param fileSyncProgressLog
     * @return
     */
    @PostMapping(value = "/sync")
    public String sync(@RequestBody FileSyncProgressLog fileSyncProgressLog) {
        return fileSyncService.startSync(fileSyncProgressLog);
    }

    @GetMapping(value = "/{logId}/progressLog")
    public FileSyncProgressLog getById(@PathVariable String logId) {
        return fileSyncProgressLogManager.findById(Long.valueOf(logId));
    }
}
