package com.qpp.cgp.controller.sync;

import com.qpp.cgp.domain.WebsiteSyncProgress;
import com.qpp.cgp.dto.WebsiteLockInfoDTO;
import com.qpp.cgp.dto.WebsiteSyncDTO;
import com.qpp.cgp.manager.website.sync.WebsiteLockConfigManager;
import com.qpp.cgp.manager.website.sync.WebsiteSyncManager;
import com.qpp.cgp.manager.website.sync.WebsiteSyncProgressManager;
import com.qpp.core.utils.SecurityUtils;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "website同步接口集")
@RequestMapping("api")
@RestController
public class WebsiteSyncController {

    @Autowired
    private WebsiteLockConfigManager websiteLockConfigManager;

    @Autowired
    private WebsiteSyncProgressManager websiteSyncProgressManager;

    @Autowired
    private WebsiteSyncManager websiteSyncManager;

    @PostMapping("websiteSync")
    public String startWebsiteSync(@RequestBody WebsiteSyncDTO websiteSyncDTO) {
        String result = "";

        Long userId = SecurityUtils.getLoginedInUserId();
        result = websiteSyncManager.startSync(userId, websiteSyncDTO.getTargetEnv(), websiteSyncDTO.getComment(), websiteSyncDTO.getWebsiteIds());

        return result;
    }

    @GetMapping("websiteSyncPreCheck")
    public WebsiteLockInfoDTO preCheck(@RequestParam List<Long> websiteIds) {
        WebsiteLockInfoDTO result;

        result = websiteLockConfigManager.syncPreCheck(websiteIds);

        return result;
    }

    @GetMapping("websiteSyncProgresses/{id}")
    public WebsiteSyncProgress retrieveProgress(@PathVariable String id) {
        WebsiteSyncProgress result;

        result = websiteSyncProgressManager.findById(id);

        return result;
    }
}
