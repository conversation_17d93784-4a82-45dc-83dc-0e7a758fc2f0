package com.qpp.cgp.controller.order.sync;

import com.google.common.collect.Lists;
import com.qpp.cgp.domain.order.ThirdOrder;
import com.qpp.cgp.dto.OrderSyncDTO;
import com.qpp.cgp.manager.order.sync.service.OrderSyncService;
import com.qpp.cgp.repository.order.OrderRepository;
import com.qpp.cgp.repository.order.ThirdOrderRepository;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * @className: OrderSyncController
 * @description:
 * @author: TT-Berg
 * @date: 2023/1/9
 **/
@Api("订单数据同步")
@RequestMapping("/api")
@RestController
public class OrderSyncController {

    @Autowired
    private OrderSyncService orderSyncService;
    @Autowired
    private ThirdOrderRepository thirdOrderRepository;

    /**
     * 订单数据同步
     *
     * @param orderSyncDTO
     * @return 订单同步记录id
     */
    @PostMapping("/orderSync")
    public String startOrderSync(@RequestBody OrderSyncDTO orderSyncDTO,
                                 @RequestParam(required = false, defaultValue = "true") boolean isId) {
        if (!isId) {
            List<String> orderIdList = thirdOrderRepository.findOrderByRefNumberAndReturnOrderId(new HashSet<>(orderSyncDTO.getOrderIds()));
            orderSyncDTO.setOrderIds(orderIdList);
        }
        return orderSyncService.startSync(orderSyncDTO.getOrderIds(), orderSyncDTO.getTargetEnv(), orderSyncDTO.getLastSyncProductInstanceTime());
    }

    @PostMapping("/orderSync/batch")
    public List<String> startOrderSyncBatch(@RequestBody OrderSyncDTO orderSyncDTO,
                                            @RequestParam(required = false, defaultValue = "true") boolean isId,
                                            @RequestParam(required = true) int preSyncNum) {
        if (!isId) {
            List<String> orderIdList = thirdOrderRepository.findOrderByRefNumberAndReturnOrderId(new HashSet<>(orderSyncDTO.getOrderIds()));
            orderSyncDTO.setOrderIds(orderIdList);
        }
        return orderSyncService.startSyncBatch(orderSyncDTO.getOrderIds(), orderSyncDTO.getTargetEnv(), orderSyncDTO.getLastSyncProductInstanceTime(), preSyncNum);
    }

}
