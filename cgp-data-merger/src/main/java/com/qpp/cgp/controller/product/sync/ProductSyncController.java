package com.qpp.cgp.controller.product.sync;

import com.google.common.collect.Lists;
import com.qpp.cgp.domain.product.sync.ProductSyncProgress;
import com.qpp.cgp.dto.OrderSyncDTO;
import com.qpp.cgp.dto.ProductInstanceSyncDTO;
import com.qpp.cgp.dto.product.sync.ProductLockInfoDTO;
import com.qpp.cgp.dto.product.sync.ProductSyncDTO;
import com.qpp.cgp.manager.product.sync.ProductLockManager;
import com.qpp.cgp.manager.product.sync.ProductSyncManager;
import com.qpp.cgp.manager.product.sync.ProductSyncProgressManager;
import com.qpp.cgp.manager.product.sync.ProductSyncUserService;
import com.qpp.cgp.manager.productInstance.sync.ProductInstanceSyncService;
import com.qpp.cgp.manager.reset.ProductDataResetService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@Api(tags = "产品同步接口集")
@RequestMapping("api")
@RestController
public class ProductSyncController {

    @Autowired
    private ProductSyncUserService productSyncUserService;

    @Autowired
    private ProductSyncManager productSyncManager;

    @Autowired
    private ProductLockManager productLockManager;

    @Autowired
    private ProductSyncProgressManager productSyncProgressManager;

    @Autowired
    private ProductInstanceSyncService productInstanceSyncService;

    @Autowired
    private ProductDataResetService productDataResetService;

    /**
     * 产品同步
     *
     * @param productSyncDTO
     * @param withSku
     * @param withValidation 是否效验产品管理员和产品锁 内部人员使用
     * @return 产品同步进度id
     */
    @PostMapping("productSync")
    public String startProductSync(@Validated @RequestBody ProductSyncDTO productSyncDTO,
                                   @RequestParam(required = false, defaultValue = "false") boolean withSku,
                                   @RequestParam(required = false, defaultValue = "true") boolean withValidation,
                                   @RequestParam(required = false, defaultValue = "true")boolean withSubProduct) {

        long userId = productSyncUserService.getUserId(productSyncDTO.getUserId(), productSyncDTO.getEmailAddress());
        return productSyncManager.startSync(userId,
                productSyncDTO.getTargetEnv(),
                productSyncDTO.getComment(),
                productSyncDTO.getProductIds(),
                withSku,
                withValidation,
                withSubProduct);
    }

    @PostMapping("productSync/batch")
    public List<String> startProductSyncBatch(@Validated @RequestBody ProductSyncDTO productSyncDTO,
                                              @RequestParam(required = false, defaultValue = "false") boolean withSku,
                                              @RequestParam(required = false, defaultValue = "true") boolean withValidation,
                                              @RequestParam(required = true) int preSyncNum,
                                              @RequestParam(required = false, defaultValue = "true")boolean withSubProduct) {

        long userId = productSyncUserService.getUserId(productSyncDTO.getUserId(), productSyncDTO.getEmailAddress());
        return productSyncManager.startSyncBatch(userId,
                productSyncDTO.getTargetEnv(),
                productSyncDTO.getComment(),
                productSyncDTO.getProductIds(),
                withSku,
                withValidation,
                preSyncNum,
                withSubProduct);
    }

    /**
     * 测试使用
     */
    @PostMapping("productInstanceSync")
    public String startProductInstanceSync(@RequestBody ProductInstanceSyncDTO productInstanceSyncDTO,
                                           @RequestParam(required = false, defaultValue = "true") Boolean syncProduct) {

        return productInstanceSyncService.startSync(productInstanceSyncDTO.getProductInstanceIds(), productInstanceSyncDTO.getDeployEnv(), syncProduct);
    }

    @PostMapping("productInstanceSync/batch")
    public List<String> startProductInstanceSyncBatch(@RequestBody ProductInstanceSyncDTO productInstanceSyncDTO,
                                                      @RequestParam(required = false, defaultValue = "true") Boolean syncProduct,
                                                      @RequestParam(required = true) int preSyncNum) {

        return productInstanceSyncService.startSyncBatch(productInstanceSyncDTO.getProductInstanceIds(), productInstanceSyncDTO.getDeployEnv(), syncProduct, preSyncNum);
    }

    /**
     * 产品同步前检查
     *
     * @param productIds
     * @return
     */
    @GetMapping("productSyncPreCheck")
    public ProductLockInfoDTO productSyncPreCheck(@RequestParam List<Long> productIds) {

        return productLockManager.productSyncPreCheck(productIds);

    }

    /**
     * 产品同步进度查询
     *
     * @param id
     * @return
     */
    @GetMapping("productSyncProgresses/{id}")
    public ProductSyncProgress getProductSyncProgress(@PathVariable String id) {

        return productSyncProgressManager.findById(id);

    }

    @DeleteMapping("/reset/removeProductData")
    public Boolean removeProductData(@RequestParam List<Long> productIds) {
        return productDataResetService.removeProductData(productIds);
    }

}
