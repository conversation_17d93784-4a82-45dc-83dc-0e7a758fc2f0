package com.qpp.cgp.manager.product.sync;

import com.qpp.cgp.domain.dto.product.ProductDTO;
import com.qpp.cgp.domain.dto.product.ProductProduceDaysTemplateDTO;
import com.qpp.cgp.domain.dto.user.UserDTO;
import com.qpp.cgp.domain.product.sync.ProductSyncProgress;
import com.qpp.cgp.manager.module.AbstractManager;
import com.qpp.cgp.manager.product.ProductManager;
import com.qpp.cgp.manager.user.UserManager;
import com.qpp.cgp.migration.AbstractCgpMongoCurdManager;
import com.qpp.cgp.migration.AbstractCgpMongoRepositoryCurdManager;
import com.qpp.cgp.repository.product.ProductSyncProgressRepository;
import com.qpp.core.dto.FilterDTO;
import com.qpp.core.dto.PageDTO;
import com.qpp.core.exception.BusinessException;
import com.qpp.core.exception.BusinessExceptionLevel;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.domain.MongoDomain;
import com.qpp.mongo.driver.HybridMongoTemplate;
import com.qpp.web.business.manager.AbstractDocumentCurdManager;
import com.qpp.web.business.manager.AbstractMongoCurdManager;
import com.qpp.web.business.manager.CurdManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ProductSyncProgressManager extends AbstractMongoCurdManager<ProductSyncProgress, String> {

    @Autowired
    private ProductSyncProgressRepository productSyncProgressRepository;
    @Autowired
    private ProductManager productManager;
    @Autowired
    private UserManager userManager;

    public ProductSyncProgressManager(HybridMongoTemplate mongoTemplate, IdGenerator idgenerator) {
        super(mongoTemplate, idgenerator);
    }

    public ProductSyncProgress findById(String id) {
        ProductSyncProgress result = new ProductSyncProgress();

        result = productSyncProgressRepository.findById(id);
        if (result == null) {
            throw new BusinessException(ProductSyncProgress.class.getSimpleName(),
                    ProductSyncProgress.class.getSimpleName() + " '" + id + "' is not exists!", BusinessExceptionLevel.DEBUG);
        }

        fillTransientFields(result);
        return result;
    }

    /**
     * 为productSyncProgress设置已同步表数量与总需同步表数量的字段值
     *
     * @param productSyncProgress
     */
    private void fillTransientFields(ProductSyncProgress productSyncProgress) {
        if (productSyncProgress != null) {
            List<String> collectionNames = productSyncProgress.getCollectionNames();
            if (collectionNames != null) {
                productSyncProgress.setCount(collectionNames.size());
            }
            List<String> totalCollectionNames = productSyncProgress.getTotalCollectionNames();
            if (totalCollectionNames != null) {
                productSyncProgress.setTotal(totalCollectionNames.size());
            }
        }
    }

    @Override
    public PageDTO<ProductSyncProgress> findAll(Pageable pageRequest, List<FilterDTO> filters) {
        PageDTO<ProductSyncProgress> all = super.findAll(pageRequest, filters);
        this.fillData(new ArrayList<>(all.getContent()));
        return all;
    }

    private void fillData(List<ProductSyncProgress> productSyncProgresses) {
        if (CollectionUtils.isEmpty(productSyncProgresses)) {
            return;
        }
        List<Long> productIds = productSyncProgresses.stream()
                .map(ProductSyncProgress::getProductIds)
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, ProductDTO> productDTOMap = productManager.findByIdsNoFill(productIds)
                .stream()
                .collect(Collectors.toMap(ProductDTO::getId, product -> product, (o1, o2) -> o1));

        Map<Long, UserDTO> userMap = productSyncProgresses.stream()
                .map(MongoDomain::getCreatedBy)
                .distinct()
                .map(id -> userManager.findById(Long.valueOf(id)))
                .collect(Collectors.toMap(UserDTO::getId, userDTO -> userDTO, (o1, o2) -> o1));


        for (ProductSyncProgress productSyncProgress : productSyncProgresses) {
            List<ProductDTO> productDTOS = productSyncProgress.getProductIds()
                    .stream()
                    .map(productDTOMap::get)
                    .collect(Collectors.toList());

            productSyncProgress.setProductDTOs(productDTOS);
            productSyncProgress.setUserDTO(userMap.get(Long.valueOf(productSyncProgress.getCreatedBy())));
        }
    }
}
