package com.qpp.cgp.manager.merger;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.InsertManyOptions;
import com.mongodb.client.model.Projections;
import com.mongodb.client.model.ReplaceOptions;
import com.qpp.cgp.domain.product.sync.ProjectDeployEnv;
import com.qpp.cgp.domain.simplifyBom.SBNode;
import com.qpp.cgp.dto.DataMergerDTO;
import com.qpp.cgp.dto.DataMergerEvent;
import com.qpp.cgp.service.database.mongo.MongoDatabaseFactory;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.apache.commons.lang.StringUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.qpp.cgp.migration.CgpMongoDomain.idProperty;

@Service
public class DataMergerService {


    @Autowired
    private DataMergerProgressLogService dataMergerProgressLogService;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private ObjectMapper objectMapper;


    /**
     * 同步mongoDB的Collection，通过异步完成，返回一个同步Id
     *
     * @param collection collection
     * @param upsert     是否采用覆盖机制，如果没有传入值的话就是不覆盖
     * @param mergerDTO  DTO
     * @return 同步记录Id
     */
    public String dataMerger(String collection, Boolean upsert, DataMergerDTO mergerDTO) {
        //查询是否存在对应的已经在同步的Collection
        boolean existsIsSyncingByCollection = dataMergerProgressLogService.existsIsSyncingByCollection(collection, mergerDTO.getTargetEnvironment());
        if (existsIsSyncingByCollection) {
            throw BusinessExceptionBuilder.of(2701312, ImmutableMap.of("message", "存在已经同步的Collection！同步记录Id：" + dataMergerProgressLogService.getSyncingByCollectionLogId(collection, mergerDTO.getTargetEnvironment())));
        }
        //创建一个ID
        String operatorLogId = dataMergerProgressLogService.generatorOperatorLogId(collection, mergerDTO.getQuery(), mergerDTO.getSourceEnvironment(), mergerDTO.getTargetEnvironment(), upsert, mergerDTO.getRemark(), mergerDTO.getIncludeFields(), mergerDTO.getExcludeFields(), mergerDTO.getUpdateField());
        //发布事件
        applicationEventPublisher.publishEvent(new DataMergerEvent(operatorLogId));
        return operatorLogId;
    }

    /**
     * 同步数据
     *
     * @param collection
     * @param upsert
     */
    public void collectionMerger(String collection, Boolean upsert, String queryJson, ProjectDeployEnv source, ProjectDeployEnv target, List<String> includeFields, List<String> excludeFields, String updateField) {

        SourceDataBase sourceDataBase = getSourceDataBase(source, collection);
        MongoDatabase sourceDatabase = sourceDataBase.getMongoDatabase();
        String dateBaseName = sourceDataBase.getDateBaseName();
        MongoDatabase targetDatabase = getTargetDataBase(target, dateBaseName, collection);
        MongoCollection<Document> sourceMongoCollection = sourceDatabase.getCollection(collection);
        MongoCollection<Document> targetMongoCollection = targetDatabase.getCollection(collection);
        List<Document> documents = getDocumentsByMongoCollection(sourceMongoCollection, queryJson);
        if (upsert == null || !upsert) {
            //剔除已经存在数据
            FindIterable<Document> projection = targetMongoCollection.find(new Document("_id", new Document("$in", documents.stream().map(e -> e.get("_id")).collect(Collectors.toSet())))).projection(Projections.include("_id"));
            Set<Object> existsIds = new HashSet<>();
            projection.forEach(e -> existsIds.add(e.get("_id")));
            documents.removeIf(e -> existsIds.contains(e.get("_id")));
            //不覆盖操作
            if (documents.isEmpty()) {
                return;
            }
            insertDocument(targetMongoCollection, documents, includeFields, excludeFields);
        } else {
            //覆盖操作
            insertAndUpdateDocument(targetMongoCollection, documents, includeFields, excludeFields, updateField);
        }
    }

    private void insertDocument(MongoCollection<Document> mongoCollection, List<Document> documents, List<String> includeFields, List<String> excludeFields) {
        mongoCollection.insertMany(documents, new InsertManyOptions().ordered(false));
    }

    private void insertAndUpdateDocument(MongoCollection<Document> mongoCollection, List<Document> documents, List<String> includeFields, List<String> excludeFields, String updateField) {
        for (Document document : documents) {
            Document updateDocument;
            if (includeFields != null && !includeFields.isEmpty()) {
                updateDocument = new Document();
                for (String includeField : includeFields) {
                    updateDocument.put(includeField, document.get(includeField));
                }

            } else if (excludeFields != null && !excludeFields.isEmpty()) {
                updateDocument = new Document();
                for (String filed : document.keySet()) {
                    if (!excludeFields.contains(filed)) {
                        updateDocument.put(filed, document.get(filed));
                    }
                }
            } else {
                updateDocument = document;
            }
            Document queryByUpdate;
            if (StringUtils.isNotBlank(updateField)) {
                Object fieldValue = getUpdateFieldValue(Arrays.stream(updateField.split("\\.")).collect(Collectors.toList()), document);
                queryByUpdate = new Document().append(updateField, fieldValue);
                mongoCollection.updateOne(queryByUpdate, new Document("$set", updateDocument));
            } else {
                queryByUpdate = new Document().append(idProperty, document.get(idProperty));
                mongoCollection.replaceOne(queryByUpdate, updateDocument, new ReplaceOptions().upsert(true));
            }
        }
    }

    public Object getUpdateFieldValue(List<String> fields, Document document) {
        if (fields == null || fields.isEmpty()) {
            return document;
        }
        String subField = fields.get(0);
        Object subDocument = document.get(subField);
        fields.remove(0);
        if (fields.isEmpty()) {
            return subDocument;
        }
        return getUpdateFieldValue(fields, (Document) subDocument);
    }

    private List<Document> getDocumentsByMongoCollection(MongoCollection<Document> mongoCollection, String queryJson) {
        List<Document> documents = new ArrayList<>();
        if (queryJson != null) {
            Criteria criteria;
            try {
                criteria = new JsonToCriteria().jsonCovertCriteria(queryJson);
            } catch (IOException e) {
                throw BusinessExceptionBuilder.of(2701312, ImmutableMap.of("message", "The query convert Document Error! message:" + e.getMessage()));
            }
            Document documentQuery = new Document();
            documentQuery.putAll(criteria.getCriteriaObject());
            FindIterable<Document> documentFindIterable = mongoCollection.find(documentQuery);
            for (Document document : documentFindIterable) {
                documents.add(document);
            }
        } else {
            FindIterable<Document> documentFindIterable = mongoCollection.find();
            for (Document document : documentFindIterable) {
                documents.add(document);
            }
        }
        return documents;
    }


    /**
     * 校验数据是否同步成功，校验逻辑，查询数量是否和Source数据库的是否一致
     *
     * @param collection     集合名称
     * @param queryJson      query查询语句
     * @param sourceDataBase 源数据库
     * @param targetDataBase 目标数据库
     * @return
     */
    public boolean checkMergerIsSuccess(String collection, String queryJson, MongoDatabase sourceDataBase, MongoDatabase targetDataBase) {
        long sourceCount = countByCollectionAndQuery(collection, queryJson, sourceDataBase);
        long targetCount = countByCollectionAndQuery(collection, queryJson, targetDataBase);
        return sourceCount == targetCount;
    }


    /**
     * 查询对应的数据记录数量
     *
     * @param collection    集合名称
     * @param queryJson     查询语句
     * @param mongoDatabase 数据库
     * @return 数量
     */
    public long countByCollectionAndQuery(String collection, String queryJson, MongoDatabase mongoDatabase) {
        MongoCollection<Document> mongoCollection = mongoDatabase.getCollection(collection);
        if (queryJson != null) {
            return mongoCollection.countDocuments(convertByMap(queryJson));
        }
        return mongoCollection.countDocuments();
    }

    private Bson convertByMap(String queryJson) {
        if (queryJson != null) {
            Criteria criteria;
            try {
                criteria = new JsonToCriteria().jsonCovertCriteria(queryJson);
            } catch (IOException e) {
                throw BusinessExceptionBuilder.of(2701312, ImmutableMap.of("message", "The Query convert Document Fail!message:" + e.getMessage()));
            }
            Document documentQuery = new Document();
            documentQuery.putAll(criteria.getCriteriaObject());
            return documentQuery;
        }
        return null;
    }

    /**
     * 通过环境和集合名称获取对应的数据信息
     *
     * @param projectDeployEnv 环境信息
     * @param collection       集合名
     * @return 数据
     */
    public MongoDatabase getDataBase(ProjectDeployEnv projectDeployEnv, String collection) {
        List<MongoDatabase> mongoDatabases = getDataBasesByEnv(projectDeployEnv);
        MongoDatabase mongoDatabase = null;
        for (MongoDatabase database : mongoDatabases) {
            for (String collectionName : database.listCollectionNames()) {
                if (collection.equals(collectionName)) {
                    mongoDatabase = database;
                    break;
                }
            }
        }
        if (mongoDatabase == null) {
            throw BusinessExceptionBuilder.of(2701312, ImmutableMap.of("message", "在数据库中没有找到对应的表名：" + collection));
        }
        return mongoDatabase;
    }

    /**
     * 通过环境和集合名称获取对应的数据信息
     *
     * @param projectDeployEnv 环境信息
     * @param collection       集合名
     * @return 数据
     */
    public SourceDataBase getSourceDataBase(ProjectDeployEnv projectDeployEnv, String collection) {
        Map<String, MongoDatabase> dataBasesMapByEnv = getDataBasesMapByEnv(projectDeployEnv);
        SourceDataBase sourceDataBase = new SourceDataBase();
        for (String dataBaseName : dataBasesMapByEnv.keySet()) {
            MongoDatabase mongoDatabase = dataBasesMapByEnv.get(dataBaseName);
            for (String collectionName : mongoDatabase.listCollectionNames()) {
                if (collection.equals(collectionName)) {
                    sourceDataBase.setDateBaseName(dataBaseName);
                    sourceDataBase.setMongoDatabase(mongoDatabase);
                    break;
                }
            }
        }
        if (sourceDataBase.getMongoDatabase() == null) {
            throw BusinessExceptionBuilder.of(2701312, ImmutableMap.of("message", "在数据库中没有找到对应的表名：" + collection));
        }
        return sourceDataBase;
    }

    /**
     * 通过环境和集合名称获取对应的数据信息
     *
     * @param projectDeployEnv 环境信息
     * @param dataBaseName     数据库名
     * @param collection       集合名
     * @return 数据
     */
    public MongoDatabase getTargetDataBase(ProjectDeployEnv projectDeployEnv, String dataBaseName, String collection) {
        MongoDatabase mongoDatabase = MongoDatabaseFactory.getMongoDatabase(projectDeployEnv, dataBaseName);
        if (mongoDatabase == null) {
            throw BusinessExceptionBuilder.of(2701312, ImmutableMap.of("message", "没有在目标数据库列表中找到对应的数据库：" + dataBaseName));
        }
        List<String> collectionNames = mongoDatabase.listCollectionNames().into(new ArrayList());
        if (!collectionNames.contains(collection)) {
            mongoDatabase.createCollection(collection);
        }
        return mongoDatabase;
    }


    /**
     * 通过目标环境获取对应的数据列表
     *
     * @param projectDeployEnv
     * @return
     */
    private List<MongoDatabase> getDataBasesByEnv(ProjectDeployEnv projectDeployEnv) {
        List<String> existsDataBaseName = new ArrayList<>();
        existsDataBaseName.add("default");
        existsDataBaseName.add("runtime");
        existsDataBaseName.add("config");
        List<MongoDatabase> mongoDatabases = new ArrayList<>();
        for (String dataBaseName : existsDataBaseName) {
            MongoDatabase mongoDatabase = MongoDatabaseFactory.getMongoDatabase(projectDeployEnv, dataBaseName);
            mongoDatabases.add(mongoDatabase);
        }
        return mongoDatabases;
    }

    /**
     * 通过目标环境获取对应的数据列表
     *
     * @param projectDeployEnv
     * @return
     */
    private Map<String, MongoDatabase> getDataBasesMapByEnv(ProjectDeployEnv projectDeployEnv) {
        List<String> existsDataBaseName = new ArrayList<>();
        existsDataBaseName.add("default");
        existsDataBaseName.add("runtime");
        existsDataBaseName.add("config");
        Map<String, MongoDatabase> mongoDatabases = new HashMap<>();
        for (String dataBaseName : existsDataBaseName) {
            MongoDatabase mongoDatabase = MongoDatabaseFactory.getMongoDatabase(projectDeployEnv, dataBaseName);
            mongoDatabases.put(dataBaseName, mongoDatabase);
        }
        return mongoDatabases;
    }

    /**
     * 同步调用
     * @param collection 集合名称
     * @param upsert 是否覆盖
     * @param mergerDTO 请求参数
     */
    public void dataMergerSyc(String collection, Boolean upsert, DataMergerDTO mergerDTO) {
        String json = null;
        if (mergerDTO.getQuery() != null) {
            try {
                json = objectMapper.writeValueAsString(mergerDTO.getQuery());
            } catch (JsonProcessingException e) {
                throw BusinessExceptionBuilder.of(2701312, ImmutableMap.of("message", "The query convert json fail！message:" + e.getMessage()));
            }
        }
        collectionMerger(collection, upsert, json, mergerDTO.getSourceEnvironment(), mergerDTO.getTargetEnvironment(), mergerDTO.getIncludeFields(), mergerDTO.getExcludeFields(), mergerDTO.getUpdateField());
    }
}
