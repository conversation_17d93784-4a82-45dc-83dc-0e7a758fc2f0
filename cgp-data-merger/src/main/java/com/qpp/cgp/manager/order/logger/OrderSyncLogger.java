package com.qpp.cgp.manager.order.logger;

import com.qpp.cgp.common.RedisForStringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @className: OrderSyncLogger
 * @description:
 * @author: TT-Berg
 * @date: 2023/1/9
 **/
public class OrderSyncLogger {

    private volatile static Logger logger;

    private static void init() {
        if (logger == null) {
            synchronized (RedisForStringUtil.class) {
                if (logger == null) {
                    logger = LoggerFactory.getLogger("copy.order");
                }
            }
        }
    }

    public static Logger getLogger() {
        if (logger == null) {
            init();
        }
        return logger;
    }

}
