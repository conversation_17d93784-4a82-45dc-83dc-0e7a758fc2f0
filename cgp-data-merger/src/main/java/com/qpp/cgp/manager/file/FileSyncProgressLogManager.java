package com.qpp.cgp.manager.file;

import com.qpp.cgp.config.mongo.MongoTemplateBeanNames;
import com.qpp.cgp.domain.FileSyncProgressLog;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.driver.HybridMongoTemplate;
import com.qpp.web.business.manager.AbstractLongMongoCurdManager;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2022/6/30 10:45
 * @Version 1.0
 */
@Service
public class FileSyncProgressLogManager extends AbstractLongMongoCurdManager<FileSyncProgressLog,Long> {

    public FileSyncProgressLogManager(@Qualifier(value = MongoTemplateBeanNames.RUNTIME) HybridMongoTemplate mongoTemplate, IdGenerator idgenerator) {
        super(mongoTemplate, idgenerator);
    }
}
