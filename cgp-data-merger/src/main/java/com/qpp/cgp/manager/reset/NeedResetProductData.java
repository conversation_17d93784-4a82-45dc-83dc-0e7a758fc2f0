package com.qpp.cgp.manager.reset;

import com.qpp.mongo.domain.MongoDomain;
import lombok.Builder;
import lombok.Data;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;

/**
 * @description:
 * @author: TT-Berg
 * @date: 2023/10/11
 **/
@Data
@Builder
public class NeedResetProductData {

    /**
     * mongo操作对象
     */
    private MongoTemplate mongoTemplate;

    /**
     * 要删除的数据
     */
    private List<Object> ids;

    /**
     * 集合名
     */
    private String collectionName;

    public Criteria getRemoveCriteria() {
        return Criteria.where(MongoDomain.idProperty).in(ids);
    }

    public Query getRemoveQuery() {
        return Query.query(this.getRemoveCriteria());
    }
}
