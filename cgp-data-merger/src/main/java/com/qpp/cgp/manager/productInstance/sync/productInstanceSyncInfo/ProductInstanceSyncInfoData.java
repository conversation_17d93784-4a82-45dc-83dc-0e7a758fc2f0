package com.qpp.cgp.manager.productInstance.sync.productInstanceSyncInfo;

import com.qpp.cgp.manager.productInstance.sync.buildCacheAnalyze.BuildCacheRuntimeSyncInfo;
import lombok.Data;

import java.util.HashSet;
import java.util.Set;

/**
 * @className: ProductInstanceSyncInfoData
 * @description:
 * @author: TT-Berg
 * @date: 2022/12/6
 **/
@Data
public class ProductInstanceSyncInfoData {

    /**
     * ProductInstance关联数据(含本身)
     */
    private ProductInstanceSyncInfo productInstanceSyncInfo;

    /**
     * BuildRuntime Cache
     */
    private BuildCacheRuntimeSyncInfo buildCacheRuntimeSyncInfo;

    /**
     * 需要同步的文件
     */
    private Set<String> fileName = new HashSet<>();
}
