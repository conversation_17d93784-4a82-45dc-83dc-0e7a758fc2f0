package com.qpp.cgp.manager.composing;

import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import com.qpp.web.core.exception.ServiceResult;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/24
 */
@Service
public class ComposingConfigRemoteService {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${composing-config-manager.url}")
    private String composingConfigManagerUrl;

    public List<Long> getPageTemplateIdsByIds(List<Long> jobConfigIds) {
        String url = composingConfigManagerUrl + "/api/jobConfigs/pageTemplateIds";

        String jobConfigIdStr = jobConfigIds.stream()
                .map(String::valueOf)
                .reduce((id1, id2) -> id1 + "," + id2)
                .orElse("");

        try {
            // http method
            HttpMethod method = HttpMethod.POST;
            // headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(ImmutableList.of(MediaType.APPLICATION_JSON));

            HttpEntity<List<Long>> httpEntity = new HttpEntity<>(jobConfigIds, headers);

            // 发送请求
            ResponseEntity<String> response
                    = restTemplate.exchange(url, method, httpEntity, String.class);

            JSONObject resp = JSONObject.fromObject(response.getBody());

            if (resp.getBoolean("success")) {
                CollectionType listType = objectMapper.getTypeFactory()
                        .constructCollectionType(List.class, Long.class);

                // 指定反序列化的类型
                JavaType responseType = objectMapper.getTypeFactory()
                        .constructParametricType(ServiceResult.class, listType);

                // 反序列化为ServiceResult类型
                ServiceResult<List<Long>> serviceResult
                        = objectMapper.readValue(response.getBody(), responseType);

                return serviceResult.getData();
            } else {
                throw BusinessExceptionBuilder.of(15400003,
                        ImmutableMap.of("jobConfigIds", jobConfigIdStr,
                                "serverUrl", url, "message", resp.get("message")));
            }

        } catch (Exception ex) {
            throw BusinessExceptionBuilder.of(15400002,
                    ImmutableMap.of("jobConfigIds", jobConfigIdStr,
                            "serverUrl", url, "message", ex.getMessage()));
        }
    }

    public List<Long> getSheetTemplateIdsByIds(List<Long> jobConfigIds) {
        String url = composingConfigManagerUrl + "/api/jobConfigs/sheetTemplateIds";

        String jobConfigIdStr = jobConfigIds.stream()
                .map(String::valueOf)
                .reduce((id1, id2) -> id1 + "," + id2)
                .orElse("");

        try {
            // http method
            HttpMethod method = HttpMethod.POST;
            // headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(ImmutableList.of(MediaType.APPLICATION_JSON));

            HttpEntity<List<Long>> httpEntity = new HttpEntity<>(jobConfigIds, headers);

            // 发送请求
            ResponseEntity<String> response
                    = restTemplate.exchange(url, method, httpEntity, String.class);

            JSONObject resp = JSONObject.fromObject(response.getBody());

            if (resp.getBoolean("success")) {
                CollectionType listType = objectMapper.getTypeFactory()
                        .constructCollectionType(List.class, Long.class);

                // 指定反序列化的类型
                JavaType responseType = objectMapper.getTypeFactory()
                        .constructParametricType(ServiceResult.class, listType);

                // 反序列化为ServiceResult类型
                ServiceResult<List<Long>> serviceResult
                        = objectMapper.readValue(response.getBody(), responseType);

                return serviceResult.getData();
            } else {
                throw BusinessExceptionBuilder.of(15600003,
                        ImmutableMap.of("jobConfigIds", jobConfigIdStr,
                                "serverUrl", url, "message", resp.get("message")));
            }

        } catch (Exception ex) {
            throw BusinessExceptionBuilder.of(15600002,
                    ImmutableMap.of("jobConfigIds", jobConfigIdStr,
                            "serverUrl", url, "message", ex.getMessage()));
        }
    }
}
