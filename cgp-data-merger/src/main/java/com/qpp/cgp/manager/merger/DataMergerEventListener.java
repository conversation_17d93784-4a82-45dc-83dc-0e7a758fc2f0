package com.qpp.cgp.manager.merger;

import com.google.common.collect.ImmutableMap;
import com.mongodb.client.MongoDatabase;
import com.qpp.cgp.domain.DataMergerProgressLog;
import com.qpp.cgp.domain.SyncProgressStatus;
import com.qpp.cgp.dto.DataMergerEvent;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Async
@Component
public class DataMergerEventListener implements ApplicationListener<DataMergerEvent> {

    @Autowired
    private DataMergerProgressLogService dataMergerProgressLogService;

    @Autowired
    private DataMergerService dataMergerService;

    @Override
    public void onApplicationEvent(DataMergerEvent event) {
        //调用同步服务，如果抛出异常则，修改对应的操作日志，改为失败状态，同时记录失败异常
        String logId = (String) event.getSource();
        DataMergerProgressLog dataMergerProgressLog = dataMergerProgressLogService.findById(logId);
        if (dataMergerProgressLog == null) {
            throw BusinessExceptionBuilder.of(2701312, ImmutableMap.of("message", "没有找到对应的操作记录Id:" + logId));
        }
        //调用同步服务
        try {
            dataMergerService.collectionMerger(dataMergerProgressLog.getCollection(), dataMergerProgressLog.getUpsert(), dataMergerProgressLog.getQueryJson(), dataMergerProgressLog.getSourceEnvironment(), dataMergerProgressLog.getTargetEnvironment(), dataMergerProgressLog.getIncludeFields(), dataMergerProgressLog.getExcludeFields(), dataMergerProgressLog.getUpdateField());
        } catch (Exception e) {
            StackTraceElement[] stackTraces = e.getStackTrace();
            String stackTraceStr = "";
            for (StackTraceElement stackTrace : stackTraces) {
                stackTraceStr += stackTrace;
                stackTraceStr += "\n";
            }
            dataMergerProgressLog.setStackTraceDesc(stackTraceStr);
            dataMergerProgressLog.setFailureDesc(e.getMessage());
            //如果抛出异常则返回
            dataMergerProgressLog.setStatus(SyncProgressStatus.failure);
            dataMergerProgressLogService.updateOperatorLogById(logId, dataMergerProgressLog);
            return;
        }
        dataMergerProgressLog.setStatus(SyncProgressStatus.verifying);
        dataMergerProgressLogService.updateOperatorLogById(logId, dataMergerProgressLog);
        //验证是否同步数据成功
        MongoDatabase sourceDataBase = dataMergerService.getDataBase(dataMergerProgressLog.getSourceEnvironment(), dataMergerProgressLog.getCollection());
        MongoDatabase targetDataBase = dataMergerService.getDataBase(dataMergerProgressLog.getTargetEnvironment(), dataMergerProgressLog.getCollection());
        boolean isSuccess = dataMergerService.checkMergerIsSuccess(dataMergerProgressLog.getCollection(), dataMergerProgressLog.getQueryJson(), sourceDataBase, targetDataBase);
        if (isSuccess) {
            dataMergerProgressLog.setStatus(SyncProgressStatus.success);
        } else {
            dataMergerProgressLog.setStatus(SyncProgressStatus.failure);
            long sourceCount = dataMergerService.countByCollectionAndQuery(dataMergerProgressLog.getCollection(), dataMergerProgressLog.getQueryJson(), sourceDataBase);
            long targetCount = dataMergerService.countByCollectionAndQuery(dataMergerProgressLog.getCollection(), dataMergerProgressLog.getQueryJson(), targetDataBase);
            dataMergerProgressLog.setSourceCount(sourceCount);
            dataMergerProgressLog.setTargetCount(targetCount);

        }
        dataMergerProgressLogService.updateOperatorLogById(logId, dataMergerProgressLog);
    }
}
