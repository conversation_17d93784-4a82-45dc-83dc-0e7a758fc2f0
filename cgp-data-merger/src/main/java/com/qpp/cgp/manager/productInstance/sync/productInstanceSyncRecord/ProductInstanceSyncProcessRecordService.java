package com.qpp.cgp.manager.productInstance.sync.productInstanceSyncRecord;

import com.qpp.cgp.domain.product.sync.ProjectDeployEnv;
import com.qpp.id.generator.IdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @className: ProductInstanceSyncProcessRecordService
 * @description:
 * @author: TT-Berg
 * @date: 2022/12/5
 **/
@Service
public class ProductInstanceSyncProcessRecordService {

    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private IdGenerator idGenerator;

    /**
     * 查询指定正在执行或准备中的任务
     *
     * @param productInstanceIds
     * @param targetEnv
     * @return
     */
    public ProductInstanceSyncProcessRecord findExecutingTask(List<String> productInstanceIds, ProjectDeployEnv targetEnv) {
        return mongoTemplate.findOne(Query.query(
                Criteria.where("productInstanceIds").is(productInstanceIds).
                        and("targetEnv").is(targetEnv).
                        and("status").in(ProductInstanceSyncProcessRecord.ProductInstanceSyncProgressStatus.waiting,
                        ProductInstanceSyncProcessRecord.ProductInstanceSyncProgressStatus.syncing)
                ),
                ProductInstanceSyncProcessRecord.class);
    }

    /**
     * 初始化化同步记录
     *
     * @param productInstanceIds
     * @param targetEnv
     * @return
     */
    public String initTaskRecord(List<String> productInstanceIds, ProjectDeployEnv targetEnv) {
        ProductInstanceSyncProcessRecord productInstanceSyncProcessRecord = new ProductInstanceSyncProcessRecord();
        productInstanceSyncProcessRecord.setId(idGenerator.generateId().toString());
        productInstanceSyncProcessRecord.setProductInstanceIds(productInstanceIds);
        productInstanceSyncProcessRecord.setTargetEnv(targetEnv);
        productInstanceSyncProcessRecord.setStatus(ProductInstanceSyncProcessRecord.ProductInstanceSyncProgressStatus.waiting);
        ProductInstanceSyncProcessRecord processRecord = mongoTemplate.save(productInstanceSyncProcessRecord);
        return processRecord.getId();
    }

    /**
     * 保存或更新记录
     *
     * @param record
     * @return
     */
    public ProductInstanceSyncProcessRecord saveProductInstanceSyncProcessRecord(ProductInstanceSyncProcessRecord record) {
        return mongoTemplate.save(record);
    }

    /**
     * 根据id查询同步记录
     *
     * @param id
     * @return
     */
    public ProductInstanceSyncProcessRecord findRecordById(String id) {
        return mongoTemplate.findById(id, ProductInstanceSyncProcessRecord.class);
    }


    /**
     * 同步任务完成
     * @param processRecordId
     * @return
     */
    public ProductInstanceSyncProcessRecord finishSyncTask(String processRecordId) {
        ProductInstanceSyncProcessRecord processRecord = mongoTemplate.findById(processRecordId, ProductInstanceSyncProcessRecord.class);
        processRecord.setStatus(ProductInstanceSyncProcessRecord.ProductInstanceSyncProgressStatus.success);
        return mongoTemplate.save(processRecord);
    }
}
