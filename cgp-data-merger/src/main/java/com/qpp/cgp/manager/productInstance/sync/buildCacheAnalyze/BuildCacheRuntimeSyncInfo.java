package com.qpp.cgp.manager.productInstance.sync.buildCacheAnalyze;

import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * @className: BuildCacheRuntimeSyncInfo
 * @description:
 * @author: TT-Berg
 * @date: 2022/12/6
 **/
@Data
public class BuildCacheRuntimeSyncInfo {

    /**
     * key : 集合名
     * value: 对应的id集合
     */
    private Map<String, Set<Object>> syncData = new HashMap<>();

}
