package com.qpp.cgp.manager.product.sync;

import com.qpp.cgp.constant.ThreadLocalContainerKey;
import com.qpp.cgp.domain.product.sync.ProductSyncProgress;
import com.qpp.cgp.dto.product.sync.ProductSyncEvent;
import com.qpp.cgp.dto.product.sync.ProductSyncEventContext;
import com.qpp.cgp.repository.product.ProductSyncProgressRepository;
import com.qpp.cgp.service.database.service.ProductMergerV2;
import com.qpp.core.utils.context.ThreadLocalContainUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.util.HashSet;

/**
 * 针对{@link ProductSyncEvent 产品同步事件}的监听器
 */
@Async
@Component
@Slf4j
public class ProductSyncEventListener implements ApplicationListener<ProductSyncEvent> {

    @Autowired
    private ProductMergerV2 productMerger;

    @Autowired
    private ProductSyncProgressRepository productSyncProgressRepository;

    @Override
    public void onApplicationEvent(ProductSyncEvent event) {

        ProductSyncEventContext source = (ProductSyncEventContext) event.getSource();
        String progressId = source.getProgressId();
        StopWatch stopWatch = new StopWatch("syncProduct");
        stopWatch.start();
        try {
            ThreadLocalContainUtils.put(ThreadLocalContainerKey.SYNC_PRODUCT_IMAGE_FILES, new HashSet<String>());
            ThreadLocalContainUtils.put(ThreadLocalContainerKey.SYNC_PRODUCT_STATIC_IMAGE_FILES, new HashSet<String>());
            productMerger.mergeProduct(source.getProductIds(),
                    source.getTargetEnv(),
                    source.getUserId(),
                    source.getComment(),
                    progressId,
                    source.getWithValidation()
            );
        } catch (Exception e) {

            // 记录异常信息到progress中
            // > update progress status to failure
            ProductSyncProgress progress = productSyncProgressRepository.findById(progressId);
            progress.setStatus(ProductSyncProgress.ProductSyncProgressStatus.failure);
            // > set exception description
            progress.setFailureDesc(e.getMessage());
            StackTraceElement[] stackTraces = e.getStackTrace();
            String stackTraceStr = "";
            for (StackTraceElement stackTrace : stackTraces) {
                stackTraceStr += stackTrace;
                stackTraceStr += "\n";
            }
            progress.setStackTraceDesc(stackTraceStr);
            productSyncProgressRepository.getMongoTemplate().save(progress);

            // 抛出异常给sentry记录
            throw e;
        } finally {
            ThreadLocalContainUtils.remove(ThreadLocalContainerKey.SYNC_PRODUCT_IMAGE_FILES);
            ThreadLocalContainUtils.remove(ThreadLocalContainerKey.SYNC_PRODUCT_STATIC_IMAGE_FILES);
            stopWatch.stop();
            log.warn("产品同步消耗时间" + stopWatch.getTotalTimeSeconds());
            productSyncProgressRepository.fillExecuteCostTime(progressId, stopWatch.getTotalTimeSeconds());
        }
    }
}
