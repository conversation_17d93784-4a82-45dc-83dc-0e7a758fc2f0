package com.qpp.cgp.manager.database.mongo;

import com.qpp.cgp.domain.dto.user.UserDTO;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.convert.ReadingConverter;

/**
 * <AUTHOR>
 * @date 2019/2/14
 */
@ReadingConverter
public class UserDTOConverter implements Converter<Long, UserDTO> {

    @Override
    public UserDTO convert(Long source) {
        UserDTO userDTO = new UserDTO();
        userDTO.setId(source);

        return userDTO;
    }
}
