package com.qpp.cgp.manager.productInstance.sync.buildCacheAnalyze;

import com.google.common.collect.Sets;
import org.apache.commons.lang.StringUtils;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @className: BuilderCacheRuntimeAnalyze
 * @description:
 * @author: TT-Berg
 * @date: 2022/12/5
 **/
@Component
public class BuilderCacheRuntimeAnalyze {

    private final Logger logger = LoggerFactory.getLogger("copy.productInstance");

    public Map<String, Set<Object>> generateSyncData(Document document) {
        //1.解析Document对象，生成 id:全限定类名的映射关系
        Map<Object, String> map = analyzeDocument(document);
        //2.根据 id:全限定类名的映射关系生成 集合名:ids的映射关系
        Map<String, Set<Object>> result = generateRuntimeSyncInfo(map);
        return result;
    }

    /**
     * 解析document对象生产(id:全限定类名)的映射关系
     *
     * @param document
     * @return
     */
    public Map<Object, String> analyzeDocument(Document document) {
        if (null == document || document.isEmpty()) {
            return null;
        }
        Map<Object, String> result = new ConcurrentHashMap<>();
        //最外层查找
        findData(document, result);
        for (Object value : document.values()) {
            //只处理list和map
            if (value instanceof List) {
                decodeList((ArrayList) value, result);
            }
            if (value instanceof Document) {
                decodeDocument((Document) value, result);
            }
        }
        return result;
    }

    /**
     * 将(id:全限定类名)的映射关系转换成(集合名:ids)的映射关系
     *
     * @param map
     * @return
     */
    public Map<String, Set<Object>> generateRuntimeSyncInfo(Map<Object, String> map) {
        if (CollectionUtils.isEmpty(map)) {
            return null;
        }
        //全限定类名:ids
        Map<String, Set<Object>> temp = new HashMap<>();
        //集合:ids
        Map<String, Set<Object>> result = new HashMap<>();
        map.forEach((k, v) -> {
            if (temp.get(v) == null) {
                temp.put(v, Sets.newHashSet(k));
            } else {
                temp.get(v).add(k);
            }
        });
        if (CollectionUtils.isEmpty(temp)) {
            return null;
        }
        temp.forEach((k, v) -> {
            String collectionName = getCollectionNameByFullClassName(k);
            if (StringUtils.isNotBlank(collectionName)) {
                result.put(collectionName, v);
            }
        });
        return result;
    }

    /**
     * 根据全限定类名获取响应集合名
     *
     * @param fullClassName
     * @return
     */
    public String getCollectionNameByFullClassName(String fullClassName) {
        if (StringUtils.isBlank(fullClassName)) {
            return null;
        }
        Class<?> clazz = null;
        String collectionName = null;
        try {
            clazz = Class.forName(fullClassName);
        } catch (ClassNotFoundException e) {
            logger.error("根据全限定类名:{},未找到相应类", fullClassName);
        }
        if (clazz != null) {
            //@Document中标志了@Inherited注解，不需要特别处理@Document注解在父类上的情况
            org.springframework.data.mongodb.core.mapping.Document documentAnnotation = clazz.getAnnotation(org.springframework.data.mongodb.core.mapping.Document.class);
            if (documentAnnotation != null) {
                String collection = documentAnnotation.collection();
                if (StringUtils.isBlank(collection)) {
                    String value = documentAnnotation.value(); //处理别名问题
                    if (StringUtils.isNotBlank(value)) {
                        collectionName = value;
                    }
                } else {
                    collectionName = collection;
                }
            } else {
                logger.warn("根据全限定类名:{},在该类上未找到相应Document注解", fullClassName);
            }
        }
        return collectionName;
    }

    /**
     * 解析Map
     *
     * @param document
     * @param result
     */
    public void decodeDocument(Document document, Map<Object, String> result) {
        if (document == null || document.isEmpty()) {
            return;
        }
        findData(document, result);
        for (Object value : document.values()) {
            if (value instanceof List) {
                decodeList((ArrayList) value, result);
            }
            if (value instanceof Document) {
                findData((Document) value, result);
                decodeDocument((Document) value, result);
            }
        }
    }

    /**
     * 解析List
     *
     * @param list
     * @param result
     */
    public void decodeList(ArrayList<Object> list, Map<Object, String> result) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (Object value : list) {
            if (value instanceof List) {
                decodeList((ArrayList) value, result);
            }
            if (value instanceof Document) {
                decodeDocument((Document) value, result);
            }
        }
    }

    /**
     * 查询该document对象是否同时存在id(_id)和clazz字段,有则填充进result中
     *
     * @param document
     * @param result
     */
    public void findData(Document document, Map<Object, String> result) {
        if ((document.containsKey("_id") || document.containsKey("id")) && document.containsKey("clazz")) {
            Object id1 = document.get("_id");
            Object id2 = document.get("id");
            Object id = handleIdValue(id1, id2);
            Object clazz = document.get("clazz");
            if (id != null && clazz != null) {
                result.put(id, (String) clazz);
            }
        }
    }

    /**
     * 查询该map对象是否同时存在id(_id)和clazz字段,有则填充进result中
     *
     * @param params
     * @param result
     */
    public void findData(Map<String, Object> params, Map<Object, String> result) {
        if ((params.containsKey("_id") || params.containsKey("id")) && params.containsKey("clazz")) {
            Object id1 = params.get("_id");
            Object id2 = params.get("id");
            Object id = handleIdValue(id1, id2);
            Object clazz = params.get("clazz");
            if (id != null && clazz != null) {
                result.put(id, (String) clazz);
            }
        }
    }

    public Object handleIdValue(Object id1, Object id2) {
        if (notNullIdValue(id1)) {
            return id1;
        }
        if (notNullIdValue(id2)) {
            return id2;
        }
        return null;
    }

    public boolean notNullIdValue(Object id) {
        //id类型只有Long和String
        if (id instanceof String) {
            return StringUtils.isNotBlank((String) id);
        }
        if (id instanceof Long) {
            return id != null;
        }
        return false;
    }

}
