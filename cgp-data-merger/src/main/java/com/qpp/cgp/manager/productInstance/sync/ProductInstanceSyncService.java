package com.qpp.cgp.manager.productInstance.sync;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.qpp.cgp.config.mongo.MongoTemplateFactory;
import com.qpp.cgp.domain.bom.runtime.ProductInstance;
import com.qpp.cgp.domain.product.Product;
import com.qpp.cgp.domain.product.sync.ProductSyncProgress;
import com.qpp.cgp.domain.product.sync.ProjectDeployEnv;
import com.qpp.cgp.manager.bom.ProductInstanceManager;
import com.qpp.cgp.manager.product.sync.ProductSyncManager;
import com.qpp.cgp.manager.productInstance.sync.buildCacheAnalyze.BuildCacheRuntimeQueryGenerator;
import com.qpp.cgp.manager.productInstance.sync.productInstanceSyncInfo.ProductInstanceSyncInfo;
import com.qpp.cgp.manager.productInstance.sync.productInstanceSyncInfo.ProductInstanceSyncInfoData;
import com.qpp.cgp.manager.productInstance.sync.productInstanceSyncInfo.ProductInstanceSyncInfoGenerator;
import com.qpp.cgp.manager.productInstance.sync.productInstanceSyncRecord.ProductInstanceSyncProcessRecord;
import com.qpp.cgp.manager.productInstance.sync.productInstanceSyncRecord.ProductInstanceSyncProcessRecordService;
import com.qpp.cgp.repository.product.ProductSyncProgressRepository;
import com.qpp.cgp.service.database.mongo.MongoDatabaseFactory;
import com.qpp.cgp.service.database.mongo.MongoDatabaseNameFactory;
import com.qpp.cgp.service.database.mongo.MongoIncreaseMergeQuery;
import com.qpp.cgp.service.database.mongo.MongoMergeQuery;
import com.qpp.cgp.service.database.service.MongoQueryGenerator;
import com.qpp.cgp.util.FunctionUtil;
import com.qpp.core.exception.BusinessException;
import com.qpp.core.utils.SecurityUtils;
import org.apache.commons.lang.StringUtils;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @className: ProductInstanceSyncService
 * @description:
 * @author: TT-Berg
 * @date: 2022/12/5
 **/
@Service
public class ProductInstanceSyncService {

    @Autowired
    private ProductInstanceSyncProcessRecordService productInstanceSyncProcessRecordService;
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    @Autowired
    private MongoQueryGenerator mongoQueryGenerator;
    @Autowired
    private ProductInstanceSyncInfoGenerator productInstanceSyncInfoGenerator;
    @Autowired
    private ProductInstanceSyncComponent productInstanceSyncComponent;
    @Autowired
    private BuildCacheRuntimeQueryGenerator buildCacheRuntimeQueryGenerator;
    @Autowired
    private ProductInstanceFileSyncService productInstanceFileSyncService;
    @Autowired
    private ProductSyncManager productSyncManager;
    @Autowired
    private ProductInstanceManager productInstanceManager;
    @Autowired
    private MongoTemplateFactory mongoTemplateFactory;
    @Autowired
    private MongoDatabaseNameFactory mongoDatabaseNameFactory;
    @Autowired
    private ProductSyncProgressRepository productSyncProgressRepository;

    /**
     * productInstance同步入口
     *
     * @param productInstanceIds 需要同步的产品实例ids
     * @param targetEnv          同步到此环境
     */
    public String startSync(List<String> productInstanceIds, ProjectDeployEnv targetEnv, boolean syncProduct) {
        //1.入参效验
        if (CollectionUtils.isEmpty(productInstanceIds) || null == targetEnv) {
            return null;
        }
        //2.幂等校验（是否有相同的同步任务已经准备或正在执行）
        hasTaskExecute(productInstanceIds, targetEnv);

        //3.初始化同步记录
        String processId = initProcessRecord(productInstanceIds, targetEnv);

        //4.发布事件（异步处理）
        publishSyncEvent(productInstanceIds, targetEnv, processId, syncProduct);

        return processId;
    }

    public List<String> startSyncBatch(List<String> productInstanceIds, ProjectDeployEnv targetEnv, boolean syncProduct, int preSyncNum) {
        List<String> records = new ArrayList<>();

        //1.入参效验
        if (CollectionUtils.isEmpty(productInstanceIds) || null == targetEnv) {
            return null;
        }

        List<List<String>> partition = Lists.partition(productInstanceIds, preSyncNum);

        partition.forEach(item -> {
            //2.幂等校验（是否有相同的同步任务已经准备或正在执行）
            hasTaskExecute(item, targetEnv);

            //3.初始化同步记录
            String processId = initProcessRecord(item, targetEnv);

            //4.发布事件（异步处理）
            publishSyncEvent(item, targetEnv, processId, syncProduct);

            records.add(processId);
        });

        return records;
    }


    /**
     * 同步任务事件发布
     *
     * @param productInstanceIds 产品实例ids
     * @param targetEnv          目标环境
     * @param processId          同步记录id
     */
    private void publishSyncEvent(List<String> productInstanceIds, ProjectDeployEnv targetEnv, String processId, boolean syncProduct) {
        ProductInstanceSyncContext context = new ProductInstanceSyncContext();
        context.setProductInstanceIds(productInstanceIds);
        context.setTargetEnv(targetEnv);
        context.setProcessRecordId(processId);
        context.setSyncProduct(syncProduct);
        ProductInstanceSyncEvent syncEvent = new ProductInstanceSyncEvent(context);
        applicationEventPublisher.publishEvent(syncEvent);
    }

    /**
     * 初始化同步记录
     *
     * @param productInstanceIds
     * @param targetEnv
     * @return
     */
    private String initProcessRecord(List<String> productInstanceIds, ProjectDeployEnv targetEnv) {
        String processRecordId = productInstanceSyncProcessRecordService.initTaskRecord(productInstanceIds, targetEnv);
        return processRecordId;
    }

    /**
     * 查询记录表，以productInstanceIds和targetEnv为条件，如有对应数据且状态为waiting,syncing，则该任务正在执行中
     *
     * @param productInstanceIds
     * @param targetEnv
     */
    private void hasTaskExecute(List<String> productInstanceIds, ProjectDeployEnv targetEnv) {
        ProductInstanceSyncProcessRecord record = productInstanceSyncProcessRecordService.findExecutingTask(productInstanceIds, targetEnv);
        if (null != record) {
            FunctionUtil.doThrow().throwException(200317, ImmutableMap.of("message", "该同步过程正在进行, progressId : " + record.getId()));
        }
    }

    /**
     * 同步
     *
     * @param context 上下文
     */
    public void syncing(ProductInstanceSyncContext context) {
        List<String> productInstanceIds = context.getProductInstanceIds();
        ProjectDeployEnv targetEnv = context.getTargetEnv();
        String processRecordId = context.getProcessRecordId();
        Boolean syncProduct = context.getSyncProduct();

        //同步产品
        List<ProductInstance> productInstances = productInstanceManager.findByIds(productInstanceIds);
        if (CollectionUtils.isEmpty(productInstanceIds)) {
            throw new BusinessException("要同步的productInstance ids在该环境中不存在");
        }
        if (syncProduct) {
            List<Long> productIds = productInstances.stream().
                    filter(Objects::nonNull).
                    map(ProductInstance::getProductId).
                    filter(Objects::nonNull).
                    collect(Collectors.toList());
            List<Long> needCopyProducts = filterExistProduct(targetEnv, productIds);
            syncProduct(needCopyProducts, targetEnv, processRecordId);
        }
        //同步产品实例
        //1.生产需要同步的数据（id数组）
        ProductInstanceSyncInfoData syncInfo = productInstanceSyncInfoGenerator.generateSyncData(productInstances);
        ProductInstanceSyncInfo productInstanceSyncInfo = syncInfo.getProductInstanceSyncInfo();
        //2.生产mongoQuery对象（集合名，条件）
        MongoIncreaseMergeQuery syncQuery = mongoQueryGenerator.generateQueryByIncrease(productInstanceSyncInfo);
        //更新同步记录的总需同步表名
        updateTotalSyncCollections(processRecordId, syncQuery);
        //3.同步数据
        productInstanceSyncComponent.syncData(syncQuery, targetEnv, processRecordId);
        //4.同步文件
        Set<String> fileName = syncInfo.getFileName();
        syncFile(fileName, targetEnv);
        //5.完成
        productInstanceSyncProcessRecordService.finishSyncTask(processRecordId);
    }

    private void syncFile(Set<String> fileName, ProjectDeployEnv targetEnv) {
        if (!CollectionUtils.isEmpty(fileName)) {
            Set<String> fileUrls = fileName.stream()
                    .filter(StringUtils::isNotBlank)
                    .map(item -> {
                        String originFileServer = productInstanceFileSyncService.getFileService(ProjectDeployEnv.current);
                        return originFileServer + "file/" + item;
                    })
                    .collect(Collectors.toSet());
            productInstanceFileSyncService.syncFile(fileUrls, targetEnv);
        }
    }

    /**
     * 过滤目标数据库已经存在的产品
     *
     * @param targetEnv
     * @param productIds
     * @return
     */
    public List<Long> filterExistProduct(ProjectDeployEnv targetEnv, List<Long> productIds) {
        List<Long> needCopyProducts = new ArrayList<>();
        if (CollectionUtils.isEmpty(productIds)) {
            return needCopyProducts;
        }
        //源
        MongoTemplate sourceMongoTemplate = mongoTemplateFactory.getMongoTemplate(Product.class);
        String targetDatabaseName = mongoDatabaseNameFactory.getMongoDatabaseName(sourceMongoTemplate);
        MongoDatabase targetMongoDatabase = MongoDatabaseFactory.getMongoDatabase(targetEnv, targetDatabaseName);
        MongoCollection<Document> collection = targetMongoDatabase.getCollection("products");
        for (Long productId : productIds) {
            Document filter = Criteria.where("_id").is(productId).getCriteriaObject();
            FindIterable<Document> documents = collection.find(filter);
            if (null == documents.first()) {
                needCopyProducts.add(productId);
            }
        }
        return needCopyProducts;
    }


    /**
     * 同步产品
     *
     * @param productIds
     * @param targetEnv
     * @return
     */
    public void syncProduct(List<Long> productIds, ProjectDeployEnv targetEnv, String processRecordId) {
        if (CollectionUtils.isEmpty(productIds)) {
            return;
        }
        long userId = SecurityUtils.getLoginedInUserId();
        String comment = "sync productInstance";
        String processId = productSyncManager.startSync(userId, targetEnv, comment, productIds, false, false, true);

        //更新记录
        ProductInstanceSyncProcessRecord syncProcessRecord = productInstanceSyncProcessRecordService.findRecordById(processRecordId);
        syncProcessRecord.setSyncProductProcessRecordId(processId);
        productInstanceSyncProcessRecordService.saveProductInstanceSyncProcessRecord(syncProcessRecord);
    }

    /**
     * 同步记录的总需同步表名
     *
     * @param processRecordId
     * @param syncQuery
     */
    private void updateTotalSyncCollections(String processRecordId, MongoIncreaseMergeQuery syncQuery) {
        List<String> totalSyncCollections = syncQuery.getCollectionQueries().stream()
                .filter(Objects::nonNull)
                .map(query -> query.getCollectionName())
                .collect(Collectors.toList());
        ProductInstanceSyncProcessRecord syncProcessRecord = productInstanceSyncProcessRecordService.findRecordById(processRecordId);
        syncProcessRecord.setTotalCollectionNames(totalSyncCollections);
        //初始化(方便后面的逻辑处理)
        syncProcessRecord.setCompleteCollectionNames(new ArrayList<>());
        syncProcessRecord.setUnCompleteCollectionNames(new ArrayList<>());
        productInstanceSyncProcessRecordService.saveProductInstanceSyncProcessRecord(syncProcessRecord);
    }

    /**
     * 同步
     *
     * @param productInstanceIds 产品实例Ids
     * @param targetEnv          目标环境
     * @param processRecordId    同步记录
     */
    public void syncing(List<String> productInstanceIds, ProjectDeployEnv targetEnv, String processRecordId) {
        ProductInstanceSyncContext context = new ProductInstanceSyncContext();
        context.setProductInstanceIds(productInstanceIds);
        context.setTargetEnv(targetEnv);
        context.setProcessRecordId(processRecordId);
        this.syncing(context);
    }
}
