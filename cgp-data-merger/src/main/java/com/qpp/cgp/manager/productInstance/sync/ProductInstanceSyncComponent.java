package com.qpp.cgp.manager.productInstance.sync;

import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.ReplaceOptions;
import com.qpp.cgp.config.mongo.MongoTemplateFactory;
import com.qpp.cgp.domain.product.sync.ProjectDeployEnv;
import com.qpp.cgp.manager.productInstance.sync.productInstanceSyncRecord.ProductInstanceSyncProcessRecord;
import com.qpp.cgp.manager.productInstance.sync.productInstanceSyncRecord.ProductInstanceSyncProcessRecordService;
import com.qpp.cgp.service.database.mongo.*;
import com.qpp.mongo.domain.MongoDomain;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Set;

/**
 * @className: ProductInstanceMergeCompoent
 * @description:
 * @author: TT-Berg
 * @date: 2022/12/5
 **/
@Component
public class ProductInstanceSyncComponent {

    private final Logger logger = LoggerFactory.getLogger("copy.productInstance");

    @Autowired
    private MongoTemplateFactory mongoTemplateFactory;
    @Autowired
    private MongoDatabaseNameFactory mongoDatabaseNameFactory;
    @Autowired
    private ProductInstanceSyncProcessRecordService productInstanceSyncProcessRecordService;

    /**
     * 同步数据
     *
     * @param query
     * @param targetEnv
     * @param processRecordId
     */
    public void syncData(MongoIncreaseMergeQuery query, ProjectDeployEnv targetEnv, String processRecordId) {
        for (CollectionIncreaseQuery collectionQuery : query.getCollectionQueries()) {
            //集合名
            String collectionName = collectionQuery.getCollectionName();
            //源
            MongoTemplate sourceMongoTemplate = mongoTemplateFactory.getMongoTemplate(collectionName);
            MongoDatabase sourceMongoDatabase = sourceMongoTemplate.getDb();
            //目标
            String targetDatabaseName = mongoDatabaseNameFactory.getMongoDatabaseName(sourceMongoTemplate);
            MongoDatabase targetMongoDatabase = MongoDatabaseFactory.getMongoDatabase(targetEnv, targetDatabaseName);
            Set<Object> ids = collectionQuery.getIds();
            if (!CollectionUtils.isEmpty(ids)) {
                for (Object id : ids) {
                    //获取源环境需要同步的数据
                    FindIterable<Document> documents = this.getDocuments(sourceMongoDatabase, collectionName, id);
                    Document first = documents.first();
                    if (null != first) {
                        logger.warn("productInstances" + "==" + first.get("_id") + ":" + first.get("clazz"));
                    } else {
                        logger.warn("productInstances == not fund data, collectionName:" + collectionName + ",id:" + id);
                    }
                    //同步数据至目标环境
                    this.mergeDocuments(targetMongoDatabase, documents, collectionName);
                }
            }
            //更新已经完成的同步表
            updateCompleteSyncCollection(processRecordId, collectionName);
        }
    }


    /**
     * 根据查询条件查询出需要合并的数据
     *
     * @param sourceMongoDatabase
     * @param collectionQuery
     * @return
     */
    private FindIterable<Document> getDocuments(MongoDatabase sourceMongoDatabase, CollectionQuery collectionQuery) {
        String collectionName = collectionQuery.getCollectionName();
        MongoCollection<Document> sourceCollection = sourceMongoDatabase.getCollection(collectionName);
        return collectionQuery.getFilter().
                map(filter -> sourceCollection.find(Document.parse(filter))).
                orElse(null);
    }

    private FindIterable<Document> getDocuments(MongoDatabase sourceMongoDatabase, String collectionName, Object id) {
        Document criteriaObject = Criteria.where(MongoDomain.idProperty).is(id).getCriteriaObject();
        MongoCollection<Document> sourceCollection = sourceMongoDatabase.getCollection(collectionName);
        return sourceCollection.find(criteriaObject);
    }

    /**
     * 合并数据
     *
     * @param mongoDatabase
     * @param documents
     * @param collectionName
     */
    private void mergeDocuments(MongoDatabase mongoDatabase, Iterable<Document> documents, String collectionName) {
        MongoCollection<Document> mongoCollection = mongoDatabase.getCollection(collectionName);
        documents.forEach(
                document -> this.insertOrUpdateOneDocument(mongoCollection, document)
        );
    }

    /**
     * 更新或者创建一个Document
     *
     * @param mongoCollection
     * @param document
     */
    private void insertOrUpdateOneDocument(MongoCollection<Document> mongoCollection, Document document) {
        mongoCollection.replaceOne(
                new Document().append(MongoDomain.idProperty, document.get(MongoDomain.idProperty)),
                document,
                new ReplaceOptions().upsert(true)
        );
    }

    /**
     * 更新已经完成的同步表
     *
     * @param processRecordId
     * @param collectionName
     */
    private void updateCompleteSyncCollection(String processRecordId, String collectionName) {
        ProductInstanceSyncProcessRecord record = productInstanceSyncProcessRecordService.findRecordById(processRecordId);
        if (record.getStatus() != ProductInstanceSyncProcessRecord.ProductInstanceSyncProgressStatus.syncing) {
            record.setStatus(ProductInstanceSyncProcessRecord.ProductInstanceSyncProgressStatus.syncing);
        }
        record.getCompleteCollectionNames().add(collectionName);
        productInstanceSyncProcessRecordService.saveProductInstanceSyncProcessRecord(record);
    }

    /**
     * 更新未完成的同步表
     *
     * @param processRecordId
     * @param collectionName
     */
    private void updateUnCompleteSyncCollection(String processRecordId, String collectionName) {
        ProductInstanceSyncProcessRecord record = productInstanceSyncProcessRecordService.findRecordById(processRecordId);
        if (record.getStatus() != ProductInstanceSyncProcessRecord.ProductInstanceSyncProgressStatus.syncing) {
            record.setStatus(ProductInstanceSyncProcessRecord.ProductInstanceSyncProgressStatus.syncing);
        }
        record.getUnCompleteCollectionNames().add(collectionName);
        productInstanceSyncProcessRecordService.saveProductInstanceSyncProcessRecord(record);
    }
}
