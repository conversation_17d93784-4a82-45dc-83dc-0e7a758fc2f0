package com.qpp.cgp.manager.product.reset;

import com.qpp.cgp.config.mongo.MongoTemplateBeanNames;
import com.qpp.cgp.domain.reset.RemoveProductDataRecord;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.driver.HybridMongoTemplate;
import com.qpp.web.business.manager.AbstractLongMongoCurdManager;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

/**
 * @description:
 * @author: TT-Berg
 * @date: 2023/10/11
 **/
@Repository
public class RemoveProductDataRecordManager extends AbstractLongMongoCurdManager<RemoveProductDataRecord, Long> {

    public RemoveProductDataRecordManager(@Qualifier(MongoTemplateBeanNames.RUNTIME) HybridMongoTemplate mongoTemplate, IdGenerator idgenerator) {
        super(mongoTemplate, idgenerator);
    }
}
