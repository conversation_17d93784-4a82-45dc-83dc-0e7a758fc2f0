package com.qpp.cgp.manager.order.sync.service;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.qpp.cgp.domain.product.sync.ProjectDeployEnv;
import com.qpp.cgp.manager.order.sync.component.OrderSyncComponent;
import com.qpp.cgp.manager.order.sync.listener.OrderSyncContext;
import com.qpp.cgp.manager.order.sync.listener.OrderSyncEvent;
import com.qpp.cgp.manager.order.sync.orderSyncInfo.OrderSyncInfo;
import com.qpp.cgp.manager.order.sync.orderSyncInfo.OrderSyncInfoGenerator;
import com.qpp.cgp.manager.order.sync.orderSyncRecord.OrderSyncRecord;
import com.qpp.cgp.manager.order.sync.orderSyncRecord.OrderSyncRecordService;
import com.qpp.cgp.service.database.mongo.MongoIncreaseMergeQuery;
import com.qpp.cgp.service.database.mongo.MongoMergeQuery;
import com.qpp.cgp.service.database.service.MongoQueryGenerator;
import com.qpp.cgp.util.FunctionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

/**
 * @className: OrderSyncService
 * @description:
 * @author: TT-Berg
 * @date: 2023/1/9
 **/
@Service
public class OrderSyncService {

    @Autowired
    private OrderSyncRecordService orderSyncRecordService;
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    @Autowired
    private OrderSyncInfoGenerator orderSyncInfoGenerator;
    @Autowired
    private MongoQueryGenerator mongoQueryGenerator;
    @Autowired
    private OrderSyncComponent orderSyncComponent;


    /**
     * 订单数据同步
     *
     * @param orderIds  订单ids
     * @param targetEnv 目标环境
     * @return
     */
    public String startSync(List<String> orderIds, ProjectDeployEnv targetEnv, Date lastSyncProductInstanceDate) {
        String recordId = "";

        //1.入参效验
        if (CollectionUtils.isEmpty(orderIds) || null == targetEnv) {
            return recordId;
        }

        //2.幂等校验（是否有相同的同步任务已经准备或正在执行）
        hasTaskExecute(orderIds, targetEnv);

        //3.初始化同步记录
        recordId = initProcessRecord(orderIds, targetEnv);

        //4.发布事件（异步处理）
        publishSyncEvent(orderIds, targetEnv, recordId, lastSyncProductInstanceDate);

        return recordId;
    }

    public List<String> startSyncBatch(List<String> orderIds, ProjectDeployEnv targetEnv, Date lastSyncProductInstanceDate, int preSyncNum) {
        List<String> records = new ArrayList<>();

        //1.入参效验
        if (CollectionUtils.isEmpty(orderIds) || null == targetEnv) {
            return null;
        }

        List<List<String>> partition = Lists.partition(orderIds, preSyncNum);

        partition.forEach(item -> {
            //2.幂等校验（是否有相同的同步任务已经准备或正在执行）
            hasTaskExecute(item, targetEnv);

            //3.初始化同步记录
            String recordId = initProcessRecord(item, targetEnv);

            //4.发布事件（异步处理）
            publishSyncEvent(item, targetEnv, recordId, lastSyncProductInstanceDate);

            records.add(recordId);
        });

        return records;
    }

    private void publishSyncEvent(List<String> orderIds, ProjectDeployEnv targetEnv, String recordId, Date lastSyncProductInstanceDate) {
        OrderSyncContext context = new OrderSyncContext();
        context.setOrderIds(orderIds);
        context.setTargetEnv(targetEnv);
        context.setProcessRecordId(recordId);
        context.setLastSyncProductInstanceTime(lastSyncProductInstanceDate);
        OrderSyncEvent syncEvent = new OrderSyncEvent(context);
        applicationEventPublisher.publishEvent(syncEvent);
    }

    private String initProcessRecord(List<String> orderIds, ProjectDeployEnv targetEnv) {
        String processRecordId = orderSyncRecordService.initTaskRecord(orderIds, targetEnv);
        return processRecordId;
    }

    private void hasTaskExecute(List<String> orderIds, ProjectDeployEnv targetEnv) {
        OrderSyncRecord record = orderSyncRecordService.findExecutingTask(orderIds, targetEnv);
        if (null != record) {
            FunctionUtil.doThrow().throwException(200317, ImmutableMap.of("message", "该同步过程正在进行, progressId : " + record.getId()));
        }
    }

    /**
     * 同步
     *
     * @param context
     */
    public void syncing(OrderSyncContext context) {
        List<String> orderIds = context.getOrderIds();
        ProjectDeployEnv targetEnv = context.getTargetEnv();
        String processRecordId = context.getProcessRecordId();
        Date lastSyncProductInstanceTime = context.getLastSyncProductInstanceTime();

        //1.生产需要同步的数据ids
        OrderSyncInfo syncInfo = orderSyncInfoGenerator.generateSyncData(new HashSet<>(orderIds), targetEnv, lastSyncProductInstanceTime);
        //2.生产mongoQuery对象（集合名，条件）
        MongoIncreaseMergeQuery syncQuery = mongoQueryGenerator.generateQueryByIncrease(syncInfo);
        //更新同步记录
        orderSyncRecordService.updateExecutingProcessRecord(processRecordId, syncQuery);
        //3.数据同步
        orderSyncComponent.syncData(syncQuery, targetEnv, processRecordId);
        //其他数据同步
        orderSyncComponent.syncOtherData(syncInfo, targetEnv, processRecordId);
        //4.记录成功
        orderSyncRecordService.completeSyncTask(processRecordId);
    }

}
