package com.qpp.cgp.manager.productInstance.sync.productInstanceSyncInfo;

import com.qpp.cgp.service.database.dto.BaseMongoMergeInfo;
import com.qpp.cgp.service.database.dto.MongoMergeInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

/**
 * @className: ProductInstanceMergeData
 * @description:
 * @author: TT-Berg
 * @date: 2022/12/5
 **/
@Data
public class ProductInstanceSyncInfo extends BaseMongoMergeInfo implements Serializable {

    /**
     * 产品实例
     */
    @MongoMergeInfo("productinstances")
    private Set<String> productInstanceIds = new HashSet<>();

    /**
     * SMV
     */
    @MongoMergeInfo("materialviews")
    private Set<String> simplifyMaterialViewIds = new HashSet<>();

    /**
     * PC
     */
    @MongoMergeInfo("pagecontents")
    private Set<String> pageContentIds = new HashSet<>();

    /**
     * PCS
     */
    @MongoMergeInfo("pagecontentschemas")
    private Set<String> pageContentSchemaIds = new HashSet<>();

    /**
     * PCS Group
     */
    @MongoMergeInfo("pagecontentschemagroups")
    private Set<String> pageContentSchemaGroupIds = new HashSet<>();

    /**
     * PCS Runtime
     */
    @MongoMergeInfo("pagecontentschemaruntimes")
    private Set<String> pageContentSchemaRuntimeIds = new HashSet<>();

    /**
     * RtObject
     */
    @MongoMergeInfo("rtobjects")
    private Set<String> rtObjectIds = new HashSet<>();

    /**
     * UserDesign
     */
    @MongoMergeInfo("userdesigns")
    private Set<String> userDesignIds = new HashSet<>();

    /**
     * 导航
     */
    @MongoMergeInfo("runtimenavigations")
    private Set<String> runTimeNavigationIds = new HashSet<>();

    /**
     * View
     */
    @MongoMergeInfo("builderviewruntimes")
    private Set<String> builderViewRuntimeIds = new HashSet<>();

    /**
     * Node
     */
    @MongoMergeInfo("sbnoderuntime")
    private Set<String> sbNodeRuntimeIds = new HashSet<>();

    /**
     * PropertyModel
     */
    @MongoMergeInfo("propertymodels")
    private Set<String> propertyModelIds = new HashSet<>();

    /**
     * VariableDataSourceObject
     */
    @MongoMergeInfo("variabledatasourceobjects")
    private Set<String> variableDataSourceObjectIds = new HashSet<>();

}
