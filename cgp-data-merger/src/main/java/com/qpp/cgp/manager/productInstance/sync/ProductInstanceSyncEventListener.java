package com.qpp.cgp.manager.productInstance.sync;

import com.qpp.cgp.manager.productInstance.sync.productInstanceSyncRecord.ProductInstanceSyncErrorData;
import com.qpp.cgp.manager.productInstance.sync.productInstanceSyncRecord.ProductInstanceSyncProcessRecord;
import com.qpp.cgp.manager.productInstance.sync.productInstanceSyncRecord.ProductInstanceSyncProcessRecordService;
import com.qpp.commons.json.JsonUtils;
import com.qpp.core.utils.SentryUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @className: ProductInstanceSyncEventListener
 * @description:
 * @author: TT-Berg
 * @date: 2022/12/5
 **/
@Component
@Async
@Slf4j
public class ProductInstanceSyncEventListener implements ApplicationListener<ProductInstanceSyncEvent> {

    @Autowired
    private ProductInstanceSyncService productInstanceSyncService;
    @Autowired
    private ProductInstanceSyncProcessRecordService productInstanceSyncProcessRecordService;

    @Override
    public void onApplicationEvent(ProductInstanceSyncEvent event) {
        ProductInstanceSyncContext context = (ProductInstanceSyncContext) event.getSource();
        String processRecordId = context.getProcessRecordId();
        try {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            productInstanceSyncService.syncing(context);
            stopWatch.stop();
            log.warn("产品实例同步消耗时间" + stopWatch.getTotalTimeSeconds());
        } catch (Exception e) {
            doErrorRecord(processRecordId, e);
        }
    }

    private void doErrorRecord(String processRecordId, Exception e) {
        //更新同步记录
        ProductInstanceSyncProcessRecord syncProcessRecord = productInstanceSyncProcessRecordService.findRecordById(processRecordId);
        syncProcessRecord.setStatus(ProductInstanceSyncProcessRecord.ProductInstanceSyncProgressStatus.failure);
        ProductInstanceSyncErrorData syncErrorData = new ProductInstanceSyncErrorData();
        syncErrorData.setOperateDate(new Date());
        if (null != e) {
            if (StringUtils.isBlank(e.getMessage())) {
                syncErrorData.setErrorMessages(e.toString());
            } else {
                syncErrorData.setErrorMessages(e.getMessage());
            }
            syncErrorData.setStackTraceDesc(syncErrorData.resolveStack(e.getStackTrace()));
        }
        syncProcessRecord.setErrorData(syncErrorData);
        productInstanceSyncProcessRecordService.saveProductInstanceSyncProcessRecord(syncProcessRecord);
        //sentry通知，人工重试
        Map<String, String> errorData = new HashMap<>();
        errorData.put("syncProcessRecordId", processRecordId);
        SentryUtils.recordEx(e, errorData);
    }

}
