package com.qpp.cgp.manager.file;

import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.config.mongo.MongoTemplateBeanNames;
import com.qpp.cgp.domain.FileSyncProgressLog;
import com.qpp.cgp.domain.SyncProgressStatus;
import com.qpp.cgp.domain.product.sync.ProjectDeployEnv;
import com.qpp.cgp.dto.FileSyncEvent;
import com.qpp.cgp.dto.FileSyncEventContext;
import com.qpp.cgp.service.database.service.ProductFileMerger;
import com.qpp.core.context.SpringApplicationContext;
import com.qpp.id.generator.IdGenerator;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
public class FileSyncService {
    @Autowired
    ProductFileMerger productFileMerger;

    @Autowired
    IdGenerator idGenerator;

    @Autowired
    @Qualifier(MongoTemplateBeanNames.RUNTIME)
    MongoTemplate mongoTemplateRun;

    @Autowired
    FileSyncProgressLogManager fileSyncProgressLogManager;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    /**
     * 发布事件
     * @param fileSyncProgressLog 文件同步请求
     * @return
     */
    public void publishEvent(FileSyncProgressLog fileSyncProgressLog) {
        FileSyncEventContext fileSyncEventContext = new FileSyncEventContext();
        fileSyncEventContext.setFileSyncProgressLog(fileSyncProgressLog);
        FileSyncEvent fileSyncEvent = new FileSyncEvent(fileSyncEventContext);
        applicationEventPublisher.publishEvent(fileSyncEvent);
    }

    public String startSync(FileSyncProgressLog fileSyncProgressLog) {
        // 检查是否允许同步
        checkAllowSync(fileSyncProgressLog);
        fileSyncProgressLog.setId(idGenerator.generateId());
        fileSyncProgressLog.setFailureDesc(null);
        fileSyncProgressLogManager.saveNew(fileSyncProgressLog);

        // 多线程异步任务：文件同步事件
        CompletableFuture.runAsync(() -> {
            publishEvent(fileSyncProgressLog);
        });
        fileSyncProgressLog.setStatus(SyncProgressStatus.syncing);
        return fileSyncProgressLog.getId().toString();
    }

    public void checkAllowSync(FileSyncProgressLog fileSyncProgressLog){


        List<SyncProgressStatus> statuses=new ArrayList<>();
        statuses.add(SyncProgressStatus.success);
        statuses.add(SyncProgressStatus.failure);
        // 检查当前文件是否正在同步
        Criteria criteria = new Criteria();
        criteria.andOperator(
                Criteria.where("fileNames").is(fileSyncProgressLog.getFileNames()),
                Criteria.where("staticFiles").is(fileSyncProgressLog.getStaticFiles()),
                Criteria.where("status").nin(statuses)
        );
        List<FileSyncProgressLog> fileSyncProgressLogs = mongoTemplateRun.find(Query.query(criteria), FileSyncProgressLog.class);
        if (!fileSyncProgressLogs.isEmpty()){
            // 该同步正在进行
            Set<Long> progressIds = fileSyncProgressLogs.stream().map(FileSyncProgressLog::getId).collect(Collectors.toSet());
            throw BusinessExceptionBuilder.of(7894, ImmutableMap.of("message", "最近有相同的同步过程正在进行, progressId:" + progressIds+"，异常代码："+7894));
        }
    }


}
