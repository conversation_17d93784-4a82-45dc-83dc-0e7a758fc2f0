package com.qpp.cgp.manager.website.sync;

import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.SyncProgressStatus;
import com.qpp.cgp.domain.WebsiteSyncProgress;
import com.qpp.cgp.domain.product.sync.ProjectDeployEnv;
import com.qpp.cgp.dto.WebsiteSyncEvent;
import com.qpp.cgp.dto.WebsiteSyncEventContext;
import com.qpp.cgp.repository.website.WebsiteSyncProgressRepository;
import com.qpp.id.generator.IdGenerator;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class WebsiteSyncManager {

    @Autowired
    private IdGenerator idGenerator;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private WebsiteSyncProgressRepository websiteSyncProgressRepository;

    /**
     * 判断是否存在正在进行的同步
     *
     * @param websiteIds websiteIds
     * @param targetEnv 目标环境
     * @return
     */
    public void checkExistsProceedingSyncProgress(List<Long> websiteIds, ProjectDeployEnv targetEnv) {

        List<SyncProgressStatus> statuses = new ArrayList<>();
        statuses.add(SyncProgressStatus.success);
        statuses.add(SyncProgressStatus.failure);

        WebsiteSyncProgress progress = websiteSyncProgressRepository.findOneByWebsiteIdsAndTargetEnvAndStatusesNin(websiteIds, targetEnv, statuses);
        if (progress != null) {
            throw BusinessExceptionBuilder.of(200317, ImmutableMap.of("message", "该同步过程正在进行, progressId : " + progress.getId()));
        }
    }

    /**
     * 生成并写入一条新的同步进度记录
     *
     * @param websiteIds
     * @param targetEnv
     * @return
     */
    private String generateSyncProgress(List<Long> websiteIds, ProjectDeployEnv targetEnv) {

        String result = idGenerator.generateId().toString();

        WebsiteSyncProgress websiteSyncProgress = new WebsiteSyncProgress();
        websiteSyncProgress.setId(result);
        websiteSyncProgress.setWebsiteIds(websiteIds);
        websiteSyncProgress.setTargetEnv(targetEnv);
        websiteSyncProgress.setStatus(SyncProgressStatus.waiting);

        websiteSyncProgressRepository.getMongoTemplate().insert(websiteSyncProgress);

        return result;
    }

    /**
     * 发布同步事件
     *
     * @param userId 发起同步用户id
     * @param targetEnv 同步目标环境
     * @param comment 同步原因及信息
     * @param websiteIds 同步id数组
     * @param progressId 同步进度id
     */
    private void publishSyncEvent(long userId, ProjectDeployEnv targetEnv, String comment, List<Long> websiteIds, String progressId) {

        WebsiteSyncEventContext websiteSyncEventContext = new WebsiteSyncEventContext();
        websiteSyncEventContext.setWebsiteIds(websiteIds);
        websiteSyncEventContext.setTargetEnv(targetEnv);
        websiteSyncEventContext.setUserId(userId);
        websiteSyncEventContext.setComment(comment);
        websiteSyncEventContext.setProgressId(progressId);

        WebsiteSyncEvent websiteSyncEvent = new WebsiteSyncEvent(websiteSyncEventContext);

        applicationEventPublisher.publishEvent(websiteSyncEvent);
    }

    /**
     * 同步接口的业务层入口方法
     *
     * @param userId 同步发起人
     * @param targetEnv 同步目标环境
     * @param comment 同步原因及信息
     * @param websiteIds 需同步的id数组
     * @return 同步进度id
     */
    public String startSync(long userId, ProjectDeployEnv targetEnv, String comment, List<Long> websiteIds) {
        String result = "";

        // 1 check
        checkExistsProceedingSyncProgress(websiteIds, targetEnv);
        // 2 init syncProgress
        result = generateSyncProgress(websiteIds, targetEnv);
        // 3 publish sync event
        publishSyncEvent(userId, targetEnv, comment, websiteIds, result);

        return result;
    }

}
