package com.qpp.cgp.manager.product.sync;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.qpp.cgp.config.mongo.MongoTemplateBeanNames;
import com.qpp.cgp.domain.product.Product;
import com.qpp.cgp.domain.product.ProductStatus;
import com.qpp.cgp.domain.product.SkuProduct;
import com.qpp.cgp.domain.product.sync.ProductSyncProgress;
import com.qpp.cgp.domain.product.sync.ProjectDeployEnv;
import com.qpp.cgp.dto.product.sync.ProductSyncEvent;
import com.qpp.cgp.dto.product.sync.ProductSyncEventContext;
import com.qpp.cgp.manager.product.ProductManager;
import com.qpp.cgp.migration.CgpMongoDomain;
import com.qpp.cgp.repository.product.ProductSyncProgressRepository;
import com.qpp.id.generator.IdGenerator;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class ProductSyncManager {

    @Autowired
    private IdGenerator idGenerator;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private ProductSyncProgressRepository productSyncProgressRepository;

    @Autowired
    private ProductManager productManager;

    @Autowired
    @Qualifier(MongoTemplateBeanNames.CONFIG)
    private MongoTemplate configMongoTemplate;

    /**
     * 更新产品id数组
     *
     * @param productIds
     * @param withSku
     */
    private void updateProductIds(List<Long> productIds, boolean withSku, boolean withSubProduct) {
        if (withSubProduct) {
            Set<Long> subProduct = productManager.findSubProduct(productIds);
            subProduct.addAll(productIds);
            productIds.clear();
            productIds.addAll(subProduct);
        }
        if (withSku) {
            List<Long> skuProductIds = configMongoTemplate.find(Query.query(Criteria.where("configurableProductId").in(productIds).and("status").is(ProductStatus.ACTIVE).and("clazz").is(SkuProduct.class.getName())), Product.class)
                    .stream().map(CgpMongoDomain::getId).collect(Collectors.toList());
            productIds.addAll(skuProductIds);
        }
    }

    /**
     * 检查该次同步是否可进行
     * 检索产品同步进度表，查看由 (productIds, targetEnv) 组合索引确定的进度状态是否为正在同步
     *
     * @param productIds
     * @param targetEnv
     */
    private void canSync(List<Long> productIds, ProjectDeployEnv targetEnv) {

        List<ProductSyncProgress.ProductSyncProgressStatus> statuses = new ArrayList<>();
        statuses.add(ProductSyncProgress.ProductSyncProgressStatus.success);
        statuses.add(ProductSyncProgress.ProductSyncProgressStatus.failure);

        ProductSyncProgress progress = productSyncProgressRepository.findOneByProductIdsAndTargetEnvAndStatuses(productIds, targetEnv, statuses);
        if (progress != null) {
            throw BusinessExceptionBuilder.of(200317, ImmutableMap.of("message", "该同步过程正在进行, progressId : " + progress.getId()));
        }

    }

    /**
     * 生成并写入一条新的产品同步进度记录
     *
     * @param productIds
     * @param targetEnv
     * @return
     */
    private String generateProductSyncProgress(List<Long> productIds, ProjectDeployEnv targetEnv, String comment) {

        String result = idGenerator.generateId().toString();

        ProductSyncProgress productSyncProgress = new ProductSyncProgress();
        productSyncProgress.setId(result);
        productSyncProgress.setProductIds(productIds);
        productSyncProgress.setTargetEnv(targetEnv);
        productSyncProgress.setRemark(comment);
        productSyncProgress.setStatus(ProductSyncProgress.ProductSyncProgressStatus.waiting);

        productSyncProgressRepository.getMongoTemplate().insert(productSyncProgress);

        return result;
    }

    /**
     * 发布产品同步事件
     *
     * @param userId         发起同步用户id
     * @param targetEnv      同步目标环境
     * @param comment        同步原因及信息
     * @param productIds     同步产品id数组
     * @param progressId     同步进度id
     * @param withValidation 是否效验产品管理员和产品锁
     */
    private void publishProductSyncEvent(long userId, ProjectDeployEnv targetEnv, String comment, List<Long> productIds, String progressId, boolean withValidation) {

        ProductSyncEventContext productSyncEventContext = new ProductSyncEventContext();
        productSyncEventContext.setProductIds(productIds);
        productSyncEventContext.setTargetEnv(targetEnv);
        productSyncEventContext.setUserId(userId);
        productSyncEventContext.setComment(comment);
        productSyncEventContext.setProgressId(progressId);
        productSyncEventContext.setWithValidation(withValidation);

        ProductSyncEvent productSyncEvent = new ProductSyncEvent(productSyncEventContext);

        applicationEventPublisher.publishEvent(productSyncEvent);
    }

    /**
     * 产品同步接口的业务层入口方法
     *
     * @param userId         产品同步发起人
     * @param targetEnv      产品同步目标环境
     * @param comment        产品同步原因及信息
     * @param productIds     需同步的产品id数组
     * @param withSku        是否将sku产品一并同步
     * @param withValidation 是否效验产品管理员和产品锁
     * @param withSubProduct 是否包含子产品
     * @return 产品同步进度id
     */
    public String startSync(long userId, ProjectDeployEnv targetEnv, String comment, List<Long> productIds, boolean withSku, boolean withValidation, boolean withSubProduct) {
        String result = "";
        if (productIds == null || productIds.isEmpty()) {
            throw BusinessExceptionBuilder.of(200317, ImmutableMap.of("message", "同步的产品为空"));
        }
        updateProductIds(productIds, withSku, withSubProduct);
        // 1 check
        canSync(productIds, targetEnv);
        // 2 init productSyncProgress
        result = generateProductSyncProgress(productIds, targetEnv, comment);
        // 3 publish event
        publishProductSyncEvent(userId, targetEnv, comment, productIds.stream().distinct().collect(Collectors.toList()), result, withValidation);

        return result;
    }

    public List<String> startSyncBatch(long userId, ProjectDeployEnv targetEnv, String comment, List<Long> productIds, boolean withSku, boolean withValidation, int preSyncNum, boolean withSubProduct) {
        List<String> records = new ArrayList<>();
        if (productIds == null || productIds.isEmpty()) {
            throw BusinessExceptionBuilder.of(200317, ImmutableMap.of("message", "同步的产品为空"));
        }
        updateProductIds(productIds, withSku, withSubProduct);

        List<List<Long>> partition = Lists.partition(productIds, preSyncNum);

        partition.forEach(item -> {
            // 1 check
            canSync(item, targetEnv);
            // 2 init productSyncProgress
            String result = generateProductSyncProgress(item, targetEnv, comment);
            // 3 publish event
            publishProductSyncEvent(userId, targetEnv, comment, item.stream().distinct().collect(Collectors.toList()), result, withValidation);
            records.add(result);
        });

        return records;
    }
}
