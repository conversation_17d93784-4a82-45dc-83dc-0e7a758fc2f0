package com.qpp.cgp.manager.website.sync;

import com.qpp.cgp.domain.BaseLockConfig;
import com.qpp.cgp.dto.LockInfoDTO;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.util.List;

public abstract class AbstractLockConfigManager<T extends BaseLockConfig> {

    protected MongoTemplate mongoTemplate;

    protected Class<T> entityType;

    public AbstractLockConfigManager(MongoTemplate mongoTemplate, Class<T> entityType) {
        this.mongoTemplate = mongoTemplate;
        this.entityType = entityType;
    }

    /**
     * 通过被同步的实体id获取同步锁表记录
     *
     * @param entityIds 同步锁关联的被同步实体表id数组
     * @return
     */
    protected abstract List<T> getLockConfigs(List<Long> entityIds);

    /**
     * 判断是否适合进行同步
     *
     * @param lockConfigs 同步锁记录
     * @return
     */
    private boolean canSync(List<T> lockConfigs) {
        boolean result;

        result = lockConfigs.stream().noneMatch(BaseLockConfig::getIsLock);

        return result;
    }

    /**
     * 判断同步是否适合进行，并返回同步锁历史
     *
     * @param ids entityType's id list
     * @return
     */
    public LockInfoDTO<T> syncPreCheck(List<Long> ids) {
        LockInfoDTO<T> result = new LockInfoDTO<>();

        List<T> lockConfigs = getLockConfigs(ids);
        boolean canSync = canSync(lockConfigs);
        result.setCanSync(canSync);
        result.setLockConfigList(lockConfigs);

        return result;
    }
}
