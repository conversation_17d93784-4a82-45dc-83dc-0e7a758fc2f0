package com.qpp.cgp.manager.reset;

import com.qpp.cgp.config.mongo.MongoTemplateFactory;
import com.qpp.cgp.domain.reset.RemoveProductDataRecord;
import com.qpp.cgp.service.database.dto.BaseMongoMergeInfo;
import com.qpp.cgp.service.database.dto.MongoMergeInfo;
import com.qpp.cgp.service.database.dto.ProductMergeInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * @description:
 * @author: TT-Berg
 * @date: 2023/10/11
 **/
@Slf4j
@Component
public class ProductDataResetComponent {

    @Autowired
    private MongoTemplateFactory mongoTemplateFactory;

    public void resetProductData(List<NeedResetProductData> needResetProductDataList, RemoveProductDataRecord record) {
        if (CollectionUtils.isEmpty(needResetProductDataList)) {
            return;
        }

        for (NeedResetProductData resetProductData : needResetProductDataList) {
            MongoTemplate mongoTemplate = resetProductData.getMongoTemplate();
            Query removeQuery = resetProductData.getRemoveQuery();
            String collectionName = resetProductData.getCollectionName();
            List<Object> ids = resetProductData.getIds();

            mongoTemplate.remove(removeQuery, collectionName);
            record.getHasRemovedData().put(collectionName, ids);
        }
    }

    public List<NeedResetProductData> generateResetProductData(ProductMergeInfo productMergeInfo) {
        if (null == productMergeInfo) {
            return null;
        }

        List<NeedResetProductData> needResetProductDataList = new ArrayList<>();

        List<NeedResetProductData> needResetProductDataOne = this.generateResetProductData(productMergeInfo.getProductMigratedMongoMergeInfo());
        List<NeedResetProductData> needResetProductDataTwo = this.generateResetProductData(productMergeInfo.getProductMongoMergeInfo());
        List<NeedResetProductData> needResetProductDataThree = this.generateResetProductData(productMergeInfo.getProductMongoSupplementMergeInfo());

        if (CollectionUtils.isNotEmpty(needResetProductDataOne)) {
            needResetProductDataList.addAll(needResetProductDataOne);
        }
        if (CollectionUtils.isNotEmpty(needResetProductDataTwo)) {
            needResetProductDataList.addAll(needResetProductDataTwo);
        }
        if (CollectionUtils.isNotEmpty(needResetProductDataThree)) {
            needResetProductDataList.addAll(needResetProductDataThree);
        }

        return needResetProductDataList;
    }

    private List<NeedResetProductData> generateResetProductData(BaseMongoMergeInfo info) {
        if (null == info) {
            return null;
        }

        List<NeedResetProductData> needResetProductDataList = new ArrayList<>();

        Class<? extends BaseMongoMergeInfo> clazz = info.getClass();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {

            MongoMergeInfo annotation = field.getAnnotation(MongoMergeInfo.class);

            if (null == annotation) {
                continue;
            }

            String collection = annotation.value();
            if (StringUtils.isBlank(collection)) {
                continue;
            }

            boolean needResetData = annotation.needResetData();
            if (!needResetData) {
                continue;
            }

            field.setAccessible(true);

            Set value = null;

            try {
                Object idsObj = field.get(info);
                if (idsObj instanceof Set) {
                    value = (Set) idsObj;
                }
            } catch (IllegalAccessException e) {
                log.error("反射生成移除产品数据是遇到异常: {}, 集合名:{}", e.getMessage(), collection);
                continue;
            }

            MongoTemplate mongoTemplate = this.getMongoTemplateByCollectionName(collection);

            if (value != null && value.size() > 0) {
                NeedResetProductData data = this.generateResetProductData(collection, value, mongoTemplate);
                needResetProductDataList.add(data);
            }
        }

        return needResetProductDataList;
    }

    private NeedResetProductData generateResetProductData(String collection, Set value, MongoTemplate mongoTemplate) {
        return NeedResetProductData.builder()
                .mongoTemplate(mongoTemplate)
                .ids(new ArrayList<>(value))
                .collectionName(collection)
                .build();
    }

    private MongoTemplate getMongoTemplateByCollectionName(String collection) {
        return mongoTemplateFactory.getMongoTemplate(collection);
    }
}
