package com.qpp.test;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.qpp.cgp.domain.bom.runtime.ProductInstance;
import com.qpp.cgp.manager.productInstance.sync.buildCacheAnalyze.BuildCacheRuntimeSyncInfo;
import com.qpp.util.JsonResourceUtils;
import org.apache.commons.lang.StringUtils;
import org.bson.Document;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @className: DocumentAnalyzeTest
 * @description:
 * @author: TT-Berg
 * @date: 2022/12/7
 **/
public class DocumentAnalyzeTest {

    ProductInstance productInstance;

    private final Logger logger = LoggerFactory.getLogger("copy.productInstance");

    @Before
    public void init() {
        productInstance = JsonResourceUtils.getObject("mongo/initProductInstance.json", ProductInstance.class);
    }

    @Test
    public void testInit() {
        Assert.assertNotNull(productInstance);
    }

    @Test
    public void test1() {
        Document runtime = productInstance.getBuilderCache().getRuntime();
        Assert.assertNotNull(runtime);
        Map<Object, String> result = analyzeDocument(runtime);
        System.out.println("=======================");
        result.forEach(
                (k, v) -> {
                    System.out.println(k + " : " + v);
                }
        );
        System.out.println("=======================");
    }

    public Map<Object, String> analyzeDocument(Document document) {
        if (null == document || document.isEmpty()) {
            return null;
        }
        Map<Object, String> result = new ConcurrentHashMap<>();
        findClass(document, result);
        for (Object value : document.values()) {
            //只处理list和map
            if (value instanceof List) {
                decodeList((ArrayList) value, result);
            }
            if (value instanceof Map) {
                decodeMap((LinkedHashMap) value, result);
            }
        }
        return result;
    }

    public void decodeMap(LinkedHashMap<String, Object> map, Map<Object, String> result) {
        if (CollectionUtils.isEmpty(map)) {
            return;
        }
        findClass(map, result);
        for (Object value : map.values()) {
            if (value instanceof List) {
                decodeList((ArrayList) value, result);
            }
            if (value instanceof Map) {
                findClass((LinkedHashMap) value, result);
                decodeMap((LinkedHashMap) value, result);
            }
        }
    }

    public void decodeList(ArrayList<Object> list, Map<Object, String> result) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (Object value : list) {
            if (value instanceof List) {
                decodeList((ArrayList) value, result);
            }
            if (value instanceof Map) {
                decodeMap((LinkedHashMap) value, result);
            }
        }
    }

    public void findClass(Document document, Map<Object, String> result) {
        if ((document.containsKey("_id") || document.containsKey("id")) && document.containsKey("clazz")) {
            Object id1 = document.get("_id");
            Object id2 = document.get("id");
            Object id = handleIdValue(id1, id2);
            Object clazz = document.get("clazz");
            if (id != null && clazz != null) {
                result.put(id, (String) clazz);
            }
        }
    }

    public void findClass(Map<String, Object> params, Map<Object, String> result) {
        if ((params.containsKey("_id") || params.containsKey("id")) && params.containsKey("clazz")) {
            Object id1 = params.get("_id");
            Object id2 = params.get("id");
            Object id = handleIdValue(id1, id2);
            Object clazz = params.get("clazz");
            if (id != null && clazz != null) {
                result.put(id, (String) clazz);
            }
        }
    }

    public Object handleIdValue(Object id1, Object id2) {
        if (notNullIdValue(id1)) {
            return id1;
        }
        if (notNullIdValue(id2)) {
            return id2;
        }
        return null;
    }

    public boolean notNullIdValue(Object id) {
        if (id instanceof String) {
            return StringUtils.isNotBlank((String) id);
        }
        if (id instanceof Long) {
            return id != null;
        }
        return false;
    }

    public String getCollectionNameByFullClassName(String fullClassName) {
        if (StringUtils.isBlank(fullClassName)) {
            return null;
        }
        Class<?> clazz = null;
        String collectionName = null;
        try {
            clazz = Class.forName(fullClassName);
        } catch (ClassNotFoundException e) {
            logger.error("根据全限定类名:{},未找到相应类", fullClassName);
        }
        if (clazz != null) {
            //@Document中标志了@Inherited注解，不需要特别处理@Document注解在父类上的情况
            org.springframework.data.mongodb.core.mapping.Document documentAnnotation = clazz.getAnnotation(org.springframework.data.mongodb.core.mapping.Document.class);
            if (documentAnnotation != null) {
                String collection = documentAnnotation.collection();
                if (StringUtils.isBlank(collection)) {
                    String value = documentAnnotation.value();
                    if (StringUtils.isNotBlank(value)) {
                        collectionName = value;
                    }
                } else {
                    collectionName = collection;
                }
            } else {
                logger.warn("根据全限定类名:{},在该类上未找到相应Document注解", fullClassName);
            }
        }
        return collectionName;
    }

    public Map<String, Set<Object>> generateRuntimeSyncInfo(Map<Object, String> map) {
        if (CollectionUtils.isEmpty(map)) {
            return null;
        }
        Map<String, Set<Object>> temp = new HashMap<>();
        Map<String, Set<Object>> result = new HashMap<>();
        map.forEach((k, v) -> {
            if (temp.get(v) == null) {
                temp.put(v, Sets.newHashSet(k));
            } else {
                temp.get(v).add(k);
            }
        });
        if (CollectionUtils.isEmpty(temp)) {
            return null;
        }
        temp.forEach((k, v) -> {
            String collectionName = getCollectionNameByFullClassName(k);
            if (StringUtils.isNotBlank(collectionName)) {
                result.put(collectionName, v);
            }
        });
        return result;
    }

    @Test
    public void test5() {
        Document runtime = productInstance.getBuilderCache().getRuntime();
        Assert.assertNotNull(runtime);
        Map<Object, String> map = analyzeDocument(runtime);
        Map<String, Set<Object>> result = generateRuntimeSyncInfo(map);
        Assert.assertNotNull(result);
        System.out.println("=======================");
        result.forEach(
                (k, v) -> {
                    System.out.println(k + " : " + v);
                }
        );
        System.out.println("=======================");
    }

    @Test
    public void test3() {
        Document dbObject = Criteria.where("_id").in(Lists.newArrayList(1, 2, 3)).getCriteriaObject();
        String filter = dbObject.toJson();
        System.out.println(filter);
    }

    @Test
    public void test4() {
        Set<Integer> set = new HashSet<>();
        set.add(null);
        set.add(null);
        set.add(null);
        System.out.println(set.size());
    }

    @Test
    public void test() {
        String collectionName = getCollectionNameByFullClassName("com.qpp.cgp.domain.bom.runtime.ProductInstance");
        Assert.assertNotNull(collectionName);
        Assert.assertTrue(collectionName.equals("productinstances"));
    }

    @Test
    public void test6() {
        Document runtime = productInstance.getBuilderCache().getRuntime();
        Assert.assertNotNull(runtime);
        Map<Object, String> map = analyzeDocument(runtime);
        Map<String, Set<Object>> result = generateRuntimeSyncInfo(map);
        Assert.assertNotNull(result);
        BuildCacheRuntimeSyncInfo buildCacheRuntimeSyncInfo = new BuildCacheRuntimeSyncInfo();
        result.forEach((k, v) -> {
            if (buildCacheRuntimeSyncInfo.getSyncData().get(k) == null) {
                buildCacheRuntimeSyncInfo.getSyncData().put(k, v);
            } else {
                buildCacheRuntimeSyncInfo.getSyncData().get(k).addAll(v);
            }
        });
        result.get("pagecontents").add("1");
        result.forEach((k, v) -> {
            if (buildCacheRuntimeSyncInfo.getSyncData().get(k) == null) {
                buildCacheRuntimeSyncInfo.getSyncData().put(k, v);
            } else {
                buildCacheRuntimeSyncInfo.getSyncData().get(k).addAll(v);
            }
        });
        Assert.assertTrue(buildCacheRuntimeSyncInfo.getSyncData().get("pagecontents").size() == result.get("pagecontents").size() + 1);
    }

    @Test
    public void test7(){
        Set<String> runTimeData = new HashSet<>();
        runTimeData.add("pagecontents");
    }
}
