//package com.qpp.test;
//
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.qpp.cgp.service.database.mysql.MysqlConfig;
//import com.qpp.cgp.service.database.mysql.MysqlMergeQuery;
//import com.qpp.cgp.service.database.mysql.MysqlMerger;
//import com.qpp.cgp.service.database.mysql.TableQuery;
//import org.junit.Test;
//
//import java.util.List;
//import java.util.Optional;
//
///**
// * Created by smart on 12/7/2017.
// */
//public class MySqlMerger86Test {
//
//
//    MysqlMerger databaseMerger = new MysqlMerger();
//
//
//    @Test
//    public void testMergeData() throws Exception {
//
//        String driver = "com.mysql.jdbc.Driver";
//
//        String sourceUrl = "*********************************************************************************************";
//        String username = "developer";
//        String password = "Dev!123a";
//        MysqlConfig sourceConfig = new MysqlConfig(sourceUrl, username, password, driver);
//
//        String targetUrl = "****************************************************************************************";
//        String username86 = "cgp2";
//        String password86 = "<EMAIL>!";
//        MysqlConfig targetConfig = new MysqlConfig(targetUrl, username86, password86, driver);
//
//
//        MysqlMergeQuery databaseMergeQuery = new MysqlMergeQuery();
//
//
//        getTableNames().stream().forEach(tableName ->
//                this.addTableQuery(tableName, null, databaseMergeQuery));
//
//
//        databaseMerger.mergeData(targetConfig, databaseMergeQuery);
//
//    }
//
//    public List<String> getTableNames() throws Exception {
//
//        //language=JSON
//        String tableNames = "[\n" +
//                "  \"cgp_product\",\n" +
//                "  \"cgp_product_attribute_value\",\n" +
//                "  \"cgp_product_category\",\n" +
//                "  \"cgp_product_category_template\",\n" +
//                "  \"cgp_product_media\",\n" +
//                "  \"cgp_product_template\",\n" +
//                "  \"cgp_builder_config\",\n" +
//                "  \"cgp_builder_config_bom\",\n" +
//                "  \"cgp_builder_config_design\",\n" +
//                "  \"cgp_builder_config_design_compatibility_to_bom\",\n" +
//                "  \"cgp_builder_config_imposition\",\n" +
//                "  \"cgp_builder_config_imposition_compatibility_to_bom\",\n" +
//                "  \"cgp_builder_config_view\",\n" +
//                "  \"cgp_builder_config_view_compatibility_to_bom\",\n" +
//                "  \"cgp_builder_config_view_compatibility_to_view\",\n" +
//                "  \"cgp_cms_entity_filter\",\n" +
//                "  \"cgp_cms_entity_query\",\n" +
//                "  \"cgp_cms_page\",\n" +
//                "  \"cgp_cms_publish\",\n" +
//                "  \"cgp_cms_publish_goal\",\n" +
//                "  \"cgp_cms_publish_task\",\n" +
//                "  \"cgp_cms_variable\",\n" +
//                "  \"cgp_cms_variable_to_cms_page\",\n" +
//                "  \"cgp_configurable_product_sku_attribute\",\n" +
//                "  \"cgp_main_product_category_attribute\",\n" +
//                "  \"cgp_attribute\",\n" +
//                "  \"cgp_attribute_option\",\n" +
//                "  \"cgp_currency\",\n" +
//                "  \"cgp_product_to_sub_category\"\n" +
//                "]";
//
//
//        return new ObjectMapper().readValue(tableNames, List.class);
//
//    }
//
//
//    public void addTableQuery(String tableName, String filter, MysqlMergeQuery databaseMergeQuery) {
//
//        TableQuery tableQuery = new TableQuery();
//        tableQuery.setTableName(tableName);
//        tableQuery.setFilter(Optional.ofNullable(filter));
//
//        databaseMergeQuery.getTableQueries().add(tableQuery);
//
//    }
//
//
//}
