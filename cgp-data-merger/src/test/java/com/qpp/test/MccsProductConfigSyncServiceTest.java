package com.qpp.test;

import com.google.common.collect.Lists;
import com.qpp.cgp.domain.order.mccs.MccsProductConfigSyncProgress;
import com.qpp.cgp.domain.product.sync.ProjectDeployEnv;
import com.qpp.cgp.manager.order.mccs.MccsProductConfigManager;
import com.qpp.cgp.manager.order.mccs.MccsProductConfigSyncProgressManager;
import com.qpp.cgp.manager.product.config.ProductConfigBomManager;
import com.qpp.cgp.manager.product.config.ProductConfigManager;
import com.qpp.cgp.service.database.mongo.MongoConfig;
import com.qpp.cgp.service.database.mongo.MongoDatabaseFactory;
import com.qpp.cgp.service.database.mongo.MongoMergeQuery;
import com.qpp.cgp.service.database.mongo.MongoMerger;
import com.qpp.cgp.service.database.service.MccsProductConfigSyncService;
import com.qpp.cgp.service.database.service.ProductMergerV2;
import org.apache.commons.math3.analysis.function.Pow;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;

import static org.mockito.ArgumentMatchers.any;
@RunWith(PowerMockRunner.class)
@PrepareForTest({ProductMergerV2.class, MongoDatabaseFactory.class})
@PowerMockIgnore({"javax.management.*", "org.w3c.dom.*", "org.apache.log4j.*", "org.xml.sax.*", "javax.xml.*", "javax.script.*"})
class MccsProductConfigSyncServiceTest {
    @Spy
    @InjectMocks
    public MccsProductConfigSyncService mccsProductConfigSyncService=new MccsProductConfigSyncService();

    @Test
    public void testSync() {
        ProductConfigBomManager productConfigBomManager = PowerMockito.mock(ProductConfigBomManager.class);
        ProductConfigManager productConfigManager = PowerMockito.mock(ProductConfigManager.class);
        MccsProductConfigManager mccsProductConfigManager = PowerMockito.mock(MccsProductConfigManager.class);
        MongoMerger mongoMerger = PowerMockito.mock(MongoMerger.class);

        Field mongoMergerFiled = ReflectionUtils.findField(MccsProductConfigSyncService.class, "mongoMerger");
        mongoMergerFiled.setAccessible(true);
        ReflectionUtils.setField(mongoMergerFiled, mccsProductConfigSyncService, mongoMerger);

        Field productConfigBomManagerFiled = ReflectionUtils.findField(MccsProductConfigSyncService.class, "productConfigBomManager");
        productConfigBomManagerFiled.setAccessible(true);
        ReflectionUtils.setField(productConfigBomManagerFiled, mccsProductConfigSyncService, productConfigBomManager);

        Field productConfigManagerFiled = ReflectionUtils.findField(MccsProductConfigSyncService.class, "productConfigManager");
        productConfigManagerFiled.setAccessible(true);
        ReflectionUtils.setField(productConfigManagerFiled, mccsProductConfigSyncService, productConfigManager);

        Field mccsProductConfigManagerFiled = ReflectionUtils.findField(MccsProductConfigSyncService.class, "mccsProductConfigManager");
        mccsProductConfigManagerFiled.setAccessible(true);
        ReflectionUtils.setField(mccsProductConfigManagerFiled,mccsProductConfigSyncService,mccsProductConfigManager);

        MccsProductConfigSyncProgressManager mccsProductConfigSyncProgressManager = PowerMockito.mock(MccsProductConfigSyncProgressManager.class);
        MccsProductConfigSyncProgress mccsProductConfigSyncProgress = new MccsProductConfigSyncProgress();
        mccsProductConfigSyncProgress.setProductIds(Lists.newArrayList(1L, 2L));
        mccsProductConfigSyncProgress.setId("3");
        mccsProductConfigSyncProgress.setStatus(MccsProductConfigSyncProgress.MccsProductSyncProgressStatus.success);

        Field mccsProductConfigSyncProgressManagerField = ReflectionUtils.findField(MccsProductConfigSyncService.class, "mccsProductConfigSyncProgressManager");
        mccsProductConfigSyncProgressManagerField.setAccessible(true);
        ReflectionUtils.setField(mccsProductConfigSyncProgressManagerField,mccsProductConfigSyncService,mccsProductConfigSyncProgressManager);
        PowerMockito.when(mccsProductConfigSyncProgressManager.findByProductIdsAndEnv(any(), any()))
                .thenReturn(mccsProductConfigSyncProgress);
        PowerMockito.when(mccsProductConfigSyncProgressManager.saveNew(any()))
                .thenReturn(mccsProductConfigSyncProgress);
        ArgumentCaptor<MccsProductConfigSyncProgress> captor = ArgumentCaptor.forClass(MccsProductConfigSyncProgress.class);
        mccsProductConfigSyncService.sync(Lists.newArrayList(1L, 2L), ProjectDeployEnv.dev);
        Mockito.verify(mccsProductConfigSyncProgressManager).saveUpdate(captor.capture(), any());
//
        MccsProductConfigSyncProgress value = captor.getValue();
        Assertions.assertThat(value.getStatus()).isEqualTo(MccsProductConfigSyncProgress.MccsProductSyncProgressStatus.success);
    }

}