package com.qpp.test;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Sorts;
import com.qpp.cgp.domain.event.ProductAdminSyncConfigContext;
import com.qpp.cgp.domain.event.ProductAdminSyncConfigManagerOperationalType;
import com.qpp.cgp.domain.product.admin.ProductAdministratorConfig;
import com.qpp.cgp.dto.ProductAdminSyncConfigsToAllEnvManagerDTO;
import com.qpp.cgp.manager.merger.ProductAdminSyncConfigsToAllEnvManager;
import com.qpp.cgp.service.database.mongo.MongoDatabaseFactory;
import com.qpp.cgp.util.JunitUtils;
import com.qpp.core.exception.BusinessException;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/25 10:49
 * @Version 1.0
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({ProductAdminSyncConfigsToAllEnvManager.class, MongoDatabaseFactory.class})
@PowerMockIgnore({"javax.management.*", "org.w3c.dom.*", "org.apache.log4j.*", "org.xml.sax.*", "javax.xml.*", "javax.script.*"})
public class ProductAdminSyncConfigsToAllEnvManagerTests {

    @Test
    public void getMongoCollectionTargetsByNewestEnvIsDevTest() throws InvocationTargetException, IllegalAccessException {
        Method getMongoCollectionTargets = PowerMockito.method(ProductAdminSyncConfigsToAllEnvManager.class, "getMongoCollectionTargets"
                , MongoCollection.class, MongoCollection.class, MongoCollection.class, ProductAdminSyncConfigsToAllEnvManagerDTO.class);
        ProductAdminSyncConfigsToAllEnvManager productAdminSyncConfigsToAllEnvManager = new ProductAdminSyncConfigsToAllEnvManager();
        MongoCollection mongoCollectionMockDev = PowerMockito.mock(MongoCollection.class);
        MongoCollection mongoCollectionMockTest= PowerMockito.mock(MongoCollection.class);
        MongoCollection mongoCollectionMockProd= PowerMockito.mock(MongoCollection.class);
        ProductAdminSyncConfigsToAllEnvManagerDTO productAdminSyncConfigsToAllEnvManagerDTOMock = Mockito.mock(ProductAdminSyncConfigsToAllEnvManagerDTO.class);

        Mockito.when(productAdminSyncConfigsToAllEnvManagerDTOMock.getWhoIsNewestCollection()).thenReturn("Dev");
        List MongoCollectionTargets =(List) getMongoCollectionTargets.invoke(productAdminSyncConfigsToAllEnvManager
               ,mongoCollectionMockDev , mongoCollectionMockTest, mongoCollectionMockProd, productAdminSyncConfigsToAllEnvManagerDTOMock);

        Assert.assertEquals(MongoCollectionTargets.get(0),mongoCollectionMockTest);
        Assert.assertEquals(MongoCollectionTargets.get(1),mongoCollectionMockProd);
    }

    @Test
    public void getMongoCollectionTargetsByNewestEnvIsTestTest() throws InvocationTargetException, IllegalAccessException {
        Method getMongoCollectionTargets = PowerMockito.method(ProductAdminSyncConfigsToAllEnvManager.class, "getMongoCollectionTargets"
                , MongoCollection.class, MongoCollection.class, MongoCollection.class, ProductAdminSyncConfigsToAllEnvManagerDTO.class);
        ProductAdminSyncConfigsToAllEnvManager productAdminSyncConfigsToAllEnvManager = new ProductAdminSyncConfigsToAllEnvManager();
        MongoCollection mongoCollectionMockDev = PowerMockito.mock(MongoCollection.class);
        MongoCollection mongoCollectionMockTest= PowerMockito.mock(MongoCollection.class );
        MongoCollection mongoCollectionMockProd= PowerMockito.mock(MongoCollection.class );
        ProductAdminSyncConfigsToAllEnvManagerDTO productAdminSyncConfigsToAllEnvManagerDTOMock = Mockito.mock(ProductAdminSyncConfigsToAllEnvManagerDTO.class);
        //productAdminSyncConfigsToAllEnvManagerDTOMock.setWhoIsNewestCollection("Dev");
//        ReflectionTestUtils.setField(productAdminSyncConfigsToAllEnvManagerDTOMock,"whoIsNewestCollection","Dev");
        Mockito.when(productAdminSyncConfigsToAllEnvManagerDTOMock.getWhoIsNewestCollection()).thenReturn("Test");
        List MongoCollectionTargets =(List) getMongoCollectionTargets.invoke(productAdminSyncConfigsToAllEnvManager
                ,mongoCollectionMockDev , mongoCollectionMockTest, mongoCollectionMockProd, productAdminSyncConfigsToAllEnvManagerDTOMock);

        Assert.assertEquals(MongoCollectionTargets.get(0),mongoCollectionMockDev);
        Assert.assertEquals(MongoCollectionTargets.get(1),mongoCollectionMockProd);
    }

    @Test
    public void getMongoCollectionTargetsByNewestEnvIsProdTest() throws InvocationTargetException, IllegalAccessException {
        Method getMongoCollectionTargets = PowerMockito.method(ProductAdminSyncConfigsToAllEnvManager.class, "getMongoCollectionTargets"
                , MongoCollection.class, MongoCollection.class, MongoCollection.class, ProductAdminSyncConfigsToAllEnvManagerDTO.class);
        ProductAdminSyncConfigsToAllEnvManager productAdminSyncConfigsToAllEnvManager = new ProductAdminSyncConfigsToAllEnvManager();
        MongoCollection mongoCollectionMockDev = PowerMockito.mock(MongoCollection.class);
        MongoCollection mongoCollectionMockTest= PowerMockito.mock(MongoCollection.class );
        MongoCollection mongoCollectionMockProd= PowerMockito.mock(MongoCollection.class );
        ProductAdminSyncConfigsToAllEnvManagerDTO productAdminSyncConfigsToAllEnvManagerDTOMock = Mockito.mock(ProductAdminSyncConfigsToAllEnvManagerDTO.class);
        //productAdminSyncConfigsToAllEnvManagerDTOMock.setWhoIsNewestCollection("Dev");
//        ReflectionTestUtils.setField(productAdminSyncConfigsToAllEnvManagerDTOMock,"whoIsNewestCollection","Dev");
        Mockito.when(productAdminSyncConfigsToAllEnvManagerDTOMock.getWhoIsNewestCollection()).thenReturn("Prod");
        List MongoCollectionTargets =(List) getMongoCollectionTargets.invoke(productAdminSyncConfigsToAllEnvManager
                ,mongoCollectionMockDev , mongoCollectionMockTest, mongoCollectionMockProd, productAdminSyncConfigsToAllEnvManagerDTOMock);

        Assert.assertEquals(MongoCollectionTargets.get(0),mongoCollectionMockDev);
        Assert.assertEquals(MongoCollectionTargets.get(1),mongoCollectionMockTest);
    }

    @Test
    public void byTestGetNewestCollectionTest() throws InvocationTargetException, IllegalAccessException {
        Method getNewestCollection = PowerMockito.method(ProductAdminSyncConfigsToAllEnvManager.class
                , "getNewestCollection", MongoCollection.class, MongoCollection.class, MongoCollection.class);

        ProductAdminSyncConfigsToAllEnvManager productAdminSyncConfigsToAllEnvManager = new ProductAdminSyncConfigsToAllEnvManager();

        MongoCollection<Document> productAdministratorConfigsDevCollection=PowerMockito.mock(MongoCollection.class);
        MongoCollection<Document> productAdministratorConfigsTestCollection=PowerMockito.mock(MongoCollection.class);
        MongoCollection<Document> productAdministratorConfigsProdCollection=PowerMockito.mock(MongoCollection.class);

        FindIterable<Document> findIterableDev=PowerMockito.mock(FindIterable.class);
        FindIterable<Document> findIterableTest=PowerMockito.mock(FindIterable.class);
        FindIterable<Document> findIterableProd=PowerMockito.mock(FindIterable.class);

        FindIterable<Document> findIterableDevSort=PowerMockito.mock(FindIterable.class);
        FindIterable<Document> findIterableTestSort=PowerMockito.mock(FindIterable.class);
        FindIterable<Document> findIterableProdSort=PowerMockito.mock(FindIterable.class);

        Document documentNewestDev=PowerMockito.mock(Document.class);
        Document documentNewestTest=PowerMockito.mock(Document.class);
        Document documentNewestPord=PowerMockito.mock(Document.class);

        Date modifiedDateDev = new Date();
        Date modifiedDateTest =new Date();
        Date modifiedDateProd =new Date();

        modifiedDateDev.setHours(1);
        modifiedDateTest.setHours(3);
        modifiedDateProd.setHours(2);

        System.out.println(modifiedDateDev);
        System.out.println(modifiedDateTest);
        System.out.println(modifiedDateProd);

        PowerMockito.when(productAdministratorConfigsDevCollection.find()).thenReturn(findIterableDev);
        PowerMockito.when(productAdministratorConfigsTestCollection.find()).thenReturn(findIterableTest);
        PowerMockito.when(productAdministratorConfigsProdCollection.find()).thenReturn(findIterableProd);

        Bson descending = Sorts.descending("modifiedDate");
        PowerMockito.when(findIterableDev.sort(descending)).thenReturn(findIterableDevSort);
        PowerMockito.when(findIterableTest.sort(descending)).thenReturn(findIterableTestSort);
        PowerMockito.when(findIterableProd.sort(descending)).thenReturn(findIterableProdSort);

        PowerMockito.when(findIterableDevSort.first()).thenReturn(documentNewestDev);
        PowerMockito.when(findIterableTestSort.first()).thenReturn(documentNewestTest);
        PowerMockito.when(findIterableProdSort.first()).thenReturn(documentNewestPord);

        PowerMockito.when(documentNewestDev.get("modifiedDate", Date.class)).thenReturn(modifiedDateDev);
        PowerMockito.when(documentNewestTest.get("modifiedDate", Date.class)).thenReturn(modifiedDateTest);
        PowerMockito.when(documentNewestPord.get("modifiedDate", Date.class)).thenReturn(modifiedDateProd);

        ProductAdminSyncConfigsToAllEnvManagerDTO result=(ProductAdminSyncConfigsToAllEnvManagerDTO)getNewestCollection.invoke(
                productAdminSyncConfigsToAllEnvManager, productAdministratorConfigsDevCollection
                , productAdministratorConfigsTestCollection, productAdministratorConfigsProdCollection);

        Assert.assertEquals(result.getNewestCollection(),productAdministratorConfigsTestCollection);
        Assert.assertEquals(result.getWhoIsNewestCollection(),"Test");

    }

    @Test
    public void byDevGetNewestCollectionTest() throws InvocationTargetException, IllegalAccessException {
        Method getNewestCollection = PowerMockito.method(ProductAdminSyncConfigsToAllEnvManager.class
                , "getNewestCollection", MongoCollection.class, MongoCollection.class, MongoCollection.class);

        ProductAdminSyncConfigsToAllEnvManager productAdminSyncConfigsToAllEnvManager = new ProductAdminSyncConfigsToAllEnvManager();

        MongoCollection<Document> productAdministratorConfigsDevCollection=PowerMockito.mock(MongoCollection.class);
        MongoCollection<Document> productAdministratorConfigsTestCollection=PowerMockito.mock(MongoCollection.class);
        MongoCollection<Document> productAdministratorConfigsProdCollection=PowerMockito.mock(MongoCollection.class);

        FindIterable<Document> findIterableDev=PowerMockito.mock(FindIterable.class);
        FindIterable<Document> findIterableTest=PowerMockito.mock(FindIterable.class);
        FindIterable<Document> findIterableProd=PowerMockito.mock(FindIterable.class);

        FindIterable<Document> findIterableDevSort=PowerMockito.mock(FindIterable.class);
        FindIterable<Document> findIterableTestSort=PowerMockito.mock(FindIterable.class);
        FindIterable<Document> findIterableProdSort=PowerMockito.mock(FindIterable.class);

        Document documentNewestDev=PowerMockito.mock(Document.class);
        Document documentNewestTest=PowerMockito.mock(Document.class);
        Document documentNewestPord=PowerMockito.mock(Document.class);

        Date modifiedDateDev = new Date();
        Date modifiedDateTest =new Date();
        Date modifiedDateProd =new Date();

        modifiedDateDev.setHours(3);
        modifiedDateTest.setHours(1);
        modifiedDateProd.setHours(2);

        System.out.println(modifiedDateDev);
        System.out.println(modifiedDateTest);
        System.out.println(modifiedDateProd);

        PowerMockito.when(productAdministratorConfigsDevCollection.find()).thenReturn(findIterableDev);
        PowerMockito.when(productAdministratorConfigsTestCollection.find()).thenReturn(findIterableTest);
        PowerMockito.when(productAdministratorConfigsProdCollection.find()).thenReturn(findIterableProd);

        Bson descending = Sorts.descending("modifiedDate");
        PowerMockito.when(findIterableDev.sort(descending)).thenReturn(findIterableDevSort);
        PowerMockito.when(findIterableTest.sort(descending)).thenReturn(findIterableTestSort);
        PowerMockito.when(findIterableProd.sort(descending)).thenReturn(findIterableProdSort);

        PowerMockito.when(findIterableDevSort.first()).thenReturn(documentNewestDev);
        PowerMockito.when(findIterableTestSort.first()).thenReturn(documentNewestTest);
        PowerMockito.when(findIterableProdSort.first()).thenReturn(documentNewestPord);

        PowerMockito.when(documentNewestDev.get("modifiedDate", Date.class)).thenReturn(modifiedDateDev);
        PowerMockito.when(documentNewestTest.get("modifiedDate", Date.class)).thenReturn(modifiedDateTest);
        PowerMockito.when(documentNewestPord.get("modifiedDate", Date.class)).thenReturn(modifiedDateProd);

        ProductAdminSyncConfigsToAllEnvManagerDTO result=(ProductAdminSyncConfigsToAllEnvManagerDTO)getNewestCollection.invoke(
                productAdminSyncConfigsToAllEnvManager, productAdministratorConfigsDevCollection
                , productAdministratorConfigsTestCollection, productAdministratorConfigsProdCollection);

        Assert.assertEquals(result.getNewestCollection(),productAdministratorConfigsDevCollection);
        Assert.assertEquals(result.getWhoIsNewestCollection(),"Dev");
    }

    @Test
    public void byProdGetNewestCollectionTest() throws InvocationTargetException, IllegalAccessException {
        Method getNewestCollection = PowerMockito.method(ProductAdminSyncConfigsToAllEnvManager.class,"getNewestCollection", MongoCollection.class,MongoCollection.class,MongoCollection.class);

        ProductAdminSyncConfigsToAllEnvManager productAdminSyncConfigsToAllEnvManager = new ProductAdminSyncConfigsToAllEnvManager();

        MongoCollection<Document> productAdministratorConfigsDevCollection=PowerMockito.mock(MongoCollection.class);
        MongoCollection<Document> productAdministratorConfigsTestCollection=PowerMockito.mock(MongoCollection.class);
        MongoCollection<Document> productAdministratorConfigsProdCollection=PowerMockito.mock(MongoCollection.class);

        FindIterable<Document> findIterableDev=PowerMockito.mock(FindIterable.class);
        FindIterable<Document> findIterableTest=PowerMockito.mock(FindIterable.class);
        FindIterable<Document> findIterableProd=PowerMockito.mock(FindIterable.class);

        FindIterable<Document> findIterableDevSort=PowerMockito.mock(FindIterable.class);
        FindIterable<Document> findIterableTestSort=PowerMockito.mock(FindIterable.class);
        FindIterable<Document> findIterableProdSort=PowerMockito.mock(FindIterable.class);

        Document documentNewestDev=PowerMockito.mock(Document.class);
        Document documentNewestTest=PowerMockito.mock(Document.class);
        Document documentNewestPord=PowerMockito.mock(Document.class);

        Date modifiedDateDev = new Date();
        Date modifiedDateTest =new Date();
        Date modifiedDateProd =new Date();

        modifiedDateDev.setHours(2);
        modifiedDateTest.setHours(1);
        modifiedDateProd.setHours(3);

        System.out.println(modifiedDateDev);
        System.out.println(modifiedDateTest);
        System.out.println(modifiedDateProd);

        PowerMockito.when(productAdministratorConfigsDevCollection.find()).thenReturn(findIterableDev);
        PowerMockito.when(productAdministratorConfigsTestCollection.find()).thenReturn(findIterableTest);
        PowerMockito.when(productAdministratorConfigsProdCollection.find()).thenReturn(findIterableProd);

        Bson descending = Sorts.descending("modifiedDate");
        PowerMockito.when(findIterableDev.sort(descending)).thenReturn(findIterableDevSort);
        PowerMockito.when(findIterableTest.sort(descending)).thenReturn(findIterableTestSort);
        PowerMockito.when(findIterableProd.sort(descending)).thenReturn(findIterableProdSort);

        PowerMockito.when(findIterableDevSort.first()).thenReturn(documentNewestDev);
        PowerMockito.when(findIterableTestSort.first()).thenReturn(documentNewestTest);
        PowerMockito.when(findIterableProdSort.first()).thenReturn(documentNewestPord);

        PowerMockito.when(documentNewestDev.get("modifiedDate", Date.class)).thenReturn(modifiedDateDev);
        PowerMockito.when(documentNewestTest.get("modifiedDate", Date.class)).thenReturn(modifiedDateTest);
        PowerMockito.when(documentNewestPord.get("modifiedDate", Date.class)).thenReturn(modifiedDateProd);

        ProductAdminSyncConfigsToAllEnvManagerDTO result=(ProductAdminSyncConfigsToAllEnvManagerDTO)getNewestCollection.invoke(
                productAdminSyncConfigsToAllEnvManager, productAdministratorConfigsDevCollection
                , productAdministratorConfigsTestCollection, productAdministratorConfigsProdCollection);

        Assert.assertEquals(result.getNewestCollection(),productAdministratorConfigsProdCollection);
        Assert.assertEquals(result.getWhoIsNewestCollection(),"Prod");
    }

    @Test
    public void byDeleteExecuteSyncProductAdminSyncConfigsTest() throws InvocationTargetException, IllegalAccessException {
        Method executeSyncProductAdminSyncConfigs = PowerMockito.method(ProductAdminSyncConfigsToAllEnvManager.class, "executeSyncProductAdminSyncConfigs"
                , ProductAdminSyncConfigContext.class, List.class);
        ProductAdminSyncConfigContext productAdminSyncConfigContextMock = PowerMockito.mock(ProductAdminSyncConfigContext.class);
        ProductAdminSyncConfigsToAllEnvManager productAdminSyncConfigsToAllEnvManager=new ProductAdminSyncConfigsToAllEnvManager();

        ProductAdministratorConfig targetProductAdministratorConfig=new ProductAdministratorConfig();
        targetProductAdministratorConfig.setId("123");

        List<MongoCollection<Document>> mongoCollectionTargets =new ArrayList<>();
        MongoCollection<Document> documentMongoCollection1=PowerMockito.mock(MongoCollection.class);
        MongoCollection<Document> documentMongoCollection2=PowerMockito.mock(MongoCollection.class);
        mongoCollectionTargets.add(documentMongoCollection1);
        mongoCollectionTargets.add(documentMongoCollection2);

        PowerMockito.when(productAdminSyncConfigContextMock.getProductAdministratorConfig()).thenReturn(targetProductAdministratorConfig);
        PowerMockito.when(productAdminSyncConfigContextMock.getOperationType()).thenReturn(ProductAdminSyncConfigManagerOperationalType.DETELE);
        Exception e=null;
        try {
            executeSyncProductAdminSyncConfigs.invoke(productAdminSyncConfigsToAllEnvManager,productAdminSyncConfigContextMock,mongoCollectionTargets);

        }catch (Exception exception){
            e=exception;
        }

        Mockito.verify(productAdminSyncConfigContextMock,Mockito.times(1)).getProductAdministratorConfig();
        Mockito.verify(productAdminSyncConfigContextMock,Mockito.times(1)).getOperationType();

    }

    @Test
    public void byUpdateExecuteSyncProductAdminSyncConfigsTest() throws InvocationTargetException, IllegalAccessException, JsonProcessingException {
        Method executeSyncProductAdminSyncConfigs = PowerMockito.method(ProductAdminSyncConfigsToAllEnvManager.class, "executeSyncProductAdminSyncConfigs"
                , ProductAdminSyncConfigContext.class, List.class);
        ProductAdminSyncConfigContext productAdminSyncConfigContextMock = PowerMockito.mock(ProductAdminSyncConfigContext.class);
        ProductAdminSyncConfigsToAllEnvManager productAdminSyncConfigsToAllEnvManager=new ProductAdminSyncConfigsToAllEnvManager();

        ProductAdministratorConfig targetProductAdministratorConfig=new ProductAdministratorConfig();
        targetProductAdministratorConfig.setId("123");

        List<MongoCollection<Document>> mongoCollectionTargets =new ArrayList<>();
        MongoCollection<Document> documentMongoCollection1=PowerMockito.mock(MongoCollection.class);
        MongoCollection<Document> documentMongoCollection2=PowerMockito.mock(MongoCollection.class);
        mongoCollectionTargets.add(documentMongoCollection1);
        mongoCollectionTargets.add(documentMongoCollection2);

        PowerMockito.when(productAdminSyncConfigContextMock.getProductAdministratorConfig()).thenReturn(targetProductAdministratorConfig);
        PowerMockito.when(productAdminSyncConfigContextMock.getOperationType()).thenReturn(ProductAdminSyncConfigManagerOperationalType.UPDATE);
        Exception e=null;
        try {
            executeSyncProductAdminSyncConfigs.invoke(productAdminSyncConfigsToAllEnvManager,productAdminSyncConfigContextMock,mongoCollectionTargets);

        }catch (Exception exception){
            e=exception;
        }

        Mockito.verify(productAdminSyncConfigContextMock,Mockito.times(1)).getProductAdministratorConfig();
        Mockito.verify(productAdminSyncConfigContextMock,Mockito.times(2)).getOperationType();
    }

    @Test
    public void bynewAddExecuteSyncProductAdminSyncConfigsTest() throws InvocationTargetException, IllegalAccessException, JsonProcessingException {
        Method executeSyncProductAdminSyncConfigs = PowerMockito.method(ProductAdminSyncConfigsToAllEnvManager.class, "executeSyncProductAdminSyncConfigs"
                , ProductAdminSyncConfigContext.class, List.class);
        ProductAdminSyncConfigContext productAdminSyncConfigContextMock = PowerMockito.mock(ProductAdminSyncConfigContext.class);
        ProductAdminSyncConfigsToAllEnvManager productAdminSyncConfigsToAllEnvManager=new ProductAdminSyncConfigsToAllEnvManager();

        ProductAdministratorConfig targetProductAdministratorConfig=new ProductAdministratorConfig();
        targetProductAdministratorConfig.setId("123");

        List<MongoCollection<Document>> mongoCollectionTargets =new ArrayList<>();
        MongoCollection<Document> documentMongoCollection1=PowerMockito.mock(MongoCollection.class);
        MongoCollection<Document> documentMongoCollection2=PowerMockito.mock(MongoCollection.class);
        mongoCollectionTargets.add(documentMongoCollection1);
        mongoCollectionTargets.add(documentMongoCollection2);

        PowerMockito.when(productAdminSyncConfigContextMock.getProductAdministratorConfig()).thenReturn(targetProductAdministratorConfig);
        PowerMockito.when(productAdminSyncConfigContextMock.getOperationType()).thenReturn(ProductAdminSyncConfigManagerOperationalType.NEWAdd);
        Exception e=null;
        try {
            executeSyncProductAdminSyncConfigs.invoke(productAdminSyncConfigsToAllEnvManager,productAdminSyncConfigContextMock,mongoCollectionTargets);

        }catch (Exception exception){
            e=exception;
        }

        Mockito.verify(productAdminSyncConfigContextMock,Mockito.times(1)).getProductAdministratorConfig();
        Mockito.verify(productAdminSyncConfigContextMock,Mockito.times(4)).getOperationType();
    }

    @Test
    public void byCollectionTargetsIsEmptyExecuteSyncProductAdminSyncConfigsTest() throws InvocationTargetException, IllegalAccessException, JsonProcessingException {
        Method executeSyncProductAdminSyncConfigs = PowerMockito.method(ProductAdminSyncConfigsToAllEnvManager.class, "executeSyncProductAdminSyncConfigs"
                , ProductAdminSyncConfigContext.class, List.class);
        ProductAdminSyncConfigContext productAdminSyncConfigContextMock = PowerMockito.mock(ProductAdminSyncConfigContext.class);
        ProductAdminSyncConfigsToAllEnvManager productAdminSyncConfigsToAllEnvManager=new ProductAdminSyncConfigsToAllEnvManager();

        ProductAdministratorConfig targetProductAdministratorConfig=new ProductAdministratorConfig();
        targetProductAdministratorConfig.setId("123");

        List<MongoCollection<Document>> mongoCollectionTargets =new ArrayList<>();
        Exception e=null;
        try {
            executeSyncProductAdminSyncConfigs.invoke(productAdminSyncConfigsToAllEnvManager,productAdminSyncConfigContextMock,mongoCollectionTargets);

        }catch (Exception exception){
            e=exception;
        }

        Assert.assertNotNull(e);
        BusinessException businessException = JunitUtils.getBusinessException(e);
        Assert.assertEquals(businessException.getCode(),6100);
    }

    @Test
    public void byCollectionTargetsIsGtTwoExecuteSyncProductAdminSyncConfigsTest() throws InvocationTargetException, IllegalAccessException, JsonProcessingException {
        Method executeSyncProductAdminSyncConfigs = PowerMockito.method(ProductAdminSyncConfigsToAllEnvManager.class, "executeSyncProductAdminSyncConfigs"
                , ProductAdminSyncConfigContext.class, List.class);
        ProductAdminSyncConfigContext productAdminSyncConfigContextMock = PowerMockito.mock(ProductAdminSyncConfigContext.class);
        ProductAdminSyncConfigsToAllEnvManager productAdminSyncConfigsToAllEnvManager=new ProductAdminSyncConfigsToAllEnvManager();

        ProductAdministratorConfig targetProductAdministratorConfig=new ProductAdministratorConfig();
        targetProductAdministratorConfig.setId("123");

        List<MongoCollection<Document>> mongoCollectionTargets =new ArrayList<>();
        MongoCollection<Document> documentMongoCollection1=PowerMockito.mock(MongoCollection.class);
        MongoCollection<Document> documentMongoCollection2=PowerMockito.mock(MongoCollection.class);
        MongoCollection<Document> documentMongoCollection3=PowerMockito.mock(MongoCollection.class);

        mongoCollectionTargets.add(documentMongoCollection1);
        mongoCollectionTargets.add(documentMongoCollection2);
        mongoCollectionTargets.add(documentMongoCollection3);

        PowerMockito.when(productAdminSyncConfigContextMock.getProductAdministratorConfig()).thenReturn(targetProductAdministratorConfig);
        PowerMockito.when(productAdminSyncConfigContextMock.getOperationType()).thenReturn(ProductAdminSyncConfigManagerOperationalType.NEWAdd);
        Exception e=null;
        try {
            executeSyncProductAdminSyncConfigs.invoke(productAdminSyncConfigsToAllEnvManager,productAdminSyncConfigContextMock,mongoCollectionTargets);

        }catch (Exception exception){
            e=exception;
        }

        Assert.assertNull(e);
        Mockito.verify(productAdminSyncConfigContextMock,Mockito.times(1)).getProductAdministratorConfig();
        Mockito.verify(productAdminSyncConfigContextMock,Mockito.times(4)).getOperationType();
    }

}
