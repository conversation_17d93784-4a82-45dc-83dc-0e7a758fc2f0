//package com.qpp.test;
//
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.qpp.cgp.service.database.mongo.CollectionQuery;
//import com.qpp.cgp.service.database.mongo.MongoConfig;
//import com.qpp.cgp.service.database.mongo.MongoMergeQuery;
//import com.qpp.cgp.service.database.mongo.MongoMerger;
//import org.junit.Test;
//
//import java.util.List;
//import java.util.Optional;
//
///**
// * Created by smart on 9/29/2017.
// */
//public class MongoMerger86Test {
//
//
//    private MongoMerger mongoMerger = new MongoMerger();
//
//    @Test
//    public void test() throws Exception {
//
//        String host = "*************";
//        String host86 = "************";
//        int port = 27017;
//
//        String username = "developer";
//        String password = "Dev!123a";
//        String username86 = "cgp2";
//        String password86 = "Cgp86db!123";
//        String sourceDatabase = "cgp2_dev";
//        String targetDatabase = "cgp2";
//
//        MongoConfig sourceMongoConfig = new MongoConfig(host, port, username, password, sourceDatabase);
//        MongoConfig targetMongoConfig = new MongoConfig(host86, port, username86, password86, targetDatabase);
//
//
//        MongoMergeQuery mongoMergeQuery = new MongoMergeQuery();
//
//        this.getTableNames().stream().forEach(collectionName -> this.addTableQuery(collectionName, null, mongoMergeQuery));
//
//        mongoMerger.mergeData( targetMongoConfig, mongoMergeQuery);
//    }
//
//    public void addTableQuery(String tableName, String filter, MongoMergeQuery databaseMergeQuery) {
//
//        CollectionQuery tableQuery = new CollectionQuery();
//        tableQuery.setCollectionName(tableName);
//        tableQuery.setFilter(Optional.ofNullable(filter));
//
//        databaseMergeQuery.getCollectionQueries().add(tableQuery);
//
//    }
//
//    public List<String> getTableNames() throws Exception {
//
//        //language=JSON
//        String tableNames = "[\n" +
//                "  \"builderconfigs\",\n" +
//                "  \"dsbuildertemplateconfigs\",\n" +
//                "  \"dsdatasources\",\n" +
//                "  \"dspagetemplateconfigs\",\n" +
//                "  \"dssheettemplateconfigs\",\n" +
//                "  \"dsurltemplates\",\n" +
//                "  \"materials\",\n" +
//                "  \"materialviewtypes\",\n" +
//                "  \"pagecontentschemas\",\n" +
//                "  \"productattributeconstraints\",\n" +
//                "  \"productmaterialviewtypes\",\n" +
//                "  \"rtattributedefs\",\n" +
//                "  \"rttypes\",\n" +
//                "  \"rtobjects\",\n" +
//                "  \"skuattributeconstraints\",\n" +
//                "  \"variabledatasources\",\n" +
//                "  \"imageintegerationconfigs\",\n" +
//                "  \"partnerproducts\",\n" +
//                "  \"materialtypetospuconfigs\",\n"+
//                "  \"multidiscretevalueconstraints\",\n"+
//                "  \"builderviewconfigs\",\n"+
//                "  \"builderwizardconfigs\",\n"+
//                "  \"threedmodels\",\n"+
//                "  \"partnerproducts\",\n"+
//                "  \"materialcategories\",\n"+
//                "  \"producecomponentconfigs\",\n"+
//                "  \"printers\",\n"+
//                "  \"customselements\",\n"+
//                "  \"skuattributesingleconstraints\"\n"+
//                "]";
//
//        return new ObjectMapper().readValue(tableNames, List.class);
//
//    }
//
//}
