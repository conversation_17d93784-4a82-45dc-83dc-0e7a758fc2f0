//package com.qpp.cgp.service.database.service;
//
//import com.mongodb.client.MongoCollection;
//import com.mongodb.client.MongoDatabase;
//import com.qpp.cgp.service.database.dto.WebsiteMongoMergeInfo;
//import com.qpp.cgp.service.database.mongo.MongoDatabaseFactory;
//import com.qpp.cgp.domain.product.sync.ProjectDeployEnv;
//import org.bson.Document;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.Arrays;
//import java.util.List;
//import java.util.Set;
//
//@RunWith(SpringRunner.class)
//@SpringBootTest
//public class WebsiteMongoMergerTest {
//
//    @Autowired
//    private WebsiteMongoMerger websiteMongoMerger;
//
//    @Test
//    public void mergeTest() {
//        // 目标环境
//        ProjectDeployEnv env = ProjectDeployEnv.local;
//        // 同步的websiteIds
//        List<Long> websiteIds = Arrays.asList(11L);
//        // 同步
//        WebsiteMongoMergeInfo mergeResult = websiteMongoMerger.merge(websiteIds, env);
//        // 同步结果检测
//        checkContainsIds(mergeResult, env);
//    }
//
//    private void checkContainsIds(WebsiteMongoMergeInfo info, ProjectDeployEnv env) {
//        // 判断 目标环境数据表中 是否存在 原环境数据表中数据id
//        MongoDatabase configMongoDatabase = MongoDatabaseFactory.getMongoDatabase(env, "config");
//
//        Set<Long> configurationIds = info.getConfigurationIds();
//        MongoCollection<Document> configurations = configMongoDatabase.getCollection("configurations");
//        Assert.assertEquals(configurations.count(Document.parse(String.format("{_id : {$in : %s}}", configurationIds))), configurationIds.size());
//
//        Set<Long> currencyIds = info.getCurrencyIds();
//        MongoCollection<Document> currencies = configMongoDatabase.getCollection("currencies");
//        Assert.assertEquals(currencies.count(Document.parse(String.format("{_id : {$in : %s}}", currencyIds))), currencyIds.size());
//
//        Set<Long> numberRuleIds = info.getNumberRuleIds();
//        MongoCollection<Document> numberRules = configMongoDatabase.getCollection("numberrules");
//        Assert.assertEquals(numberRules.count(Document.parse(String.format("{_id : {$in : %s}}", numberRuleIds))), numberRuleIds.size());
//
//        Set<Long> paymentModuleConfigIds = info.getPaymentModuleConfigIds();
//        MongoCollection<Document> paymentModuleConfigs = configMongoDatabase.getCollection("paymentmoduleconfigs");
//        Assert.assertEquals(paymentModuleConfigs.count(Document.parse(String.format("{_id : {$in : %s}}", paymentModuleConfigIds))), paymentModuleConfigIds.size());
//
//        Set<Long> shippingModuleConfigIds = info.getShippingModuleConfigIds();
//        MongoCollection<Document> shippingModuleConfigs = configMongoDatabase.getCollection("shippingmoduleconfigs");
//        Assert.assertEquals(shippingModuleConfigs.count(Document.parse(String.format("{_id : {$in : %s}}", shippingModuleConfigIds))), shippingModuleConfigIds.size());
//
//        Set<Long> orderTotalModuleConfigIds = info.getOrderTotalModuleConfigIds();
//        MongoCollection<Document> orderTotalModuleConfigs = configMongoDatabase.getCollection("ordertotalmoduleconfigs");
//        Assert.assertEquals(orderTotalModuleConfigs.count(Document.parse(String.format("{_id : {$in : %s}}", orderTotalModuleConfigIds))), orderTotalModuleConfigIds.size());
//
//        Set<Long> postageEmsIds = info.getPostageEmsIds();
//        MongoCollection<Document> postageEms = configMongoDatabase.getCollection("postageems");
//        Assert.assertEquals(postageEms.count(Document.parse(String.format("{_id : {$in : %s}}", postageEmsIds))), postageEmsIds.size());
//
//        Set<Long> postageExpressIds = info.getPostageExpressIds();
//        MongoCollection<Document> postageExpress = configMongoDatabase.getCollection("postageexpress");
//        Assert.assertEquals(postageExpress.count(Document.parse(String.format("{_id : {$in : %s}}", postageExpressIds))), postageExpressIds.size());
//
//        Set<Long> postageSfIds = info.getPostageSfIds();
//        MongoCollection<Document> postageSf = configMongoDatabase.getCollection("postagesf");
//        Assert.assertEquals(postageSf.count(Document.parse(String.format("{_id : {$in : %s}}", postageSfIds))), postageSfIds.size());
//
//        Set<Long> postageStandardIds = info.getPostageStandardIds();
//        MongoCollection<Document> postageStandard = configMongoDatabase.getCollection("postagestandard");
//        Assert.assertEquals(postageStandard.count(Document.parse(String.format("{_id : {$in : %s}}", postageStandardIds))), postageStandardIds.size());
//
//        Set<Long> postageZtIds = info.getPostageZtIds();
//        MongoCollection<Document> postageZt = configMongoDatabase.getCollection("postagezt");
//        Assert.assertEquals(postageZt.count(Document.parse(String.format("{_id : {$in : %s}}", postageZtIds))), postageZtIds.size());
//    }
//}
