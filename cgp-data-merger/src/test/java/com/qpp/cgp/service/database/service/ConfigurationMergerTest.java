//package com.qpp.cgp.service.database.service;
//
//import com.qpp.cgp.service.database.mysql.MysqlConfig;
//import com.qpp.cgp.service.database.mysql.MysqlMergeQuery;
//import com.qpp.cgp.service.database.mysql.MysqlMerger;
//import com.qpp.cgp.service.database.mysql.TableQuery;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.Arrays;
//import java.util.List;
//
///**
// * <AUTHOR> <PERSON> 2019/4/23 18:48
// */
//
//@RunWith(SpringRunner.class)
//@SpringBootTest
//public class ConfigurationMergerTest {
//
//
//    @Autowired
//    private MysqlMerger mysqlMerger;
//
//    @Test
//    public void test() {
//
//        MysqlMergeQuery        query        = new MysqlMergeQuery();
//        final List<TableQuery> tableQueries = query.getTableQueries();
//
//        //添加固定需要同步的表
//        List<String> fixTables = Arrays.asList("cgp_configuration"
//                , "cgp_cms_entity_filter"
//                , "cgp_cms_entity_query"
//                , "cgp_cms_page"
//                , "cgp_cms_publish"
//                , "cgp_cms_publish_goal"
//                , "cgp_cms_publish_task",
//                "cgp_cms_variable", "cgp_cms_variable_to_cms_page");
//
//        for (String fixTable : fixTables) {
//            TableQuery tableQuery = new TableQuery();
//            tableQuery.setTableName(fixTable);
//            tableQueries.add(tableQuery);
//        }
//
//        mysqlMerger.mergeData(this.buildMysqlConfig(), query);
//
//    }
//
//    private MysqlConfig buildMysqlConfig() {
//        String driver    = "com.mysql.jdbc.Driver";
//        String targetUrl = "**********************************************************************************************";
//        String username  = "developer";
//        String password  = "Dev!123a";
//
//        MysqlConfig targetConfig = new MysqlConfig(targetUrl, username, password, driver);
//        return targetConfig;
//    }
//
//}
