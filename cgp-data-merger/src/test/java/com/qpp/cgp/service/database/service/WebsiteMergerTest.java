//package com.qpp.cgp.service.database.service;
//
//import com.qpp.cgp.service.database.mysql.MysqlConfig;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
///**
// * <AUTHOR> <PERSON> 2020/1/15 16:58
// */
//@RunWith(SpringRunner.class)
//@SpringBootTest
//public class WebsiteMergerTest {
//
//    @Autowired
//    private WebsiteMerger websiteMerger;
//
//    @Test
//    public void merger() {
//
//        Long websiteId = 1899706L;
//        final MysqlConfig mysqlConfig = this.buildMysqlConfig();
//        websiteMerger.merger(websiteId, mysqlConfig);
//
//    }
//
//
//    private MysqlConfig buildMysqlConfig() {
//        String driver    = "com.mysql.jdbc.Driver";
//        String targetUrl = "**********************************************************************************************";
//        String username  = "developer";
//        String password  = "Dev!123a";
//
//        MysqlConfig targetConfig = new MysqlConfig(targetUrl, username, password, driver);
//        return targetConfig;
//    }
//
//}