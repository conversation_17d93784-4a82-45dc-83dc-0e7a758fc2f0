//package com.qpp.cgp.service.database.service;
//
//import com.qpp.cgp.service.database.dto.ProductMergeInfo;
//import com.qpp.cgp.service.database.mongo.MongoMergeQuery;
//import com.qpp.cgp.service.database.mysql.MysqlMergeQuery;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.Arrays;
//
///**
// * <AUTHOR> Lee 2019/4/3 15:33
// */
//@RunWith(SpringRunner.class)
//@SpringBootTest
//public class ProductMergeInfoGeneratorTest {
//
//    @Autowired
//    private ProductMergeInfoGenerator productMergeInfoGenerator;
//
//    @Autowired
//    private ProductMongoQueryGenerator productMongoQueryGenerator;
//
//    @Autowired
//    private ProductMysqlQueryGenerator productMysqlQueryGenerator;
//
//
//    @Test
//    public void generateProductMergeInfo() {
//
//        final ProductMergeInfo productMergeInfo = productMergeInfoGenerator.generateProductMergeInfo(Arrays.asList(1532004L,1458319L));
//
//        final MongoMergeQuery mongoMergeQuery = productMongoQueryGenerator.generateQuery(productMergeInfo.getProductMongoMergeInfo());
//        final MysqlMergeQuery mysqlMergeQuery = productMysqlQueryGenerator.generateQuery(productMergeInfo.getProductMysqlMergeInfo());
//
//        System.out.println(mongoMergeQuery);
//
//    }
//}