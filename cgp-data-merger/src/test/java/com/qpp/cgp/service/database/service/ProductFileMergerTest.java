package com.qpp.cgp.service.database.service;

import com.qpp.cgp.domain.product.Product;
import com.qpp.cgp.domain.product.ProductStatus;
import com.qpp.cgp.domain.product.SkuProduct;
import com.qpp.cgp.repository.product.SkuProductRepository;
import com.qpp.cgp.service.database.mongo.MongoConfig;
import com.qpp.cgp.service.database.mysql.MysqlConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Lee 2019/4/3 17:18
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class ProductFileMergerTest {

    @Autowired
    private ProductMerger productMerger;

    @Autowired
    private SkuProductRepository skuProductRepository;

    
    @Test
    public void mergeProduct() {

        List<Long> configurableIds = Arrays.asList(986640L);

        List<SkuProduct> byConfigurableProductId = skuProductRepository.findByConfigurableProductId(986640L);

        final Set<Long> collect = byConfigurableProductId.stream().filter(product -> product.getStatus().equals(ProductStatus.ACTIVE)).map(Product::getId).collect(Collectors.toSet());

//        byConfigurableProductId = skuProductRepository.findByConfigurableProductId(1532004L);
//
//        collect.addAll(byConfigurableProductId.stream().filter(product -> product.getStatus().equals(ProductStatus.ACTIVE)).map(Product::getId).collect(Collectors.toSet()));

        List<Long> productIds = new ArrayList<>();
        productIds.addAll(configurableIds);
        productIds.addAll(collect);

//        productMerger.mergeProduct(productIds, this.buildMysqlConfig(), this.buildMongoConfig());

    }

//    private MysqlConfig buildMysqlConfig() {
//        String driver    = "com.mysql.jdbc.Driver";
//        String targetUrl = "****************************************************************************************";
//        String username  = "cgp2";
//        String password  = "<EMAIL>!";
//
//        return new MysqlConfig(targetUrl, username, password, driver);
//    }
//
//    private MongoConfig buildMongoConfig() {
//        String host     = "************";
//        int    port     = 27017;
//        String username = "cgp2";
//        String password = "Cgp86db!123";
//
//        String targetDatabase = "cgp2";
//
//        return new MongoConfig(host, port, username, password, targetDatabase);
//    }

    private MysqlConfig buildMysqlConfig() {
        String driver    = "com.mysql.jdbc.Driver";
        String targetUrl = "*************************************************************************************************";
        String username  = "developer";
        String password  = "Dev!123a";

        MysqlConfig targetConfig = new MysqlConfig(targetUrl, username, password, driver);
        return targetConfig;
    }

    private MongoConfig buildMongoConfig() {
        String host     = "**************";
        int    port     = 27017;
        String username = "developer";
        String password = "Dev!123a";

        String targetDatabase = "local_impression_test";

        MongoConfig targetMongoConfig = new MongoConfig(host, port, username, password, targetDatabase);
        return targetMongoConfig;
    }
}