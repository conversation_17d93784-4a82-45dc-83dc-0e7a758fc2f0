package com.qpp.cgp.util;

import com.qpp.core.exception.BusinessException;
import org.junit.Assert;

import java.lang.reflect.InvocationTargetException;

public class JunitUtils {
    public static BusinessException getBusinessException(Exception exception) {
        Assert.assertNotNull(exception);
        Assert.assertTrue(exception instanceof InvocationTargetException);
        Assert.assertNotNull(((InvocationTargetException) exception).getTargetException());
        Assert.assertTrue(((InvocationTargetException) exception).getTargetException() instanceof BusinessException);
        return (BusinessException) ((InvocationTargetException) exception).getTargetException();
    }
}
