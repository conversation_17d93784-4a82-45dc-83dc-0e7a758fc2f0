//package com.qpp.cgp.manager.product.sync;
//
//import com.qpp.cgp.domain.product.sync.ProductSyncProgress;
//import com.qpp.cgp.domain.product.sync.ProjectDeployEnv;
//import com.qpp.cgp.repository.product.ProductSyncProgressRepository;
//import com.qpp.cgp.util.EmbeddedMongoTester;
//import com.qpp.core.exception.BusinessException;
//import com.qpp.id.generator.IdGenerator;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.Mockito;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.data.mongodb.core.query.Criteria;
//import org.springframework.data.mongodb.core.query.Query;
//import org.springframework.test.context.junit4.SpringRunner;
//import org.springframework.test.util.ReflectionTestUtils;
//
//import java.io.IOException;
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.List;
//
//@RunWith(SpringRunner.class)
//@SpringBootTest
//public class ProductSyncManagerTests {
//
//    @Test(expected = BusinessException.class)
//    public void testCheckIsCanSync() {
//        // build params
//        List<Long> productIds = new ArrayList<>();
//        productIds.add(123L);
//        ProjectDeployEnv env = ProjectDeployEnv.local;
//        //
//        ProductSyncManager productSyncManager = Mockito.spy(ProductSyncManager.class);
//        ProductSyncProgressRepository productSyncProgressRepository = Mockito.mock(ProductSyncProgressRepository.class);
//        ProductSyncProgress productSyncProgress = new ProductSyncProgress();
//        productSyncProgress.setId("123");
//        Mockito.when(productSyncProgressRepository.findOneByProductIdsAndTargetEnvAndStatuses(Mockito.any(), Mockito.any(), Mockito.anyList())).thenReturn(productSyncProgress);
//        // invoke
//        ReflectionTestUtils.setField(productSyncManager, "productSyncProgressRepository", productSyncProgressRepository);
//        ReflectionTestUtils.invokeMethod(productSyncManager, "checkIsCanSync", productIds, env);
//    }
//
//    @Autowired
//    private EmbeddedMongoTester embeddedMongoTester;
//
//    @Autowired
//    private IdGenerator idGenerator;
//
//    @Test
//    public void testGenerateProgress() throws IOException {
//        embeddedMongoTester.initMongo();
//
//        // mock dependencies
//        ProductSyncProgressRepository productSyncProgressRepository = Mockito.mock(ProductSyncProgressRepository.class);
//        Mockito.when(productSyncProgressRepository.getMongoTemplate()).thenReturn(embeddedMongoTester.getTestMongoTemplate());
//        ProductSyncManager productSyncManager = Mockito.spy(ProductSyncManager.class);
//        ReflectionTestUtils.setField(productSyncManager, "productSyncProgressRepository", productSyncProgressRepository);
//        ReflectionTestUtils.setField(productSyncManager, "idGenerator", idGenerator);
//
//        // builder params && invoke method
//        List<Long> productIds = Arrays.asList(123L);
//        ProjectDeployEnv env = ProjectDeployEnv.local;
//        Object retVal = ReflectionTestUtils.invokeMethod(productSyncManager, "generateProductSyncProgress", productIds, env);
//
//        // assert
//        boolean exists = embeddedMongoTester.getTestMongoTemplate().exists(Query.query(Criteria.where("_id").is(retVal)), ProductSyncProgress.class);
//        Assert.assertTrue(exists);
//
//        embeddedMongoTester.stopMongo();
//    }
//
//}