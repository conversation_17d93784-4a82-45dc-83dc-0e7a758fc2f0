package com.qpp.cgp.manager.product.sync;

import com.qpp.cgp.config.mongo.MongoTemplateFactory;
import com.qpp.cgp.domain.user.Customer;
import com.qpp.cgp.domain.user.User;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.Assert.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest(ProductSyncUserEmailService.class)
@PowerMockIgnore({"javax.management.*", "org.w3c.dom.*", "org.apache.log4j.*", "org.xml.sax.*", "javax.xml.*", "javax.script.*"})
public class ProductSyncUserEmailServiceTests {

    @InjectMocks
    private ProductSyncUserEmailService productSyncUserEmailService;

    @Mock
    private MongoTemplateFactory mongoTemplateFactory;

    @Mock
    private MongoTemplate mongoTemplate;

    @Before
    public void setUp() {
        Mockito.when(mongoTemplate.find(Mockito.any(), Mockito.eq(User.class))).thenReturn(new ArrayList<>());
        Mockito.when(mongoTemplateFactory.getMongoTemplate(Mockito.eq(User.class))).thenReturn(mongoTemplate);
        productSyncUserEmailService = PowerMockito.spy(productSyncUserEmailService);
    }

    @Test
    public void testGetUserIdGivenUserEmailNull() throws Exception {

        Optional<Long> retVal = productSyncUserEmailService.getUserId(null);

        Mockito.verify(productSyncUserEmailService).getUserId(null);
        PowerMockito.verifyPrivate(productSyncUserEmailService).invoke("listUsers", new Object[]{null});
        PowerMockito.verifyPrivate(productSyncUserEmailService).invoke("getUserId", new ArrayList<>());

        assert !retVal.isPresent();
    }

    @Test
    public void testGetUserIdGivenUserEmailNotNull() throws Exception {

        String userEmail = "<EMAIL>";

        productSyncUserEmailService.getUserId(userEmail);

        Mockito.verify(productSyncUserEmailService).getUserId(userEmail);
        PowerMockito.verifyPrivate(productSyncUserEmailService).invoke("listUsers", userEmail);
        PowerMockito.verifyPrivate(productSyncUserEmailService).invoke("getUserId", new ArrayList<>());
    }

    @Test
    public void testListUsersGivenNull() {

        ReflectionTestUtils.invokeMethod(productSyncUserEmailService, "listUsers", new Object[] {null});

        Mockito.verifyNoInteractions(mongoTemplateFactory);
        Mockito.verifyNoInteractions(mongoTemplate);
    }

    @Test
    public void testListUsersGivenText() {

        ReflectionTestUtils.invokeMethod(productSyncUserEmailService, "listUsers", "<EMAIL>");

        Mockito.verify(mongoTemplateFactory).getMongoTemplate(User.class);
        Mockito.verify(mongoTemplate).find(Mockito.any(), Mockito.eq(User.class));
    }

    @Test
    public void testGetUserIdGivenUserListNull() {

        Optional<Long> retVal = ReflectionTestUtils.invokeMethod(productSyncUserEmailService, "getUserId", new Object[]{null});

        assert !retVal.isPresent();
    }

    @Test
    public void testGetUserIdGivenUserListEmpty() {

        Optional<Long> retVal = ReflectionTestUtils.invokeMethod(productSyncUserEmailService, "getUserId", new ArrayList<>());

        assert !retVal.isPresent();
    }

    @Test
    public void testGetUserIdGivenUserListContainsNull() {
        List<User> userList = new ArrayList<>();
        userList.add(null);

        Optional<Long> retVal = ReflectionTestUtils.invokeMethod(productSyncUserEmailService, "getUserId", userList);

        assert !retVal.isPresent();
    }

    @Test
    public void testGetUserIdGivenUserListIdNull() {
        List<User> userList = new ArrayList<>();
        Customer customer = new Customer();
        customer.setWebsiteId(5L);
        userList.add(customer);

        Optional<Long> retVal = ReflectionTestUtils.invokeMethod(productSyncUserEmailService, "getUserId", userList);

        assert !retVal.isPresent();
    }

    @Test
    public void testGetUserIdGivenUserListWebsiteIdNull() {
        List<User> userList = new ArrayList<>();
        Customer customer = new Customer();
        userList.add(customer);

        Optional<Long> retVal = ReflectionTestUtils.invokeMethod(productSyncUserEmailService, "getUserId", userList);

        assert !retVal.isPresent();
    }

    @Test
    public void testGetUserGivenUserListThenSuccess() {
        List<User> userList = new ArrayList<>();
        Customer customer = new Customer();
        customer.setWebsiteId(5L);
        customer.setId(1L);
        userList.add(customer);

        Optional<Long> retVal = ReflectionTestUtils.invokeMethod(productSyncUserEmailService, "getUserId", userList);

        assert retVal.isPresent();
    }

}