package com.qpp.cgp.manager.merger;

import com.qpp.cgp.config.mongo.MongoTemplateFactory;
import com.qpp.cgp.domain.DataMergerProgressLog;
import com.qpp.cgp.domain.SyncProgressStatus;
import com.qpp.cgp.domain.product.sync.ProjectDeployEnv;
import com.qpp.core.utils.SecurityUtils;
import com.qpp.id.generator.IdGenerator;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;


@RunWith(PowerMockRunner.class)
@PrepareForTest({DataMergerProgressLogService.class,SecurityUtils.class})
@PowerMockIgnore({"javax.management.*", "org.w3c.dom.*", "org.apache.log4j.*", "org.xml.sax.*", "javax.xml.*", "javax.script.*"})
public class DataMergerProgressLogServiceTest {



    @Test
    public void generatorOperatorLogId() throws Exception {
        //String collection, Map<String,Object> query, ProjectDeployEnv source, ProjectDeployEnv target, Boolean upsert, String remark)
        Method method = PowerMockito.method(DataMergerProgressLogService.class, "generatorOperatorLogId", String.class, Map.class, ProjectDeployEnv.class, ProjectDeployEnv.class, Boolean.class, String.class);
        MongoTemplateFactory mongoTemplateFactory = Mockito.mock(MongoTemplateFactory.class);
        DataMergerProgressLogService spy = PowerMockito.spy(new DataMergerProgressLogService(mongoTemplateFactory));
        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
        IdGenerator idGenerator = Mockito.mock(IdGenerator.class);
        PowerMockito.doReturn(mongoTemplate).when(spy, "getMongoTemplate");
        Whitebox.setInternalState(spy, "idGenerator", idGenerator);
        List<DataMergerProgressLog> dataMergerProgressLogs = new ArrayList<>();
        Mockito.doAnswer(e ->{
            Object argument = e.getArgument(0);
            dataMergerProgressLogs.add((DataMergerProgressLog) argument);
            return argument;
        }).when(mongoTemplate).save(Mockito.any());
        String collection = "test";
        Map<String, Object> query = new HashMap<>();
        ProjectDeployEnv source = ProjectDeployEnv.test;
        ProjectDeployEnv target = ProjectDeployEnv.dev;
        Boolean upsert = false;
        String remark = "测试";
        PowerMockito.mockStatic(SecurityUtils.class);
        Long userId = 12L;
        PowerMockito.when(SecurityUtils.getLoginedInUserId()).thenReturn(userId);
        Mockito.when(idGenerator.generateId()).thenReturn(1233L);
        Object result = method.invoke(spy, collection, query, source, target, upsert, remark);
        PowerMockito.verifyPrivate(spy, Mockito.times(1)).invoke("getMongoTemplate");
        PowerMockito.verifyStatic(SecurityUtils.class);
        SecurityUtils.getLoginedInUserId();
        Mockito.verify(mongoTemplate).save(Mockito.any());
        Assert.assertNotNull(result);
        Assert.assertEquals("1233", result);
        Assert.assertFalse(dataMergerProgressLogs.isEmpty());
        DataMergerProgressLog dataMergerProgressLogSave = dataMergerProgressLogs.get(0);
        Assert.assertEquals(collection, dataMergerProgressLogSave.getCollection());
        Assert.assertEquals(remark, dataMergerProgressLogSave.getRemark());
        Assert.assertEquals(source, dataMergerProgressLogSave.getSourceEnvironment());
        Assert.assertEquals(target, dataMergerProgressLogSave.getTargetEnvironment());
        Assert.assertEquals(userId, dataMergerProgressLogSave.getOperatorUserId());
        Assert.assertEquals(SyncProgressStatus.waiting, dataMergerProgressLogSave.getStatus());
        Assert.assertEquals(false, dataMergerProgressLogSave.getUpsert());
    }

    @Test
    public void updateOperatorLogById() throws Exception {
        Method method = PowerMockito.method(DataMergerProgressLogService.class, "updateOperatorLogById", String.class, DataMergerProgressLog.class);
        MongoTemplateFactory mongoTemplateFactory = Mockito.mock(MongoTemplateFactory.class);
        DataMergerProgressLogService spy = PowerMockito.spy(new DataMergerProgressLogService(mongoTemplateFactory));
        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
        PowerMockito.doReturn(mongoTemplate).when(spy, "getMongoTemplate");
        String id = "123";
        Mockito.when(mongoTemplate.exists(Query.query(Criteria.where("_id").is(id)), DataMergerProgressLog.class)).thenReturn(true);
        DataMergerProgressLog dataMergerProgressLog = new DataMergerProgressLog();
        Mockito.when(mongoTemplate.save(dataMergerProgressLog)).thenReturn(dataMergerProgressLog);
        method.invoke(spy, id, dataMergerProgressLog);
        PowerMockito.verifyPrivate(spy, Mockito.times(1)).invoke("getMongoTemplate");
        Mockito.verify(mongoTemplate).exists(Query.query(Criteria.where("_id").is(id)), DataMergerProgressLog.class);
        Mockito.verify(mongoTemplate).save(dataMergerProgressLog);
    }

    @Test
    public void findById() throws Exception {
        Method method = PowerMockito.method(DataMergerProgressLogService.class, "findById", String.class);
        MongoTemplateFactory mongoTemplateFactory = Mockito.mock(MongoTemplateFactory.class);
        DataMergerProgressLogService spy = PowerMockito.spy(new DataMergerProgressLogService(mongoTemplateFactory));
        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
        PowerMockito.doReturn(mongoTemplate).when(spy, "getMongoTemplate");
        String id = "123";
        DataMergerProgressLog dataMergerProgressLog = new DataMergerProgressLog();
        Mockito.when(mongoTemplate.findById(id, DataMergerProgressLog.class)).thenReturn(dataMergerProgressLog);
        Mockito.when(mongoTemplate.exists(Query.query(Criteria.where("_id").is(id)), DataMergerProgressLog.class)).thenReturn(true);
        Object result = method.invoke(spy, id);
        PowerMockito.verifyPrivate(spy, Mockito.times(1)).invoke("getMongoTemplate");
        Mockito.verify(mongoTemplate).findById(id, DataMergerProgressLog.class);
        Assert.assertNotNull(result);
        Mockito.verify(mongoTemplate).exists(Query.query(Criteria.where("_id").is(id)), DataMergerProgressLog.class);
        Assert.assertTrue(result instanceof DataMergerProgressLog);
        Assert.assertEquals(dataMergerProgressLog, result);
    }

    @Test
    public void existsIsSyncingByCollection() throws Exception {
        Method method = PowerMockito.method(DataMergerProgressLogService.class, "existsIsSyncingByCollection", String.class, ProjectDeployEnv.class);
        MongoTemplateFactory mongoTemplateFactory = Mockito.mock(MongoTemplateFactory.class);
        DataMergerProgressLogService spy = PowerMockito.spy(new DataMergerProgressLogService(mongoTemplateFactory));
        String collection = "test";
        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
        List<SyncProgressStatus> runStatus = new ArrayList<>();
        runStatus.add(SyncProgressStatus.failure);
        runStatus.add(SyncProgressStatus.success);
        Mockito.when(mongoTemplate.exists(Query.query(Criteria.where("collection").is(collection).and("status").nin(runStatus).and("targetEnvironment").is(ProjectDeployEnv.dev)), DataMergerProgressLog.class)).thenReturn(true);
        PowerMockito.doReturn(mongoTemplate).when(spy, "getMongoTemplate");
        Object result = method.invoke(spy, collection, ProjectDeployEnv.dev);
        PowerMockito.verifyPrivate(spy, Mockito.times(1)).invoke("getMongoTemplate");
        Mockito.verify(mongoTemplate).exists(Query.query(Criteria.where("collection").is(collection).and("status").nin(runStatus).and("targetEnvironment").is(ProjectDeployEnv.dev)), DataMergerProgressLog.class);
        Assert.assertTrue(result instanceof Boolean);
        Assert.assertTrue((Boolean) result);
    }

    @Test
    public void getMongoTemplate() throws InvocationTargetException, IllegalAccessException {
        Method method = PowerMockito.method(DataMergerProgressLogService.class, "getMongoTemplate");
        MongoTemplateFactory mongoTemplateFactory = Mockito.mock(MongoTemplateFactory.class);
        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
        Mockito.when(mongoTemplateFactory.getMongoTemplate(DataMergerProgressLog.class)).thenReturn(mongoTemplate);
        DataMergerProgressLogService dataMergerProgressLogService = new DataMergerProgressLogService(mongoTemplateFactory);
        Object result = method.invoke(dataMergerProgressLogService);
        Mockito.verify(mongoTemplateFactory).getMongoTemplate(DataMergerProgressLog.class);
        assertEquals(mongoTemplate, result);
    }

    @Test
    public void  getSyncingByCollectionLogId(){
        MongoTemplateFactory mongoTemplateFactory = Mockito.mock(MongoTemplateFactory.class);
        DataMergerProgressLogService dataMergerProgressLogService = new DataMergerProgressLogService(mongoTemplateFactory);
        String collection = "test";
        ProjectDeployEnv test = ProjectDeployEnv.test;
        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
        String logId = "123";
        List<SyncProgressStatus> statuses = new ArrayList<>();
        statuses.add(SyncProgressStatus.failure);
        statuses.add(SyncProgressStatus.success);
        DataMergerProgressLog dataMergerProgressLog = new DataMergerProgressLog();
        dataMergerProgressLog.setId(logId);
        Mockito.when(mongoTemplateFactory.getMongoTemplate(DataMergerProgressLog.class)).thenReturn(mongoTemplate);
        Mockito.when(mongoTemplate.findOne(Query.query(Criteria.where("collection").is(collection).and("status").nin(statuses).and("targetEnvironment").is(ProjectDeployEnv.test)), DataMergerProgressLog.class)).thenReturn(dataMergerProgressLog);
        String syncingByCollectionLogId = dataMergerProgressLogService.getSyncingByCollectionLogId(collection, test);
        Mockito.verify(mongoTemplateFactory).getMongoTemplate(DataMergerProgressLog.class);
        Mockito.verify(mongoTemplate, Mockito.times(1)).findOne(Query.query(Criteria.where("collection").is(collection).and("status").nin(statuses).and("targetEnvironment").is(ProjectDeployEnv.test)), DataMergerProgressLog.class);        Assert.assertEquals(logId, syncingByCollectionLogId);
        Assert.assertEquals(logId, syncingByCollectionLogId);
    }
}