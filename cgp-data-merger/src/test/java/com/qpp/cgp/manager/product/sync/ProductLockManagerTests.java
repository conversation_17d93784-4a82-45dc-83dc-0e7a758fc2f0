//package com.qpp.cgp.manager.product.sync;
//
//import com.mongodb.MongoClient;
//import com.mongodb.client.MongoCollection;
//import com.mongodb.client.MongoDatabase;
//import com.qpp.cgp.domain.product.lock.ProductLockConfig;
//import com.qpp.cgp.util.EmbeddedMongoTester;
//import de.flapdoodle.embed.mongo.config.IMongodConfig;
//import org.bson.Document;
//import org.junit.After;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.Mockito;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.core.env.Environment;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.test.context.junit4.SpringRunner;
//import org.springframework.test.util.ReflectionTestUtils;
//
//import java.io.IOException;
//import java.util.ArrayList;
//import java.util.List;
//
//import static org.junit.Assert.assertEquals;
//import static org.junit.Assert.assertFalse;
//
//@RunWith(SpringRunner.class)
//@SpringBootTest
//public class ProductLockManagerTests {
//
//    @Autowired
//    private EmbeddedMongoTester embeddedMongoTester;
//
//    @Autowired
//    private Environment environment;
//
//    private final Logger logger = LoggerFactory.getLogger(this.getClass());
//
//    @Autowired
//    private IMongodConfig iMongodConfig;
//
//    @Before
//    public void init() throws IOException {
//        embeddedMongoTester.initMongo();
//    }
//
//    @After
//    public void destroy() {
//        embeddedMongoTester.stopMongo();
//    }
//
//    @Test
//    public void testFindByProductIds() {
//        // mock data
//        MongoClient mongoClient = embeddedMongoTester.getMongoClient();
//        MongoDatabase mongoDatabase = mongoClient.getDatabase("test");
//        MongoCollection<Document> mongoCollection = mongoDatabase.getCollection("productlockconfigs");
//        Document document = new Document();
//        document.put("_id", "1");
//        document.put("clazz", ProductLockConfig.class.getName());
//        document.put("productId", 1);
//        mongoCollection.insertOne(document);
//
//        // mock dependencies
//        ProductLockManager productLockManager = Mockito.spy(ProductLockManager.class);
//
//        MongoTemplate testMongoTemplate = embeddedMongoTester.getTestMongoTemplate();
//        ReflectionTestUtils.setField(productLockManager, "mongoTemplate", testMongoTemplate);
//        // build params & invoke method
//        List<Long> productIds = new ArrayList<>();
//        productIds.add(1L);
//        Object retVal = ReflectionTestUtils.invokeMethod(productLockManager, "findByProductIds", productIds);
//        assertEquals(1, ((List<ProductLockConfig>) retVal).size());
//    }
//
//    /*@Test
//    public void testGetByProductIds() {
//        // 修改目标环境Mongo数据库为嵌入式Mongo数据库
//        updateEnvironmentProperty(ProjectDeployEnv.local);
//        // mock data
//        MongoClient mongoClient = embeddedMongoTester.getMongoClient();
//        MongoDatabase mongoDatabase = mongoClient.getDatabase("test");
//        MongoCollection<Document> mongoCollection = mongoDatabase.getCollection("productlockconfigs");
//        Document document = new Document();
//        document.put("_id", "1");
//        document.put("clazz", ProductLockConfig.class.getName());
//        document.put("productId", 1);
//        mongoCollection.insertOne(document);
//
//        // mock dependencies
//        ProductLockManager productLockManager = Mockito.spy(ProductLockManager.class);
//        MongoTemplate testMongoTemplate = embeddedMongoTester.getTestMongoTemplate();
//        ReflectionTestUtils.setField(productLockManager, "mongoTemplate", testMongoTemplate);
//        // build params & invoke method
//        List<Long> productIds = new ArrayList<>();
//        productIds.add(1L);
//        Object retVal = ReflectionTestUtils.invokeMethod(productLockManager, "getByProductIds", productIds, ProjectDeployEnv.local);
//        assertEquals(1, ((List<ProductLockConfig>) retVal).size());
//    }
//
//    private void updateEnvironmentProperty(ProjectDeployEnv targetEnv) {
//        String propertyName = "data-merge-env." + targetEnv.name() + ".default.uri";
//        String property = environment.getProperty(propertyName);
//        logger.info("origin property: {}", property);
//        if (environment instanceof ConfigurableEnvironment) {
//            MutablePropertySources propertySources = ((ConfigurableEnvironment) environment).getPropertySources();
//            MockPropertySource mockPropertySource = new MockPropertySource();
//            mockPropertySource.setProperty(propertyName, "mongodb://localhost:" + iMongodConfig.net().getPort() + "/test");
//            propertySources.addFirst(mockPropertySource);
//        }
//        ApplicationContext applicationContextStatic = SpringApplicationContext.getApplicationContextStatic();
//        logger.info("updated property: {}", applicationContextStatic.getEnvironment().getProperty(propertyName));
//    }*/
//
//    @Test
//    public void testCanSync() {
//
//        ProductLockManager productLockManager = Mockito.spy(ProductLockManager.class);
//        List<ProductLockConfig> list = new ArrayList<>();
//        ProductLockConfig productLockConfig = new ProductLockConfig();
//        productLockConfig.setIsLock(true);
//        productLockConfig.setId("1");
//        productLockConfig.setClazz(ProductLockConfig.class.getName());
//        list.add(productLockConfig);
//        Boolean retVal = ReflectionTestUtils.<Boolean>invokeMethod(productLockManager, "canSync", list);
//        assertFalse(retVal);
//    }
//}