package com.qpp.cgp.manager.file;

import com.qpp.cgp.domain.FileSyncProgressLog;
import com.qpp.cgp.domain.StaticFile;
import com.qpp.cgp.domain.SyncProgressStatus;
import com.qpp.cgp.repository.user.UserRepository;
import com.qpp.core.exception.BusinessException;
import com.qpp.core.utils.SecurityUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/7/6 9:54
 * @Version 1.0
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({FileSyncService.class, SecurityUtils.class, UserRepository.class})
@PowerMockIgnore({"javax.management.*", "org.w3c.dom.*", "org.apache.log4j.*", "org.xml.sax.*", "javax.xml.*", "javax.script.*"})
public class FileSyncServiceTest {


    @Test
    public void checkAllowSyncNotTest(){
        FileSyncService fileSyncService = Mockito.spy(FileSyncService.class);
        // 参数
        FileSyncProgressLog fileSyncProgressLog=new FileSyncProgressLog();
        List<String> names=new ArrayList<>();
        names.add("ee.jpg");
        fileSyncProgressLog.setFileNames(names);
        List<StaticFile> staticFiles=new ArrayList<>();
        StaticFile staticFile = new StaticFile();
        staticFile.setFileName("ee.jpg");
        staticFile.setStaticFileDir("abc/vc");
        staticFiles.add(staticFile);
        fileSyncProgressLog.setStaticFiles(staticFiles);
        // mock
        MongoTemplate mongoTemplateRun=Mockito.mock(MongoTemplate.class);
        ReflectionTestUtils.setField(fileSyncService,"mongoTemplateRun",mongoTemplateRun);
        // when

        List<SyncProgressStatus> statuses=new ArrayList<>();
        statuses.add(SyncProgressStatus.success);
        statuses.add(SyncProgressStatus.failure);
        Criteria criteria = new Criteria();
        criteria.andOperator(
                Criteria.where("fileNames").is(fileSyncProgressLog.getFileNames()),
                Criteria.where("staticFiles").is(fileSyncProgressLog.getStaticFiles()),
                Criteria.where("status").nin(statuses)
        );
        List<FileSyncProgressLog> fileSyncProgressLogs = new ArrayList<>();
        Mockito.when(mongoTemplateRun.find(Query.query(criteria), FileSyncProgressLog.class)).thenReturn(fileSyncProgressLogs);
        // 执行
        try {
            fileSyncService.checkAllowSync(fileSyncProgressLog);

        }catch (Exception e){
            Assert.assertNull(e);
        }
    }

    @Test
    public void checkAllowSyncTest(){
        FileSyncService fileSyncService = Mockito.spy(FileSyncService.class);
        // 参数
        FileSyncProgressLog fileSyncProgressLog=new FileSyncProgressLog();
        List<String> names=new ArrayList<>();
        names.add("ee.jpg");
        fileSyncProgressLog.setFileNames(names);
        List<StaticFile> staticFiles=new ArrayList<>();
        StaticFile staticFile = new StaticFile();
        staticFile.setFileName("ee.jpg");
        staticFile.setStaticFileDir("abc/vc");
        staticFiles.add(staticFile);
        fileSyncProgressLog.setStaticFiles(staticFiles);
        // mock
        MongoTemplate mongoTemplateRun=Mockito.mock(MongoTemplate.class);
        ReflectionTestUtils.setField(fileSyncService,"mongoTemplateRun",mongoTemplateRun);

        // when

        List<SyncProgressStatus> statuses=new ArrayList<>();
        statuses.add(SyncProgressStatus.success);
        statuses.add(SyncProgressStatus.failure);
        Criteria criteria = new Criteria();
        criteria.andOperator(
                Criteria.where("fileNames").is(fileSyncProgressLog.getFileNames()),
                Criteria.where("staticFiles").is(fileSyncProgressLog.getStaticFiles()),
                Criteria.where("status").nin(statuses)
        );
        List<FileSyncProgressLog> fileSyncProgressLogs = new ArrayList<>();
        FileSyncProgressLog fileSyncProgressLog1= new FileSyncProgressLog();
        fileSyncProgressLogs.add(fileSyncProgressLog1);
        Mockito.when(mongoTemplateRun.find(Query.query(criteria), FileSyncProgressLog.class)).thenReturn(fileSyncProgressLogs);
        // 执行
        try {
            fileSyncService.checkAllowSync(fileSyncProgressLog);

        }catch (Exception e){
            Assert.assertNotNull(e);
            BusinessException businessException=(BusinessException) e;
            Assert.assertEquals(7894,businessException.getCode());
        }
    }

}
