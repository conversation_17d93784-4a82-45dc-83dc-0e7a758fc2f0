//package com.qpp.cgp.manager.website.sync;
//
//import com.mongodb.MongoClient;
//import com.mongodb.client.MongoCollection;
//import com.mongodb.client.MongoDatabase;
//import com.qpp.cgp.domain.WebsiteSyncProgress;
//import com.qpp.cgp.domain.product.sync.ProductSyncProgress;
//import com.qpp.cgp.domain.product.sync.ProjectDeployEnv;
//import com.qpp.cgp.util.EmbeddedMongoTester;
//import com.qpp.core.exception.BusinessException;
//import org.bson.Document;
//import org.junit.After;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.Mockito;
//import org.mockito.internal.util.reflection.FieldSetter;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//import org.springframework.test.util.ReflectionTestUtils;
//
//import java.io.IOException;
//import java.util.ArrayList;
//import java.util.List;
//
//import static org.junit.Assert.assertNotNull;
//
//@RunWith(SpringRunner.class)
//@SpringBootTest
//public class WebsiteSyncProgressManagerTests {
//
//    @Autowired
//    private EmbeddedMongoTester embeddedMongoTester;
//
//    @Before
//    public void initMongo() throws IOException {
//        embeddedMongoTester.initMongo();
//    }
//
//    @After
//    public void destroyMongo() {
//        embeddedMongoTester.stopMongo();
//    }
//
//    private void mockSyncProgressData(List<Long> websiteIds, ProjectDeployEnv env) {
//        // 模拟数据
//        MongoClient mongoClient = embeddedMongoTester.getMongoClient();
//        MongoDatabase mongoDatabase = mongoClient.getDatabase("test");
//        String collectionName = "websitesyncprogresses";
//        mongoDatabase.createCollection(collectionName);
//        MongoCollection<Document> collection = mongoDatabase.getCollection(collectionName);
//        Document document = new Document();
//        document.put("_id", "123");
//        document.put("clazz", ProductSyncProgress.class.getName());
//        document.put("websiteIds", websiteIds);
//        document.put("targetEnv", env.name());
//        document.put("status", ProductSyncProgress.ProductSyncProgressStatus.syncing.name());
//        collection.insertOne(document);
//    }
//
//    @Test
//    public void testFindById() throws NoSuchFieldException {
//        // mock data
//        List<Long> websiteIds = new ArrayList<>();
//        websiteIds.add(11L);
//        mockSyncProgressData(websiteIds, ProjectDeployEnv.local);
//
//        // mock dependencies
//        WebsiteSyncProgressManager websiteSyncProgressManager = Mockito.mock(WebsiteSyncProgressManager.class);
//        ReflectionTestUtils.setField(websiteSyncProgressManager, "mongoTemplate", embeddedMongoTester.getTestMongoTemplate());
//        ReflectionTestUtils.setField(websiteSyncProgressManager, "entityType", WebsiteSyncProgress.class);
//        Mockito.when(websiteSyncProgressManager.findById(Mockito.any())).thenCallRealMethod();
//
//        // invoke & assert
//        WebsiteSyncProgress retVal = websiteSyncProgressManager.findById("123");
//        assertNotNull(retVal);
//    }
//
//    @Test(expected = BusinessException.class)
//    public void testFindByIdNotExists() {
//        // mock data
//        List<Long> websiteIds = new ArrayList<>();
//        websiteIds.add(11L);
//        mockSyncProgressData(websiteIds, ProjectDeployEnv.local);
//
//        // mock dependencies
//        WebsiteSyncProgressManager websiteSyncProgressManager = Mockito.mock(WebsiteSyncProgressManager.class);
//        ReflectionTestUtils.setField(websiteSyncProgressManager, "mongoTemplate", embeddedMongoTester.getTestMongoTemplate());
//        ReflectionTestUtils.setField(websiteSyncProgressManager, "entityType", WebsiteSyncProgress.class);
//        Mockito.when(websiteSyncProgressManager.findById(Mockito.any())).thenCallRealMethod();
//
//        // invoke & assert
//        WebsiteSyncProgress retVal = websiteSyncProgressManager.findById("1");
//    }
//}