package com.qpp.cgp.manager.merger;

import com.mongodb.client.MongoDatabase;
import com.qpp.cgp.domain.DataMergerProgressLog;
import com.qpp.cgp.domain.SyncProgressStatus;
import com.qpp.cgp.domain.product.sync.ProjectDeployEnv;
import com.qpp.cgp.dto.DataMergerEvent;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;

import java.util.concurrent.atomic.AtomicReference;


public class DataMergerEventListenerTest {

    @Test
    public void onApplicationEvent() {
        DataMergerEventListener dataMergerEventListener = new DataMergerEventListener();
        /**
         * 流程：
         * 1：首先获取对应的操作记录Id对应的实体
         * 2：调用同步服务
         * 3：Try catch 同步服务，如果没有异常则修改记录状态改为成功
         * 4：如果有异常，改为失败，同时把异常信息记录在内
         */
        String logId = "123";
        DataMergerProgressLog dataMergerProgressLog = new DataMergerProgressLog();
        dataMergerProgressLog.setId(logId);
        dataMergerProgressLog.setStatus(SyncProgressStatus.waiting);
        dataMergerProgressLog.setUpsert(false);
        dataMergerProgressLog.setTargetEnvironment(ProjectDeployEnv.test);
        dataMergerProgressLog.setSourceEnvironment(ProjectDeployEnv.current);
        dataMergerProgressLog.setCollection("name");
        dataMergerProgressLog.setRemark("测试");
        DataMergerProgressLogService dataMergerProgressLogService = Mockito.mock(DataMergerProgressLogService.class);
        DataMergerService dataMergerService = Mockito.mock(DataMergerService.class);
        Whitebox.setInternalState(dataMergerEventListener, "dataMergerService", dataMergerService);
        Whitebox.setInternalState(dataMergerEventListener, "dataMergerProgressLogService", dataMergerProgressLogService);
        DataMergerEvent dataMergerEvent = new DataMergerEvent(logId);
        Mockito.when(dataMergerProgressLogService.findById(logId)).thenReturn(dataMergerProgressLog);
        AtomicReference<SyncProgressStatus> result1 = new AtomicReference<>();
        Mockito.doAnswer(e -> {
            DataMergerProgressLog log = e.getArgument(1);
            result1.set(log.getStatus());
            return null;
        }).when(dataMergerProgressLogService).updateOperatorLogById(Mockito.argThat(e -> e.equals(logId)), Mockito.argThat(e -> e.getStatus().equals(SyncProgressStatus.verifying)));
        AtomicReference<SyncProgressStatus> result2 = new AtomicReference<>();
        Mockito.doAnswer(e -> {
            DataMergerProgressLog log = e.getArgument(1);
            result2.set(log.getStatus());
            return null;
        }).when(dataMergerProgressLogService).updateOperatorLogById(Mockito.argThat(e -> e.equals(logId)), Mockito.argThat(e -> e.getStatus().equals(SyncProgressStatus.success)));
        Mockito.doNothing().when(dataMergerService).collectionMerger(dataMergerProgressLog.getCollection(), dataMergerProgressLog.getUpsert(), dataMergerProgressLog.getQueryJson(), dataMergerProgressLog.getSourceEnvironment(), dataMergerProgressLog.getTargetEnvironment(), null, null, null);
        MongoDatabase sourceMongoDatabase = Mockito.mock(MongoDatabase.class);
        MongoDatabase targetMongoDatabase = Mockito.mock(MongoDatabase.class);
        Mockito.when(dataMergerService.getDataBase(dataMergerProgressLog.getTargetEnvironment(), dataMergerProgressLog.getCollection())).thenReturn(targetMongoDatabase);
        Mockito.when(dataMergerService.getDataBase(dataMergerProgressLog.getSourceEnvironment(), dataMergerProgressLog.getCollection())).thenReturn(sourceMongoDatabase);
        Mockito.when(dataMergerService.checkMergerIsSuccess(dataMergerProgressLog.getCollection(), dataMergerProgressLog.getQueryJson(), sourceMongoDatabase, targetMongoDatabase)).thenReturn(true);
        dataMergerEventListener.onApplicationEvent(dataMergerEvent);
        Mockito.verify(dataMergerProgressLogService).findById(logId);
        Mockito.verify(dataMergerService).collectionMerger(dataMergerProgressLog.getCollection(), dataMergerProgressLog.getUpsert(), dataMergerProgressLog.getQueryJson(), dataMergerProgressLog.getSourceEnvironment(), dataMergerProgressLog.getTargetEnvironment(),null,null,null);
        Mockito.verify(dataMergerService).getDataBase(dataMergerProgressLog.getSourceEnvironment(), dataMergerProgressLog.getCollection());
        Mockito.verify(dataMergerService).getDataBase(dataMergerProgressLog.getTargetEnvironment(), dataMergerProgressLog.getCollection());
        Mockito.verify(dataMergerService).checkMergerIsSuccess(dataMergerProgressLog.getCollection(), dataMergerProgressLog.getQueryJson(), sourceMongoDatabase, targetMongoDatabase);
        Assert.assertNotNull(result1.get());
        Assert.assertNotNull(result2.get());
        Assert.assertEquals(SyncProgressStatus.verifying, result1.get());
        Assert.assertEquals(SyncProgressStatus.success, result2.get());
    }

    @Test
    public void onApplicationEventVerifyFail() {
        DataMergerEventListener dataMergerEventListener = new DataMergerEventListener();
        /**
         * 流程：
         * 1：首先获取对应的操作记录Id对应的实体
         * 2：调用同步服务
         * 3：Try catch 同步服务，如果没有异常则修改记录状态改为成功
         * 4：如果有异常，改为失败，同时把异常信息记录在内
         */
        String logId = "123";
        DataMergerProgressLog dataMergerProgressLog = new DataMergerProgressLog();
        dataMergerProgressLog.setId(logId);
        dataMergerProgressLog.setStatus(SyncProgressStatus.waiting);
        dataMergerProgressLog.setUpsert(false);
        dataMergerProgressLog.setTargetEnvironment(ProjectDeployEnv.test);
        dataMergerProgressLog.setSourceEnvironment(ProjectDeployEnv.current);
        dataMergerProgressLog.setCollection("name");
        dataMergerProgressLog.setRemark("测试");
        DataMergerProgressLogService dataMergerProgressLogService = Mockito.mock(DataMergerProgressLogService.class);
        DataMergerService dataMergerService = Mockito.mock(DataMergerService.class);
        Whitebox.setInternalState(dataMergerEventListener, "dataMergerService", dataMergerService);
        Whitebox.setInternalState(dataMergerEventListener, "dataMergerProgressLogService", dataMergerProgressLogService);
        DataMergerEvent dataMergerEvent = new DataMergerEvent(logId);
        Mockito.when(dataMergerProgressLogService.findById(logId)).thenReturn(dataMergerProgressLog);
        AtomicReference<SyncProgressStatus> result1 = new AtomicReference<>();
        Mockito.doAnswer(e -> {
            DataMergerProgressLog log = e.getArgument(1);
            result1.set(log.getStatus());
            return null;
        }).when(dataMergerProgressLogService).updateOperatorLogById(Mockito.argThat(e -> e.equals(logId)), Mockito.argThat(e -> e.getStatus().equals(SyncProgressStatus.verifying)));
        AtomicReference<DataMergerProgressLog> dataMergerProgressLogFail = new AtomicReference<>();
        Mockito.doAnswer(e -> {
            DataMergerProgressLog log = e.getArgument(1);
            dataMergerProgressLogFail.set(log);
            return null;
        }).when(dataMergerProgressLogService).updateOperatorLogById(Mockito.argThat(e -> e.equals(logId)), Mockito.argThat(e -> e.getStatus().equals(SyncProgressStatus.failure)));
        Mockito.doNothing().when(dataMergerService).collectionMerger(dataMergerProgressLog.getCollection(), dataMergerProgressLog.getUpsert(), dataMergerProgressLog.getQueryJson(), dataMergerProgressLog.getSourceEnvironment(), dataMergerProgressLog.getTargetEnvironment(), null, null,null);
        MongoDatabase sourceMongoDatabase = Mockito.mock(MongoDatabase.class);
        MongoDatabase targetMongoDatabase = Mockito.mock(MongoDatabase.class);
        long sourceCount = 100;
        long targetCount = 100;
        Mockito.when(dataMergerService.countByCollectionAndQuery(dataMergerProgressLog.getCollection(), dataMergerProgressLog.getQueryJson(), sourceMongoDatabase)).thenReturn(sourceCount);
        Mockito.when(dataMergerService.countByCollectionAndQuery(dataMergerProgressLog.getCollection(), dataMergerProgressLog.getQueryJson(), targetMongoDatabase)).thenReturn(targetCount);
        Mockito.when(dataMergerService.getDataBase(dataMergerProgressLog.getTargetEnvironment(), dataMergerProgressLog.getCollection())).thenReturn(targetMongoDatabase);
        Mockito.when(dataMergerService.getDataBase(dataMergerProgressLog.getSourceEnvironment(), dataMergerProgressLog.getCollection())).thenReturn(sourceMongoDatabase);
        Mockito.when(dataMergerService.checkMergerIsSuccess(dataMergerProgressLog.getCollection(), dataMergerProgressLog.getQueryJson(), sourceMongoDatabase, targetMongoDatabase)).thenReturn(false);
        dataMergerEventListener.onApplicationEvent(dataMergerEvent);
        Mockito.verify(dataMergerProgressLogService).findById(logId);
        Mockito.verify(dataMergerService).collectionMerger(dataMergerProgressLog.getCollection(), dataMergerProgressLog.getUpsert(), dataMergerProgressLog.getQueryJson(), dataMergerProgressLog.getSourceEnvironment(), dataMergerProgressLog.getTargetEnvironment(), null, null,null);
        Mockito.verify(dataMergerService).getDataBase(dataMergerProgressLog.getSourceEnvironment(), dataMergerProgressLog.getCollection());
        Mockito.verify(dataMergerService).getDataBase(dataMergerProgressLog.getTargetEnvironment(), dataMergerProgressLog.getCollection());
        Mockito.verify(dataMergerService).checkMergerIsSuccess(dataMergerProgressLog.getCollection(), dataMergerProgressLog.getQueryJson(), sourceMongoDatabase, targetMongoDatabase);
        Mockito.verify(dataMergerService).countByCollectionAndQuery(dataMergerProgressLog.getCollection(), dataMergerProgressLog.getQueryJson(), sourceMongoDatabase);
        Mockito.verify(dataMergerService).countByCollectionAndQuery(dataMergerProgressLog.getCollection(), dataMergerProgressLog.getQueryJson(), targetMongoDatabase);
        Assert.assertNotNull(result1.get());
        Assert.assertNotNull(dataMergerProgressLogFail.get());
        Assert.assertEquals(SyncProgressStatus.verifying, result1.get());
        Assert.assertEquals(SyncProgressStatus.failure, dataMergerProgressLogFail.get().getStatus());
        DataMergerProgressLog dataMergerProgressVerifyFail = dataMergerProgressLogFail.get();
        Long sourceCountByResult = dataMergerProgressVerifyFail.getSourceCount();
        Long targetCountByResult = dataMergerProgressVerifyFail.getSourceCount();
        Assert.assertEquals(sourceCount, (long) sourceCountByResult);
        Assert.assertEquals(targetCount, (long) targetCountByResult);
    }

    @Test
    public void onApplicationEventExecuteFail() {
        DataMergerEventListener dataMergerEventListener = new DataMergerEventListener();
        /**
         * 流程：
         * 1：首先获取对应的操作记录Id对应的实体
         * 2：调用同步服务
         * 3：Try catch 同步服务，如果没有异常则修改记录状态改为成功
         * 4：如果有异常，改为失败，同时把异常信息记录在内
         */
        String logId = "123";
        DataMergerProgressLog dataMergerProgressLog = new DataMergerProgressLog();
        dataMergerProgressLog.setId(logId);
        dataMergerProgressLog.setStatus(SyncProgressStatus.waiting);
        dataMergerProgressLog.setUpsert(false);
        dataMergerProgressLog.setTargetEnvironment(ProjectDeployEnv.test);
        dataMergerProgressLog.setSourceEnvironment(ProjectDeployEnv.current);
        dataMergerProgressLog.setCollection("name");
        dataMergerProgressLog.setRemark("测试");
        DataMergerProgressLogService dataMergerProgressLogService = Mockito.mock(DataMergerProgressLogService.class);
        DataMergerService dataMergerService = Mockito.mock(DataMergerService.class);
        Whitebox.setInternalState(dataMergerEventListener, "dataMergerService", dataMergerService);
        Whitebox.setInternalState(dataMergerEventListener, "dataMergerProgressLogService", dataMergerProgressLogService);
        DataMergerEvent dataMergerEvent = new DataMergerEvent(logId);
        Mockito.when(dataMergerProgressLogService.findById(logId)).thenReturn(dataMergerProgressLog);
        AtomicReference<SyncProgressStatus> result = new AtomicReference<>();
        Mockito.doAnswer(e -> {
            DataMergerProgressLog log = e.getArgument(1);
            result.set(log.getStatus());
            return null;
        }).when(dataMergerProgressLogService).updateOperatorLogById(Mockito.any(), Mockito.any(DataMergerProgressLog.class));
        Mockito.doThrow(BusinessExceptionBuilder.of(0)).when(dataMergerService).collectionMerger(dataMergerProgressLog.getCollection(), dataMergerProgressLog.getUpsert(), dataMergerProgressLog.getQueryJson(), dataMergerProgressLog.getSourceEnvironment(), dataMergerProgressLog.getTargetEnvironment(), null, null,null);
        dataMergerEventListener.onApplicationEvent(dataMergerEvent);
        Mockito.verify(dataMergerProgressLogService).findById(logId);
        Mockito.verify(dataMergerProgressLogService).updateOperatorLogById(Mockito.any(), Mockito.any(DataMergerProgressLog.class));
        Mockito.verify(dataMergerService).collectionMerger(dataMergerProgressLog.getCollection(), dataMergerProgressLog.getUpsert(), dataMergerProgressLog.getQueryJson(), dataMergerProgressLog.getSourceEnvironment(), dataMergerProgressLog.getTargetEnvironment(), null, null, null);
        Assert.assertNotNull(result.get());
        Assert.assertEquals(SyncProgressStatus.failure, result.get());

    }
}