package com.qpp.cgp.manager.file;

import com.qpp.cgp.domain.FileSyncProgressLog;
import com.qpp.cgp.domain.StaticFile;
import com.qpp.cgp.domain.product.sync.ProjectDeployEnv;
import com.qpp.cgp.repository.user.UserRepository;
import com.qpp.cgp.service.database.service.ProductFileMerger;
import com.qpp.core.exception.BusinessException;
import com.qpp.core.utils.SecurityUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/7/5 15:11
 * @Version 1.0
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({FileSyncEventListener.class, SecurityUtils.class, UserRepository.class})
@PowerMockIgnore({"javax.management.*", "org.w3c.dom.*", "org.apache.log4j.*", "org.xml.sax.*", "javax.xml.*", "javax.script.*"})
public class FileSyncEventListenerTests {

    @Test
    public void staticSyncStaticFileTrueTest(){
        FileSyncEventListener fileSyncEventListener = Mockito.spy(FileSyncEventListener.class);
        // 参数
        ProjectDeployEnv targetEnvironment=ProjectDeployEnv.prod;
        String targetBaseFileUrl="http://************/file/";
        String sourceBaseFileUrl="http://*************/file/";

        List<StaticFile> staticFiles=new ArrayList<>();
        StaticFile staticFile = new StaticFile();
        staticFile.setFileName("a.jpg");
        staticFile.setStaticFileDir("user/che/");
        staticFiles.add(staticFile);
        // mock
        ProductFileMerger productFileMerger=Mockito.mock(ProductFileMerger.class);
        ReflectionTestUtils.setField(fileSyncEventListener,"productFileMerger",productFileMerger);
        // when
        boolean fileExists =true;
        Mockito.when(productFileMerger.targetFileUrlExists(sourceBaseFileUrl +"files/static/"+staticFile.getStaticFileDir()+staticFile.getFileName()+"/fileInfo/exists")).thenReturn(fileExists);
        // 执行
        try {
            fileSyncEventListener.staticSyncStaticFile(  targetBaseFileUrl,  sourceBaseFileUrl,  staticFiles);
        }catch (Exception e){
            Assert.assertNull(e);
        }
    }

    @Test
    public void staticSyncStaticFileFalseTest(){
        FileSyncEventListener fileSyncEventListener = Mockito.spy(FileSyncEventListener.class);
        // 参数
        ProjectDeployEnv targetEnvironment=ProjectDeployEnv.prod;
        String targetBaseFileUrl="http://************/file/";
        String sourceBaseFileUrl="http://*************/file/";

        List<StaticFile> staticFiles=new ArrayList<>();
        StaticFile staticFile = new StaticFile();
        staticFile.setFileName("a.jpg");
        staticFile.setStaticFileDir("user/che/");
        staticFiles.add(staticFile);
        // mock
        ProductFileMerger productFileMerger=Mockito.mock(ProductFileMerger.class);
        ReflectionTestUtils.setField(fileSyncEventListener,"productFileMerger",productFileMerger);
        // when
        boolean fileExists =false;
        Mockito.when(productFileMerger.targetFileUrlExists(sourceBaseFileUrl +"files/static/"+staticFile.getStaticFileDir()+staticFile.getFileName()+"/fileInfo/exists")).thenReturn(fileExists);
        // 执行
        try {
            fileSyncEventListener.staticSyncStaticFile(  targetBaseFileUrl,  sourceBaseFileUrl,  staticFiles);
        }catch (Exception e){
            Assert.assertNotNull(e);
            BusinessException businessException=(BusinessException) e;
            Assert.assertEquals(75214,businessException.getCode());
        }
    }

    @Test
    public void staticTrueTestFile(){
        FileSyncEventListener fileSyncEventListener = Mockito.spy(FileSyncEventListener.class);
        // 参数
        FileSyncProgressLog fileSyncProgressLog=new FileSyncProgressLog();
        List<String > names=new ArrayList<>();
        names.add("ao.jpg");
        fileSyncProgressLog.setFileNames(names);
        String targetBaseFileUrl="http://************/file/";
        String sourceBaseFileUrl="http://*************/file/";
        // mock
        ProductFileMerger productFileMerger = Mockito.mock(ProductFileMerger.class);
        ReflectionTestUtils.setField(fileSyncEventListener,"productFileMerger",productFileMerger);
        // when
        boolean fileExists =true;
        Mockito.when(productFileMerger.targetFileUrlExists(sourceBaseFileUrl +"files/"+fileSyncProgressLog.getFileNames().get(0)+"/fileInfo/exists")).thenReturn(fileExists);
        // 执行
        try {
            fileSyncEventListener.staticFile( fileSyncProgressLog,  targetBaseFileUrl,  sourceBaseFileUrl);

        }catch (Exception e){
            Assert.assertNull(e);
        }
    }

    @Test
    public void staticFileFalseTest(){
        FileSyncEventListener fileSyncEventListener = Mockito.spy(FileSyncEventListener.class);
        // 参数
        FileSyncProgressLog fileSyncProgressLog=new FileSyncProgressLog();
        List<String > names=new ArrayList<>();
        names.add("ao.jpg");
        fileSyncProgressLog.setFileNames(names);
        String targetBaseFileUrl="http://************/file/";
        String sourceBaseFileUrl="http://*************/file/";
        // mock
        ProductFileMerger productFileMerger = Mockito.mock(ProductFileMerger.class);
        ReflectionTestUtils.setField(fileSyncEventListener,"productFileMerger",productFileMerger);
        // when
        boolean fileExists =false;
        Mockito.when(productFileMerger.targetFileUrlExists(sourceBaseFileUrl +"files/"+fileSyncProgressLog.getFileNames().get(0)+"/fileInfo/exists")).thenReturn(fileExists);
        // 执行
        try {
            fileSyncEventListener.staticFile( fileSyncProgressLog,  targetBaseFileUrl,  sourceBaseFileUrl);

        }catch (Exception e){
            Assert.assertNotNull(e);
            BusinessException businessException=(BusinessException) e;
            Assert.assertEquals(75213,businessException.getCode());
        }
    }
}
