//package com.qpp.cgp.repository.website;
//
//import com.qpp.cgp.util.EmbeddedMongoTester;
//import com.qpp.core.utils.Jacksons;
//import org.bson.Document;
//import org.junit.After;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.Mockito;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.test.context.junit4.SpringRunner;
//import org.springframework.test.util.ReflectionTestUtils;
//import org.springframework.util.ResourceUtils;
//
//import java.io.File;
//import java.io.IOException;
//import java.net.URI;
//import java.net.URISyntaxException;
//import java.nio.file.Files;
//import java.nio.file.Paths;
//import java.util.ArrayList;
//import java.util.List;
//
//import static org.junit.Assert.*;
//
//@RunWith(SpringRunner.class)
//@SpringBootTest
//public class WebsiteLockConfigRepositoryTests {
//
//    @Autowired
//    private EmbeddedMongoTester embeddedMongoTester;
//
//    @Before
//    public void initMongo() throws IOException {
//        embeddedMongoTester.initMongo();
//    }
//
//    @After
//    public void destroy() {
//        embeddedMongoTester.stopMongo();
//    }
//
//    private void populateData() throws IOException {
//        String resourceLocation = "classpath:mongo/websiteLockConfigs.json";
//        File file = ResourceUtils.getFile(resourceLocation);
//        String absolutePath = file.getAbsolutePath();
//        String collectionName = "websitelockconfigs";
//        byte[] bytes = Files.readAllBytes(Paths.get(absolutePath));
//        List<Document> websiteLockConfigs = Jacksons.str2obj(new String(bytes), Jacksons.getInstance().getTypeFactory().constructCollectionType(List.class, Document.class));
//        embeddedMongoTester.getTestMongoTemplate().createCollection(collectionName).insertMany(websiteLockConfigs);
//    }
//
//    @Test
//    public void testFindByWebsiteIdIn() throws IOException {
//
//        populateData();
//
//        MongoTemplate testMongoTemplate = embeddedMongoTester.getTestMongoTemplate();
//
//        // mock dependencies
//        WebsiteLockConfigRepository websiteLockConfigRepository = Mockito.spy(WebsiteLockConfigRepository.class);
//        ReflectionTestUtils.setField(websiteLockConfigRepository, "mongoTemplate", testMongoTemplate);
//
//        // invoke & assert
//        List<Long> websiteIds = new ArrayList<>();
//        websiteIds.add(11L);
//        assertTrue(websiteLockConfigRepository.findByWebsiteIdIn(websiteIds).size() > 0);
//    }
//
//    @Test
//    public void testFindByWebsiteIdInNotFound() throws IOException {
//
//        populateData();
//
//        MongoTemplate testMongoTemplate = embeddedMongoTester.getTestMongoTemplate();
//
//        // mock dependencies
//        WebsiteLockConfigRepository websiteLockConfigRepository = Mockito.spy(WebsiteLockConfigRepository.class);
//        ReflectionTestUtils.setField(websiteLockConfigRepository, "mongoTemplate", testMongoTemplate);
//
//        // invoke & assert
//        List<Long> websiteIds = new ArrayList<>();
//        websiteIds.add(1L);
//        assertEquals(0, websiteLockConfigRepository.findByWebsiteIdIn(websiteIds).size());
//    }
//}