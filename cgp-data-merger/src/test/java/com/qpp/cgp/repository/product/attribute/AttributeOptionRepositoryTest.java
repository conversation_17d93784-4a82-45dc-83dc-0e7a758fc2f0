//package com.qpp.cgp.repository.product.attribute;
//
//import com.qpp.cgp.domain.attribute.Attribute;
//import com.qpp.cgp.domain.attribute.AttributeOption;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//
//import java.util.List;
//
//import static com.qpp.cgp.domain.product.ProductStatus.ACTIVE;
//import static com.qpp.cgp.domain.product.ProductStatus.REMOVED;
//import static org.junit.Assert.*;
//
//@RunWith(SpringRunner.class)
//@SpringBootTest
//public class AttributeOptionRepositoryTest {
//
//    @Autowired private AttributeOptionRepository optionRepository;
//
//    
//    @Test
//    public void findByIdTest() {
//        assertEquals(optionRepository.findById(123L), optionRepository.findByIdAndStatus(123L, ACTIVE));
//
//        long id = 133724;
//        assertEquals(optionRepository.findById(id), optionRepository.findByIdAndStatus(id, ACTIVE));
//
//        AttributeOption option = optionRepository.findById(id).get();
//        option.setStatus(REMOVED);
//        optionRepository.save(option);
//        assertTrue(optionRepository.findById(id).isPresent());
//        assertFalse(optionRepository.findByIdAndStatus(id, ACTIVE).isPresent());
//    }
//
//    
//    @Test
//    public void getOneTest() {
////        assertNull(optionRepository.getOne(123L));
////        assertNull(optionRepository.getOneByIdAndStatus(123L, ACTIVE));
//
//        long id = 133724;
//        assertNotNull(optionRepository.getOne(id));
//        assertEquals(optionRepository.getOne(id).getName(), optionRepository.getOneByIdAndStatus(id, ACTIVE).getName());
//
//        AttributeOption option = optionRepository.getOne(id);
//        option.setStatus(REMOVED);
//        optionRepository.save(option);
//        assertNull(optionRepository.getOneByIdAndStatus(id, ACTIVE));
//        assertNotNull(optionRepository.getOne(id));
//    }
//
//    
//    @Test
//    public void findByAttributeIdAndIdAndStatusTest() {
//
//        long attributeId = 133723;
//        long id = 133724;
//        assertNotNull(optionRepository.findByAttributeIdAndIdAndStatus(attributeId, id, ACTIVE));
////        assertEquals(optionRepository.findByAttributeIdAndIdAndStatus(attributeId, id, ACTIVE), optionRepository.findByAttributeIdAndId(attributeId, id));
//
////        AttributeOption option = optionRepository.findByAttributeIdAndId(attributeId, id);
////        option.setStatus(REMOVED);
////        optionRepository.save(option);
//        assertNull(optionRepository.findByAttributeIdAndIdAndStatus(attributeId, id, ACTIVE));
////        assertNotNull(optionRepository.findByAttributeIdAndId(attributeId, id));
//    }
//
//    
//    @Test
//    public void findByAttributeIdAndStatusTest() {
//
//        long attributeId = 133723;
//        assertNotNull(optionRepository.findByAttributeIdAndStatus(attributeId, ACTIVE));
////        assertEquals(optionRepository.findByAttributeIdAndStatus(attributeId, ACTIVE), optionRepository.findByAttributeId(attributeId));
//
////        List<AttributeOption> options = optionRepository.findByAttributeId(attributeId);
////        options.get(0).setStatus(REMOVED);
////        optionRepository.save(options.get(0));
////        assertEquals(optionRepository.findByAttributeIdAndStatus(attributeId, ACTIVE).size(), options.size() - 1);
//    }
//
//}