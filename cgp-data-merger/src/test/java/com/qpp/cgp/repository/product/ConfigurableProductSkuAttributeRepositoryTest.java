//package com.qpp.cgp.repository.product;
//
//import com.qpp.cgp.domain.product.ConfigurableProductSkuAttribute;
//import com.qpp.cgp.domain.product.ProductStatus;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//
//import java.util.List;
//
//import static com.qpp.cgp.domain.product.ProductStatus.ACTIVE;
//import static com.qpp.cgp.domain.product.ProductStatus.REMOVED;
//import static org.junit.Assert.assertEquals;
//
//@RunWith(SpringRunner.class)
//@SpringBootTest
//public class ConfigurableProductSkuAttributeRepositoryTest {
//
//    @Autowired
//    private ConfigurableProductSkuAttributeRepository configurableProductSkuAttributeRepository;
//
//    @Test
//    public void test() {
////        final List<ConfigurableProductSkuAttribute> skuAttributes = configurableProductSkuAttributeRepository.findByProductIdAndIsSku(1649434L, false);
////        final List<ConfigurableProductSkuAttribute> skuAttributes = configurableProductSkuAttributeRepository.findByProductIdAndIsSku(1649434L, false);
////        System.out.println(skuAttributes);
//    }
//
//
//    @Test
//    public void findByProductIdTest() {
//        long productId = 133829L;
////        List<ConfigurableProductSkuAttribute> skuAttributes = configurableProductSkuAttributeRepository.findByProductId(productId);
////        assertEquals(skuAttributes, configurableProductSkuAttributeRepository.findByProductIdAndStatus(productId, ACTIVE));
//
////        skuAttributes.get(0).setStatus(REMOVED);
////        configurableProductSkuAttributeRepository.save(skuAttributes.get(0));
////        assertEquals(configurableProductSkuAttributeRepository.findByProductId(productId).size() - 1, configurableProductSkuAttributeRepository.findByProductIdAndStatus(productId, ACTIVE).size());
//    }
//
//}