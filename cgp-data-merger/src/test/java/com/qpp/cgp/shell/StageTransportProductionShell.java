package com.qpp.cgp.shell;

import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class StageTransportProductionShell {

//    @Autowired
//    private MongoTemplate mongoTemplate;
//
//    @Qualifier(value = MongoTemplateBeanNames.CONFIG)
//    @Autowired
//    private MongoTemplate configMongoTemplate;
//
//    @Qualifier(value = MongoTemplateBeanNames.RUNTIME)
//    @Autowired
//    private MongoTemplate runtimeMongoTemplate;
//
//    @Test
//    public void transportCGP2DataBase() throws IOException {
//        List<String> collectionNames = FileUtils.readLines(new File("C:\\Users\\<USER>\\IdeaProjects\\cgp2\\cgp-project\\src\\test\\resources\\insertStage.txt"), "utf-8");
//        transportDataBase("default", collectionNames, mongoTemplate);
//    }
//
//    private void transportDataBase(String dataBase, List<String> collectionNames, MongoTemplate sourceMongoTemplate) {
//        List ids;
//        for (String collectionName : collectionNames) {
//            //首先获取正式已经存在的Id
//            ids = getExistsIds(dataBase, collectionName);
//            //判断Stage环境是否存在ExcludeIds
//            List<Bson> excludeIds = new ArrayList<>();
//            Bson ninFilter = new Document("$nin", ids);
//            Bson filterIds = new Document("_id", ninFilter);
//            Bson match = new Document("$match", filterIds);
//            excludeIds.add(match);
//            excludeIds.add(getProjectIdBson());
//            AggregateIterable<Document> filterCollection = sourceMongoTemplate.getCollection(collectionName).aggregate(excludeIds);
//            List saveIds = getIdList(filterCollection.iterator());
//            if (!saveIds.isEmpty()) {
//                //如果不为空的话，执行insert操作
//                Bson inOperation = new Document("$in", saveIds);
//                Bson insertFindQuery = new Document("_id", inOperation);
//                FindIterable<Document> insertDocument = sourceMongoTemplate.getCollection(collectionName).find(insertFindQuery);
//                List list = IteratorUtils.toList(insertDocument.iterator());
//                //校验数据是否正确
//                Assert.assertEquals(saveIds.size(), list.size());
//                MongoDatabase targetDataBase = MongoDatabaseFactory.getMongoDatabase(ProjectDeployEnv.prod, dataBase);
//                if (!list.isEmpty()) {
//                    System.out.println(collectionName+"=================inset size:" + saveIds.size() + " Start");
//                    targetDataBase.getCollection(collectionName).insertMany(list);
//                    System.out.println(collectionName + "=================inset size:" + saveIds.size() + " End");
//                }
//                //校验是否插入成功
//                Bson checkinFilter = new Document("$in", saveIds);
//                Bson checkFilterIds = new Document("_id", checkinFilter);
//                Bson checkMatch = new Document("$match", checkFilterIds);
//                List<Bson> checkQuery = new ArrayList<>();
//                checkQuery.add(checkMatch);
//                checkQuery.add(getProjectIdBson());
//                AggregateIterable<Document> checkInsertDocuments = targetDataBase.getCollection(collectionName).aggregate(checkQuery);
//                Assert.assertEquals(IteratorUtils.toList(checkInsertDocuments.iterator()).size(), saveIds.size());
//                System.out.println(collectionName + "======insert========" + saveIds);
//            }
//        }
//    }
//
//    private List getExistsIds(String dateBase,String collectionName) {
//        MongoDatabase mongoDatabase = MongoDatabaseFactory.getMongoDatabase(ProjectDeployEnv.prod, dateBase);
//        Bson projectId = getProjectIdBson();
//        List<Bson> bsons = new ArrayList<>();
//        bsons.add(projectId);
//        AggregateIterable<Document> orders = mongoDatabase.getCollection(collectionName).aggregate(bsons);
//        MongoCursor<Document> iterator = orders.iterator();
//        List ids = getIdList(iterator);
//        return ids;
//    }
//
//    private List getIdList(MongoCursor<Document> iterator) {
//        List list = IteratorUtils.toList(iterator);
//        List ids = (List) list.stream().map(e -> ((Document) e).get("_id")).collect(Collectors.toList());
//        return ids;
//    }
//
//    private Bson getProjectIdBson() {
//        Bson id = new Document("_id", 1);
//        Bson projectId = new Document("$project", id);
//        return projectId;
//    }
//
//    @Test
//    public void transportRunTime() throws IOException {
//        List<String> collectionNames = FileUtils.readLines(new File("C:\\Users\\<USER>\\IdeaProjects\\cgp2\\cgp-project\\src\\test\\resources\\insertRunTime.txt"), "utf-8");
//        transportDataBase("runtime",collectionNames,runtimeMongoTemplate);
//    }
//
//    @Test
//    public void transportConfig() throws IOException {
//        List<String> collectionNames = FileUtils.readLines(new File("C:\\Users\\<USER>\\IdeaProjects\\cgp2\\cgp-project\\src\\test\\resources\\insertConfig.txt"), "utf-8");
//        transportDataBase("config", collectionNames, configMongoTemplate);
//    }
}
