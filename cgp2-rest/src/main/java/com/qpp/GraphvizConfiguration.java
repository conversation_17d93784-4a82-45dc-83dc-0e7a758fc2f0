package com.qpp;

import guru.nidi.graphviz.engine.*;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PreDestroy;

/**
 * <AUTHOR> Lee 2020/8/29 15:27
 */
@Configuration
public class GraphvizConfiguration {

    private GraphvizEngine cmdEngine;

    private GraphvizEngine jdkEngine;


    public GraphvizConfiguration() {
        this.cmdEngine = new GraphvizCmdLineEngine();
        this.jdkEngine = new GraphvizJdkEngine();
        Graphviz.useEngine(this.cmdEngine, this.jdkEngine);
    }

    @PreDestroy
    public void preDestroy() {
        System.out.println("close graphviz engine");
        Graphviz.releaseEngine();

        try {
            cmdEngine.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {
            jdkEngine.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
