package com.qpp;

import com.google.common.collect.ImmutableMap;
import com.qpp.core.exception.BusinessException;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import com.qpp.web.core.exception.ServiceError;
import com.qpp.web.core.exception.ServiceResult;
import com.qpp.web.core.exception.ServiceResultGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MultipartException;

@RestControllerAdvice
public class MultipartExceptionResponseEntityExceptionHandler {
   private Logger logger = LoggerFactory.getLogger(this.getClass());

   @Autowired
   private ServiceResultGenerator serviceResultGenerator;

   @ExceptionHandler(MultipartException.class)
   public ResponseEntity<Object> handleMultipartException(MultipartException multipartException) {
      logger.error("multipartException:" + multipartException.getMessage());
      BusinessException businessException = BusinessExceptionBuilder.of(410001, ImmutableMap.of("message", multipartException.getMessage()==null?"":multipartException.getMessage()));
      ServiceResult<ServiceError> serviceResult = serviceResultGenerator.generateServiceResult(businessException);
      return new ResponseEntity<>(serviceResult, HttpStatus.OK);
   }
}
