<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="rpt_WorkOrder" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="d1a4a821-697a-4852-93a0-21652ccc024e">
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<subDataset name="Table Dataset 1" uuid="e1b2b4ee-b54e-4cfd-8415-4e7bb402d833"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["D:\\java\\workspace\\ps-mobile\\branches\\developing\\api\\cgp-api\\cgp-admin\\src\\main\\resources\\reports\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="ids" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="workItemIds" class="java.lang.String"/>
	<parameter name="itemIds" class="java.lang.String"/>
	<queryString>
		<![CDATA[SELECT
     *
FROM
      cgp_order
WHERE
     1 = 1
     and  order_id in($P!{ids})]]>
	</queryString>
	<field name="order_id" class="java.lang.Long">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="order_type" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="order_number" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="date_purchased" class="java.sql.Timestamp">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="paid_date" class="java.sql.Timestamp">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="confirmed_date" class="java.sql.Timestamp">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="customer_id" class="java.lang.Long">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="customer_name" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="customer_email" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_addr_id" class="java.lang.Long">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_name" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_company" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_country" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_state" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_city" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_suburb" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_street_address1" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_street_address2" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_location_type" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_postcode" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_telephone" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_mobile" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_email" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="billing_addr_id" class="java.lang.Long">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="billing_name" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="billing_company" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="billing_country" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="billing_state" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="billing_city" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="billing_suburb" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="billing_street_address1" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="billing_street_address2" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="billing_location_type" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="billing_postcode" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="billing_telephone" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="billing_mobile" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="billing_email" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="shipping_method" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="shipping_module_code" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="payment_method" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="payment_module_code" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="currency_code" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="currency_value" class="java.math.BigDecimal">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="total_qty" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="tax" class="java.math.BigDecimal">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="subtotal_ex_tax" class="java.math.BigDecimal">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="subtotal_inc_tax" class="java.math.BigDecimal">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="total_ex_tax" class="java.math.BigDecimal">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="total_inc_tax" class="java.math.BigDecimal">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_no" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_date" class="java.sql.Timestamp">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="shipping_refunded" class="java.math.BigDecimal">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="total_refunded" class="java.math.BigDecimal">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="created_date" class="java.sql.Timestamp">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="created_by" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="updated_date" class="java.sql.Timestamp">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="modified_by" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="version" class="java.lang.Long">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="website_id" class="java.lang.Long">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="order_status_id" class="java.lang.Long">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="currency_id" class="java.lang.Long">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="order_flow_id" class="java.lang.Long">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="40" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="0" width="555" height="40" uuid="df320ec5-4bff-4948-8dec-a11d5031640b"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Microsoft YaHei" size="24" isBold="false"/>
				</textElement>
				<text><![CDATA[網絡訂單生產跟蹤報表]]></text>
			</staticText>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="20" splitType="Stretch">
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="22" height="20" backcolor="#DDDDDD" uuid="894d6c1e-eecc-4e35-a3d2-30a70d71d669"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Microsoft YaHei"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<text><![CDATA[序號]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="22" y="0" width="77" height="20" backcolor="#DDDDDD" uuid="0397913d-c5b2-4d4e-a645-fbf943e5e3bd"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Microsoft YaHei"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<text><![CDATA[訂單號]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="134" y="0" width="198" height="20" backcolor="#DDDDDD" uuid="3afc517c-e869-4906-9321-d09bdddad214"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Microsoft YaHei"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<text><![CDATA[貨號 Item 名稱規格]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="367" y="0" width="45" height="20" backcolor="#DDDDDD" uuid="e5f28b3d-df18-4a26-82bb-163997f33be6"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Microsoft YaHei"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<text><![CDATA[未完成數]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="412" y="0" width="57" height="20" backcolor="#DDDDDD" uuid="e77305e8-f436-48fe-ac90-6e2a65932b1c"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Microsoft YaHei"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<text><![CDATA[審核日期]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="469" y="0" width="86" height="20" backcolor="#DDDDDD" uuid="a88deca9-75d5-4142-a26c-08348e5e79a6"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Microsoft YaHei"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<text><![CDATA[備注]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="99" y="0" width="35" height="20" backcolor="#DDDDDD" uuid="088f71b6-11f8-458d-906b-3788099131e4"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Microsoft YaHei"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<text><![CDATA[訂單項]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="332" y="0" width="35" height="20" backcolor="#DDDDDD" uuid="2054866d-02fe-44f2-892a-11835e73fe67"/>
				<box>
					<pen lineWidth="0.5"/>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Microsoft YaHei"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<text><![CDATA[類別]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<subreport>
				<reportElement stretchType="RelativeToBandHeight" x="99" y="0" width="313" height="20" uuid="f445711f-c11c-44b2-87bc-5d9dde61dab7"/>
				<subreportParameter name="workItemIds">
					<subreportParameterExpression><![CDATA[($P{workItemIds} == null || $P{workItemIds}.equals(""))?"1=1":"w.work_order_lineitem_id in ("+$P{workItemIds}+")"]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="itemIds">
					<subreportParameterExpression><![CDATA[($P{itemIds} == null || $P{itemIds}.equals(""))?"1=1":"c.order_lineitem_id in ("+$P{itemIds}+")"]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="orderId">
					<subreportParameterExpression><![CDATA[$F{order_id}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "rpt_WorkOrder_subreport.jasper"]]></subreportExpression>
			</subreport>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToTallestObject" x="22" y="0" width="77" height="20" uuid="55d156f1-c030-457a-a0a6-c5e93a4a6fa7"/>
				<box topPadding="2" leftPadding="2" rightPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Microsoft YaHei"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{order_number}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement stretchType="RelativeToTallestObject" x="0" y="0" width="22" height="20" uuid="f4d9478a-9471-4ac6-9715-4a2a9e05db30"/>
				<box topPadding="2" leftPadding="2" rightPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Microsoft YaHei"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="yyyy-MM-dd HH:mm" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject" mode="Transparent" x="412" y="0" width="57" height="20" isPrintWhenDetailOverflows="true" forecolor="#000000" backcolor="#FFFFFF" uuid="3798635b-4ac5-4d57-90a5-c5714de3a099"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Microsoft YaHei" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{confirmed_date}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToTallestObject" x="469" y="0" width="86" height="20" isPrintWhenDetailOverflows="true" uuid="72944dc7-3401-46b4-9bc2-955dbe94e2b3"/>
				<box topPadding="2" leftPadding="2" rightPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Microsoft YaHei"/>
				</textElement>
				<textFieldExpression><![CDATA[""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToTallestObject" x="99" y="0" width="313" height="20" isPrintWhenDetailOverflows="true" uuid="fa8735a7-42e0-4c7c-bdc2-c8de4f64cbfb"/>
				<box topPadding="2" leftPadding="2" rightPadding="2">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Microsoft YaHei"/>
				</textElement>
				<textFieldExpression><![CDATA[""]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
