<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="invoice" pageWidth="595" pageHeight="842" columnWidth="531" leftMargin="32" rightMargin="32" topMargin="36" bottomMargin="7" isFloatColumnFooter="true" uuid="10038303-b42c-460f-ac4f-6af97d0e97af">
	<property name="ireport.zoom" value="1.2100000000000006"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="226"/>
	<parameter name="imageServer" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="composingServer" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<parameter name="orderId" class="java.lang.String">
		<defaultValueExpression><![CDATA[]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[select
o.order_number,
o.date_purchased,
o.delivery_name,
o.delivery_email,
o.delivery_suburb,
o.delivery_state,
o.delivery_country,
o.delivery_mobile,
o.delivery_telephone,
o.delivery_street_address1,
o.delivery_company,
o.delivery_city,
o.delivery_street_address2,
o.delivery_location_type,
o.delivery_postcode,
o.delivery_location_type,

o.billing_name,
o.billing_email,
o.billing_suburb,
o.billing_state,
o.billing_country,
o.billing_mobile,
o.billing_telephone,
o.billing_street_address1,
o.billing_company,
o.billing_city,
o.billing_street_address2,
o.billing_location_type,
o.billing_postcode,
o.billing_location_type,
round(o.total_inc_tax,2) total_inc_tax,
round(o.subtotal_inc_tax,2) subtotal_inc_tax,
o.delivery_no,

s.order_status_name,
i.order_invoice_id,
shipping.title deliveryTitle,
shipping.code deliveryCode,
shipping.description deliveryDescription,

currency.cur_symbol_left,
currency.cur_symbol_right,
currency.cur_code,

payment.title payment_title,
o.total_qty,
lineitem.seq_no,
round(product.product_weight,2) product_weight,
round(lineitem.product_price,2) product_price,
product.product_name,
lineitem.qty,
round(lineitem.amt,2) amt,
product.product_sku,
round(ordertotal.value,2) shipping_cost,

project.project_thumb project_thumb,
image.media_name image_name

from cgp_order o
left join cgp_order_status s on (o.order_status_id=s.order_status_id)
left join cgp_order_invoice i on (i.order_id=o.order_id)
left join cgp_module_shipping shipping on (o.shipping_module_code=shipping.code and o.website_id=shipping.website_id)
left join cgp_currency currency on (o.website_id=currency.cur_website_id and o.currency_code=currency.cur_code)
join cgp_module_payment payment on (o.payment_module_code=payment.code and o.website_id=payment.website_id)
left join cgp_order_lineitem lineitem on (lineitem.order_id=o.order_id)
left join cgp_product product on (lineitem.product_id=product.product_id)
left join cgp_order_total ordertotal on (ordertotal.order_id=o.order_id and ordertotal.code='ot_shipping')
left join cgp_project project on (lineitem.project_id = project.project_id)
left join cgp_product_media image on (image.media_id=product.product_default_image_id and image.media_id is not null)
where o.order_id=$P{orderId};]]>
	</queryString>
	<field name="order_number" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="date_purchased" class="java.sql.Timestamp">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_name" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_email" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_suburb" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_state" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_country" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_mobile" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_telephone" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_street_address1" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_company" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_city" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_street_address2" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_location_type" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_postcode" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="billing_name" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="billing_email" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="billing_suburb" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="billing_state" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="billing_country" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="billing_mobile" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="billing_telephone" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="billing_street_address1" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="billing_company" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="billing_city" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="billing_street_address2" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="billing_location_type" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="billing_postcode" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="total_inc_tax" class="java.math.BigDecimal">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="subtotal_inc_tax" class="java.math.BigDecimal">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="delivery_no" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="order_status_name" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="order_invoice_id" class="java.lang.Long">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="deliveryTitle" class="java.lang.String"/>
	<field name="deliveryCode" class="java.lang.String"/>
	<field name="deliveryDescription" class="java.lang.String"/>
	<field name="cur_symbol_left" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="cur_symbol_right" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="cur_code" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="payment_title" class="java.lang.String"/>
	<field name="total_qty" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="seq_no" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="product_weight" class="java.lang.Double">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="product_price" class="java.math.BigDecimal">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="product_name" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="qty" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="amt" class="java.math.BigDecimal">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="product_sku" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="shipping_cost" class="java.math.BigDecimal"/>
	<field name="project_thumb" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="image_name" class="java.lang.String"/>
	<variable name="variable1" class="java.lang.String">
		<variableExpression><![CDATA[]]></variableExpression>
	</variable>
	<background>
		<band height="785">
			<textField>
				<reportElement x="191" y="765" width="80" height="20" uuid="c498ca4a-a0ab-44dc-89d2-97c0b4e12e06"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["Page: "+$V{PAGE_NUMBER}+"/"]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="270" y="765" width="40" height="20" uuid="73a1bf26-963b-4be7-abff-31ca45d2eff6"/>
				<textFieldExpression><![CDATA["" + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</background>
	<title>
		<band height="475" splitType="Stretch">
			<staticText>
				<reportElement x="204" y="11" width="122" height="29" uuid="85772a4a-3c58-4b5f-a667-6556aa16fbb7"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="24" isBold="true"/>
				</textElement>
				<text><![CDATA[INVOICE]]></text>
			</staticText>
			<staticText>
				<reportElement x="134" y="46" width="396" height="20" uuid="bf85c25f-6133-4730-8a4f-060fe10f61f8"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="8"/>
				</textElement>
				<text><![CDATA[CustomPrintbox.com  | 25th Floor, Delta House, 3 On Yiu Street, Shatin, New Territories, Hong Kong]]></text>
			</staticText>
			<image>
				<reportElement x="0" y="0" width="124" height="68" uuid="2df24813-90f7-4d65-8b9f-b8894d1c63db"/>
				<imageExpression><![CDATA["http://www.customprintbox.com/fileService/file/********************************.png"]]></imageExpression>
			</image>
			<textField isStretchWithOverflow="true">
				<reportElement x="0" y="100" width="219" height="20" uuid="eb11d732-d80e-4356-85b5-81b022742890"/>
				<textElement>
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Order Number: "+$F{order_number}+"(" + $F{order_status_name} + ")"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="219" y="100" width="57" height="20" uuid="fe59b06e-bcac-413a-9b83-3230de33ba85"/>
				<textElement>
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Order Date:]]></text>
			</staticText>
			<textField pattern="yyyy/MM/dd HH:mm:ss">
				<reportElement x="275" y="100" width="100" height="20" uuid="3c29b1b1-7254-4e0a-b4de-855a6e771acb"/>
				<textElement>
					<font fontName="Arial"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{date_purchased}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="395" y="100" width="133" height="20" uuid="48d1d5b2-5f94-4fab-9664-34b842f13811"/>
				<textElement markup="none">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Invoice #: SI" + $F{order_number}]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement mode="Opaque" x="0" y="123" width="530" height="167" uuid="1858ad2a-3f94-471f-abd0-ed26477a7580"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<staticText>
					<reportElement x="12" y="12" width="100" height="20" uuid="76f1c0fb-844b-4fbc-8dec-be6b6544eb6b"/>
					<textElement>
						<font fontName="Arial" isBold="true"/>
					</textElement>
					<text><![CDATA[Billing Address]]></text>
				</staticText>
				<staticText>
					<reportElement x="276" y="12" width="100" height="20" uuid="79011177-bf02-4f3e-8bf6-23537f7cbbdb"/>
					<textElement>
						<font fontName="Arial" isBold="true"/>
					</textElement>
					<text><![CDATA[Shipping Address]]></text>
				</staticText>
				<textField>
					<reportElement x="296" y="32" width="190" height="122" uuid="d5c4c4ac-d0b8-4d76-9888-320ffca3efb4"/>
					<textElement>
						<font fontName="Microsoft YaHei" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{billing_name} + "\n" +
$F{billing_location_type} + " " + $F{billing_street_address1} + "\n" +
(($F{billing_street_address2}==null||$F{billing_street_address2}.equals(""))?"":($F{billing_street_address2}+"\n"))+
$F{billing_country} + "\n" +
($F{billing_mobile}==null?"":($F{billing_mobile}+"\n")) +
$F{billing_email}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="32" y="32" width="190" height="122" uuid="f76b1fde-7897-4bce-a0d7-352b09a8d525"/>
					<textElement>
						<font fontName="Microsoft YaHei" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{delivery_name} + "\n" +
$F{delivery_location_type} + " " + $F{delivery_street_address1} + "\n" +
(($F{delivery_street_address2}==null||$F{delivery_street_address2}.equals(""))?"":($F{delivery_street_address2}+"\n"))+
$F{delivery_country} + "\n" +
($F{delivery_mobile}==null?"":($F{delivery_mobile}+"\n")) +
$F{delivery_email}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="290" width="530" height="105" uuid="f32e7a4e-1576-43f8-bc78-c9c2345f381d"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<staticText>
					<reportElement x="12" y="17" width="100" height="20" uuid="2771d829-a4f8-41c3-b176-562e07ff17b0"/>
					<textElement>
						<font fontName="Arial" isBold="true"/>
					</textElement>
					<text><![CDATA[Transport Mode]]></text>
				</staticText>
				<textField>
					<reportElement x="73" y="37" width="423" height="20" uuid="de31c9ed-0704-4040-9505-21ecf6df9f10"/>
					<textFieldExpression><![CDATA[$F{deliveryTitle} + " - " + $F{deliveryDescription} +$F{cur_symbol_left}+$F{shipping_cost}+$F{cur_symbol_right}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="12" y="64" width="417" height="41" uuid="35cd19d7-9f84-4f23-9aa4-5d5702ee90ad">
						<printWhenExpression><![CDATA[$F{delivery_no}!=null]]></printWhenExpression>
					</reportElement>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Tracking No.:" + $F{delivery_no} + "\n\n" +
"(Please note for Express orders, there is a $10 surcharge for address corrections after tracking no. is issued)"]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="395" width="530" height="58" uuid="d37e808e-eb81-479e-bbe4-522bd04b7cc9"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<staticText>
					<reportElement x="12" y="13" width="291" height="20" uuid="2eee7f32-f6b3-42ec-b4bc-70548b5f783d"/>
					<textElement>
						<font fontName="Arial" isBold="true"/>
					</textElement>
					<text><![CDATA[Payment Method:]]></text>
				</staticText>
				<textField>
					<reportElement x="43" y="33" width="260" height="20" uuid="ebb72502-00b4-4e1a-94f9-885d0a29d51a"/>
					<textFieldExpression><![CDATA[$F{payment_title}]]></textFieldExpression>
				</textField>
			</frame>
			<frame>
				<reportElement x="0" y="453" width="530" height="22" uuid="34f1fc8e-a891-4243-b65b-f620fd50f64f"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textField>
					<reportElement x="12" y="0" width="340" height="20" uuid="6e1fb8c4-f1ae-4b8d-8b4e-b1c626859735"/>
					<textElement>
						<font fontName="Arial" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA["Product(Total: "+ $F{total_qty} +")"]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</title>
	<columnHeader>
		<band height="28" splitType="Stretch">
			<staticText>
				<reportElement x="6" y="5" width="27" height="23" uuid="1f9d7e04-6b0b-43ad-840c-4961e4d78475"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<text><![CDATA[No]]></text>
			</staticText>
			<staticText>
				<reportElement x="33" y="5" width="77" height="23" uuid="8297d814-a917-43c2-8752-00b755aff9c5"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<text><![CDATA[Item#]]></text>
			</staticText>
			<staticText>
				<reportElement x="110" y="5" width="217" height="23" uuid="64dee439-3d17-4890-a2af-e926bb4598c5"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<text><![CDATA[Product]]></text>
			</staticText>
			<staticText>
				<reportElement x="327" y="5" width="57" height="23" uuid="3af1063d-3f0e-4fa0-bf53-cd92f1513b60"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<text><![CDATA[Weight(grams)]]></text>
			</staticText>
			<staticText>
				<reportElement x="384" y="5" width="40" height="23" uuid="98745afd-9be5-4e49-b218-811ff8b0d973"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<text><![CDATA[Price]]></text>
			</staticText>
			<staticText>
				<reportElement x="424" y="5" width="45" height="23" uuid="0ef279be-905d-4181-aed9-752684b1bddf"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<text><![CDATA[Quantity]]></text>
			</staticText>
			<staticText>
				<reportElement x="469" y="5" width="55" height="23" uuid="56c2d750-5034-44bc-bc97-70e3a5efe677"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<text><![CDATA[Amount]]></text>
			</staticText>
			<frame>
				<reportElement x="0" y="0" width="530" height="28" uuid="42ad66cf-73f9-4a50-97d7-7721b2d08b70"/>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="78" splitType="Stretch">
			<textField>
				<reportElement x="327" y="-1" width="57" height="78" uuid="362928a1-52e7-4bc2-84d9-4e4c9757aa7b"/>
				<box rightPadding="5">
					<topPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{product_weight}]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement x="33" y="0" width="77" height="77" uuid="93e8dc99-8d86-4561-b885-b4936e25c716"/>
				<box>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<image>
					<reportElement x="17" y="13" width="50" height="40" uuid="9094bb94-6dec-4aef-9c42-097e517cc509"/>
					<imageExpression><![CDATA[$F{project_thumb}==null? ($P{imageServer}+"/"+$F{image_name}.toString() + "/50/40/png"): ($P{imageServer}+"/composingPreview/" + $F{project_thumb}.toString() + "-0.jpg/50/40/png")]]></imageExpression>
				</image>
			</frame>
			<textField>
				<reportElement x="110" y="-1" width="217" height="78" uuid="6991ffc6-9002-4c44-bd31-815af4199341"/>
				<box>
					<topPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{product_name}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="6" y="-1" width="27" height="78" uuid="61836841-bd8b-4b7b-952f-76db8ef62cec"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{seq_no}]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00">
				<reportElement x="384" y="-1" width="40" height="78" uuid="cf2b855d-0658-4010-b919-6164689f671d"/>
				<box rightPadding="5">
					<topPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cur_symbol_left}+$F{product_price}+$F{cur_symbol_right}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="424" y="-1" width="45" height="78" uuid="79cf0320-848c-4e2a-8559-db052a4acfea"/>
				<box rightPadding="5">
					<topPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{qty}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="469" y="-1" width="55" height="78" uuid="7944688e-d0f6-422c-bd8f-373850bcc84b"/>
				<box rightPadding="5">
					<topPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cur_symbol_left}+$F{amt}+$F{cur_symbol_right}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="33" y="52" width="77" height="25" uuid="7bd52b5d-39b2-4317-8685-df3d149ab29f"/>
				<box>
					<topPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{product_sku}]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement x="0" y="0" width="530" height="78" uuid="76b384e8-70f4-4bbf-b799-49a9f570090c"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
			</frame>
		</band>
	</detail>
	<columnFooter>
		<band height="6">
			<frame>
				<reportElement positionType="Float" x="0" y="0" width="530" height="6" uuid="d5292e04-6332-4f45-9d3d-48bb4171ba15"/>
				<box>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
			</frame>
		</band>
	</columnFooter>
	<pageFooter>
		<band height="50"/>
	</pageFooter>
	<summary>
		<band height="108" splitType="Stretch">
			<staticText>
				<reportElement x="0" y="75" width="530" height="33" uuid="e265d18e-7386-4409-bbc8-f397f676988f"/>
				<box leftPadding="5">
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<frame>
				<reportElement positionType="Float" x="0" y="0" width="530" height="75" uuid="78a2427e-ba67-433f-a075-73d9e2a7cb57"/>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<frame>
					<reportElement x="0" y="0" width="530" height="6" uuid="4494a436-0c39-43d2-bd0c-587e8bc51dce">
						<printWhenExpression><![CDATA[$V{PAGE_COUNT}!=0]]></printWhenExpression>
					</reportElement>
					<box>
						<leftPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
				</frame>
				<textField>
					<reportElement x="377" y="6" width="72" height="48" uuid="78c3ca31-fef2-48c7-a46a-8b3d5726dc89"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA["Product Total : \nShipping Charges : \nReward Credit : \nSubTotal : "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="473" y="6" width="55" height="10" uuid="83316c4b-06f7-4547-9a11-8299e5de868f"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{cur_symbol_left} + $F{subtotal_inc_tax} + $F{cur_symbol_right}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="473" y="16" width="55" height="10" uuid="d20ae201-2469-4c26-bb1f-260b5726408a"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{cur_symbol_left} + $F{shipping_cost} + $F{cur_symbol_right}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="473" y="26" width="55" height="10" uuid="41e35b82-d4fc-44b1-af51-92adb986d5dd"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{cur_symbol_left} +
($F{total_inc_tax}
    .subtract($F{subtotal_inc_tax})
    .subtract($F{shipping_cost}))
+ $F{cur_symbol_right}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="473" y="36" width="55" height="10" uuid="9e64da2e-77d2-4cb8-9470-33964abd5035"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{cur_code} + $F{cur_symbol_left} + ($F{total_inc_tax}) + $F{cur_symbol_right}]]></textFieldExpression>
				</textField>
				<frame>
					<reportElement x="0" y="0" width="530" height="6" uuid="5ddb68ad-f9a9-4802-a9e5-511dec9350db"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="0.5"/>
						<rightPen lineWidth="0.0"/>
					</box>
				</frame>
				<frame>
					<reportElement x="0" y="6" width="530" height="69" uuid="26390198-bc4b-45ea-9d73-19ee14737047"/>
					<box>
						<leftPen lineWidth="0.5"/>
						<rightPen lineWidth="0.5"/>
					</box>
				</frame>
			</frame>
			<staticText>
				<reportElement x="0" y="93" width="530" height="15" uuid="95467b2a-8ec0-4859-98a4-ad5b3f1d3562"/>
				<box>
					<bottomPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Thank You For Your Business!]]></text>
			</staticText>
			<staticText>
				<reportElement x="5" y="80" width="525" height="15" uuid="f73038f9-36a6-4686-95bd-7911d3d6f852"/>
				<textElement markup="html">
					<font fontName="Arial" size="8"/>
				</textElement>
				<text><![CDATA[If you have any questions about this invoice, please contact us via <a href="mailto:<EMAIL>"><span style="text-decoration:underline;color:orange"><EMAIL></span></a> at anytime.]]></text>
			</staticText>
			<frame>
				<reportElement x="0" y="95" width="528" height="13" uuid="db0dc44a-1123-4aca-8d6f-1d33786d6ff2">
					<printWhenExpression><![CDATA[$V{PAGE_COUNT}==0]]></printWhenExpression>
				</reportElement>
				<box>
					<bottomPen lineWidth="0.5"/>
				</box>
			</frame>
		</band>
	</summary>
</jasperReport>
