<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="rpt_WorkOrder_subreport_subreport2" pageWidth="555" pageHeight="802" columnWidth="555" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="c88bdd44-22d4-481b-ad28-db4a773c3298">
	<property name="ireport.zoom" value="2.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="itemId" class="java.lang.String"/>
	<queryString>
		<![CDATA[select * from cgp_order_lineitem_prd_attr_value a,cgp_attribute b
where a.oli_pav_attr_id = b.attr_id  and oli_pav_attr_input_type<>'DiyConfig'
and b.attr_show_in_frontend=0
and oli_pav_lineitem_id = $P{itemId}]]>
	</queryString>
	<field name="oli_pav_id" class="java.lang.Long">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="oli_pav_attr_id" class="java.lang.Long">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="oli_pav_attr_input_type" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="oli_pav_attr_name" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="oli_pav_value" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="oli_pav_option_ids" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="created_date" class="java.sql.Timestamp">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="created_by" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="updated_date" class="java.sql.Timestamp">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="modified_by" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="version" class="java.lang.Long">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="oli_pav_lineitem_id" class="java.lang.Long">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="attr_id" class="java.lang.Long">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="attr_code" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="attr_name" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="attr_required" class="java.lang.Boolean">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="attr_input_type" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="attr_validation_exp" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="attr_show_in_frontend" class="java.lang.Boolean">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="attr_use_in_category_nav" class="java.lang.Boolean">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="attr_sort_order" class="java.lang.Integer">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="22" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="0" width="204" height="22" uuid="9ee2100c-99fc-43a6-a0c2-206c5738865d"/>
				<box leftPadding="2"/>
				<textElement verticalAlignment="Top">
					<font fontName="Microsoft YaHei" size="10" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{oli_pav_value}.toString()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
