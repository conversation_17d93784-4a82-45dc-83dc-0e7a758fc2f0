//package com.qpp.cgp.util;
//
//import com.fasterxml.jackson.core.JsonProcessingException;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.itextpdf.text.DocumentException;
//import com.itextpdf.text.Rectangle;
//import com.itextpdf.text.pdf.PdfReader;
//import com.itextpdf.text.pdf.PdfStamper;
//import com.qpp.cgp.config.mongo.MongoTemplateBeanNames;
//import com.qpp.cgp.domain.bom.PageContentSchema;
//import com.qpp.cgp.domain.bom.runtime.PageContent;
//import com.qpp.cgp.domain.common.Country;
//import com.qpp.cgp.domain.common.Language;
//import com.qpp.cgp.domain.preprocess.dto.BuilderCheckDTO;
//import com.qpp.cgp.domain.preprocess.holiday.*;
//import com.qpp.cgp.domain.product.SkuProduct;
//import com.qpp.cgp.domain.product.config.ProductConfig;
//import com.qpp.cgp.domain.product.config.ProductConfigImposition;
//import com.qpp.cgp.domain.upload.UploadFile;
//import com.qpp.cgp.expression.Expression;
//import com.qpp.cgp.expression.ExpressionEngine;
//import com.qpp.cgp.manager.file.FileManager;
//import com.qpp.cgp.repository.product.ProductRepository;
//import com.qpp.cgp.value.ExpressionValueEx;
//import com.qpp.cgp.value.ValueType;
//import com.qpp.commons.pdf.PdfPageUtils;
//import com.qpp.id.generator.IdGenerator;
//import com.qpp.mongo.driver.HybridMongoTemplate;
//import org.apache.commons.io.FileUtils;
//import org.apache.commons.io.IOUtils;
//import org.apache.commons.lang.StringUtils;
//import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
//import org.apache.poi.ss.usermodel.*;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.core.io.ByteArrayResource;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.data.mongodb.core.query.Criteria;
//import org.springframework.data.mongodb.core.query.Query;
//import org.springframework.test.context.junit4.SpringRunner;
//import org.springframework.util.StreamUtils;
//
//import java.io.*;
//import java.math.BigDecimal;
//import java.net.URL;
//import java.text.DecimalFormat;
//import java.text.ParseException;
//import java.text.SimpleDateFormat;
//import java.util.*;
//import java.util.stream.Collectors;
//import java.util.zip.ZipEntry;
//import java.util.zip.ZipOutputStream;
//
///**
// * <AUTHOR> jerry
// * @version : 1.0
// * Description : 文件测试
// * Create Date : 2019-10-22 14:51
// **/
//@SpringBootTest
//@RunWith(SpringRunner.class)
//public class FileTest {
//
//
//    @Autowired
//    private ProductRepository productRepository;
//
//    @Autowired
//    private MongoTemplate mongoTemplate;
//
//    @Test
//    public void testFor() {
//        System.out.println("start:");
//        BigDecimal weight = new BigDecimal(0);
//        final Double wid = new Double(22);
//        for (int i = 0; i < 10000000; i++) {
//            weight = weight.add(new BigDecimal(wid));
//        }
//        System.out.println(weight);
//        System.out.println("end!");
//    }
//
//    @Test
//    public void Te() {
//        double d = 0.0;
//        DecimalFormat df = new DecimalFormat("0.00");
//        System.out.println(df.format(d));
//    }
//
//
//    @Test
//    public void testDate() throws ParseException {
//        Long date = 1573540246577L;
//        final SimpleDateFormat simpleDateFormat = new SimpleDateFormat();
//        final Date parse = simpleDateFormat.parse(String.valueOf(date));
//    }
//
//    @Test
//    public void parserPDFTest() throws IOException {
//        final File file = new File("C:\\Program Files\\feiq\\Recv Files\\20200113_Playing card qty_108 pcs.PDF");
//        InputStream inputStream = new FileInputStream(file);
//        final PdfReader pdfReader = new PdfReader(inputStream);
//        for (int i = 0; i < pdfReader.getNumberOfPages(); i++) {
//            final Rectangle pageSize = pdfReader.getPageSize(i + 1);
//            System.out.println("i:" + i + " width:" + pageSize.getWidth());
//            System.out.println("i:" + i + " height" + pageSize.getHeight());
//        }
//    }
//
//    @Test
//    public void testJsonParser() throws JsonProcessingException {
//        Map<String, Object> templateConfigIds = new HashMap<>();
//        final BuilderCheckDTO builderCheckDTO = new BuilderCheckDTO();
//        builderCheckDTO.setSbomPath("123");
//        builderCheckDTO.setSsmvtId("321");
//        builderCheckDTO.setTemplateFormat("3213");
//        templateConfigIds.put("123", builderCheckDTO);
//        final String s = new ObjectMapper().writeValueAsString(templateConfigIds);
//        System.out.println(s);
//    }
//
//    @Test
//    public void testMath() {
//        float i = 100.48f;
//
//        final Double floor = Math.floor(i);
//        final int i1 = floor.intValue();
//        System.out.println(floor);
//    }
//
//    @Test
//    public void test() throws IOException {
//        final URL url = new URL("http://192.168.26.70/file/file/fcc4558556fb5f50169e2522b223c89a.jpg");
//        final InputStream inputStream = url.openStream();
//        final File file = new File("a.jpg");
//        FileUtils.copyInputStreamToFile(inputStream, file);
//    }
//
//    @Test
//    public void fileTest() throws IOException {
//        final File file1 = new File("D:\\Work\\IDEA\\WorkSpace\\CGP2\\cgp-product\\ab.pdf");
//        final File file2 = new File("D:\\Work\\IDEA\\WorkSpace\\CGP2\\cgp-product\\abc.pdf");
//        final ByteArrayOutputStream byteArrayOutputStream1 = new ByteArrayOutputStream();
//        final ByteArrayOutputStream byteArrayOutputStream2 = new ByteArrayOutputStream();
//        StreamUtils.copy(new FileInputStream(file1), byteArrayOutputStream1);
//        StreamUtils.copy(new FileInputStream(file2), byteArrayOutputStream2);
//
//    }
//
//    @Test
//    public void zipTest() throws IOException {
//        final File file1 = new File("D:\\Work\\IDEA\\WorkSpace\\CGP2\\cgp-product\\ab.pdf");
//        final File file2 = new File("D:\\Work\\IDEA\\WorkSpace\\CGP2\\cgp-product\\abc.pdf");
//        final ByteArrayOutputStream byteArrayOutputStream1 = new ByteArrayOutputStream();
//        final ByteArrayOutputStream byteArrayOutputStream2 = new ByteArrayOutputStream();
//        StreamUtils.copy(new FileInputStream(file1), byteArrayOutputStream1);
//        StreamUtils.copy(new FileInputStream(file2), byteArrayOutputStream2);
//        final ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
//        final ZipOutputStream zipOutputStream = new ZipOutputStream(byteArrayOutputStream);
//        zipOutputStream.putNextEntry(new ZipEntry(file2.getName()));
//        zipOutputStream.write(byteArrayOutputStream2.toByteArray());
//        zipOutputStream.putNextEntry(new ZipEntry(file1.getName()));
//        zipOutputStream.write(byteArrayOutputStream1.toByteArray());
//        zipOutputStream.close();
//        final File file = new File("a.zip");
//        FileUtils.copyInputStreamToFile(new ByteArrayInputStream(byteArrayOutputStream.toByteArray()), file);
//    }
//
//    @Test
//    public void test2() {
//        String name = "ApplicationCotenxt";
//        System.out.println(StringUtils.uncapitalize(name));
//    }
//
//    @Test
//    public void test3() throws IOException, DocumentException {
//
//        final File file1 = new File("1.pdf");
//        final File file2 = new File("2.pdf");
//        final FileInputStream fileInputStream = new FileInputStream(file1);
//        final FileInputStream fileInputStream1 = new FileInputStream(file2);
//
//        final ByteArrayOutputStream targetOutputStream = new ByteArrayOutputStream();
//        final PdfReader sourcePdfReader = new PdfReader(fileInputStream1);
//        final PdfReader targetPdfReader = new PdfReader(fileInputStream);
//
//        final PdfStamper target = new PdfStamper(targetPdfReader, targetOutputStream);
//        PdfPageUtils.insertPage(target, sourcePdfReader, 1);
//        target.flush();
//        target.close();
//        final ByteArrayInputStream targetByteInputStream = new ByteArrayInputStream(targetOutputStream.toByteArray());
//        final File file = new File("a.pdf");
//        FileUtils.copyInputStreamToFile(targetByteInputStream, file);
//
//    }
//
//    @Test
//    public void test4() throws JsonProcessingException {
//        Map<String, Object> map = new HashMap<>();
//        map.put("1953374", 1953376);
//        map.put("1953369", 1953371);
//        map.put("1953336", 1953337);
//        map.put("1953329", 10);
//        map.put("1953362", 1953363);
//        map.put("1953330", 1953331);
//        map.put("1953321", 1953355);
//        map.put("1953354", 1953355);
//        map.put("1953349", 12);
//        map.put("1953380", 1953382);
//        map.put("1953348", 13);
//        map.put("1953383", 1953384);
//        map.put("1953351", 14);
//        map.put("1953350", 15);
//        map.put("1953345", 1953346);
//        map.put("1961884", 1961886);
//        System.out.println(new ObjectMapper().writeValueAsString(map));
//    }
//
//    private Object check(Object target) {
//        if (target instanceof Map) {
//            ((Map) target).keySet().forEach(e -> {
//                if (e.equals("modifiedDate") || e.equals("createdDate")) {
//                    //将该值设置为null
//                    ((Map) target).replace(e, null);
//                } else {
//                    ((Map) target).replace(e, check(((Map) target).get(e)));
//                }
//            });
//            return target;
//        } else if (target instanceof List) {
//            List replace = new ArrayList();
//            ((List) target).forEach(e -> {
//                final Object check = check(e);
//                replace.add(check);
//            });
//            return replace;
//        } else {
//            return target;
//        }
//    }
//
//    @Test
//    public void test5() throws IOException {
//        final ObjectMapper objectMapper = new ObjectMapper();
//        String json = "{\n" +
//                "  \"_id\": \"2120640\",\n" +
//                "  \"idReference\": null,\n" +
//                "  \"clazz\": \"com.qpp.cgp.domain.bom.runtime.ProductInstance\",\n" +
//                "  \"productId\": 2120641,\n" +
//                "  \"name\": \"echo\",\n" +
//                "  \"material\": {\n" +
//                "    \"_id\": \"2030755\",\n" +
//                "    \"idReference\": null,\n" +
//                "    \"clazz\": null,\n" +
//                "    \"createdDate\": null,\n" +
//                "    \"createdBy\": null,\n" +
//                "    \"modifiedDate\": null,\n" +
//                "    \"modifiedBy\": null,\n" +
//                "    \"code\": null,\n" +
//                "    \"name\": null,\n" +
//                "    \"childItems\": null,\n" +
//                "    \"defaultViewTypes\": null,\n" +
//                "    \"parentMaterialType\": null,\n" +
//                "    \"rtType\": null,\n" +
//                "    \"rtObject\": null,\n" +
//                "    \"spuRtTypeRtObject\": null,\n" +
//                "    \"category\": null,\n" +
//                "    \"categoryName\": null,\n" +
//                "    \"leaf\": null,\n" +
//                "    \"isOutSourcing\": false,\n" +
//                "    \"multilingualKey\": \"com.qpp.cgp.domain.bom.Material\"\n" +
//                "  },\n" +
//                "  \"productMaterialViews\": [\n" +
//                "    {\n" +
//                "      \"_id\": null,\n" +
//                "      \"idReference\": null,\n" +
//                "      \"clazz\": \"com.qpp.cgp.domain.bom.runtime.ProductMaterialView\",\n" +
//                "      \"createdDate\": null,\n" +
//                "      \"createdBy\": null,\n" +
//                "      \"modifiedDate\": null,\n" +
//                "      \"modifiedBy\": null,\n" +
//                "      \"materialViewType\": {\n" +
//                "        \"_id\": \"2085328\",\n" +
//                "        \"idReference\": \"MaterialViewType\",\n" +
//                "        \"clazz\": \"com.qpp.cgp.domain.bom.MaterialViewType\",\n" +
//                "        \"createdDate\": null,\n" +
//                "        \"createdBy\": null,\n" +
//                "        \"modifiedDate\": null,\n" +
//                "        \"modifiedBy\": null,\n" +
//                "        \"name\": null,\n" +
//                "        \"designTypeId\": null,\n" +
//                "        \"preDesignObject\": null,\n" +
//                "        \"sequenceNumber\": null,\n" +
//                "        \"description\": null,\n" +
//                "        \"pageContentStrategy\": null,\n" +
//                "        \"pageContentFetchStrategy\": null,\n" +
//                "        \"pageContentQuantity\": null,\n" +
//                "        \"viewQuantity\": null,\n" +
//                "        \"pageType\": null,\n" +
//                "        \"pageContentSchema\": null,\n" +
//                "        \"pageContentIndexExpression\": null,\n" +
//                "        \"pageContentInstanceRange\": null,\n" +
//                "        \"pageContentRange\": null,\n" +
//                "        \"userAssign\": null,\n" +
//                "        \"mainVariableDataSource\": null,\n" +
//                "        \"dsDataSource\": null,\n" +
//                "        \"pcsPlaceholders\": null,\n" +
//                "        \"templateType\": null,\n" +
//                "        \"multilingualKey\": \"com.qpp.cgp.domain.bom.MaterialViewType\"\n" +
//                "      },\n" +
//                "      \"pageContents\": [\n" +
//                "        {\n" +
//                "          \"_id\": \"2085235\",\n" +
//                "          \"idReference\": null,\n" +
//                "          \"clazz\": \"com.qpp.cgp.domain.bom.runtime.PageContent\",\n" +
//                "          \"createdDate\": 1573209567603,\n" +
//                "          \"createdBy\": \"578\",\n" +
//                "          \"modifiedDate\": 1573695820458,\n" +
//                "          \"modifiedBy\": \"1487389\",\n" +
//                "          \"index\": null,\n" +
//                "          \"code\": \"\",\n" +
//                "          \"name\": \"\",\n" +
//                "          \"width\": null,\n" +
//                "          \"height\": null,\n" +
//                "          \"layers\": [\n" +
//                "            {\n" +
//                "              \"items\": [\n" +
//                "                {\n" +
//                "                  \"x\": 0,\n" +
//                "                  \"y\": 0,\n" +
//                "                  \"rotation\": 0,\n" +
//                "                  \"width\": 164,\n" +
//                "                  \"height\": 252,\n" +
//                "                  \"printFile\": null\n" +
//                "                }\n" +
//                "              ],\n" +
//                "              \"description\": \"天盒印刷层\",\n" +
//                "              \"code\": \"printingLayer\"\n" +
//                "            },\n" +
//                "            {\n" +
//                "              \"items\": [\n" +
//                "                {\n" +
//                "                  \"x\": 0,\n" +
//                "                  \"y\": 0,\n" +
//                "                  \"rotation\": 0,\n" +
//                "                  \"width\": 164,\n" +
//                "                  \"height\": 252,\n" +
//                "                  \"printFile\": null\n" +
//                "                }\n" +
//                "              ],\n" +
//                "              \"description\": \"天盒烫金层\",\n" +
//                "              \"code\": \"goldFoilLayer\"\n" +
//                "            }\n" +
//                "          ],\n" +
//                "          \"rtObject\": null,\n" +
//                "          \"pageContentSchemaId\": null,\n" +
//                "          \"clipPath\": null,\n" +
//                "          \"templateId\": \"\",\n" +
//                "          \"multilingualKey\": \"com.qpp.cgp.domain.bom.runtime.PageContent\"\n" +
//                "        }\n" +
//                "      ],\n" +
//                "      \"materialPath\": \"1947217,1947236,1947228\",\n" +
//                "      \"pageContentItemPlaceholderObjects\": null,\n" +
//                "      \"designObject\": null,\n" +
//                "      \"productMaterialViewType\": {\n" +
//                "        \"_id\": \"2085385\",\n" +
//                "        \"idReference\": null,\n" +
//                "        \"clazz\": \"com.qpp.cgp.domain.bom.ProductMaterialViewType\",\n" +
//                "        \"createdDate\": null,\n" +
//                "        \"createdBy\": null,\n" +
//                "        \"modifiedDate\": 1573281460700,\n" +
//                "        \"modifiedBy\": \"578\",\n" +
//                "        \"materialPath\": \"1947217,1947236,1947228\",\n" +
//                "        \"conditionExpression\": \"\",\n" +
//                "        \"condition\": null,\n" +
//                "        \"materialViewType\": {\n" +
//                "          \"_id\": \"2085328\",\n" +
//                "          \"idReference\": \"MaterialViewType\",\n" +
//                "          \"clazz\": \"com.qpp.cgp.domain.bom.MaterialViewType\",\n" +
//                "          \"createdDate\": null,\n" +
//                "          \"createdBy\": null,\n" +
//                "          \"modifiedDate\": null,\n" +
//                "          \"modifiedBy\": null,\n" +
//                "          \"name\": null,\n" +
//                "          \"designTypeId\": null,\n" +
//                "          \"preDesignObject\": null,\n" +
//                "          \"sequenceNumber\": null,\n" +
//                "          \"description\": null,\n" +
//                "          \"pageContentStrategy\": null,\n" +
//                "          \"pageContentFetchStrategy\": null,\n" +
//                "          \"pageContentQuantity\": null,\n" +
//                "          \"viewQuantity\": null,\n" +
//                "          \"pageType\": null,\n" +
//                "          \"pageContentSchema\": null,\n" +
//                "          \"pageContentIndexExpression\": null,\n" +
//                "          \"pageContentInstanceRange\": null,\n" +
//                "          \"pageContentRange\": null,\n" +
//                "          \"userAssign\": null,\n" +
//                "          \"mainVariableDataSource\": null,\n" +
//                "          \"dsDataSource\": null,\n" +
//                "          \"pcsPlaceholders\": null,\n" +
//                "          \"templateType\": null,\n" +
//                "          \"multilingualKey\": \"com.qpp.cgp.domain.bom.MaterialViewType\"\n" +
//                "        },\n" +
//                "        \"productConfigDesignId\": 1947495,\n" +
//                "        \"productMaterialViewTypeId\": \"2085384\",\n" +
//                "        \"materialSelector\": {\n" +
//                "          \"_id\": null,\n" +
//                "          \"idReference\": null,\n" +
//                "          \"clazz\": \"com.qpp.cgp.domain.bom.material.IdPathSelector\",\n" +
//                "          \"createdDate\": null,\n" +
//                "          \"createdBy\": null,\n" +
//                "          \"modifiedDate\": null,\n" +
//                "          \"modifiedBy\": null,\n" +
//                "          \"idPath\": \"1947217,1947236,1947228\",\n" +
//                "          \"multilingualKey\": \"com.qpp.cgp.domain.bom.material.IdPathSelector\"\n" +
//                "        },\n" +
//                "        \"pageContentQty\": {\n" +
//                "          \"_id\": null,\n" +
//                "          \"idReference\": null,\n" +
//                "          \"clazz\": \"com.qpp.cgp.value.ConstantValue\",\n" +
//                "          \"createdDate\": null,\n" +
//                "          \"createdBy\": null,\n" +
//                "          \"modifiedDate\": null,\n" +
//                "          \"modifiedBy\": null,\n" +
//                "          \"type\": \"Number\",\n" +
//                "          \"constraints\": [],\n" +
//                "          \"value\": \"1\",\n" +
//                "          \"multilingualKey\": \"com.qpp.cgp.value.ConstantValue\"\n" +
//                "        },\n" +
//                "        \"defaultNode\": null,\n" +
//                "        \"productMaterialViewTypeIds\": null,\n" +
//                "        \"materialViewAttribute\": null,\n" +
//                "        \"name\": \"天盒ViewType\",\n" +
//                "        \"productMaterialViewTemplateConfigId\": \"2086935\",\n" +
//                "        \"multilingualKey\": \"com.qpp.cgp.domain.bom.ProductMaterialViewType\"\n" +
//                "      },\n" +
//                "      \"multilingualKey\": \"com.qpp.cgp.domain.bom.runtime.ProductMaterialView\"\n" +
//                "    },\n" +
//                "    {\n" +
//                "      \"_id\": null,\n" +
//                "      \"idReference\": null,\n" +
//                "      \"clazz\": \"com.qpp.cgp.domain.bom.runtime.ProductMaterialView\",\n" +
//                "      \"createdDate\": null,\n" +
//                "      \"createdBy\": null,\n" +
//                "      \"modifiedDate\": null,\n" +
//                "      \"modifiedBy\": null,\n" +
//                "      \"materialViewType\": {\n" +
//                "        \"_id\": \"2085347\",\n" +
//                "        \"idReference\": \"MaterialViewType\",\n" +
//                "        \"clazz\": \"com.qpp.cgp.domain.bom.MaterialViewType\",\n" +
//                "        \"createdDate\": null,\n" +
//                "        \"createdBy\": null,\n" +
//                "        \"modifiedDate\": null,\n" +
//                "        \"modifiedBy\": null,\n" +
//                "        \"name\": null,\n" +
//                "        \"designTypeId\": null,\n" +
//                "        \"preDesignObject\": null,\n" +
//                "        \"sequenceNumber\": null,\n" +
//                "        \"description\": null,\n" +
//                "        \"pageContentStrategy\": null,\n" +
//                "        \"pageContentFetchStrategy\": null,\n" +
//                "        \"pageContentQuantity\": null,\n" +
//                "        \"viewQuantity\": null,\n" +
//                "        \"pageType\": null,\n" +
//                "        \"pageContentSchema\": null,\n" +
//                "        \"pageContentIndexExpression\": null,\n" +
//                "        \"pageContentInstanceRange\": null,\n" +
//                "        \"pageContentRange\": null,\n" +
//                "        \"userAssign\": null,\n" +
//                "        \"mainVariableDataSource\": null,\n" +
//                "        \"dsDataSource\": null,\n" +
//                "        \"pcsPlaceholders\": null,\n" +
//                "        \"templateType\": null,\n" +
//                "        \"multilingualKey\": \"com.qpp.cgp.domain.bom.MaterialViewType\"\n" +
//                "      },\n" +
//                "      \"pageContents\": [\n" +
//                "        {\n" +
//                "          \"_id\": \"2085236\",\n" +
//                "          \"idReference\": null,\n" +
//                "          \"clazz\": \"com.qpp.cgp.domain.bom.runtime.PageContent\",\n" +
//                "          \"createdDate\": 1573209656913,\n" +
//                "          \"createdBy\": \"578\",\n" +
//                "          \"modifiedDate\": 1573695820483,\n" +
//                "          \"modifiedBy\": \"1487389\",\n" +
//                "          \"index\": null,\n" +
//                "          \"code\": \"\",\n" +
//                "          \"name\": \"\",\n" +
//                "          \"width\": null,\n" +
//                "          \"height\": null,\n" +
//                "          \"layers\": [\n" +
//                "            {\n" +
//                "              \"items\": [\n" +
//                "                {\n" +
//                "                  \"x\": 0,\n" +
//                "                  \"y\": 0,\n" +
//                "                  \"rotation\": 0,\n" +
//                "                  \"width\": 164,\n" +
//                "                  \"height\": 252,\n" +
//                "                  \"printFile\": null\n" +
//                "                }\n" +
//                "              ],\n" +
//                "              \"description\": \"地盒印刷层\",\n" +
//                "              \"code\": \"printingLayer\"\n" +
//                "            },\n" +
//                "            {\n" +
//                "              \"items\": [\n" +
//                "                {\n" +
//                "                  \"x\": 0,\n" +
//                "                  \"y\": 0,\n" +
//                "                  \"rotation\": 0,\n" +
//                "                  \"width\": 164,\n" +
//                "                  \"height\": 252,\n" +
//                "                  \"printFile\": null\n" +
//                "                }\n" +
//                "              ],\n" +
//                "              \"description\": \"地盒烫金层\",\n" +
//                "              \"code\": \"goldFoilLayer\"\n" +
//                "            }\n" +
//                "          ],\n" +
//                "          \"rtObject\": null,\n" +
//                "          \"pageContentSchemaId\": null,\n" +
//                "          \"clipPath\": null,\n" +
//                "          \"templateId\": \"\",\n" +
//                "          \"multilingualKey\": \"com.qpp.cgp.domain.bom.runtime.PageContent\"\n" +
//                "        }\n" +
//                "      ],\n" +
//                "      \"materialPath\": \"1947217,1947241,1947234\",\n" +
//                "      \"pageContentItemPlaceholderObjects\": null,\n" +
//                "      \"designObject\": null,\n" +
//                "      \"productMaterialViewType\": {\n" +
//                "        \"_id\": \"2085387\",\n" +
//                "        \"idReference\": null,\n" +
//                "        \"clazz\": \"com.qpp.cgp.domain.bom.ProductMaterialViewType\",\n" +
//                "        \"createdDate\": null,\n" +
//                "        \"createdBy\": null,\n" +
//                "        \"modifiedDate\": 1573281530557,\n" +
//                "        \"modifiedBy\": \"578\",\n" +
//                "        \"materialPath\": \"1947217,1947241,1947234\",\n" +
//                "        \"conditionExpression\": \"\",\n" +
//                "        \"condition\": null,\n" +
//                "        \"materialViewType\": {\n" +
//                "          \"_id\": \"2085347\",\n" +
//                "          \"idReference\": \"MaterialViewType\",\n" +
//                "          \"clazz\": \"com.qpp.cgp.domain.bom.MaterialViewType\",\n" +
//                "          \"createdDate\": null,\n" +
//                "          \"createdBy\": null,\n" +
//                "          \"modifiedDate\": null,\n" +
//                "          \"modifiedBy\": null,\n" +
//                "          \"name\": null,\n" +
//                "          \"designTypeId\": null,\n" +
//                "          \"preDesignObject\": null,\n" +
//                "          \"sequenceNumber\": null,\n" +
//                "          \"description\": null,\n" +
//                "          \"pageContentStrategy\": null,\n" +
//                "          \"pageContentFetchStrategy\": null,\n" +
//                "          \"pageContentQuantity\": null,\n" +
//                "          \"viewQuantity\": null,\n" +
//                "          \"pageType\": null,\n" +
//                "          \"pageContentSchema\": null,\n" +
//                "          \"pageContentIndexExpression\": null,\n" +
//                "          \"pageContentInstanceRange\": null,\n" +
//                "          \"pageContentRange\": null,\n" +
//                "          \"userAssign\": null,\n" +
//                "          \"mainVariableDataSource\": null,\n" +
//                "          \"dsDataSource\": null,\n" +
//                "          \"pcsPlaceholders\": null,\n" +
//                "          \"templateType\": null,\n" +
//                "          \"multilingualKey\": \"com.qpp.cgp.domain.bom.MaterialViewType\"\n" +
//                "        },\n" +
//                "        \"productConfigDesignId\": 1947495,\n" +
//                "        \"productMaterialViewTypeId\": \"2085386\",\n" +
//                "        \"materialSelector\": {\n" +
//                "          \"_id\": null,\n" +
//                "          \"idReference\": null,\n" +
//                "          \"clazz\": \"com.qpp.cgp.domain.bom.material.IdPathSelector\",\n" +
//                "          \"createdDate\": null,\n" +
//                "          \"createdBy\": null,\n" +
//                "          \"modifiedDate\": null,\n" +
//                "          \"modifiedBy\": null,\n" +
//                "          \"idPath\": \"1947217,1947241,1947234\",\n" +
//                "          \"multilingualKey\": \"com.qpp.cgp.domain.bom.material.IdPathSelector\"\n" +
//                "        },\n" +
//                "        \"pageContentQty\": {\n" +
//                "          \"_id\": null,\n" +
//                "          \"idReference\": null,\n" +
//                "          \"clazz\": \"com.qpp.cgp.value.ConstantValue\",\n" +
//                "          \"createdDate\": null,\n" +
//                "          \"createdBy\": null,\n" +
//                "          \"modifiedDate\": null,\n" +
//                "          \"modifiedBy\": null,\n" +
//                "          \"type\": \"Number\",\n" +
//                "          \"constraints\": [],\n" +
//                "          \"value\": \"1\",\n" +
//                "          \"multilingualKey\": \"com.qpp.cgp.value.ConstantValue\"\n" +
//                "        },\n" +
//                "        \"defaultNode\": null,\n" +
//                "        \"productMaterialViewTypeIds\": null,\n" +
//                "        \"materialViewAttribute\": null,\n" +
//                "        \"name\": \"地盒ViewType\",\n" +
//                "        \"productMaterialViewTemplateConfigId\": \"2086936\",\n" +
//                "        \"multilingualKey\": \"com.qpp.cgp.domain.bom.ProductMaterialViewType\"\n" +
//                "      },\n" +
//                "      \"multilingualKey\": \"com.qpp.cgp.domain.bom.runtime.ProductMaterialView\"\n" +
//                "    }\n" +
//                "  ],\n" +
//                "  \"productConfigBomId\": 1947486,\n" +
//                "  \"productConfigViewId\": 1947492,\n" +
//                "  \"productConfigImpositionId\": 1947488,\n" +
//                "  \"productConfigDesignId\": 1947495,\n" +
//                "  \"userDesignIds\": null,\n" +
//                "  \"comparisonThumbnail\": null,\n" +
//                "  \"thumbnail\": \"0f2fe5d5-aa67-4bed-811b-ca0f28b55bda-0.jpg\",\n" +
//                "  \"photos\": [],\n" +
//                "  \"productConfigBom\": {\n" +
//                "    \"id\": 1947486,\n" +
//                "    \"type\": \"UF2\",\n" +
//                "    \"configVersion\": \"1\",\n" +
//                "    \"configValue\": \"\",\n" +
//                "    \"status\": 2,\n" +
//                "    \"context\": \"PC\",\n" +
//                "    \"productMaterialId\": \"1947217\",\n" +
//                "    \"schemaVersion\": \"5\",\n" +
//                "    \"productConfigId\": 1947484,\n" +
//                "    \"multilingualKey\": \"com.qpp.cgp.domain.product.config.ProductConfigBom\"\n" +
//                "  },\n" +
//                "  \"productConfigView\": {\n" +
//                "    \"id\": 1947492,\n" +
//                "    \"type\": \"HTML\",\n" +
//                "    \"configVersion\": \"1\",\n" +
//                "    \"configValue\": \"\",\n" +
//                "    \"status\": 2,\n" +
//                "    \"context\": \"PC\",\n" +
//                "    \"path\": \"\",\n" +
//                "    \"bomType\": \"UF2\",\n" +
//                "    \"productConfigId\": 1947484,\n" +
//                "    \"viewConfigs\": [\n" +
//                "      {\n" +
//                "        \"_id\": \"2085631\",\n" +
//                "        \"productConfigViewId\": 1947492,\n" +
//                "        \"productMaterialViewTypeId\": \"2085385\",\n" +
//                "        \"editViewConfigs\": [\n" +
//                "          {\n" +
//                "            \"id\": \"1965289\",\n" +
//                "            \"positionTarget\": {\n" +
//                "              \"targetType\": \"PageContent\",\n" +
//                "              \"smvPath\": \"2085399\"\n" +
//                "            },\n" +
//                "            \"componentConfigs\": [\n" +
//                "              {\n" +
//                "                \"id\": \"1917880\",\n" +
//                "                \"allowMultiple\": true,\n" +
//                "                \"allowRepeat\": true,\n" +
//                "                \"autoUpload\": true,\n" +
//                "                \"fileSum\": 4,\n" +
//                "                \"fileConstraint\": {\n" +
//                "                  \"fromat\": \"PDF,Ai,PSD,Zip,jpg\",\n" +
//                "                  \"fileMaxSize\": \"512\"\n" +
//                "                },\n" +
//                "                \"description\": \"Printing File\"\n" +
//                "              },\n" +
//                "              {\n" +
//                "                \"id\": \"1917881\",\n" +
//                "                \"allowMultiple\": true,\n" +
//                "                \"allowRepeat\": true,\n" +
//                "                \"autoUpload\": true,\n" +
//                "                \"fileSum\": 4,\n" +
//                "                \"fileConstraint\": {\n" +
//                "                  \"fromat\": \"PDF,Ai,PSD,Zip,jpg\",\n" +
//                "                  \"fileMaxSize\": \"512\"\n" +
//                "                },\n" +
//                "                \"description\": \"Gold Foil Stamping File\"\n" +
//                "              },\n" +
//                "              {\n" +
//                "                \"id\": \"1966113\",\n" +
//                "                \"targetPath\": \"$.layers[?(@.code==='printingLayer')]\",\n" +
//                "                \"pdfLayered\": [\n" +
//                "                  {\n" +
//                "                    \"targetPath\": \"$.0\",\n" +
//                "                    \"layered\": \"1\"\n" +
//                "                  },\n" +
//                "                  {\n" +
//                "                    \"targetPath\": \"$.1\",\n" +
//                "                    \"layered\": \"2\"\n" +
//                "                  }\n" +
//                "                ],\n" +
//                "                \"description\": \"Printing File\",\n" +
//                "                \"componentName\": \"printingFile\"\n" +
//                "              },\n" +
//                "              {\n" +
//                "                \"id\": \"1966114\",\n" +
//                "                \"targetPath\": \"$.layers[?(@.code==='goldFoilLayer')]\",\n" +
//                "                \"pdfLayered\": [\n" +
//                "                  {\n" +
//                "                    \"targetPath\": \"$.0\",\n" +
//                "                    \"layered\": \"1\"\n" +
//                "                  },\n" +
//                "                  {\n" +
//                "                    \"targetPath\": \"$.1\",\n" +
//                "                    \"layered\": \"2\"\n" +
//                "                  }\n" +
//                "                ],\n" +
//                "                \"description\": \"Gold Foil Stamping File\",\n" +
//                "                \"componentName\": \"goldFoil\"\n" +
//                "              },\n" +
//                "              {\n" +
//                "                \"id\": \"2004648\",\n" +
//                "                \"explainTemplateId\": \"1980473\",\n" +
//                "                \"clazz\": \"FixedDownloadTemplate\",\n" +
//                "                \"fileConfigList\": [\n" +
//                "                  {\n" +
//                "                    \"sbomPath\": \"1947217\",\n" +
//                "                    \"ssmvtId\": \"2085399\",\n" +
//                "                    \"templateFormat\": \"PDF\"\n" +
//                "                  }\n" +
//                "                ]\n" +
//                "              }\n" +
//                "            ]\n" +
//                "          }\n" +
//                "        ],\n" +
//                "        \"clazz\": \"com.qpp.cgp.domain.product.config.view.ViewConfig\",\n" +
//                "        \"createdDate\": 1573213627048,\n" +
//                "        \"createdBy\": 578,\n" +
//                "        \"modifiedDate\": 1573435860705,\n" +
//                "        \"modifiedBy\": 578\n" +
//                "      }\n" +
//                "    ],\n" +
//                "    \"wizardConfig\": {\n" +
//                "      \"_id\": \"2085632\",\n" +
//                "      \"productConfigViewId\": 1947492,\n" +
//                "      \"clazz\": \"com.qpp.cgp.domain.product.config.view.WizardConfig\",\n" +
//                "      \"createdDate\": 1573213641343,\n" +
//                "      \"createdBy\": 578,\n" +
//                "      \"navBarConfig\": [\n" +
//                "        {\n" +
//                "          \"_id\": \"1962046\",\n" +
//                "          \"isOrderly\": true,\n" +
//                "          \"description\": \"builder主导航\",\n" +
//                "          \"navType\": \"FixedNavBar\",\n" +
//                "          \"navItems\": [\n" +
//                "            {\n" +
//                "              \"_id\": \"1962047\",\n" +
//                "              \"displayName\": \"PDF Upload\",\n" +
//                "              \"displayTitle\": \"\",\n" +
//                "              \"isEnable\": true,\n" +
//                "              \"SBOMNodeId\": \"2085389\",\n" +
//                "              \"targetSelector\": {\n" +
//                "                \"jsonPath\": \"$.simplifyMaterialViews[?(@._id== 2085399)].pageContents[0]\",\n" +
//                "                \"clazz\": \"JsonPathSelector\"\n" +
//                "              }\n" +
//                "            }\n" +
//                "          ]\n" +
//                "        }\n" +
//                "      ],\n" +
//                "      \"modifiedDate\": 1573637869116,\n" +
//                "      \"modifiedBy\": 578\n" +
//                "    },\n" +
//                "    \"threeDModels\": null,\n" +
//                "    \"bomCompatibilities\": [\n" +
//                "      {\n" +
//                "        \"id\": 1947486,\n" +
//                "        \"configVersion\": \"1\",\n" +
//                "        \"schemaVersion\": \"5\"\n" +
//                "      }\n" +
//                "    ],\n" +
//                "    \"viewCompatibilities\": [],\n" +
//                "    \"multilingualKey\": \"com.qpp.cgp.domain.product.config.ProductConfigView\"\n" +
//                "  },\n" +
//                "  \"productConfigImposition\": {\n" +
//                "    \"id\": 1947488,\n" +
//                "    \"configVersion\": \"1\",\n" +
//                "    \"configValue\": \"\",\n" +
//                "    \"status\": 2,\n" +
//                "    \"bomType\": \"UF2\",\n" +
//                "    \"productConfigId\": 1947484,\n" +
//                "    \"bomCompatibilities\": [\n" +
//                "      {\n" +
//                "        \"id\": 1947486,\n" +
//                "        \"configVersion\": \"1\",\n" +
//                "        \"schemaVersion\": \"5\"\n" +
//                "      }\n" +
//                "    ],\n" +
//                "    \"multilingualKey\": \"com.qpp.cgp.domain.product.config.ProductConfigImposition\"\n" +
//                "  },\n" +
//                "  \"productConfigDesign\": {\n" +
//                "    \"id\": 1947495,\n" +
//                "    \"configVersion\": \"1\",\n" +
//                "    \"configValue\": \"\",\n" +
//                "    \"status\": 2,\n" +
//                "    \"productConfigId\": 1947484,\n" +
//                "    \"bomCompatibilities\": [\n" +
//                "      {\n" +
//                "        \"id\": 1947486,\n" +
//                "        \"configVersion\": \"1\",\n" +
//                "        \"schemaVersion\": \"5\"\n" +
//                "      }\n" +
//                "    ],\n" +
//                "    \"multilingualKey\": \"com.qpp.cgp.domain.product.config.ProductConfigDesign\"\n" +
//                "  },\n" +
//                "  \"userDesigns\": null,\n" +
//                "  \"libraries\": null,\n" +
//                "  \"finishes\": null,\n" +
//                "  \"foilStampingPhotos\": null,\n" +
//                "  \"propertyModelId\": \"2120630\",\n" +
//                "  \"sbNode\": {\n" +
//                "    \"_id\": \"2085389\",\n" +
//                "    \"idReference\": null,\n" +
//                "    \"clazz\": \"com.qpp.cgp.domain.simplifyBom.SBNode\",\n" +
//                "    \"createdDate\": null,\n" +
//                "    \"createdBy\": null,\n" +
//                "    \"modifiedDate\": 1573695819211,\n" +
//                "    \"modifiedBy\": \"1487389\",\n" +
//                "    \"parent\": null,\n" +
//                "    \"child\": null,\n" +
//                "    \"sbomPath\": \"1947217\",\n" +
//                "    \"rtType\": null,\n" +
//                "    \"completePath\": \"1947217\",\n" +
//                "    \"defaultNode\": null,\n" +
//                "    \"rtObject\": null,\n" +
//                "    \"description\": \"天地盒简易Bom的根节点\",\n" +
//                "    \"isEnable\": true,\n" +
//                "    \"simplifyMaterialViews\": [\n" +
//                "      {\n" +
//                "        \"_id\": \"2085399\",\n" +
//                "        \"idReference\": null,\n" +
//                "        \"clazz\": \"com.qpp.cgp.domain.simplifyBom.SimplifyMaterialView\",\n" +
//                "        \"createdDate\": null,\n" +
//                "        \"createdBy\": null,\n" +
//                "        \"modifiedDate\": null,\n" +
//                "        \"modifiedBy\": null,\n" +
//                "        \"materialViewType\": {\n" +
//                "          \"_id\": \"2085388\",\n" +
//                "          \"idReference\": null,\n" +
//                "          \"clazz\": \"com.qpp.cgp.domain.bom.MaterialViewType\",\n" +
//                "          \"createdDate\": 1573210327719,\n" +
//                "          \"createdBy\": \"578\",\n" +
//                "          \"modifiedDate\": 1573210370052,\n" +
//                "          \"modifiedBy\": \"578\",\n" +
//                "          \"name\": \"天地盒ViewType\",\n" +
//                "          \"designTypeId\": null,\n" +
//                "          \"preDesignObject\": null,\n" +
//                "          \"sequenceNumber\": null,\n" +
//                "          \"description\": \"\",\n" +
//                "          \"pageContentStrategy\": \"\",\n" +
//                "          \"pageContentFetchStrategy\": \"fixed\",\n" +
//                "          \"pageContentQuantity\": null,\n" +
//                "          \"viewQuantity\": null,\n" +
//                "          \"pageType\": null,\n" +
//                "          \"pageContentSchema\": {\n" +
//                "            \"_id\": \"2085237\",\n" +
//                "            \"idReference\": \"PageContentSchema\",\n" +
//                "            \"clazz\": \"com.qpp.cgp.domain.bom.PageContentSchema\",\n" +
//                "            \"createdDate\": null,\n" +
//                "            \"createdBy\": null,\n" +
//                "            \"modifiedDate\": null,\n" +
//                "            \"modifiedBy\": null,\n" +
//                "            \"code\": null,\n" +
//                "            \"name\": null,\n" +
//                "            \"description\": null,\n" +
//                "            \"width\": 0,\n" +
//                "            \"height\": 0,\n" +
//                "            \"layers\": null,\n" +
//                "            \"clipPath\": null,\n" +
//                "            \"priceAreas\": null,\n" +
//                "            \"displayObjectConstraints\": null,\n" +
//                "            \"canvases\": null,\n" +
//                "            \"rtType\": null,\n" +
//                "            \"pageContentItemPlaceholders\": null,\n" +
//                "            \"templateId\": null,\n" +
//                "            \"pageContentSchemaGroup\": null,\n" +
//                "            \"mainVariableDataSource\": null,\n" +
//                "            \"multilingualKey\": \"com.qpp.cgp.domain.bom.PageContentSchema\"\n" +
//                "          },\n" +
//                "          \"pageContentIndexExpression\": \"\",\n" +
//                "          \"pageContentInstanceRange\": {\n" +
//                "            \"_id\": null,\n" +
//                "            \"idReference\": null,\n" +
//                "            \"clazz\": \"com.qpp.cgp.domain.bom.QuantityRange\",\n" +
//                "            \"createdDate\": null,\n" +
//                "            \"createdBy\": null,\n" +
//                "            \"modifiedDate\": null,\n" +
//                "            \"modifiedBy\": null,\n" +
//                "            \"rangeType\": \"RANGE\",\n" +
//                "            \"minExpression\": \"\",\n" +
//                "            \"maxExpression\": \"\",\n" +
//                "            \"multilingualKey\": \"com.qpp.cgp.domain.bom.QuantityRange\"\n" +
//                "          },\n" +
//                "          \"pageContentRange\": {\n" +
//                "            \"_id\": null,\n" +
//                "            \"idReference\": null,\n" +
//                "            \"clazz\": \"com.qpp.cgp.domain.bom.QuantityRange\",\n" +
//                "            \"createdDate\": null,\n" +
//                "            \"createdBy\": null,\n" +
//                "            \"modifiedDate\": null,\n" +
//                "            \"modifiedBy\": null,\n" +
//                "            \"rangeType\": \"RANGE\",\n" +
//                "            \"minExpression\": \"\",\n" +
//                "            \"maxExpression\": \"\",\n" +
//                "            \"multilingualKey\": \"com.qpp.cgp.domain.bom.QuantityRange\"\n" +
//                "          },\n" +
//                "          \"userAssign\": null,\n" +
//                "          \"mainVariableDataSource\": {\n" +
//                "            \"_id\": \"\",\n" +
//                "            \"idReference\": \"IVariableDataSource\",\n" +
//                "            \"clazz\": \"com.qpp.cgp.domain.bom.datasource.LocalDataSource\",\n" +
//                "            \"createdDate\": null,\n" +
//                "            \"createdBy\": null,\n" +
//                "            \"modifiedDate\": null,\n" +
//                "            \"modifiedBy\": null,\n" +
//                "            \"expression\": null,\n" +
//                "            \"selector\": null,\n" +
//                "            \"quantityRange\": null,\n" +
//                "            \"rtType\": null,\n" +
//                "            \"multilingualKey\": \"com.qpp.cgp.domain.bom.datasource.LocalDataSource\"\n" +
//                "          },\n" +
//                "          \"dsDataSource\": null,\n" +
//                "          \"pcsPlaceholders\": [],\n" +
//                "          \"templateType\": \"NONE\",\n" +
//                "          \"multilingualKey\": \"com.qpp.cgp.domain.bom.MaterialViewType\"\n" +
//                "        },\n" +
//                "        \"isEnable\": true,\n" +
//                "        \"isInit\": true,\n" +
//                "        \"isSelect\": null,\n" +
//                "        \"defaultNode\": null,\n" +
//                "        \"pageContents\": [\n" +
//                "          {\n" +
//                "            \"_id\": \"2120639\",\n" +
//                "            \"idReference\": null,\n" +
//                "            \"clazz\": \"com.qpp.cgp.domain.bom.runtime.PageContent\",\n" +
//                "            \"createdDate\": 1573209761451,\n" +
//                "            \"createdBy\": \"578\",\n" +
//                "            \"modifiedDate\": null,\n" +
//                "            \"modifiedBy\": null,\n" +
//                "            \"index\": null,\n" +
//                "            \"code\": \"\",\n" +
//                "            \"name\": \"customSizeGameBox\",\n" +
//                "            \"width\": null,\n" +
//                "            \"height\": null,\n" +
//                "            \"layers\": [\n" +
//                "              {\n" +
//                "                \"items\": [\n" +
//                "                  {\n" +
//                "                    \"x\": 0,\n" +
//                "                    \"y\": 0,\n" +
//                "                    \"rotation\": 0,\n" +
//                "                    \"width\": 164,\n" +
//                "                    \"height\": 252,\n" +
//                "                    \"printFile\": null\n" +
//                "                  }\n" +
//                "                ],\n" +
//                "                \"description\": \"天盒印刷层\",\n" +
//                "                \"code\": \"printingLayer\"\n" +
//                "              },\n" +
//                "              {\n" +
//                "                \"items\": [\n" +
//                "                  {\n" +
//                "                    \"x\": 0,\n" +
//                "                    \"y\": 0,\n" +
//                "                    \"rotation\": 0,\n" +
//                "                    \"width\": 164,\n" +
//                "                    \"height\": 252,\n" +
//                "                    \"printFile\": null\n" +
//                "                  }\n" +
//                "                ],\n" +
//                "                \"description\": \"天盒烫金层\",\n" +
//                "                \"code\": \"goldFoilLayer\"\n" +
//                "              },\n" +
//                "              {\n" +
//                "                \"items\": [\n" +
//                "                  {\n" +
//                "                    \"x\": 0,\n" +
//                "                    \"y\": 0,\n" +
//                "                    \"rotation\": 0,\n" +
//                "                    \"width\": 164,\n" +
//                "                    \"height\": 252,\n" +
//                "                    \"printFile\": null\n" +
//                "                  }\n" +
//                "                ],\n" +
//                "                \"description\": \"地盒印刷层\",\n" +
//                "                \"code\": \"printingLayer\"\n" +
//                "              },\n" +
//                "              {\n" +
//                "                \"items\": [\n" +
//                "                  {\n" +
//                "                    \"x\": 0,\n" +
//                "                    \"y\": 0,\n" +
//                "                    \"rotation\": 0,\n" +
//                "                    \"width\": 164,\n" +
//                "                    \"height\": 252,\n" +
//                "                    \"printFile\": null\n" +
//                "                  }\n" +
//                "                ],\n" +
//                "                \"description\": \"地盒烫金层\",\n" +
//                "                \"code\": \"goldFoilLayer\"\n" +
//                "              }\n" +
//                "            ],\n" +
//                "            \"rtObject\": null,\n" +
//                "            \"pageContentSchemaId\": \"2085237\",\n" +
//                "            \"clipPath\": null,\n" +
//                "            \"templateId\": \"\",\n" +
//                "            \"multilingualKey\": \"com.qpp.cgp.domain.bom.runtime.PageContent\"\n" +
//                "          }\n" +
//                "        ],\n" +
//                "        \"simplifySBOMMaterialViewTypeId\": \"2085399\",\n" +
//                "        \"productMaterialViewTypeId\": null,\n" +
//                "        \"groupId\": 0,\n" +
//                "        \"pageContentItemPlaceholderObjects\": null,\n" +
//                "        \"designObject\": null,\n" +
//                "        \"designRtType\": null,\n" +
//                "        \"multilingualKey\": \"com.qpp.cgp.domain.simplifyBom.SimplifyMaterialView\"\n" +
//                "      }\n" +
//                "    ],\n" +
//                "    \"productConfigDesignId\": 1947495,\n" +
//                "    \"leaf\": true,\n" +
//                "    \"multilingualKey\": \"com.qpp.cgp.domain.simplifyBom.SBNode\"\n" +
//                "  }\n" +
//                "}";
//
//        final Map map = objectMapper.readValue(json, Map.class);
//        final Object check = check(map);
//        System.out.println(objectMapper.writeValueAsString(check));
//    }
//
//    public int[] twoSum(int[] nums, int target) {
//        int[] result = {1, 2};
//        for (int i = 0; i < nums.length; i++) {
//            result[0] = i;
//            for (int j = 0; j < nums.length; j++) {
//                result[1] = j;
//                if ((i != j) && (nums[i] + nums[j] == target)) {
//                    return result;
//                }
//            }
//        }
//        return result;
//    }
//
//    @Test
//    public void testSum() {
//        int[] array = {3, 2, 4};
//        final int[] result = twoSum(array, 6);
//        System.out.println(result[0] + "=============" + result[1]);
//    }
//
//    @Autowired
//    private IdGenerator idGenerator;
//
//    @org.junit.Test
//    public void importHoliday() throws IOException, InvalidFormatException {
////        File file = new File("C:\\Users\\<USER>\\Desktop\\日历数据\\CN.xlsx");
////        File file = new File("C:\\Users\\<USER>\\Desktop\\日历数据\\US.xlsx");
//        File file = new File("C:\\Users\\<USER>\\Desktop\\日历数据\\DE.xlsx");
//        Workbook wb = WorkbookFactory.create(new FileInputStream(file));
//        List<HolidayInfoConfig> holidayInfoConfigs = new ArrayList<>();
//        Sheet sheetAt = wb.getSheetAt(0);
//        for (int i = 0; i < sheetAt.getLastRowNum(); i++) {
//            Row row = sheetAt.getRow(i);
//
//            if (i > 0) {
//                HolidayInfoConfig holidayInfoConfig = new HolidayInfoConfig();
//                if (row.getCell(0) != null) {
//                    Cell cell = row.getCell(0);
//                    Country country = new Country();
//                    Language language = new Language();
//                    language.setClazz(Language.class.getName());
//                    country.setClazz(Country.class.getName());
//                    language.setId(9L);
//                    if (cell.toString().equals("US")) {
//                        country.setId(3L);
//                        language.setId(9L);
//                    } else if (cell.toString().equals("CA")) {
//                        country.setId(20L);
//                    } else if (cell.toString().equals("GB")) {
//                        country.setId(36L);
//                    } else if (cell.toString().equals("AU")) {
//                        country.setId(8L);
//                    } else if (cell.toString().equals("DE")) {
//                        country.setId(2410671L);
//                    } else if (cell.toString().equals("CN")) {
//                        country.setId(25L);
//                    }
//                    holidayInfoConfig.setCountry(country);
//                }
//                if (row.getCell(3) != null) {
//                    holidayInfoConfig.setDescription(row.getCell(3).toString());
//                    holidayInfoConfig.setName(row.getCell(3).toString());
//                }
//                if (row.getCell(1) != null) {
//                    String Strategy = row.getCell(1).toString();
//                    if (Strategy.equals("W")) {
//                        WeekRepeat holidayStrategy = new WeekRepeat();
//                        if (row.getCell(5) != null) {
//                            double numericCellValue = row.getCell(5).getNumericCellValue();
//                            holidayStrategy.setContinuousDays((int) numericCellValue);
//                        }
//                        String date = row.getCell(2).toString();
//                        int month = Integer.parseInt(date.substring(1, 3));
//                        int week = Integer.parseInt(date.substring(4, 5));
//                        String dayToString = date.substring(5);
//                        int day = 0;
//                        switch (dayToString) {
//                            case "monday":
//                                day = 1;
//                                break;
//                            case "tuesday":
//                                day = 2;
//                                break;
//                            case "wednesday":
//                                day = 3;
//                                break;
//                            case "thursday":
//                                day = 4;
//                                break;
//                            case "friday":
//                                day = 5;
//                                break;
//                            case "saturday":
//                                day = 6;
//                                break;
//                            case "sunday":
//                                day = 7;
//                                break;
//                            default:
//                                throw new RuntimeException();
//                        }
//                        holidayStrategy.setWeek(week);
//                        holidayStrategy.setDay(day);
//                        holidayStrategy.setMonth(month);
//                        holidayInfoConfig.setHolidayStrategy(holidayStrategy);
//                    } else if (Strategy.equals("S")) {
//                        YearRepeat holidayStrategy = new YearRepeat();
//                        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
//                        String formattedDate = formatter.format(Date.from(row.getCell(2).getDateCellValue().toInstant()));
//                        String[] split = formattedDate.split("-");
//                        holidayStrategy.setYear(Integer.parseInt(split[0]));
//                        holidayStrategy.setMonth(Integer.parseInt(split[1]));
//                        holidayStrategy.setDay(Integer.parseInt(split[2]));
//                        if (row.getCell(5) != null) {
//                            double numericCellValue = row.getCell(5).getNumericCellValue();
//                            holidayStrategy.setContinuousDays((int) numericCellValue);
//                        }
//                        holidayInfoConfig.setHolidayStrategy(holidayStrategy);
//                    } else if (Strategy.equals("C")) {
//                        DirectAssign holidayStrategy = new DirectAssign();
//                        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
//                        String formattedDate = formatter.format(Date.from(row.getCell(2).getDateCellValue().toInstant()));
//                        String[] split = formattedDate.split("-");
//                        holidayStrategy.setYear(Integer.parseInt(split[0]));
//                        holidayStrategy.setMonth(Integer.parseInt(split[1]));
//                        holidayStrategy.setDay(Integer.parseInt(split[2]));
//                        holidayStrategy.setDate(Date.from(row.getCell(2).getDateCellValue().toInstant()));
//                        if (row.getCell(5) != null) {
//                            double numericCellValue = row.getCell(5).getNumericCellValue();
//                            holidayStrategy.setContinuousDays((int) numericCellValue);
//                        }
//                        holidayInfoConfig.setHolidayStrategy(holidayStrategy);
//                    }
//
//                }
//                if (row.getCell(7) != null) {
//                    holidayInfoConfig.setRemark(row.getCell(7).toString());
//                }
//                holidayInfoConfigs.add(holidayInfoConfig);
//            }
//        }
//        for (HolidayInfoConfig holidayInfoConfig : holidayInfoConfigs) {
//            holidayInfoConfig.setId(idGenerator.generateId().toString());
//            mongoTemplate.save(holidayInfoConfig);
//            System.out.println(holidayInfoConfig);
//        }
//        System.out.println();
//    }
//
//    @Autowired
//    private FileManager fileManager;
//
//    @Test
//    public void DirectAssignByGenerateDate() throws ParseException {
//        List<HolidayInfoConfig> holidayInfoConfigs = mongoTemplate.find(Query.query(Criteria.where("holidayStrategy.clazz").is(DirectAssign.class.getName())), HolidayInfoConfig.class);
//        for (HolidayInfoConfig holidayInfoConfig : holidayInfoConfigs) {
//            HolidayStrategy holidayStrategy = holidayInfoConfig.getHolidayStrategy();
//            if (holidayStrategy instanceof DirectAssign) {
//                int day = ((DirectAssign) holidayStrategy).getDay();
//                int year = ((DirectAssign) holidayStrategy).getYear();
//                int month = ((DirectAssign) holidayStrategy).getMonth();
//                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
//                Date date = simpleDateFormat.parse(year + "-" + month + "-" + day);
//                ((DirectAssign) holidayStrategy).setDate(date);
//                holidayInfoConfig.setHolidayStrategy(holidayStrategy);
//            }
//            mongoTemplate.save(holidayInfoConfig);
//        }
//    }
//
//    @org.junit.Test
//    public void importBackGround() throws IOException {
//        File files = new File("C:\\Users\\<USER>\\Desktop\\排版圖\\掛歷\\CD_0029-14x11\\Category");
//        MonthBackGroundPictureGroup monthBackGroundPictureGroup = new MonthBackGroundPictureGroup();
//        monthBackGroundPictureGroup.setName("CD_0029-14x11");
//        monthBackGroundPictureGroup.setFirstDateOfWeek(0);
//        monthBackGroundPictureGroup.setDescription("CD_0029-14x11");
//        monthBackGroundPictureGroup.setActualWidth(14.00);
//        monthBackGroundPictureGroup.setActualHeight(11.00);
//        Language language = new Language();
//        language.setId(9L);
//        language.setClazz(Language.class.getName());
//        monthBackGroundPictureGroup.setLanguage(language);
//        List<MonthBackGroundPicture> monthBackGroundPictures = new ArrayList<>();
//        for (File file : files.listFiles()) {
//            if (file.getName().contains("pdf")) {
//                MonthBackGroundPicture monthBackGroundPicture = new MonthBackGroundPicture();
//                String fileName = file.getName();
//                monthBackGroundPicture.setMonth(Integer.parseInt(fileName.split("\\.")[0].substring(4)));
//                monthBackGroundPicture.setYear(Integer.parseInt(fileName.split("\\.")[0].substring(0, 4)));
//                monthBackGroundPicture.setSourceName(fileName);
//                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
//                FileInputStream fileInputStream = new FileInputStream(file);
//                IOUtils.copy(fileInputStream, byteArrayOutputStream);
//                ByteArrayResource byteArrayResource = new ByteArrayResource(byteArrayOutputStream.toByteArray()) {
//                    @Override
//                    public String getFilename() {
//                        return file.getName();
//                    }
//                };
//                UploadFile uploadFile = fileManager.uploadFileByByteArrayResource(byteArrayResource);
//                fileInputStream.close();
//                byteArrayOutputStream.close();
//                byteArrayOutputStream.close();
//                monthBackGroundPicture.setFileName(uploadFile.getName());
//                System.out.println(monthBackGroundPicture);
//                monthBackGroundPictures.add(monthBackGroundPicture);
//            }
//        }
//        monthBackGroundPictureGroup.setMonthBackGroundPictures(monthBackGroundPictures);
//        monthBackGroundPictureGroup.setId(idGenerator.generateId().toString());
//        mongoTemplate.save(monthBackGroundPictureGroup);
//    }
//
//    @Test
//    public void testMonthImageGroupGenerate() throws Exception{
//        File files = new File("C:\\Users\\<USER>\\Desktop\\CD_0001-5x11\\Category");
//        MonthImageGroup monthImageGroup = new MonthImageGroup();
//        monthImageGroup.setDescription("CD_0001-5x11");
//        List<MonthImageCondition> monthImageConditions = new ArrayList<>();
//
//        Language language = new Language();
//        language.setId(9L);
//        language.setClazz(Language.class.getName());
//        MonthImageCondition monthImageCondition = new MonthImageCondition();
//        monthImageCondition.setLanguage(language);
//        monthImageCondition.setFirstDateOfWeek(0);
//        List<MonthImage> monthImages = new ArrayList<>();
//        for (File file : files.listFiles()) {
//            if (file.getName().contains("pdf")) {
//                MonthBackGroundPicture monthBackGroundPicture = new MonthBackGroundPicture();
//                MonthImage monthImage = new MonthImage();
//                String fileName = file.getName();
//                monthImage.setMonth(Integer.parseInt(fileName.split("\\.")[0].substring(4)));
//                monthImage.setYear(Integer.parseInt(fileName.split("\\.")[0].substring(0, 4)));
//                monthImage.setSourceName(fileName);
//                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
//                FileInputStream fileInputStream = new FileInputStream(file);
//                IOUtils.copy(fileInputStream, byteArrayOutputStream);
//                ByteArrayResource byteArrayResource = new ByteArrayResource(byteArrayOutputStream.toByteArray()) {
//                    @Override
//                    public String getFilename() {
//                        return file.getName();
//                    }
//                };
//                UploadFile uploadFile = fileManager.uploadFileByByteArrayResource(byteArrayResource);
//
//                ByteArrayOutputStream byteArrayOutputStreamByPng = new ByteArrayOutputStream();
//                String fileNameByPNG = fileName.replace("pdf", "png");
//                FileInputStream fileInputStreamByPng = new FileInputStream("C:\\Users\\<USER>\\Desktop\\CD_0001-5x11\\Category-png\\" + fileNameByPNG);
//                IOUtils.copy(fileInputStreamByPng, byteArrayOutputStreamByPng);
//                ByteArrayResource byteArrayResourceByPng = new ByteArrayResource(byteArrayOutputStreamByPng.toByteArray()) {
//                    @Override
//                    public String getFilename() {
//                        return fileNameByPNG;
//                    }
//                };
//                UploadFile uploadFileByPng = fileManager.uploadFileByByteArrayResource(byteArrayResourceByPng);
//
//
//                fileInputStream.close();
//                byteArrayOutputStream.close();
//                byteArrayOutputStream.close();
//                fileInputStreamByPng.close();
//                byteArrayOutputStreamByPng.close();
//                byteArrayOutputStreamByPng.close();
//                monthImage.setPrintFile(uploadFile.getName());
//                //PNG图片
//                monthImage.setImageName(uploadFileByPng.getName());
//                monthImages.add(monthImage);
//                System.out.println(monthImage);
//            }
//        }
//        monthImageCondition.setImages(monthImages);
//        monthImageConditions.add(monthImageCondition);
//        monthImageGroup.setMonthImageConditions(monthImageConditions);
//        monthImageGroup.setId(idGenerator.generateId().toString());
//        mongoTemplate.save(monthImageGroup);
//    }
//
//    @Autowired
//    private ObjectMapper objectMapper;
//
//    @Test
//    public void uploadFile() throws Exception {
//        PageContentSchema pageContentSchema = mongoTemplate.findById("15005554", PageContentSchema.class);
//        PageContent pageContent = new PageContent();
//        assert pageContentSchema != null;
//        BeanUtils.copyProperties(pageContentSchema, pageContent, "_id","id", "clazz");
//        pageContent.setPageContentSchemaId(pageContentSchema.getId());
//        pageContent.setId(idGenerator.generateId().toString());
//        System.out.println(objectMapper.writeValueAsString(pageContent));
//    }
//
//    @Test
//    public void testAddCalculate(){
//        CalculateValueExConfig calculateValueExConfig = new CalculateValueExConfig();
//        ExpressionValueEx expressionValueEx = new ExpressionValueEx();
//        Expression expression = new Expression();
//        expression.setExpressionEngine(ExpressionEngine.JavaScript);
//        expression.setResultType(ValueType.Array);
//        expression.setExpression("function expression(inputContext) {\n" +
//                "    var context = inputContext.context;\n" +
//                "    var backgroundSizeDifferenceConfigs = context.backgroundSizeDifferenceConfigs;\n" +
//                "    //对背景图片中每一张图片进行遍历，找去其对于单位的误差范围,然后将目标\n" +
//                "    //算出对应三种类型转换后的单位,\n" +
//                "    //1英寸==25.4 毫米 1英寸==25.4 厘米\n" +
//                "    var sourceUnit = context.sourceBackgroundImage.configUnit;//转换前的单位\n" +
//                "    var convertResult = {\n" +
//                "        mm: {},\n" +
//                "        in: {},\n" +
//                "        cm: {}\n" +
//                "    };\n" +
//                "    var optionalImageIds = [];//可选的图片列表\n" +
//                "    for (var i = 0; i < context.backgroundSizeDifferenceConfigs.length; i++) {\n" +
//                "        var item = context.backgroundSizeDifferenceConfigs[i];\n" +
//                "        var aimUnit = item.unit;\n" +
//                "        var decimalPrecision = item.decimalPrecision;//精度\n" +
//                "        var configActualHeight = context.sourceBackgroundImage.configActualHeight;\n" +
//                "        var configActualWidth = context.sourceBackgroundImage.configActualWidth;\n" +
//                "        var widthValue = '';\n" +
//                "        var heightValue = '';\n" +
//                "        if (sourceUnit == 'mm' && aimUnit == 'mm') {\n" +
//                "            widthValue = configActualWidth;\n" +
//                "            heightValue = configActualHeight;\n" +
//                "        } else if (sourceUnit == 'in' && aimUnit == 'mm') {\n" +
//                "            widthValue = configActualWidth * 25.4;\n" +
//                "            heightValue = configActualHeight * 25.4;\n" +
//                "        } else if (sourceUnit == 'cm' && aimUnit == 'mm') {\n" +
//                "            widthValue = configActualWidth * 10;\n" +
//                "            heightValue = configActualHeight * 10;\n" +
//                "        } else if (sourceUnit == 'mm' && aimUnit == 'in') {\n" +
//                "            //1英寸==25.4 毫米\n" +
//                "            widthValue = configActualWidth / 25.4;\n" +
//                "            heightValue = configActualHeight / 25.4;\n" +
//                "        } else if (sourceUnit == 'in' && aimUnit == 'in') {\n" +
//                "            widthValue = configActualWidth;\n" +
//                "            heightValue = configActualHeight;\n" +
//                "        } else if (sourceUnit == 'cm' && aimUnit == 'in') {\n" +
//                "            //1英寸==2.54 厘米\n" +
//                "            widthValue = configActualWidth / 2.54;\n" +
//                "            heightValue = configActualHeight / 2.54;\n" +
//                "        } else if (sourceUnit == 'mm' && aimUnit == 'cm') {\n" +
//                "            widthValue = configActualWidth / 10;\n" +
//                "            heightValue = configActualHeight / 10;\n" +
//                "        } else if (sourceUnit == 'in' && aimUnit == 'cm') {\n" +
//                "            widthValue = configActualWidth * 2.54;\n" +
//                "            heightValue = configActualHeight * 2.54;\n" +
//                "        } else if (sourceUnit == 'cm' && aimUnit == 'cm') {\n" +
//                "            widthValue = configActualWidth;\n" +
//                "            heightValue = configActualHeight;\n" +
//                "        }\n" +
//                "        convertResult[aimUnit] = {\n" +
//                "            \"configUnit\": aimUnit,\n" +
//                "            \"difference\": item.difference,\n" +
//                "            \"isEqualTo\": item.isEqualTo,\n" +
//                "            \"configActualWidth\": Number(Number(widthValue).toFixed(decimalPrecision)),\n" +
//                "            \"configActualHeight\": Number(Number(heightValue).toFixed(decimalPrecision)),\n" +
//                "        }\n" +
//                "    }\n" +
//                "    for (var i = 0; i < context.backgroundImages.length; i++) {\n" +
//                "        var item = context.backgroundImages[i];\n" +
//                "        var result = convertResult[item.unit];\n" +
//                "        if (result.isEqualTo == true) {\n" +
//                "            if (Math.abs(item.actualWidth - result.configActualWidth) <= result.difference && Math.abs(item.actualHeight - result.configActualHeight) <= result.difference) {\n" +
//                "                optionalImageIds.push(item.backGroundId);\n" +
//                "            }\n" +
//                "        } else {\n" +
//                "            if (Math.abs(item.actualWidth - result.configActualWidth) < result.difference && Math.abs(item.actualHeight - result.configActualHeight) < result.difference) {\n" +
//                "                optionalImageIds.push(item.backGroundId);\n" +
//                "            }\n" +
//                "        }\n" +
//                "    }\n" +
//                "    return optionalImageIds;\n" +
//                "}");
//        expressionValueEx.setExpression(expression);
//        expressionValueEx.setType(ValueType.Array);
//        calculateValueExConfig.setCalculateValueEx(expressionValueEx);
//        calculateValueExConfig.setCode(CalculateActualContextName.BackGroupCalculate.toString());
//        mongoTemplate.save(calculateValueExConfig);
//    }
//
//    @Autowired
//    @Qualifier(MongoTemplateBeanNames.CONFIG)
//    MongoTemplate configMongoTemplate;
//
//    @Test
//    public void updateProductConfigImpositionScript(){
//        Query query = Query.query(Criteria.where("configurableProductId").is(9352112L));
//        query.fields().include("_id");
//        List<SkuProduct> skuProducts = configMongoTemplate.find(query, SkuProduct.class);
//        List<Long> collect = skuProducts.stream().map(SkuProduct::getId).collect(Collectors.toList());
//        List<ProductConfig> productConfigs = configMongoTemplate.find(Query.query(Criteria.where("productId").in(collect)), ProductConfig.class);
//        List<Long> productConfigIds = productConfigs.stream().map(ProductConfig::getId).collect(Collectors.toList());
//        List<ProductConfigImposition> productConfigImpositions = configMongoTemplate.find(Query.query(Criteria.where("productConfigId").in(productConfigIds)), ProductConfigImposition.class);
//        for (ProductConfigImposition productConfigImposition : productConfigImpositions) {
//            if (productConfigImposition.getIsManual() == null || !productConfigImposition.getIsManual()) {
//                System.out.println(productConfigImposition.getId());
//                System.out.println(productConfigImposition.getIsManual());
//                productConfigImposition.setIsManual(true);
////                configMongoTemplate.save(productConfigImposition);
//            }
//        }
//    }
//}
