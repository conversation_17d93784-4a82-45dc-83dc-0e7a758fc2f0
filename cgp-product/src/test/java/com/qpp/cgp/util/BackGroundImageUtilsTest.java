package com.qpp.cgp.util;

import com.qpp.cgp.domain.pcresource.SizeRange;
import com.qpp.core.dto.ObjectDTO;
import com.qpp.service.file.FileService;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.springframework.util.ResourceUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class BackGroundImageUtilsTest {


    @Test
    public void imageUpload() throws IOException {
        FileService fileService = Mockito.mock(FileService.class);
        BackGroundImageUtils backGroundImageUtils = new BackGroundImageUtils();
        Whitebox.setInternalState(backGroundImageUtils, "fileService", fileService);
        ObjectDTO objectDTO = new ObjectDTO();
        objectDTO.put("fileName", "123.jpg");
        File file = ResourceUtils.getFile("classpath:001.jpg").getAbsoluteFile();
        BufferedImage image = ImageIO.read(file);
        Mockito.when(fileService.uploadFileByByteArrayResource(Mockito.any())).thenReturn(objectDTO);
        Object result = backGroundImageUtils.imageUpload(image, "jpg");
        Mockito.verify(fileService).uploadFileByByteArrayResource(Mockito.any());
        Assert.assertNotNull(result);
        Assert.assertEquals(objectDTO.get("fileName"), result);
    }

    @Test
    public void isMatchSize() {
        BackGroundImageUtils backGroundImageUtils = new BackGroundImageUtils();
        SizeRange sizeRange = new SizeRange();
        Number width = 123.20;
        Number height = 111;
        sizeRange.setEqualMax(false);
        sizeRange.setEqualMin(false);
        sizeRange.setMaxHeight(112);
        sizeRange.setMaxWidth(133);
        sizeRange.setMinHeight(98);
        sizeRange.setMinWidth(100);
        Boolean result = backGroundImageUtils.isMatchSize(sizeRange, width, height);
        Assert.assertTrue(result);
        SizeRange sizeRange1 = new SizeRange();
        sizeRange1.setEqualMax(true);
        sizeRange1.setEqualMin(true);
        sizeRange1.setMaxHeight(112);
        sizeRange1.setMaxWidth(133);
        sizeRange1.setMinHeight(98);
        sizeRange1.setMinWidth(100);
        Boolean result1 = backGroundImageUtils.isMatchSize( sizeRange1, 100, 112);
        Assert.assertTrue(result1);
        Boolean result2 = backGroundImageUtils.isMatchSize( sizeRange1, 99, 112);
        Assert.assertFalse(result2);

    }

    @Test
    public void insertBackgroundImage() {
        BackGroundImageUtils backGroundImageUtils = new BackGroundImageUtils();
        Map<String, Object> presetContent = new HashMap<>();
        WidthHeightDTO widthHeightDTO = new WidthHeightDTO();
        widthHeightDTO.setTargetHeight(120);
        widthHeightDTO.setTargetWidth(3000);
        String imageURl = "123.jpg";
        Map<String,Object> result = backGroundImageUtils.insertBackgroundImage(imageURl, presetContent, widthHeightDTO);
        Assert.assertNotNull(result);
        Assert.assertNotNull(((Map<?, ?>) result).get("backgroundImage"));
        Object backgroundImage = ((Map<?, ?>) result).get("backgroundImage");
        Assert.assertTrue(backgroundImage instanceof Map);
        Assert.assertNotNull(((Map<?, ?>) backgroundImage).get("imageName"));
        Assert.assertNotNull(((Map<?, ?>) backgroundImage).get("imageWidth"));
        Assert.assertNotNull(((Map<?, ?>) backgroundImage).get("imageHeight"));
        Assert.assertEquals(imageURl, ((Map<?, ?>) backgroundImage).get("imageName"));
        Assert.assertEquals(widthHeightDTO.getTargetWidth(), ((Map<?, ?>) backgroundImage).get("imageWidth"));
        Assert.assertEquals(widthHeightDTO.getTargetHeight(), ((Map<?, ?>) backgroundImage).get("imageHeight"));
    }
}