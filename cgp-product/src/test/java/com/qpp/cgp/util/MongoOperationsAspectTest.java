//package com.qpp.cgp.util;
//
//import com.mongodb.MongoClient;
//import com.mongodb.MongoClientURI;
//import com.mongodb.client.MongoCollection;
//import com.mongodb.client.MongoDatabase;
//import com.qpp.cgp.domain.attributeconfig.AttributeProfile;
//import com.qpp.cgp.domain.common.EntityStatus;
//import com.qpp.cgp.domain.customs.CustomsElement;
//import com.qpp.cgp.manager.customs.CustomsElementManager;
//import com.qpp.id.generator.IdGenerator;
//import com.qpp.mongo.domain.MongoDomain;
//import com.qpp.mongo.driver.HybridMongoTemplate;
//import org.aspectj.lang.ProceedingJoinPoint;
//import org.bson.Document;
//import org.bson.conversions.Bson;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.data.mongodb.MongoDbFactory;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.data.mongodb.core.SimpleMongoDbFactory;
//import org.springframework.data.mongodb.core.query.Criteria;
//import org.springframework.data.mongodb.core.query.Query;
//import org.springframework.test.context.junit4.SpringRunner;
//
//
//import java.util.List;
//import java.util.stream.Collectors;
//
//import static org.junit.Assert.*;
//
//@RunWith(SpringRunner.class)
//@AutoConfigureMockMvc(addFilters = false)
//@SpringBootTest
//public class MongoOperationsAspectTest {
//
//    private final Logger logger = LoggerFactory.getLogger(this.getClass());
//
//    @Autowired private MongoTemplate mongoTemplate;
//
//    @Autowired private HybridMongoTemplate hybridMongoTemplate;
//
//    @Autowired private IdGenerator idGenerator;
//
//    // region remove tests
//    /**
//     * @see com.qpp.cgp.common.MongoOperationsAspect#removeObjAdvisor(ProceedingJoinPoint, Object) advisor
//     * @see org.springframework.data.mongodb.core.MongoOperations#remove(Object) joinPoint
//     */
//    
//    @Test
//    public void deleteObjTest() {
//        // remove(obj)
//        AttributeProfile attributeProfile = new AttributeProfile();
//        attributeProfile.setId(idGenerator.generateId().toString());
//        mongoTemplate.insert(attributeProfile);
//        mongoTemplate.remove(attributeProfile);
//        // find check
//        retrieveCheck(attributeProfile.getId(), AttributeProfile.class, "attributeprofiles");
//        assertEquals(0, mongoTemplate.findAll(AttributeProfile.class).stream().map(MongoDomain::getId).filter(s -> s.equals(attributeProfile.getId())).collect(Collectors.toList()).size());
//    }
//
//    /**
//     * @see com.qpp.cgp.common.MongoOperationsAspect#removeObjAdvisor(ProceedingJoinPoint, Object) advisor
//     * @see org.springframework.data.mongodb.core.MongoOperations#remove(Object, String) joinPoint
//     */
//    @Test
//    public void deleteObjStrTest() {
//        // remove(obj, str)
//        AttributeProfile attributeProfile = new AttributeProfile();
//        attributeProfile.setId(idGenerator.generateId().toString());
//        mongoTemplate.insert(attributeProfile);
//        mongoTemplate.remove(attributeProfile, "attributeprofiles");
//        // find check
//        retrieveCheck(attributeProfile.getId(), AttributeProfile.class, "attributeprofiles");
//        assertEquals(0, mongoTemplate.findAll(AttributeProfile.class).stream().map(MongoDomain::getId).filter(s -> s.equals(attributeProfile.getId())).collect(Collectors.toList()).size());
//    }
//
//    /**
//     * @see com.qpp.cgp.common.MongoOperationsAspect#removeQueryAdvisor(ProceedingJoinPoint, Query, Class)  advisor
//     * @see org.springframework.data.mongodb.core.MongoOperations#remove(Query, Class)  joinPoint
//     */
//    @Test
//    public void deleteQueryClassTest() {
//        // remove(obj, str)
//        AttributeProfile attributeProfile = new AttributeProfile();
//        attributeProfile.setId(idGenerator.generateId().toString());
//        mongoTemplate.insert(attributeProfile);
//        mongoTemplate.remove(Query.query(Criteria.where("_id").is(attributeProfile.getId())), AttributeProfile.class);
//        // find check
//        retrieveCheck(attributeProfile.getId(), AttributeProfile.class, "attributeprofiles");
//        assertEquals(0, mongoTemplate.findAll(AttributeProfile.class).stream().map(MongoDomain::getId).filter(s -> s.equals(attributeProfile.getId())).collect(Collectors.toList()).size());
//    }
//
//    /**
//     * @see com.qpp.cgp.common.MongoOperationsAspect#removeQueryAdvisor(ProceedingJoinPoint, Query, Class) advisor
//     * @see org.springframework.data.mongodb.core.MongoOperations#remove(Query, Class, String) joinPoint
//     */
//    @Test
//    public void deleteQueryClassStrTest() {
//        // remove(obj, str)
//        AttributeProfile attributeProfile = new AttributeProfile();
//        attributeProfile.setId(idGenerator.generateId().toString());
//        mongoTemplate.insert(attributeProfile);
//        mongoTemplate.remove(Query.query(Criteria.where("_id").is(attributeProfile.getId())), AttributeProfile.class, "attributeprofiles");
//        // find check
//        retrieveCheck(attributeProfile.getId(), AttributeProfile.class, "attributeprofiles");
//        assertEquals(0, mongoTemplate.findAll(AttributeProfile.class).stream().map(MongoDomain::getId).filter(s -> s.equals(attributeProfile.getId())).collect(Collectors.toList()).size());
//    }
//
//    /**
//     * @see com.qpp.cgp.common.MongoOperationsAspect#removeQueryStringAdvisor(ProceedingJoinPoint, Query, String) advisor
//     * @see org.springframework.data.mongodb.core.MongoOperations#remove(Query, String) joinPoint
//     */
//    @Test
//    public void deleteQueryStrTest() {
//        // remove(obj, str)
//        AttributeProfile attributeProfile = new AttributeProfile();
//        attributeProfile.setId(idGenerator.generateId().toString());
//        mongoTemplate.insert(attributeProfile);
//        mongoTemplate.remove(Query.query(Criteria.where("_id").is(attributeProfile.getId())), "attributeprofiles");
//        // find check
//        retrieveCheck(attributeProfile.getId(), AttributeProfile.class, "attributeprofiles");
//        assertEquals(0, mongoTemplate.findAll(AttributeProfile.class).stream().map(MongoDomain::getId).filter(s -> s.equals(attributeProfile.getId())).collect(Collectors.toList()).size());
//    }
//    // endregion
//
//    private MongoDatabase getDb() {
//        MongoClientURI mongoClientURI = new MongoClientURI("mongodb://developer:Dev!123a@192.168.26.81:27017,192.168.26.82:27017,192.168.26.83:27017/cgp2_dev");
//        MongoDbFactory dbFactory = new SimpleMongoDbFactory(mongoClientURI);
//        return dbFactory.getDb();
//    }
//
//    private void retrieveCheck(String id, Class clazz, String collectionName) {
//        Document filter = new Document();
//        filter.put("_id", id);
//        // expected not exist: mongoTemplate
//        assertEquals(0, mongoTemplate.find(Query.query(Criteria.where("_id").is(id)), clazz).size());
//        assertNull(mongoTemplate.findById(id, clazz));
//        assertNull(mongoTemplate.findOne(Query.query(Criteria.where("_id").is(id)), clazz));
//        // expected not exist: hybridMongoTemplate
//        assertNull(hybridMongoTemplate.findById(collectionName, id));
//        assertNull(hybridMongoTemplate.query(collectionName, filter).first());
//        assertNull(hybridMongoTemplate.query(collectionName, filter, null).first());
//        assertNull(hybridMongoTemplate.query(collectionName, filter, null, null).first());
//        for (Document document : hybridMongoTemplate.queryAll(collectionName)) {
//            assertNotEquals(id, document.get("_id"));
//        }
//        // expected exist: mongoCollection
//        MongoCollection<Document> mongoCollection = getDb().getCollection(collectionName);
//        Document first = mongoCollection.find(filter).first();
//        assertNotNull(first);
//        assertEquals(EntityStatus.REMOVED, first.get("status"));
//        // count compare: mongoTemplate vs mongoCollection
//        assertEquals(mongoCollection.count(filter) - 1, mongoTemplate.count(Query.query(Criteria.where("_id").is(id)), clazz));
//        assertEquals(mongoCollection.count(filter) - 1, mongoTemplate.count(Query.query(Criteria.where("_id").is(id)), clazz, collectionName));
//        assertEquals(mongoCollection.count(filter) - 1, mongoTemplate.count(Query.query(Criteria.where("_id").is(id)), collectionName));
//        // count compare: hybridMongoTemplate vs mongoCollection
//        assertEquals(mongoCollection.count() - 1, hybridMongoTemplate.count(collectionName));
//        assertEquals(mongoCollection.count(filter) - 1, hybridMongoTemplate.count(collectionName, filter));
//        // clear unused data finally(delete actually)
//        mongoCollection.deleteOne(filter);
//    }
//}