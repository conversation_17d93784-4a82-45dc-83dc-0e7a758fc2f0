package com.qpp.cgp.manager.product.shipping;

import com.qpp.cgp.config.mongo.MongoTemplateFactory;
import com.qpp.cgp.domain.product.shipping.ProductAreaShippingConfig;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class ProductAreaShippingConfigUpdaterTest {

    @Spy
    @InjectMocks
    private ProductAreaShippingConfigUpdater productAreaShippingConfigUpdater;

    @Mock
    private MongoTemplateFactory mongoTemplateFactory;

    @Test
    void testUpdateDefaultConfigGivenNull() {
        // input
        Long productId = null;
        String id = null;
        // mock

        // invoke
        productAreaShippingConfigUpdater.updateDefaultConfig(productId, id);
        // verify
        Mockito.verify(productAreaShippingConfigUpdater).updateDefaultConfig(productId, id);
        Mockito.verifyNoMoreInteractions(productAreaShippingConfigUpdater);
        Mockito.verifyNoInteractions(mongoTemplateFactory);
    }

    @Test
    void testUpdateDefaultConfigGivenNotNull() {
        // input
        Long productId = 1L;
        String id = "1";
        // mock
//        Mockito.when(productAreaShippingConfigUpdater.getIdsByProductId(productId)).thenReturn(new ArrayList<>());
        Mockito.doReturn(new ArrayList<>()).when(productAreaShippingConfigUpdater).getIdsByProductId(productId);
        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
        Mockito.when(mongoTemplateFactory.getMongoTemplate(ProductAreaShippingConfig.class)).thenReturn(mongoTemplate);
        // invoke
        boolean retVal = productAreaShippingConfigUpdater.updateDefaultConfig(productId, id);
        // verify
        Mockito.verify(productAreaShippingConfigUpdater).updateDefaultConfig(productId, id);
        Mockito.verify(productAreaShippingConfigUpdater).getIdsByProductId(productId);
        Mockito.verify(mongoTemplateFactory).getMongoTemplate(ProductAreaShippingConfig.class);
        Mockito.verify(mongoTemplate).updateFirst(Mockito.any(), Mockito.any(), Mockito.eq(ProductAreaShippingConfig.class));
        Mockito.verify(mongoTemplate).updateMulti(Mockito.any(), Mockito.any(), Mockito.eq(ProductAreaShippingConfig.class));
        assertTrue(retVal);
    }
}