package com.qpp.cgp.manager.pc;

import com.qpp.cgp.domain.bom.AbstractMaterialViewType;
import com.qpp.cgp.domain.pcresource.BusinessLibrary;
import com.qpp.cgp.domain.pcresource.IPCResource;
import com.qpp.cgp.domain.pcresource.PCResourceItem;
import com.qpp.cgp.domain.pcresource.PCResourceLibrary;
import com.qpp.cgp.domain.pcresource.color.Color;
import com.qpp.cgp.domain.theme.RandomPcResourceContent;
import com.qpp.cgp.manager.pcresource.curd.PCResourceItemManager;
import com.qpp.cgp.manager.pcresource.curd.PCResourceLibraryFinder;
import com.qpp.core.exception.BusinessException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @Date 2021/9/28 17:28
 * @Version 1.0
 */
@RunWith(MockitoJUnitRunner.class)
public class RanDomPcResourceContentStrategyTest {

    @Spy
    @InjectMocks
    private RandomPcResourceContentStrategy rStrategy;

    @Mock
    private PcResourceApplyConfigService applyConfigService;

    @Mock
    private PCResourceItemManager pcResourceItemManager;

    @Mock
    private PCResourceLibraryFinder pcResourceLibraryFinder;

    @Test
    public void testGetRandomPcResourceByParameterIsNull(){
        boolean flag = false;
        RandomPcResourceContent randomPcResourceContent = null;
        try{
            ReflectionTestUtils.invokeMethod(rStrategy,"getRandomPcResource",randomPcResourceContent);
        }catch (BusinessException e){
            Assert.assertEquals(2000446,e.getCode());
            flag = true;
        }
        Assert.assertTrue(flag);

    }

    @Test
    public void testGetRandomPcResourceByBusinessLibOfPcResourceContentIsNull(){
        RandomPcResourceContent randomPcResourceContent = new RandomPcResourceContent();
        randomPcResourceContent.setBusinessLib(null);
        IPCResource result = ReflectionTestUtils.invokeMethod(rStrategy, "getRandomPcResource", randomPcResourceContent);
        Assert.assertNull(result);
    }

    @Test
    public void testGetRandomPcResourceByMvtOfPcResourceContentIsNull(){
        RandomPcResourceContent randomPcResourceContent = new RandomPcResourceContent();
        randomPcResourceContent.setBusinessLib(new BusinessLibrary());
        randomPcResourceContent.setMvt(null);
        IPCResource result = ReflectionTestUtils.invokeMethod(rStrategy, "getRandomPcResource", randomPcResourceContent);
        Assert.assertNull(result);
    }

    @Test
    public void testGetRandomPcResourceByPcResourceLibIsNotPresent(){
        RandomPcResourceContent randomPcResourceContent = new RandomPcResourceContent();
        randomPcResourceContent.setBusinessLib(new BusinessLibrary());
        randomPcResourceContent.setMvt(new AbstractMaterialViewType());
        Mockito.doReturn(Optional.empty()).when(pcResourceLibraryFinder).findPCResourceLib((String) any(),any());
        IPCResource result = ReflectionTestUtils.invokeMethod(rStrategy, "getRandomPcResource", randomPcResourceContent);
        Assert.assertNull(result);
    }

    @Test
    public void testGetRandomPcResourceByPcSizeOfResourceLibFilterIsZero(){
        RandomPcResourceContent randomPcResourceContent = new RandomPcResourceContent();
        randomPcResourceContent.setBusinessLib(new BusinessLibrary());
        randomPcResourceContent.setMvt(new AbstractMaterialViewType());
        List<PCResourceItem> list = new ArrayList<>();
        list.add(new PCResourceItem());
        PCResourceLibrary pcResourceLibrary = new PCResourceLibrary();
        Mockito.doReturn(Optional.ofNullable(pcResourceLibrary)).when(pcResourceLibraryFinder).findPCResourceLib((String) any(),any());
        Mockito.doReturn(list).when(pcResourceItemManager).findAllByPCResourceLibraryId(any());
        IPCResource result = ReflectionTestUtils.invokeMethod(rStrategy, "getRandomPcResource", randomPcResourceContent);
        Assert.assertNull(result);
    }

    @Test
    public void testGetRandomPcResourceByCorrect(){
        RandomPcResourceContent randomPcResourceContent = new RandomPcResourceContent();
        randomPcResourceContent.setBusinessLib(new BusinessLibrary());
        randomPcResourceContent.setMvt(new AbstractMaterialViewType());
        List<PCResourceItem> list = new ArrayList<>();
        PCResourceItem pcResourceItem = new PCResourceItem();
        pcResourceItem.setResource(new Color());
        list.add(pcResourceItem);
        PCResourceLibrary pcResourceLibrary = new PCResourceLibrary();
        Mockito.doReturn(Optional.ofNullable(pcResourceLibrary)).when(pcResourceLibraryFinder).findPCResourceLib((String) any(),any());
        Mockito.doReturn(list).when(pcResourceItemManager).findAllByPCResourceLibraryId(any());
        IPCResource result = ReflectionTestUtils.invokeMethod(rStrategy, "getRandomPcResource", randomPcResourceContent);
        Assert.assertTrue(result instanceof Color);
    }
}
