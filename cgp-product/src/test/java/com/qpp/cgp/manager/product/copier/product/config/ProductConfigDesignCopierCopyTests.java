package com.qpp.cgp.manager.product.copier.product.config;

import com.qpp.cgp.domain.product.config.ProductConfigDesign;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.HashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR> <PERSON>u
 * @Date 2021/3/8 13:39
 * @Description
 * @Version 1.0
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
public class ProductConfigDesignCopierCopyTests {

    @Autowired
    private ProductConfigDesignCopier productConfigDesignCopier;

    @Test
    public void testByNullProductConfigDesign(){
        try {
            ProductConfigDesign productConfigDesign = productConfigDesignCopier.copy(null, 123L,
                    null, new AtomicLong(1), true, new HashMap<>(), false);
        } catch (Exception e) {
            Assert.assertEquals("Parameter：source can not be null!", e.getMessage());
        }
    }

}
