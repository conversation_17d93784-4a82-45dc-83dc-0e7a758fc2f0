package com.qpp.cgp.manager.bom;

import com.qpp.cgp.config.mongo.MongoTemplateFactory;
import com.qpp.cgp.domain.bom.Material;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import static org.junit.Assert.*;

public class MaterialManagerTest {

    @Test
    public void existsById() {
        MaterialManager materialManager = new MaterialManager(null, null);
        MongoTemplateFactory mongoTemplateFactory = Mockito.mock(MongoTemplateFactory.class);
        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
        Mockito.when(mongoTemplateFactory.getMongoTemplate(Material.class)).thenReturn(mongoTemplate);
        String id = "123";
        Mockito.when(mongoTemplate.exists(Query.query(Criteria.where("_id").is(id)), Material.class)).thenReturn(true);
        Whitebox.setInternalState(materialManager, "mongoTemplateFactory", mongoTemplateFactory);
        boolean result = materialManager.existsById(id);
        Mockito.verify(mongoTemplateFactory).getMongoTemplate(Material.class);
        Mockito.verify(mongoTemplate).exists(Query.query(Criteria.where("_id").is(id)), Material.class);
        Assert.assertTrue(result);
    }
}