//package com.qpp.cgp.manager.product.config;
//
//import com.fasterxml.jackson.core.JsonProcessingException;
//import com.google.common.collect.ImmutableList;
//import com.qpp.cgp.domain.common.font.CommonBuilderFontConfig;
//import com.qpp.cgp.domain.common.font.Font;
//import com.qpp.cgp.domain.common.font.FontDTO;
//import com.qpp.cgp.domain.preprocess.holiday.MonthBackGroundPictureGroup;
//import com.qpp.cgp.domain.preprocess.holiday.MonthImageGroup;
//import com.qpp.cgp.domain.product.config.BuilderConfig;
//import com.qpp.cgp.domain.product.config.ProductConfigContext;
//import com.qpp.cgp.manager.common.LanguageManager;
//import com.qpp.cgp.manager.common.font.CommonBuilderFontConfigManager;
//import org.apache.commons.lang.StringUtils;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.data.mongodb.core.query.Criteria;
//import org.springframework.data.mongodb.core.query.Query;
//
//import org.springframework.test.context.junit4.SpringRunner;
//
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.stream.Collectors;
//
//@RunWith(SpringRunner.class)
//@SpringBootTest
//
//public class BuilderConfigManagerTest {
//
//
//    @Autowired
//    private BuilderConfigManager builderConfigManager;
//
//    @Autowired
//    private MongoTemplate mongoTemplate;
//
//    @Autowired
//    private CommonBuilderFontConfigManager commonBuilderFontConfigManager;
//
//    @Test
//    public void addCommonBuilderFonts(){
//        final CommonBuilderFontConfig commonBuilderFontConfig = new CommonBuilderFontConfig();
//        commonBuilderFontConfig.setClazz(CommonBuilderFontConfig.class.getName());
//        final List<Font> all = mongoTemplate.findAll(Font.class);
//        List<Font> fonts = new ArrayList<>();
//        all.forEach(e->{
//            final Font font = new Font();
//            font.setClazz(Font.class.getName());
//            font.setId(e.getId());
//            fonts.add(font);
//        });
//        commonBuilderFontConfig.setFonts(fonts);
//        commonBuilderFontConfigManager.saveDTO(commonBuilderFontConfig);
//    }
//
//    @Autowired
//    private LanguageManager languageManager;
//
//    @Test
//    public void getFonts() throws JsonProcessingException {
//        case1(null);
//        case2(null);
//        case3(null);
//        case4(null);
//        case5(null);
//        case6(null);
//        case1(ProductConfigContext.PC);
//        case2(ProductConfigContext.PC);
//        case3(ProductConfigContext.PC);
//        case4(ProductConfigContext.PC);
//        case5(ProductConfigContext.PC);
//        case6(ProductConfigContext.PC);
//    }
//
//    public void case1(String context) throws JsonProcessingException {
//        //propertyModel=2266562
//        final List<FontDTO> fonts = builderConfigManager.getFonts("2266562", "zh",null,null, "FrenchBuilder", context);
//        Assert.assertEquals(1, fonts.stream().map(FontDTO::get_id).distinct().count());
//        Assert.assertTrue(fonts.get(0).get_id().equalsIgnoreCase("5786153"));
//    }
//
//    public void case2(String context) {
//        final List<FontDTO> fonts = builderConfigManager.getFonts("2266562", "zh", null,null,"SimplifiedChineseBuilderTest", context);
//        Assert.assertEquals(4, fonts.stream().map(FontDTO::get_id).distinct().count());
//        Assert.assertTrue(fonts.stream().map(FontDTO::get_id).distinct().collect(Collectors.toList()).containsAll(ImmutableList.of("5786588","5786591","5786609","5786153")));
//    }
//
//    public void case3(String context) {
//
//        final List<FontDTO> fonts = builderConfigManager.getFonts("2266562", "en", null,null,"SimplifiedChineseBuilderTest", context);
//        Assert.assertEquals(4, fonts.stream().map(FontDTO::get_id).distinct().count());
//        Assert.assertTrue(fonts.stream().map(FontDTO::get_id).distinct().collect(Collectors.toList()).containsAll(ImmutableList.of("5786533","5786551","5786568","5786154")));
//
//    }
//
//    public void case4(String context) {
//        final List<FontDTO> fonts = builderConfigManager.getFonts("2266562", "en",null,null, "FrenchBuilder", context);
//        Assert.assertEquals(1, fonts.stream().map(FontDTO::get_id).distinct().count());
//        Assert.assertTrue(fonts.stream().map(FontDTO::get_id).distinct().collect(Collectors.toList()).containsAll(ImmutableList.of("5786154")));
//    }
//    public void case5(String context){
//        final List<FontDTO> fonts = builderConfigManager.getFonts("4981022", "EN", null,null,"SimplifiedChineseBuilderTest", context);
//        Assert.assertEquals(460, fonts.stream().map(FontDTO::get_id).distinct().count());
//    }
//
//    public void case6(String context){
//        final List<FontDTO> fonts = builderConfigManager.getFonts("4981022", "EN",null,null, "FrenchBuilder", context);
//        Assert.assertEquals(127, fonts.stream().map(FontDTO::get_id).distinct().count());
//    }
//
//    @Autowired
//    private JdbcTemplate jdbcTemplate;
//
//    @Autowired
//
//    @Test
//    public void testAdd(){
//        MonthImageGroup monthImageGroup = new MonthImageGroup();
//        monthImageGroup.setId("15982944");
//        MonthBackGroundPictureGroup monthBackGroundPictureGroup = new MonthBackGroundPictureGroup();
//        monthBackGroundPictureGroup.setId("15982944");
//        mongoTemplate.save(monthBackGroundPictureGroup);
//        mongoTemplate.save(monthImageGroup);
//    }
//
//    @Test
//    public void UpdateProductPreURL() {
//        List<Long> productConfigViewIds = jdbcTemplate.queryForList("select bcv_id\n" +
//                "from cgp_builder_config_view\n" +
//                "where bc_id in (select bc_id\n" +
//                "                from cgp_builder_config\n" +
//                "                where product_id in (select product_id\n" +
//                "                                     from cgp_product\n" +
//                "                                     where product_id in\n" +
//                "                                           (6419427, 6062872, 7718265, 7718864, 8624055, 8774869, 8775133, 8775339,\n" +
//                "                                            8643759, 8775688,\n" +
//                "                                            7599480)\n" +
//                "                                        or product_configurable_product_id in\n" +
//                "                                           (6419427, 6062872, 7718265, 7718864, 8624055, 8774869, 8775133, 8775339,\n" +
//                "                                            8643759, 8775688,\n" +
//                "                                            7599480)));", Long.class);
//        List<BuilderConfig> builderConfigs = mongoTemplate.find(Query.query(Criteria.where("productConfigViewId").in(productConfigViewIds)), BuilderConfig.class);
//        for (BuilderConfig builderConfig : builderConfigs) {
//            if (builderConfig.getBuilderLocations() != null && builderConfig.getBuilderLocations().size() > 0) {
//                builderConfig.getBuilderLocations().forEach(localeBuilderLocationConfig -> {
//                    if (StringUtils.isEmpty(localeBuilderLocationConfig.getUserPreviewUrl())) {
////                        System.out.println("预览地址为空：" + builderConfig.get_id());
//                    }
//                    localeBuilderLocationConfig.setUserPreviewUrl("/whitelabel-site/h5builder/preview/pc/1/index.html");
//                });
//                mongoTemplate.save(builderConfig);
//            }
//        }
//        System.out.println(builderConfigs.size());
//        System.out.println(productConfigViewIds);
//    }
//}