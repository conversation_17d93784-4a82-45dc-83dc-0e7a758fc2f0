package com.qpp.cgp.manager.product.config;

import com.qpp.cgp.domain.product.config.ProductConfigBom;
import com.qpp.cgp.domain.product.config.ProductConfigImposition;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

/**
 * <AUTHOR> Chiu
 * @Date 2021/3/9 9:27
 * @Description
 * @Version 1.0
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
public class ProductConfigImpositionManagerTests {

    @Autowired
    private ProductConfigImpositionManager productConfigImpositionManager;

    @Test
    public void testSaveNew(){
        try {
            ProductConfigImposition productConfigImposition = productConfigImpositionManager.saveNew(null);
        } catch (Exception e) {
            Assert.assertEquals("Parameter：productConfigImposition can not be null!", e.getMessage());
        }
    }

    @Test
    public void testSaveUpdate(){
        try {
            ProductConfigImposition productConfigImposition = productConfigImpositionManager.saveUpdate(null, 1213L);
        } catch (Exception e) {
            Assert.assertEquals("Parameter：productConfigImposition can not be null!", e.getMessage());
        }
    }

    @Test
    public void testGetBomsFromProductConfigImposition(){
        try {
            List<ProductConfigBom> bomsFromProductConfigImposition = productConfigImpositionManager.getBomsFromProductConfigImposition(null);
        } catch (Exception e) {
            Assert.assertEquals("Parameter：productConfigImposition can not be null!", e.getMessage());
        }
    }

}
