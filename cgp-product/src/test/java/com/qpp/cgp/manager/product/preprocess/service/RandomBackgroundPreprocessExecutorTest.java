//package com.qpp.cgp.manager.product.preprocess.service;
//
//import com.qpp.cgp.domain.background.Background;
//import com.qpp.cgp.domain.preprocess.dto.PreprocessDTO;
//import com.qpp.cgp.domain.product.config.ProductConfig;
//import com.qpp.cgp.domain.product.config.ProductConfigDesign;
//import com.qpp.cgp.domain.product.config.v2.builder.BuilderConfigV2;
//import com.qpp.cgp.domain.product.config.v2.builder.BuilderViewResourceConfig;
//import com.qpp.cgp.domain.product.config.v2.builder.ResourceConfigType;
//import com.qpp.core.exception.BusinessException;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.Mockito;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.boot.test.mock.mockito.SpyBean;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.data.mongodb.core.query.Criteria;
//import org.springframework.data.mongodb.core.query.Query;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.Arrays;
//import java.util.Optional;
//
//import static org.mockito.Mockito.times;
//
//
//@RunWith(SpringRunner.class)
//@SpringBootTest
//public class RandomBackgroundPreprocessExecutorTest {
//
//    @Autowired
//    private RandomBackgroundPreprocessExecutor randomBackgroundPreprocessExecutor;
//
//    @SpyBean(name = "hybirdMongoTemplate")
//    private MongoTemplate mongoTemplate;
//
//    @SpyBean(name = "configMongoTemplate")
//    private MongoTemplate configMongoTemplate;
//
//    @Test
//    public void testGenerateRandomBackground() {
//
//    }
//
//    @Test
//    public void testConvertContext() {
//
//    }
//
//    @Test
//    public void testGetBackgroundSizeDifferenceConfigs() {
//
//    }
//
//    @Test
//    public void testGetCalculateValueExConfigByCode() {
//
//    }
//
//    @Test
//    public void testGenerateBackCalculateDTO() {
//
//    }
//
//    @Test
//    public void testFilterBackGroundIds() {
//
//    }
//
//    @Test
//    public void testGetBackgroundImages() {
//
//    }
//
//    @Test(expected = BusinessException.class)
//    public void testPreprocess() {
//        //目前没有实现，直接抛出异常
//        randomBackgroundPreprocessExecutor.preprocess(null);
//        randomBackgroundPreprocessExecutor.preprocess(new PreprocessDTO());
//    }
//
//    @Before
//    public void initMock() {
////        MongoTemplate mongoTemplate = new MongoTemplate(new SimpleMongoDbFactory(null, null, null));
//
//        //mock BuilderConfigV2 findById数据
//        BuilderConfigV2 builderConfigV2 = new BuilderConfigV2();
//        BuilderConfigV2 containBuilderResource = new BuilderConfigV2();
//        BuilderViewResourceConfig builderViewResourceConfig = new BuilderViewResourceConfig();
//        builderViewResourceConfig.setBackgrounds(Arrays.asList(new Background()));
//        builderViewResourceConfig.setBgConfigType(ResourceConfigType.Replace);
//        containBuilderResource.setBuilderViewResourceConfig(builderViewResourceConfig);
//        Mockito.when(mongoTemplate.findOne(Query.query(Criteria.where("productConfigViewId").is(123L)), BuilderConfigV2.class)).thenReturn(builderConfigV2);
//        Mockito.when(mongoTemplate.findOne(Query.query(Criteria.where("productConfigViewId").is(124L)), BuilderConfigV2.class)).thenReturn(containBuilderResource);
//        BuilderConfigV2 case5 = new BuilderConfigV2();
//        BuilderViewResourceConfig builderViewResourceConfigByCase5 = new BuilderViewResourceConfig();
//        builderViewResourceConfigByCase5.setBackgrounds(null);
//        case5.setBuilderViewResourceConfig(builderViewResourceConfigByCase5);
//        Mockito.when(mongoTemplate.findOne(Query.query(Criteria.where("productConfigViewId").is(125L)), BuilderConfigV2.class)).thenReturn(case5);
//        BuilderConfigV2 builderConfigV2ByCase6 = new BuilderConfigV2();
//        BuilderViewResourceConfig builderViewResourceConfigByCase6 = new BuilderViewResourceConfig();
//        builderViewResourceConfigByCase6.setBackgrounds(Arrays.asList(new Background()));
//        builderConfigV2ByCase6.setBuilderViewResourceConfig(builderViewResourceConfigByCase6);
//        Mockito.when(mongoTemplate.findOne(Query.query(Criteria.where("productConfigViewId").is(126L)), BuilderConfigV2.class)).thenReturn(builderConfigV2ByCase6);
//        //case 111L
//        Mockito.when(mongoTemplate.findOne(Query.query(Criteria.where("productConfigViewId").is(111L)), BuilderConfigV2.class)).thenReturn(null);
//
//        //mock findProductConfigIdByProductDesignId
//         /*
//            测试用例：
//            case 1：测试如果传入的DesignId is null throw Exception
//            case 2: test productDesignId not match Entity throw Exception
//            case 3: test productDesignId match Entity , and ProductConfigId is null ,throw exception
//            case 4: test productDesignId match Entity and productConfigId is Exists return Id
//         */
//        Mockito.when(configMongoTemplate.findById(2111L, ProductConfigDesign.class)).thenReturn(null);
//        ProductConfigDesign productConfigDesignByCase4 = new ProductConfigDesign();
//        productConfigDesignByCase4.setProductConfigId(123L);
//        Mockito.when(configMongoTemplate.findById(2112L, ProductConfigDesign.class)).thenReturn(new ProductConfigDesign());
//        Mockito.when(configMongoTemplate.findById(2113L, ProductConfigDesign.class)).thenReturn(productConfigDesignByCase4);
//        //mock
//        Mockito.when(configMongoTemplate.findById(3111L, ProductConfig.class)).thenReturn(null);
//        Mockito.when(configMongoTemplate.findById(3112L, ProductConfig.class)).thenReturn(new ProductConfig());
//        ProductConfig productConfig = new ProductConfig();
//        productConfig.setProductId(113L);
//
//        Mockito.when(configMongoTemplate.findById(3113L, ProductConfig.class)).thenReturn(productConfig);
//    }
//
//    @Test
//    public void testGetBuilderViewResourceConfigByProductConfigViewId() {
//        /*
//         * 测试的情况：
//         *  1：如果传入的为空，抛出异常
//         *  2：如果传入的Id没有匹配的，抛出异常
//         *  3：如果传入的对应的Id存在，但是对应的BuilderViewResourceConfig对应的资源不存在返回Null
//         *  4：如果传入的对应的Id存在，同时对应的BuilderViewResourceConfig不为空，并且背景图和对应的炒作类型不为空，返回对应的对象
//         *  5：如果对应的背景图片图片为空的话，返回NUll
//         *  6: 如果对应的BuilderReSource的并且对应的背景图片不为空，并且炒作类型为空的话抛出异常
//         */
//        //case 1
//        try {
//            BuilderViewResourceConfig builderViewResourceConfig = randomBackgroundPreprocessExecutor.getBuilderViewResourceConfigByProductConfigViewId(null);
//        } catch (Exception e) {
//            Assert.assertTrue(e instanceof BusinessException);
//        }
//        //case 2
//        try {
//            BuilderViewResourceConfig builderViewResourceConfigByProductConfigViewId = randomBackgroundPreprocessExecutor.getBuilderViewResourceConfigByProductConfigViewId(111L);
//        } catch (Exception e) {
//            Assert.assertTrue(e instanceof BusinessException);
//        }
//        //case 3
//        Assert.assertNull(randomBackgroundPreprocessExecutor.getBuilderViewResourceConfigByProductConfigViewId(123L));
//        Mockito.verify(mongoTemplate,times(2)).findOne(Query.query(Criteria.where("productConfigViewId").is(123L)), BuilderConfigV2.class);
//        //case 4
//        Assert.assertNotNull(randomBackgroundPreprocessExecutor.getBuilderViewResourceConfigByProductConfigViewId(124L));
//        //case 5
//        Assert.assertNull(randomBackgroundPreprocessExecutor.getBuilderViewResourceConfigByProductConfigViewId(125L));
//        //case 6
//        try {
//            BuilderViewResourceConfig builderViewResourceConfigByProductConfigViewId = randomBackgroundPreprocessExecutor.getBuilderViewResourceConfigByProductConfigViewId(126L);
//        } catch (Exception e) {
//            Assert.assertTrue(e instanceof BusinessException);
//        }
//    }
//
//    @Test
//    public void testFindProductConfigIdByDesignId() {
//        /*
//            测试用例：
//            case 1：测试如果传入的DesignId is null throw Exception
//            case 2: test productDesignId not match Entity throw Exception
//            case 3: test productDesignId match Entity , and ProductConfigId is null ,throw exception
//            case 4: test productDesignId match Entity and productConfigId is Exists return Id
//         */
//
//        try {
//            Long productIdByProductConfigId = randomBackgroundPreprocessExecutor.findProductConfigIdByDesignId(null);
//        } catch (Exception e) {
//            Assert.assertTrue(e instanceof BusinessException);
//        }
//
//        try {
//            Long productConfigIdByDesignIdByCase2 = randomBackgroundPreprocessExecutor.findProductConfigIdByDesignId("2111");
//        } catch (Exception e) {
//            org.junit.Assert.assertTrue(e instanceof BusinessException);
//        }
//
//        try {
//            Long productConfigIdByDesignIdByCase3 = randomBackgroundPreprocessExecutor.findProductConfigIdByDesignId("2112");
//        } catch (Exception e) {
//            Assert.assertTrue(e instanceof BusinessException);
//        }
//        Long productConfigIdByDesignIdByCase4 = randomBackgroundPreprocessExecutor.findProductConfigIdByDesignId("2113");
//        org.junit.Assert.assertEquals(java.util.Optional.of(123L), Optional.of(productConfigIdByDesignIdByCase4));
//
//    }
//
//    @Test
//    public void testFindProductIdByProductConfigId() {
//        //通过产品配置Id查询对应的产品配置的Id
//        /*
//         * 测试用例：
//         *         1：如果传入的产品配置为空，抛出异常
//         *         2：如果传入的产品配置Id找不到实体，抛出异常
//         *         3：如果传入的产品配置的Id没有产品Id字段，抛出异常
//         *         4: 如果传入的数据都正确的话，返回对应的产品Id
//         */
//        //case 1
//        try {
//            Long productIdByProductConfigId = randomBackgroundPreprocessExecutor.findProductIdByProductConfigId(null);
//        } catch (Exception e) {
//            Assert.assertTrue(e instanceof BusinessException);
//        }
//
//        //case 2
//        try {
//            Long productIdByProductConfigId = randomBackgroundPreprocessExecutor.findProductIdByProductConfigId(3111L);
//        } catch (Exception e) {
//            Assert.assertTrue(e instanceof BusinessException);
//        }
//        //case3
//        try {
//            Long productIdByProductConfigId = randomBackgroundPreprocessExecutor.findProductIdByProductConfigId(3112L);
//        } catch (Exception e) {
//            Assert.assertTrue(e instanceof BusinessException);
//        }
//        //case 4
//        Long productIdByProductConfigId = randomBackgroundPreprocessExecutor.findProductIdByProductConfigId(3113L);
//        Assert.assertEquals(113, (long) productIdByProductConfigId);
//    }
//}