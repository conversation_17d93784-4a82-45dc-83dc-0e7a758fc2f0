package com.qpp.cgp.manager.product.config;

import com.qpp.cgp.domain.product.config.ProductConfigBom;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @Date 2021/3/9 10:14
 * @Description
 * @Version 1.0
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
public class ProductConfigBomManagerTests {

    @Autowired
    private ProductConfigBomManager productConfigBomManager;

    @Test
    public void testSaveNewByProductConfigBom(){
        try {
            ProductConfigBom productConfigBom = productConfigBomManager.saveNew(null);
        } catch (Exception e) {
            Assert.assertEquals("Parameter：productConfigBom can not be null!", e.getMessage());
        }
    }

    @Test
    public void testSaveUpdateByProductConfigBom(){
        try {
            ProductConfigBom productConfigBom = productConfigBomManager.saveUpdate(null, 123L);
        } catch (Exception e) {
            Assert.assertEquals("Parameter：productConfigBom can not be null!", e.getMessage());
        }
    }
}
