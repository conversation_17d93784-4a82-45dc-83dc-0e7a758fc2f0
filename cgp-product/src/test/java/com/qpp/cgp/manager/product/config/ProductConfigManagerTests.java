package com.qpp.cgp.manager.product.config;

import com.qpp.cgp.domain.common.Language;
import com.qpp.cgp.domain.product.config.ProductConfigBom;
import org.bson.Document;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.*;

/**
 * <AUTHOR> Chiu
 * @Date 2021/3/5 18:00
 * @Description
 * @Version 1.0
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
public class ProductConfigManagerTests {

    @Autowired
    private ProductConfigManager productConfigManager;

    @Test
    public void testGetBuilderUrlByViewConfigByNullProductConfigView(){
        try {
            productConfigManager.getBuilderUrlByViewConfig(null, Optional.of(new Language()), null, true, "123");
        } catch (Exception e) {
            Assert.assertEquals("Parameter：productViewConfig can not be null!", e.getMessage());
        }
    }

    @Test
    public void testGetBuilderUrlByViewConfigNewByNullProductConfigView(){
        try {
            productConfigManager.getBuilderUrlByViewConfig(null, Optional.of(new Language()), null, true, "123");
        } catch (Exception e) {
            Assert.assertEquals("Parameter：productViewConfig can not be null!", e.getMessage());
        }
    }

    @Test
    public void testGetBuilderUrlByViewConfigV1ByNullProductConfigView(){
        try {
            productConfigManager.getBuilderUrlByViewConfig(null, Optional.of(new Language()), null, true, "123");
        } catch (Exception e) {
            Assert.assertEquals("Parameter：productViewConfig can not be null!", e.getMessage());
        }
    }

    @Test
    public void testGetBuilderUrlByViewConfigNewV1ByNullProductConfigView(){
        try {
            productConfigManager.getBuilderUrlByViewConfig(null, Optional.of(new Language()), null, true, "123");
        } catch (Exception e) {
            Assert.assertEquals("Parameter：productViewConfig can not be null!", e.getMessage());
        }
    }

    @Test
    public void testGetBomReferenceEntityByNullProductConfigBom(){

        try {
            Map<String, Set<Document>> bomReferenceEntity = productConfigManager.getBomReferenceEntity(null);
        } catch (Exception e) {
            Assert.assertEquals("Parameter：configBom can not be null!", e.getMessage());
        }
    }

    @Test
    public void testGetSkuProductConfigBomsByNullProductConfigBom(){
        try {
            List<ProductConfigBom> skuProductConfigBoms = productConfigManager.getSkuProductConfigBoms(null, new ArrayList<>());
        } catch (Exception e) {
            Assert.assertEquals("Parameter：configurableProductConfigBom can not be null!", e.getMessage());
        }
    }
}
