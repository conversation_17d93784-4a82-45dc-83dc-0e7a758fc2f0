//package com.qpp.cgp.manager.product.simplifybom;
//
//import com.google.common.collect.ImmutableList;
//import com.qpp.cgp.domain.bom.ProductMaterialViewType;
//import com.qpp.cgp.domain.bom.runtime.PageContent;
//import com.qpp.cgp.domain.common.Language;
//import com.qpp.cgp.domain.dto.product.SkuAttributeValueDTO;
//import com.qpp.cgp.domain.log.TimingLogThreadContext;
//import com.qpp.cgp.domain.log.TimingLogger;
//import com.qpp.cgp.domain.product.ConfigurableProduct;
//import com.qpp.cgp.domain.product.Product;
//import com.qpp.cgp.domain.product.SkuProduct;
//import com.qpp.cgp.domain.product.admin.ProductAdministratorConfig;
//import com.qpp.cgp.domain.product.config.ProductConfigDesign;
//import com.qpp.cgp.domain.simplifyBom.SBNode;
//import com.qpp.cgp.domain.simplifyBom.SimplifyBomConfig;
//import com.qpp.cgp.manager.pcresource.MaterialViewTypeThemGenerator;
//import com.qpp.cgp.manager.product.ProductAdministratorConfigManager;
//import com.qpp.cgp.manager.product.ProductManager;
//import com.qpp.cgp.manager.product.config.view.builderurl.MultilingualValueFinder;
//import com.qpp.cgp.manager.product.search.SkuProductSearchService;
//import com.qpp.cgp.repository.common.LanguageRepository;
//import com.qpp.cgp.repository.user.UserRepository;
//import com.qpp.core.utils.SecurityUtils;
//import com.qpp.mongo.driver.HybridMongoTemplate;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.powermock.api.mockito.PowerMockito;
//import org.powermock.core.classloader.annotations.PowerMockIgnore;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.modules.junit4.PowerMockRunner;
//import org.powermock.reflect.Whitebox;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.data.mongodb.core.query.Criteria;
//import org.springframework.data.mongodb.core.query.Query;
//import org.springframework.test.util.ReflectionTestUtils;
//import org.springframework.ui.ModelExtensionsKt;
//
//import java.lang.reflect.InvocationTargetException;
//import java.lang.reflect.Method;
//import java.time.Instant;
//import java.util.*;
//import java.util.stream.Collectors;
//
//@RunWith(PowerMockRunner.class)
//@PrepareForTest({SimplifyBomConfigManager.class,TimingLogThreadContext.class, UserRepository.class})
//@PowerMockIgnore({"javax.management.*", "org.w3c.dom.*", "org.apache.log4j.*", "org.xml.sax.*", "javax.xml.*", "javax.script.*"})
//public class SimplifyBomConfigManagerTest {
//
//    @Test
//    public void insertLanguageContext(){
//        SimplifyBomConfigManager simplifyBomConfigManager = new SimplifyBomConfigManager(null, null);
//        Map<String, Object> context = new HashMap<>();
//        MultilingualValueFinder multilingualValueFinder = Mockito.mock(MultilingualValueFinder.class);
//        LanguageRepository languageRepository = Mockito.mock(LanguageRepository.class);
//        Whitebox.setInternalState(simplifyBomConfigManager, "multilingualValueFinder", multilingualValueFinder);
//        Whitebox.setInternalState(simplifyBomConfigManager, "languageRepository", languageRepository);
//        Mockito.when(multilingualValueFinder.getLanguageIdFromContext()).thenReturn(9L);
//        simplifyBomConfigManager.insertLanguageContext(context);
//        Assert.assertEquals(context.get("languageId"), 9L);
//        Mockito.verify(multilingualValueFinder).getLanguageIdFromContext();
//    }
//
//    @Test
//    public void insertLanguageContextByNotHeader(){
//        SimplifyBomConfigManager simplifyBomConfigManager = new SimplifyBomConfigManager(null, null);
//        Map<String, Object> context = new HashMap<>();
//        MultilingualValueFinder multilingualValueFinder = Mockito.mock(MultilingualValueFinder.class);
//        LanguageRepository languageRepository = Mockito.mock(LanguageRepository.class);
//        Whitebox.setInternalState(simplifyBomConfigManager, "multilingualValueFinder", multilingualValueFinder);
//        Language language = new Language();
//        language.setId(9L);
//        Whitebox.setInternalState(simplifyBomConfigManager, "languageRepository", languageRepository);
//        Mockito.when(languageRepository.findByName("English")).thenReturn(language);
//        Mockito.when(multilingualValueFinder.getLanguageIdFromContext()).thenReturn(null);
//        simplifyBomConfigManager.insertLanguageContext(context);
//        Assert.assertEquals(context.get("languageId"), 9L);
//        Mockito.verify(multilingualValueFinder, Mockito.times(1)).getLanguageIdFromContext();
//        Mockito.verify(languageRepository).findByName("English");
//    }
//
//    @Test
//    public void presetContent() throws InvocationTargetException, IllegalAccessException {
//        Method method = PowerMockito.method(SimplifyBomConfigManager.class, "presetContent", ProductMaterialViewType.class, String.class, Map.class, List.class);
//        HybridMongoTemplate mongoTemplate = Mockito.mock(HybridMongoTemplate.class);
//        SimplifyBomConfigManager simplifyBomConfigManager = new SimplifyBomConfigManager(mongoTemplate, null);
//        MaterialViewTypeThemGenerator materialViewTypeThemGenerator = Mockito.mock(MaterialViewTypeThemGenerator.class);
//        String builderVersion = "V3";
//        ProductMaterialViewType productMaterialViewType = new ProductMaterialViewType();
//        productMaterialViewType.setId("123");
//        Mockito.when(materialViewTypeThemGenerator.isExistsTheme(productMaterialViewType.getId())).thenReturn(true);
//        List<PageContent> pageContents = new ArrayList<>();
//        PageContent pageContent = new PageContent();
//        pageContents.add(pageContent);
//        Map<String, Object> context = new HashMap<>();
//        List<PageContent> pageContentList = new ArrayList<>();
//        PageContent pageContent1 = new PageContent();
//        pageContentList.add(pageContent1);
//        Mockito.when(mongoTemplate.save(pageContent1)).thenReturn(pageContent1);
//        Mockito.when(materialViewTypeThemGenerator.themeFill(pageContents, productMaterialViewType.getId(), context)).thenReturn(pageContentList);
//        Whitebox.setInternalState(simplifyBomConfigManager, "materialViewTypeThemGenerator", materialViewTypeThemGenerator);
//        Object result = method.invoke(simplifyBomConfigManager, productMaterialViewType, builderVersion, context, pageContents);
//        Mockito.verify(mongoTemplate).save(pageContent1);
//        Mockito.verify(materialViewTypeThemGenerator).isExistsTheme(productMaterialViewType.getId());
//        Mockito.verify(materialViewTypeThemGenerator).themeFill(pageContents, productMaterialViewType.getId(), context);
//        Assert.assertNotNull(result);
//        Assert.assertEquals(pageContentList, result);
//        Mockito.when(materialViewTypeThemGenerator.isExistsTheme("1233")).thenReturn(false);
//        ProductMaterialViewType productMaterialViewType1 = new ProductMaterialViewType();
//        productMaterialViewType1.setId("1233");
//        Object result2 = method.invoke(simplifyBomConfigManager, productMaterialViewType1, builderVersion, context, pageContents);
//        Mockito.verify(materialViewTypeThemGenerator).isExistsTheme("1233");
//        Assert.assertNotNull(result2);
//        Assert.assertEquals(pageContents, result2);
//        Object result3 = method.invoke(simplifyBomConfigManager, productMaterialViewType, null, context, pageContents);
//        Assert.assertNotNull(result3);
//        Assert.assertEquals(pageContents, result3);
//
//        Object result4 = method.invoke(simplifyBomConfigManager, productMaterialViewType, "V2", context, pageContents);
//        Assert.assertNotNull(result4);
//        Assert.assertEquals(pageContents, result4);
//    }
//
//    @Test
//    public void SortChild() throws InvocationTargetException, IllegalAccessException {
//        Method method = PowerMockito.method(SimplifyBomConfigManager.class, "SortChild", List.class, SBNode.class);
//        SimplifyBomConfigManager simplifyBomConfigManager = new SimplifyBomConfigManager(null, null);
//        List<SBNode> sbNodeList = new ArrayList<>();
//        SBNode root = new SBNode();
//        root.setId("123");
//        SBNode nodeFirstLayer = new SBNode();
//        nodeFirstLayer.setId("111");
//        nodeFirstLayer.setParent(root);
//        SBNode node = new SBNode();
//        node.setId("133");
//        SBNode node1 = new SBNode();
//        node1.setParent(root);
//        node1.setId("1234");
//        node.setParent(nodeFirstLayer);
//        sbNodeList.add(nodeFirstLayer);
//        sbNodeList.add(node);
//        sbNodeList.add(node1);
//        sbNodeList.add(root);
//        method.invoke(simplifyBomConfigManager, sbNodeList, root);
//        Assert.assertTrue(root.getChild() != null && !root.getChild().isEmpty());
//        Assert.assertEquals(2, root.getChild().size());
//        Assert.assertTrue(root.getChild().stream().map(SBNode::getId).collect(Collectors.toList()).contains(node1.getId()));
//        Assert.assertTrue(root.getChild().stream().map(SBNode::getId).collect(Collectors.toList()).contains(nodeFirstLayer.getId()));
//        ;
//        for (SBNode child : root.getChild()) {
//            if (child.getId().equals(nodeFirstLayer.getId())) {
//                Assert.assertTrue(child.getChild() != null && !child.getChild().isEmpty());
//                Assert.assertEquals(1, child.getChild().size());
//                Assert.assertEquals(node, child.getChild().get(0));
//            }
//        }
//    }
//
//    @Test
//    public void getNodeList() throws InvocationTargetException, IllegalAccessException {
//        Method method = PowerMockito.method(SimplifyBomConfigManager.class, "getNodeList", SBNode.class, List.class);
//        HybridMongoTemplate mongoTemplate = Mockito.mock(HybridMongoTemplate.class);
//        SimplifyBomConfigManager simplifyBomConfigManager = new SimplifyBomConfigManager(mongoTemplate, null);
//        List<SBNode> sbNodeList = new ArrayList<>();
//        SBNode root = new SBNode();
//        root.setId("123");
//        root.setCompletePath(root.getId());
//        root.setSbomPath(root.getId());
//        SBNode nodeFirstLayer = new SBNode();
//        nodeFirstLayer.setId("111");
//        SBNode node2 = new SBNode();
//        node2.setSbomPath("1555");
//        node2.setId("1114");
//        node2.setParent(nodeFirstLayer);
//        nodeFirstLayer.setSbomPath(nodeFirstLayer.getId());
//        nodeFirstLayer.setParent(root);
//        SBNode node = new SBNode();
//        node.setId("133");
//        node.setSbomPath(node.getId());
//        SBNode node1 = new SBNode();
//        node1.setParent(root);
//        node1.setId("1234");
//        node1.setSbomPath(node1.getId());
//        node.setParent(nodeFirstLayer);
//        Mockito.when(mongoTemplate.find(Query.query(Criteria.where("parent._id").is("123")), SBNode.class)).thenReturn(ImmutableList.of(nodeFirstLayer, node1));
//        Mockito.when(mongoTemplate.find(Query.query(Criteria.where("parent._id").is("111")), SBNode.class)).thenReturn(ImmutableList.of(node, node2));
//        Mockito.when(mongoTemplate.find(Query.query(Criteria.where("parent._id").is("1234")), SBNode.class)).thenReturn(ImmutableList.of());
//        Mockito.when(mongoTemplate.find(Query.query(Criteria.where("parent._id").is("1234")), SBNode.class)).thenReturn(ImmutableList.of());
//        Mockito.when(mongoTemplate.find(Query.query(Criteria.where("parent._id").is("133")), SBNode.class)).thenReturn(ImmutableList.of());
//        method.invoke(simplifyBomConfigManager, root, sbNodeList);
//        Mockito.verify(mongoTemplate).find(Query.query(Criteria.where("parent._id").is("123")), SBNode.class);
//        Mockito.verify(mongoTemplate).find(Query.query(Criteria.where("parent._id").is("111")), SBNode.class);
//        Mockito.verify(mongoTemplate).find(Query.query(Criteria.where("parent._id").is("1234")), SBNode.class);
//        Mockito.verify(mongoTemplate).find(Query.query(Criteria.where("parent._id").is("133")), SBNode.class);
//        Assert.assertEquals(5, sbNodeList.size());
//        Optional<SBNode> result1 = sbNodeList.stream().filter(e -> e.getId().equals(root.getId())).findFirst();
//        Optional<SBNode> result2 = sbNodeList.stream().filter(e -> e.getId().equals(node.getId())).findFirst();
//        Optional<SBNode> result3 = sbNodeList.stream().filter(e -> e.getId().equals(nodeFirstLayer.getId())).findFirst();
//        Optional<SBNode> result4 = sbNodeList.stream().filter(e -> e.getId().equals(node1.getId())).findFirst();
//        Optional<SBNode> result5 = sbNodeList.stream().filter(e -> e.getId().equals(node2.getId())).findFirst();
//        Assert.assertTrue(result1.isPresent());
//        Assert.assertTrue(result2.isPresent());
//        Assert.assertTrue(result3.isPresent());
//        Assert.assertTrue(result4.isPresent());
//        Assert.assertTrue(result5.isPresent());
//        SBNode nodeValue1 = result1.get();
//        SBNode nodeValue2 = result2.get();
//        SBNode nodeValue3 = result3.get();
//        SBNode nodeValue4 = result4.get();
//        SBNode nodeValue5 = result5.get();
//        Assert.assertEquals(root.getId(), nodeValue1.getCompletePath());
//        Assert.assertEquals(root.getId() + "," + nodeFirstLayer.getId(), nodeValue3.getCompletePath());
//        Assert.assertEquals(root.getId() + "," + nodeFirstLayer.getId() + "," + node.getId(), nodeValue2.getCompletePath());
//        Assert.assertEquals(root.getId() + "," + nodeFirstLayer.getId() + "," + node2.getSbomPath(), nodeValue5.getCompletePath());
//        Assert.assertEquals(root.getId() + "," + node1.getId(), nodeValue4.getCompletePath());
//    }
//
//    /**
//     * 传入的productId是可配置产品id
//     */
//    @Test
//    public void getSbNodesConfigurableProductIdTest(){
//        HybridMongoTemplate mongoTemplate = Mockito.mock(HybridMongoTemplate.class);
//        SimplifyBomConfigManager simplifyBomConfigManager = new SimplifyBomConfigManager(mongoTemplate,null);
//        // mock
//        MongoTemplate mongoTemplateConfig = Mockito.mock(MongoTemplate.class);
//        ReflectionTestUtils.setField(simplifyBomConfigManager,"mongoTemplateConfig",mongoTemplateConfig);
//        ProductManager productManager = Mockito.mock(ProductManager.class);
//        ReflectionTestUtils.setField(simplifyBomConfigManager,"productManager",productManager);
//        SkuProductSearchService skuProductSearchService = Mockito.mock(SkuProductSearchService.class);
//        ReflectionTestUtils.setField(simplifyBomConfigManager,"skuProductSearchService",skuProductSearchService);
//
//        // 参数
//        Map<String, Object> attributeValuesMap=new HashMap<>();
//        List<SkuAttributeValueDTO> attributeValues=new ArrayList<>();
//        ProductConfigDesign productConfigDesign=new ProductConfigDesign();
//        SimplifyBomConfig simplifyBomConfig=new SimplifyBomConfig();
//        Long productId=100L;
//        Boolean isTest=true;
//
//
//        // when
//        PowerMockito.mockStatic(TimingLogThreadContext.class);
//        TimingLogger timingLogger = new TimingLogger();
//        timingLogger.setLastTime(Instant.now());
//        Mockito.when(TimingLogThreadContext.get()).thenReturn(timingLogger);
//        ConfigurableProduct product =new ConfigurableProduct();
//        Mockito.when(mongoTemplateConfig.findById(productId, Product.class)).thenReturn(product);
//        Mockito.when(productManager.isPureSkuProductOrConfigProduct(productId)).thenReturn(true);
//        SkuProduct skuProduct = new SkuProduct();
//        Optional< SkuProduct > skuProductOptional =Optional.of(skuProduct);
//        Mockito.when(skuProductSearchService.findByAttributeValueV2(productId, attributeValues, Optional.empty())).thenReturn(skuProductOptional);
//        try {
//            simplifyBomConfigManager.getSbNodes(attributeValuesMap, attributeValues, productConfigDesign,
//                    simplifyBomConfig, productId, Optional.empty(), isTest);
//
//        }catch (Exception e){
//            Assert.assertNotNull(e);
//            Mockito.verify(mongoTemplateConfig,Mockito.times(1)).findById(productId, Product.class);
//            Mockito.verify(productManager,Mockito.times(1)).isPureSkuProductOrConfigProduct(productId);
//            Mockito.verify(skuProductSearchService,Mockito.times(1)).findByAttributeValueV2(productId, attributeValues, Optional.empty());
//        }
//
//    }
//
//
//    /**
//     * 传入的productId是纯SkuProductId
//     */
//    @Test
//    public void getSbNodesSkuProductIdTest(){
//        HybridMongoTemplate mongoTemplate = Mockito.mock(HybridMongoTemplate.class);
//        SimplifyBomConfigManager simplifyBomConfigManager = new SimplifyBomConfigManager(mongoTemplate,null);
//        // mock
//        MongoTemplate mongoTemplateConfig = Mockito.mock(MongoTemplate.class);
//        ReflectionTestUtils.setField(simplifyBomConfigManager,"mongoTemplateConfig",mongoTemplateConfig);
//        ProductManager productManager = Mockito.mock(ProductManager.class);
//        ReflectionTestUtils.setField(simplifyBomConfigManager,"productManager",productManager);
//        SkuProductSearchService skuProductSearchService = Mockito.mock(SkuProductSearchService.class);
//        ReflectionTestUtils.setField(simplifyBomConfigManager,"skuProductSearchService",skuProductSearchService);
//
//        // 参数
//        Map<String, Object> attributeValuesMap=new HashMap<>();
//        List<SkuAttributeValueDTO> attributeValues=new ArrayList<>();
//        ProductConfigDesign productConfigDesign=new ProductConfigDesign();
//        SimplifyBomConfig simplifyBomConfig=new SimplifyBomConfig();
//        Long productId=100L;
//        Boolean isTest=true;
//
//
//        // when
//        PowerMockito.mockStatic(TimingLogThreadContext.class);
//        TimingLogger timingLogger = new TimingLogger();
//        timingLogger.setLastTime(Instant.now());
//        Mockito.when(TimingLogThreadContext.get()).thenReturn(timingLogger);
//        SkuProduct product =new SkuProduct();
//        Mockito.when(mongoTemplateConfig.findById(productId, Product.class)).thenReturn(product);
//        Mockito.when(productManager.isPureSkuProductOrConfigProduct(productId)).thenReturn(true);
//        SkuProduct skuProduct = new SkuProduct();
//        Optional< SkuProduct > skuProductOptional =Optional.of(skuProduct);
//        Mockito.when(skuProductSearchService.findByAttributeValueV2(productId, attributeValues, Optional.empty())).thenReturn(skuProductOptional);
//        try {
//            simplifyBomConfigManager.getSbNodes(attributeValuesMap, attributeValues, productConfigDesign,
//                    simplifyBomConfig, productId, Optional.empty(), isTest);
//
//        }catch (Exception e){
//            Assert.assertNotNull(e);
//            Mockito.verify(mongoTemplateConfig,Mockito.times(1)).findById(productId, Product.class);
//            Mockito.verify(productManager,Mockito.times(1)).isPureSkuProductOrConfigProduct(productId);
//            Mockito.verify(skuProductSearchService,Mockito.times(0)).findByAttributeValueV2(productId, attributeValues, Optional.empty());
//        }
//
//    }
//
//
//}