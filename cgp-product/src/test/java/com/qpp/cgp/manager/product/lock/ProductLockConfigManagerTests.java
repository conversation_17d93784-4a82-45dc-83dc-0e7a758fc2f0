package com.qpp.cgp.manager.product.lock;

import com.qpp.cgp.domain.product.lock.LockHistory;
import com.qpp.cgp.manager.product.ProductAdministratorConfigManager;
import com.qpp.cgp.util.JunitUtils;
import com.qpp.core.exception.BusinessException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/21 11:14
 * @Version 1.0
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({ProductAdministratorConfigManager.class})
@PowerMockIgnore({"javax.management.*", "org.w3c.dom.*", "org.apache.log4j.*", "org.xml.sax.*", "javax.xml.*", "javax.script.*"})
public class ProductLockConfigManagerTests {

    @Test
    public void checkAllowUnlockProductAdministratorConfigIsExistTest() throws InvocationTargetException, IllegalAccessException {
        Method checkAllowUnlock = PowerMockito.method(ProductLockConfigManager.class, "checkAllowUnlock", Long.class, List.class);
        ProductLockConfigManager productLockConfigManager = new ProductLockConfigManager();
        ProductAdministratorConfigManager productAdministratorConfigManagerMock = PowerMockito.mock(ProductAdministratorConfigManager.class);
        ReflectionTestUtils.setField(productLockConfigManager,"productAdministratorConfigManager",productAdministratorConfigManagerMock);
        PowerMockito.when(productAdministratorConfigManagerMock.existProductId(789L)).thenReturn(true);
        List<LockHistory> lockHistories=new ArrayList<>();
        Exception e=null;
        try {
            checkAllowUnlock.invoke(productLockConfigManager,789L,lockHistories);
        }catch (Exception exception){
            e=exception;
        }
        Mockito.verify(productAdministratorConfigManagerMock,Mockito.times(1)).existProductId(789L);
        Assert.assertNull(e);
    }

    @Test
    public void checkAllowUnlockProductAdministratorConfigNotExistTest() throws InvocationTargetException, IllegalAccessException {
        Method checkAllowUnlock = PowerMockito.method(ProductLockConfigManager.class, "checkAllowUnlock", Long.class, List.class);
        ProductLockConfigManager productLockConfigManager = new ProductLockConfigManager();
        ProductAdministratorConfigManager productAdministratorConfigManagerMock = PowerMockito.mock(ProductAdministratorConfigManager.class);
        ReflectionTestUtils.setField(productLockConfigManager,"productAdministratorConfigManager",productAdministratorConfigManagerMock);
        PowerMockito.when(productAdministratorConfigManagerMock.existProductId(780L)).thenReturn(false);
        List<LockHistory> lockHistories=new ArrayList<>();
        checkAllowUnlock.invoke(productLockConfigManager,780L,lockHistories);
        Mockito.verify(productAdministratorConfigManagerMock,Mockito.times(0)).existProductId(789L);
    }

    @Test
    public void checkAllowLockProductAdministratorConfigNotExistTest() throws InvocationTargetException, IllegalAccessException {
        Method checkAllowLock = PowerMockito.method(ProductLockConfigManager.class, "checkAllowLock", Long.class);
        ProductLockConfigManager productLockConfigManager = new ProductLockConfigManager();
        ProductAdministratorConfigManager productAdministratorConfigManagerMock = PowerMockito.mock(ProductAdministratorConfigManager.class);
        ReflectionTestUtils.setField(productLockConfigManager,"productAdministratorConfigManager",productAdministratorConfigManagerMock);
        PowerMockito.when(productAdministratorConfigManagerMock.existProductId(8891L)).thenReturn(false);
        PowerMockito.doNothing().when(productAdministratorConfigManagerMock).checkCurrentUserProductIdAdmin(8897L);

        Exception e=null;
        try {
            checkAllowLock.invoke(productLockConfigManager,8891L);
        }catch (Exception exception){
            e=exception;
        }
        Assert.assertNotNull(e);
        BusinessException businessException = JunitUtils.getBusinessException(e);
        Assert.assertEquals(businessException.getCode(),3000);
    }

    @Test
    public void checkAllowLockProductAdministratorConfigIsExistTest() throws InvocationTargetException, IllegalAccessException {
        Method checkAllowLock = PowerMockito.method(ProductLockConfigManager.class, "checkAllowLock", Long.class);
        ProductLockConfigManager productLockConfigManager = new ProductLockConfigManager();
        ProductAdministratorConfigManager productAdministratorConfigManagerMock = PowerMockito.mock(ProductAdministratorConfigManager.class);
        ReflectionTestUtils.setField(productLockConfigManager,"productAdministratorConfigManager",productAdministratorConfigManagerMock);
        PowerMockito.when(productAdministratorConfigManagerMock.existProductId(8897L)).thenReturn(true);
        PowerMockito.doNothing().when(productAdministratorConfigManagerMock).checkCurrentUserProductIdAdmin(8897L);
        checkAllowLock.invoke(productLockConfigManager,8897L);
        Exception e=null;
        try {
            checkAllowLock.invoke(productLockConfigManager,8897L);
        }catch (Exception exception){
            e=exception;
        }
        Assert.assertNull(e);
    }
}
