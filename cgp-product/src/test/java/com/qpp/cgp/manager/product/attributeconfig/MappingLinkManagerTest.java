//package com.qpp.cgp.manager.product.attributeconfig;
//
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//@RunWith(SpringRunner.class)
//@SpringBootTest
//public class MappingLinkManagerTest {
//
//    @Autowired
//    private MappingLinkManager mappingLinkManager;
//
//    @Test
//    public void test1(){
//        try {
//            mappingLinkManager.check("4671537");
//        } catch (Exception e) {
//            System.out.println("4671537 message:"+e.getMessage());
//            Assert.assertTrue(e.getMessage().contains("结果导致非依赖导致！"));
//        }
//    }
//    @Test
//    public void test2(){
//        try {
//            mappingLinkManager.check("4624264");
//        } catch (Exception e) {
//            System.out.println("4624264 message:"+e.getMessage());
//            Assert.assertTrue(e.getMessage().contains("可能存在断链，断链映射ID集合"));
//        }
//    }
//    @Test
//    public void test3(){
//        try {
//            mappingLinkManager.check("4699332");
//
//        } catch (Exception e) {
//            System.out.println("4699332 message:"+e.getMessage());
//            Assert.assertTrue(e.getMessage().contains("造成了依赖死循环"));
//        }
//    }
//    @Test
//    public void test4(){
//        try {
//            mappingLinkManager.check("4317940");
//        } catch (Exception e) {
//            System.out.println("4317940 message:"+e.getMessage());
//            Assert.assertTrue(e.getMessage().contains("没有找到任何的映射与该链路匹配"));
//        }
//    }
//    @Test
//    public void test5(){
//        try {
//            mappingLinkManager.check("4624741");
//        } catch (Exception e) {
//            System.out.println("4624741 message:"+e.getMessage());
//            Assert.assertTrue(e.getMessage().contains("造成了多次修改相同sku"));
//        }
//    }
//
//}