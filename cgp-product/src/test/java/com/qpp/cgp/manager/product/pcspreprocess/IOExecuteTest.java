//package com.qpp.cgp.manager.product.pcspreprocess;
//
//import com.qpp.cgp.domain.pcspreprocess.convert.SvgSourceOperationConvertor;
//import com.qpp.cgp.domain.pcspreprocess.operatorconfig.SvgSourceOperationConfig;
//import com.qpp.cgp.domain.pcspreprocess.opertion.SvgSourceOperation;
//import com.qpp.cgp.domain.pcspreprocess.parsertype.ParserType;
//import com.qpp.cgp.domain.pcspreprocess.operatorconfig.AttributeParserParameter;
//import com.qpp.cgp.domain.pcspreprocess.source.CgpDynamicSizeSourceConfig;
//import com.qpp.cgp.domain.pcspreprocess.operatorconfig.ParserParameter;
//import com.qpp.cgp.manager.execute.IOperationExecutor;
//import com.qpp.cgp.value.ConstantValue;
//import com.qpp.cgp.value.ValueEx;
//import com.qpp.cgp.value.ValueType;
//import com.qpp.core.context.SpringApplicationContext;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//@RunWith(SpringRunner.class)
//@SpringBootTest
//public class IOExecuteTest {
//
////    public void placeHolderOperationTest() {
////        RtObjectSourceOperation rtObjectSourceOperation = new RtObjectSourceOperation();
////        RtObject rtObject = new RtObject();
////        rtObject.setId("10879661");
////        PlaceholderOperation placeholderOperation = new PlaceholderOperation();
////        placeholderOperation.setOperationType(PlaceholderOperationType.Replace);
////        JsonSelectorOperation jsonSelectorOperation = new JsonSelectorOperation();
////        placeholderOperation.setValueOperation(jsonSelectorOperation);
////        rtObjectSourceOperation.setRtObject(rtObject);
////        jsonSelectorOperation.setTemplateOperation(rtObjectSourceOperation);
////
////        placeholderOperation.setTemplateOperation(rtObjectSourceOperation);
////    }
//
//    @Autowired
//    private SvgSourceOperationConvertor svgSourceOperationConvert;
//
//
//    @Test
//    public void testDynamicSize() {
//        CgpDynamicSizeSourceConfig cgpDynamicSizeSourceConfig = new CgpDynamicSizeSourceConfig();
////        cgpDynamicSizeSourceConfig.setParserType(ParserType.File);
//        cgpDynamicSizeSourceConfig.setDpi(72);
//        cgpDynamicSizeSourceConfig.setFormat("SVG");
//        List<String> list = new ArrayList<>();
//        list.add("Cut");
//        cgpDynamicSizeSourceConfig.setPalettes(list);
//        cgpDynamicSizeSourceConfig.setIsExclude(true);
//        cgpDynamicSizeSourceConfig.setStandard("PS014028002-Easy_BottomOuter");
//        cgpDynamicSizeSourceConfig.setExcludePalettes(list);
//        SvgSourceOperationConfig svgSourceOperationConfig = new SvgSourceOperationConfig();
//        Map<String, ValueEx> map = new HashMap<>();
//        ConstantValue constantValue = new ConstantValue();
//        constantValue.setClazz(ConstantValue.class.getName());
//        constantValue.setValue("48");
//        constantValue.setType(ValueType.String);
//        ConstantValue constantValue2 = new ConstantValue();
//        constantValue2.setValue("48");
//        constantValue2.setType(ValueType.String);
//        ConstantValue constantValue3 = new ConstantValue();
//        constantValue3.setValue("48");
//        constantValue3.setType(ValueType.String);
//        ConstantValue constantValue4 = new ConstantValue();
//        constantValue4.setValue("120");
//        constantValue4.setType(ValueType.String);
//        svgSourceOperationConfig.setParserType(ParserType.SvgToJson);
//        constantValue2.setClazz(ConstantValue.class.getName());
//        constantValue4.setClazz(ConstantValue.class.getName());
//        constantValue.setClazz(ConstantValue.class.getName());
//        map.put("D", constantValue2);
//        map.put("H", constantValue2);
//        map.put("L", constantValue2);
//        map.put("W", constantValue4);
//        svgSourceOperationConfig.setProjection(map);
//        svgSourceOperationConfig.setKey("SVG");
//        svgSourceOperationConfig.setSource(cgpDynamicSizeSourceConfig);
//        SvgSourceOperation convert = svgSourceOperationConvert.convert(svgSourceOperationConfig);
//        IOperationExecutor iOperationExecutor = SpringApplicationContext.getBean(convert.getClass().getSimpleName() + "Executor");
//        Object execute = iOperationExecutor.execute(convert, new HashMap());
//        System.out.println(execute);
//    }
//
//
//    @Test
//    public void testSVG() {
//        CgpDynamicSizeSourceConfig cgpDynamicSizeSourceConfig = new CgpDynamicSizeSourceConfig();
////        cgpDynamicSizeSourceConfig.setParserType(ParserType.SvgToJson);
//        cgpDynamicSizeSourceConfig.setDpi(72);
//        cgpDynamicSizeSourceConfig.setFormat("SVG");
//        List<String> list = new ArrayList<>();
//        list.add("all");
//        cgpDynamicSizeSourceConfig.setPalettes(list);
//        cgpDynamicSizeSourceConfig.setIsExclude(false);
//        cgpDynamicSizeSourceConfig.setStandard("LWLayflat6_F");
//        cgpDynamicSizeSourceConfig.setExcludePalettes(list);
//        SvgSourceOperationConfig cgpDynamicSizeSourceOperationConfig = new SvgSourceOperationConfig();
//        Map<String, ValueEx> map = new HashMap<>();
//        ConstantValue constantValue = new ConstantValue();
//        constantValue.setValue("50");
//        constantValue.setType(ValueType.String);
//        ConstantValue constantValue2 = new ConstantValue();
//        constantValue2.setValue("50");
//        constantValue2.setType(ValueType.String);
//        ConstantValue constantValue3 = new ConstantValue();
//        constantValue3.setValue("50");
//        constantValue3.setType(ValueType.String);
//        ConstantValue constantValue4 = new ConstantValue();
//        constantValue4.setValue("50");
//        constantValue4.setType(ValueType.String);
//        constantValue.setClazz(ConstantValue.class.getName());
//        constantValue2.setClazz(ConstantValue.class.getName());
//        constantValue.setClazz(ConstantValue.class.getName());
//        constantValue4.setClazz(ConstantValue.class.getName());
//
//        map.put("D", constantValue2);
//        map.put("H", constantValue2);
//        map.put("L", constantValue2);
//        map.put("W", constantValue4);
//        cgpDynamicSizeSourceOperationConfig.setProjection(map);
//        List<ParserParameter> parserParameters = new ArrayList<>();
//        AttributeParserParameter attributeParserParameter = new AttributeParserParameter();
//        attributeParserParameter.setElementSelector("svg");
//        attributeParserParameter.setAttributeName("width");
//        attributeParserParameter.setKey("width");
//        parserParameters.add(attributeParserParameter);
//        cgpDynamicSizeSourceOperationConfig.setParserParameters(parserParameters);
//        cgpDynamicSizeSourceOperationConfig.setParserType(ParserType.SvgToJson);
//        cgpDynamicSizeSourceOperationConfig.setSource(cgpDynamicSizeSourceConfig);
//        cgpDynamicSizeSourceOperationConfig.setKey("SVG");
//        SvgSourceOperation convert = svgSourceOperationConvert.convert(cgpDynamicSizeSourceOperationConfig);
//        IOperationExecutor iOperationExecutor = SpringApplicationContext.getBean(convert.getClass().getSimpleName() + "Executor");
//        Map<String, Object> context = new HashMap<>();
//        Object execute = iOperationExecutor.execute(convert, context);
//        System.out.println(execute);
//
//    }
////
////
////    @Test
////    public void testSVGBBox() {
////        CgpDynamicSizeSourceConfig cgpDynamicSizeSourceConfig = new CgpDynamicSizeSourceConfig();
//////        cgpDynamicSizeSourceConfig.setParserType(ParserType.SvgToJson);
////        cgpDynamicSizeSourceConfig.setDpi(72);
////        cgpDynamicSizeSourceConfig.setFormat("SVG");
////        List<String> list = new ArrayList<>();
////        list.add("Cut");
////        cgpDynamicSizeSourceConfig.setPalettes(list);
////        cgpDynamicSizeSourceConfig.setIsExclude(true);
////        cgpDynamicSizeSourceConfig.setStandard("PS014028002-Easy_BottomOuter");
////        cgpDynamicSizeSourceConfig.setExcludePalettes(list);
////        CgpDynamicSizeSourceOperationConfig cgpDynamicSizeSourceOperationConfig = new CgpDynamicSizeSourceOperationConfig();
////        Map<String, Object> map = new HashMap<>();
////        ConstantValue constantValue = new ConstantValue();
////        constantValue.setValue("48");
////        constantValue.setType(ValueType.String);
////        ConstantValue constantValue2 = new ConstantValue();
////        constantValue2.setValue("48");
////        constantValue2.setType(ValueType.String);
////        ConstantValue constantValue3 = new ConstantValue();
////        constantValue3.setValue("48");
////        constantValue3.setType(ValueType.String);
////        ConstantValue constantValue4 = new ConstantValue();
////        constantValue4.setValue("120");
////        constantValue4.setType(ValueType.String);
////
////
////        map.put("D", constantValue2);
////        map.put("H", constantValue2);
////        map.put("L", constantValue2);
////        map.put("W", constantValue4);
////        cgpDynamicSizeSourceOperationConfig.setVariables(map);
////        List<ParserParameter> parserParameters = new ArrayList<>();
////        BBoxParserParameter attributeParserParameter = new BBoxParserParameter();
////        attributeParserParameter.setElementSelector(".//*[local-name()=\"path\" and contains(@stroke,\"#00FF00\")]");
////        attributeParserParameter.setKey("svgBox");
////        parserParameters.add(attributeParserParameter);
//////        cgpDynamicSizeSourceConfig.setParserParameters(parserParameters);
////        cgpDynamicSizeSourceOperationConfig.setSource(cgpDynamicSizeSourceConfig);
////        cgpDynamicSizeSourceOperationConfig.setKey("SVG");
//////        DynamicSizeSourceOperation convert = cgpDynamicSizeSourceOperationConvert.convert(cgpDynamicSizeSourceOperationConfig);
////
//////        IOperationExecute iOperationExecute = SpringApplicationContext.getBean(convert.getClass().getSimpleName() + "Execute");
//////        Map<String, Object> context = new HashMap<>();
//////        Map<String, Object> execute = (Map<String, Object>) iOperationExecute.execute(convert, context);
//////        Map<String,Object> svgContext = (Map<String, Object>) execute.get("SVG");
//////        SVGRect rect = (SVGRect) svgContext.get("svgBox");
//////        System.out.println(rect.getWidth());
//////        System.out.println(rect.getHeight());
////
////
////    }
//}
