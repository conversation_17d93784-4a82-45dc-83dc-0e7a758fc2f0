package com.qpp.cgp.manager.product.config.view;

import com.qpp.cgp.domain.product.ConfigurableProduct;
import com.qpp.cgp.manager.product.config.ProductConfigSynchronizer;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Optional;

/**
 * <AUTHOR> Chiu
 * @Date 2021/3/1 10:57
 * @Description
 * @Version 1.0
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
public class ProductConfigSynchronizerSyncProductConfigTests {

    @Autowired
    private ProductConfigSynchronizer productConfigSynchronizer;

    @Test
    public void testByNullConfigurableProductOrNullSkuProduct(){
        try {
            productConfigSynchronizer.syncProductConfig(null, null,
                    "123",false, Optional.empty());
        } catch (Exception e) {
            Assert.assertEquals("Parameter：configurableProduct can not be null!", e.getMessage());
        }

        try {
            productConfigSynchronizer.syncProductConfig(new ConfigurableProduct().getId(), null,
                    "123", false, Optional.empty());
        } catch (Exception e) {
            Assert.assertEquals("Parameter：skuProduct can not be null!", e.getMessage());
        }
    }

}
