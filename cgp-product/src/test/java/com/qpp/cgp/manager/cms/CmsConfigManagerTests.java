package com.qpp.cgp.manager.cms;

import com.google.common.collect.ImmutableList;
import com.qpp.cgp.config.mongo.MongoTemplateFactory;
import com.qpp.cgp.domain.cms.*;
import com.qpp.cgp.domain.dto.cms.CMSProductDetailInfoOperatorInfo;
import com.qpp.cgp.domain.dto.product.ProductCategoryDTO;
import com.qpp.cgp.domain.event.CMSProductDetailInfoOperatorEvent;
import com.qpp.cgp.domain.product.ConfigurableProduct;
import com.qpp.cgp.domain.product.Product;
import com.qpp.cgp.domain.product.SkuProduct;
import com.qpp.cgp.domain.product.category.SubProductCategory;
import com.qpp.cgp.domain.upload.UploadFile;
import com.qpp.cgp.manager.product.ProductCategoryManager;
import com.qpp.cgp.manager.product.ProductManager;
import com.qpp.cgp.util.Operator;
import com.qpp.core.dto.FilterDTO;
import com.qpp.core.dto.PageDTO;
import com.qpp.core.exception.BusinessException;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.driver.HybridMongoTemplate;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @Date 2022/5/10 9:12
 * @Version 1.0
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({ProductManager.class, HybridMongoTemplate.class, FilterDTO.class, IdGenerator.class, ProductCategoryManager.class})
@PowerMockIgnore({"javax.management.*", "org.w3c.dom.*", "org.apache.log4j.*", "org.xml.sax.*", "javax.xml.*", "javax.script.*"})
public class CmsConfigManagerTests {
    @Test
    public void getCanPublishProductsTest(){
        HybridMongoTemplate mongoTemplate = PowerMockito.mock(HybridMongoTemplate.class);
        ProductManager productManager = PowerMockito.mock(ProductManager.class);
        CmsConfigManager cmsConfigManager = new CmsConfigManager(mongoTemplate,null);
        ReflectionTestUtils.setField(cmsConfigManager,"productManager",productManager);

        Sort sorts = Sort.by("_id");
        int pageNumber = 0;
        int pageSize = 0;
        pageNumber = Integer.valueOf(2) - 1;
        pageSize = Integer.valueOf(3);
        String filter = "";
        PageRequest pageable = PageRequest.of(pageNumber, pageSize, sorts);

        Query query = Query.query(Criteria.where("clazz").is("com.qpp.cgp.domain.cms.ProductDetailCMSConfig"));
        query.fields().include("product","clazz");
        List<AbstractCMSConfig> abstractCMSConfigs=new ArrayList<>();
        ProductDetailCMSConfig productDetailCMSConfig1 = new ProductDetailCMSConfig();
        ProductDetailCMSConfig productDetailCMSConfig2 = new ProductDetailCMSConfig();
        SkuProduct product1 = new SkuProduct();
        SkuProduct product2 = new SkuProduct();
        product1.setId(1L);
        product2.setId(2L);
        productDetailCMSConfig1.setProduct(product1);
        productDetailCMSConfig2.setProduct(product2);

        PowerMockito.when(mongoTemplate.find(query, AbstractCMSConfig.class)).thenReturn(abstractCMSConfigs);

        PowerMockito.mockStatic(FilterDTO.class);
        List<FilterDTO> filters=new ArrayList<>();
        PowerMockito.when(FilterDTO.getFilters(filter)).thenReturn(filters);
        PageDTO<Product> products=new PageDTO<>();
        PowerMockito.when(productManager.findAllProducts(pageable, filters)).thenReturn(products);

        PageDTO<Product> canPublishProducts = cmsConfigManager.getCanPublishProducts(pageable, filter);

        Assert.assertEquals(products,canPublishProducts);
    }

    @Test
    public void  getCanPublishSubProductCategorysTest(){
        HybridMongoTemplate mongoTemplate = PowerMockito.mock(HybridMongoTemplate.class);
        ProductManager productManager = PowerMockito.mock(ProductManager.class);
        CmsConfigManager cmsConfigManager = new CmsConfigManager(mongoTemplate,null);
        ProductCategoryManager productCategoryManager = PowerMockito.mock(ProductCategoryManager.class);
        ReflectionTestUtils.setField(cmsConfigManager,"productManager",productManager);
        ReflectionTestUtils.setField(cmsConfigManager,"productCategoryManager",productCategoryManager);

        Long id = 1L;
        Long websiteId=2L;
        Boolean isMain=true;
        Boolean needProductInfo=true;
        String filter="";
        Pageable pageable=PowerMockito.mock(Pageable.class);

        Query query=Query.query(Criteria.where("clazz").is("com.qpp.cgp.domain.product.category.SubProductCategory"));
        query.fields().include("category","clazz");
        List<ProductsOfCatalogCMSConfig> productsOfCatalogCMSConfigs=new ArrayList<>();
        ProductsOfCatalogCMSConfig productsOfCatalogCMSConfig1 = new ProductsOfCatalogCMSConfig();
        ProductsOfCatalogCMSConfig productsOfCatalogCMSConfig2 = new ProductsOfCatalogCMSConfig();
        SubProductCategory subProductCategory1 = new SubProductCategory();
        SubProductCategory subProductCategory2 = new SubProductCategory();
        subProductCategory1.setId(555L);
        subProductCategory2.setId(555L);
        productsOfCatalogCMSConfig1.setCategory(subProductCategory1);
        productsOfCatalogCMSConfig2.setCategory(subProductCategory2);
        PowerMockito.when(mongoTemplate.find(query, ProductsOfCatalogCMSConfig.class)).thenReturn(productsOfCatalogCMSConfigs);

        PowerMockito.mockStatic(FilterDTO.class);
        List<FilterDTO> filters=new ArrayList<>();
        PowerMockito.when(FilterDTO.getFilters(filter)).thenReturn(filters);

        PageDTO<ProductCategoryDTO> pageDTO=new PageDTO<>();
        PowerMockito.when(productCategoryManager.getSpecifiedWebsiteProductCategory(id, websiteId
                , isMain, needProductInfo, filters, pageable)).thenReturn(pageDTO);

        PageDTO<ProductCategoryDTO> canPublishSubProductCategorys = cmsConfigManager.getCanPublishSubProductCategorys(id,websiteId,isMain,needProductInfo,filter,pageable,true);
        Assert.assertEquals(pageDTO,canPublishSubProductCategorys);
    }

    @Test
    public void checkRequiredParamsTest(){
        CmsConfigManager cmsConfigManager = new CmsConfigManager(null, null);
        ProductDetailCMSConfig productDetailCMSConfig = new ProductDetailCMSConfig();
        //校验 产品是否传入，并且产品Id是否传入
        Exception exception = null;
        try {
            cmsConfigManager.checkRequiredParams(productDetailCMSConfig);
        } catch (Exception e) {
            exception = e;
        }
        Assert.assertNotNull(exception);
        BusinessException businessException = (BusinessException) exception;
        Assert.assertEquals(30100, businessException.getCode());
        productDetailCMSConfig.setCmsPageId("123");
        Exception exception1 = null;
        try {
            cmsConfigManager.checkRequiredParams(productDetailCMSConfig);
        } catch (Exception e) {
            exception1 = e;
        }
        Assert.assertNotNull(exception1);
        BusinessException businessException1 = (BusinessException) exception1;
        Assert.assertEquals(30100, businessException1.getCode());
        ConfigurableProduct configurableProduct = new ConfigurableProduct();
        productDetailCMSConfig.setProduct(configurableProduct);
        Exception exception22 = null;
        try {
            cmsConfigManager.checkRequiredParams(productDetailCMSConfig);
        } catch (Exception e) {
            exception22 = e;
        }
        Assert.assertNotNull(exception22);
        Assert.assertEquals(30100, ((BusinessException)exception22).getCode());
        configurableProduct.setId(1234L);
        productDetailCMSConfig.setProduct(configurableProduct);
        Exception exception2 = null;
        try {
            cmsConfigManager.checkRequiredParams(productDetailCMSConfig);
        } catch (Exception e) {
            exception2 = e;
        }
        Assert.assertNull(exception2);
        ProductsOfCatalogCMSConfig productsOfCatalogCMSConfig = new ProductsOfCatalogCMSConfig();
        Exception exception3 = null;
        try {
            cmsConfigManager.checkRequiredParams(productsOfCatalogCMSConfig);
        } catch (Exception e) {
            exception3 = e;
        }
        Assert.assertNotNull(exception3);
        BusinessException businessException3 = (BusinessException) exception3;
        Assert.assertEquals(30100, businessException3.getCode());
        productsOfCatalogCMSConfig.setCmsPageId("123");
        SubProductCategory subProductCategory = new SubProductCategory();
        productsOfCatalogCMSConfig.setCategory(subProductCategory);
        Exception exception4 = null;
        try {
            cmsConfigManager.checkRequiredParams(productsOfCatalogCMSConfig);
        } catch (Exception e) {
            exception4 = e;
        }
        Assert.assertNotNull(exception4);
        BusinessException businessException4 = (BusinessException) exception4;
        Assert.assertEquals(30100, businessException4.getCode());
        subProductCategory.setId(12345L);
        productsOfCatalogCMSConfig.setCategory(subProductCategory);
        Exception exception5 = null;
        try {
            cmsConfigManager.checkRequiredParams(productsOfCatalogCMSConfig);
        } catch (Exception e) {
            exception5 = e;
        }
        Assert.assertNull(exception5);
    }

    @Test
    public void publishEventTest(){
        CmsConfigManager cmsConfigManager = new CmsConfigManager(null, null);
        ProductDetailCMSConfig productDetailCMSConfigOld = new ProductDetailCMSConfig();
        productDetailCMSConfigOld.setGlobalPriority(5);
        ProductDetailCMSConfig productDetailCMSConfig = new ProductDetailCMSConfig();
        productDetailCMSConfig.setGlobalPriority(10);
        productDetailCMSConfig.setId("123");
        ApplicationEventPublisher applicationEventPublisher = Mockito.mock(ApplicationEventPublisher.class);
        Whitebox.setInternalState(cmsConfigManager, "applicationEventPublisher", applicationEventPublisher);
        AtomicReference<CMSProductDetailInfoOperatorEvent> cmsProductDetailInfoOperatorEvent = new AtomicReference<>(new CMSProductDetailInfoOperatorEvent(123));
        Mockito.doAnswer(e -> {
            cmsProductDetailInfoOperatorEvent.set(e.getArgument(0));
            return null;
        }).when(applicationEventPublisher).publishEvent(Mockito.any(CMSProductDetailInfoOperatorEvent.class));
        cmsConfigManager.publishEvent(productDetailCMSConfig, productDetailCMSConfigOld, Operator.SAVE);
        Mockito.verify(applicationEventPublisher, Mockito.times(1)).publishEvent(Mockito.any(CMSProductDetailInfoOperatorEvent.class));
        Assert.assertNotNull(cmsProductDetailInfoOperatorEvent.get());
        Assert.assertEquals(productDetailCMSConfig, ((CMSProductDetailInfoOperatorInfo) cmsProductDetailInfoOperatorEvent.get().getSource()).getProductDetailCMSConfigByNew());
        Assert.assertEquals(productDetailCMSConfigOld, ((CMSProductDetailInfoOperatorInfo) cmsProductDetailInfoOperatorEvent.get().getSource()).getProductDetailCMSConfigByOld());
        Assert.assertEquals(Operator.SAVE, ((CMSProductDetailInfoOperatorInfo) cmsProductDetailInfoOperatorEvent.get().getSource()).getOperator());
    }

    @Test
    public void getFileNameByProductIdsTest(){
        CmsConfigManager cmsConfigManager = new CmsConfigManager(null, null);
        MongoTemplateFactory mongoTemplateFactory = Mockito.mock(MongoTemplateFactory.class);
        Whitebox.setInternalState(cmsConfigManager, "mongoTemplateFactory", mongoTemplateFactory);
        MongoTemplate mongoTemplateByProductConfigCmsConfig = Mockito.mock(MongoTemplate.class);
        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
        Mockito.doReturn(mongoTemplate).when(mongoTemplateFactory).getMongoTemplate(ProductOfCatalog.class);
        Mockito.doReturn(mongoTemplateByProductConfigCmsConfig).when(mongoTemplateFactory).getMongoTemplate(ProductDetailCMSConfig.class);
        Set<Long> productIds = new HashSet<>();
        productIds.add(123L);
        ProductDetailCMSConfig productDetailCMSConfig = new ProductDetailCMSConfig();
        ProductImage productImage = new ProductImage();
        UploadFile uploadFile = new UploadFile();
        uploadFile.setName("file1");
        productImage.setMedium(uploadFile);
        productDetailCMSConfig.setProductImages(ImmutableList.of(productImage));
        ProductOfCatalog productOfCatalog = new ProductOfCatalog();
        UploadFile productImage2 = new UploadFile();
        productImage2.setName("file2");
        productOfCatalog.setProductImage(productImage2);
        Mockito.doReturn(ImmutableList.of(productDetailCMSConfig)).when(mongoTemplateByProductConfigCmsConfig).find(Query.query(Criteria.where("product._id").in(productIds)), ProductDetailCMSConfig.class);
        Mockito.doReturn(ImmutableList.of(productOfCatalog)).when(mongoTemplate).find(Query.query(Criteria.where("product._id").in(productIds)), ProductOfCatalog.class);
        Set<String> result = cmsConfigManager.getFileNameByProductIds(productIds);
        Mockito.verify(mongoTemplateFactory, Mockito.times(1)).getMongoTemplate(ProductDetailCMSConfig.class);
        Mockito.verify(mongoTemplateFactory, Mockito.times(1)).getMongoTemplate(ProductOfCatalog.class);
        Mockito.verify(mongoTemplateByProductConfigCmsConfig,Mockito.times(1)).find(Query.query(Criteria.where("product._id").in(productIds)), ProductDetailCMSConfig.class);
        Mockito.verify(mongoTemplate,Mockito.times(1)).find(Query.query(Criteria.where("product._id").in(productIds)), ProductOfCatalog.class);
        Assert.assertEquals(2, result.size());
        Assert.assertTrue(result.contains(uploadFile.getName()));
        Assert.assertTrue(result.contains(productImage2.getName()));


    }
}
