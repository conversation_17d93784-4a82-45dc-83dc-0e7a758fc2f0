//package com.qpp.cgp.manager.product.config.material;
//
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.fasterxml.jackson.databind.type.MapType;
//import com.qpp.cgp.domain.bom.MaterialSpu;
//import com.qpp.cgp.domain.product.config.ProductConfigBom;
//import com.qpp.cgp.repository.product.config.ProductConfigBomRepository;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//
//import java.util.HashMap;
//
///**
// * <AUTHOR> Lee 2019/5/29 20:40
// */
//@RunWith(SpringRunner.class)
//@SpringBootTest
//public class MaterialGeneratorTest {
//
//    @Autowired
//    private MaterialGenerator materialGenerator;
//
//    @Autowired
//    private ObjectMapper objectMapper;
//
//    @Autowired
//    private ProductConfigBomRepository productConfigBomRepository;
//
//
//    @Test
//    public void generateMaterialSpu() throws Exception {
//
//        //language=JSON
//        String attributeValues = "{\n" +
//                "    \"1493230\": 1493232,\n" +
//                "    \"1493271\": 1493272,\n" +
//                "    \"1493310\": 1493311,\n" +
//                "    \"1492940\": 1492941,\n" +
//                "    \"1493328\": [1493329],\n" +
//                "    \"133720\": 100,\n" +
//                "    \"133721\": 100,\n" +
//                "    \"1508911\": 2\n" +
//                "}\n";
//
//
//        final MapType mapType = objectMapper.getTypeFactory().constructMapType(HashMap.class, String.class, Object.class);
//        final HashMap<String,Object>  o       = objectMapper.readValue(attributeValues, mapType);
//
//        Long productConfigBomId = 1681500L;
//
//        final ProductConfigBom productConfigBom = productConfigBomRepository.getOne(productConfigBomId);
//
//        final MaterialSpu materialSpu = materialGenerator.generateMaterialSpu(productConfigBom, o);
//
//        System.out.println(materialSpu);
//
//    }
//}