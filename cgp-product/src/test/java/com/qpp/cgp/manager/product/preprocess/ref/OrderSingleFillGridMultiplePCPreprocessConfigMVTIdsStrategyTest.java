package com.qpp.cgp.manager.product.preprocess.ref;

import com.qpp.cgp.domain.bom.AbstractMaterialViewType;
import com.qpp.cgp.domain.bom.ProductMaterialViewType;
import com.qpp.cgp.domain.preprocess.config.tile.OrderSingleFillGridMultiplePCPreprocessConfig;
import com.qpp.cgp.domain.simplifyBom.SimplifyMaterialViewType;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

public class OrderSingleFillGridMultiplePCPreprocessConfigMVTIdsStrategyTest {

    @Test
    public void generatorMaterialViewTypeIds() {
        OrderSingleFillGridMultiplePCPreprocessConfigMVTIdsStrategy orderSingleFillGridMultiplePCPreprocessConfigMVTIdsStrategy = new OrderSingleFillGridMultiplePCPreprocessConfigMVTIdsStrategy();
        OrderSingleFillGridMultiplePCPreprocessConfig orderSingleFillGridMultiplePCPreprocessConfig = new OrderSingleFillGridMultiplePCPreprocessConfig();
        SimplifyMaterialViewType simplifyMaterialViewType = new SimplifyMaterialViewType();
        simplifyMaterialViewType.setId("111");
        List<AbstractMaterialViewType> left = new ArrayList<>();
        ProductMaterialViewType productMaterialViewType = new ProductMaterialViewType();
        productMaterialViewType.setId("123");
        left.add(productMaterialViewType);
        orderSingleFillGridMultiplePCPreprocessConfig.setRight(simplifyMaterialViewType);
        orderSingleFillGridMultiplePCPreprocessConfig.setLeft(left);
        List<String> ids = orderSingleFillGridMultiplePCPreprocessConfigMVTIdsStrategy.generatorMaterialViewTypeIds(orderSingleFillGridMultiplePCPreprocessConfig);
        Assert.assertFalse(ids.isEmpty());
        Assert.assertTrue(ids.contains(simplifyMaterialViewType.getId()));
        Assert.assertTrue(ids.contains(productMaterialViewType.getId()));
    }
}