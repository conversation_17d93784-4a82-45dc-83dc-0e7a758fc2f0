package com.qpp.cgp.manager.product.config.materialmapping.bomitem;

import com.qpp.cgp.domain.bom.Material;
import com.qpp.cgp.domain.bom.bomitem.FixedBOMItem;
import com.qpp.cgp.domain.product.config.material.mapping2.FixedBOMItemMappingConfig;
import com.qpp.cgp.domain.product.config.material.mapping2.MaterialMappingConfig;
import com.qpp.cgp.manager.bom.BOMItemManager;
import com.qpp.cgp.manager.product.config.materialmapping.support.BOMItemMappingSupport;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;

@RunWith(MockitoJUnitRunner.class)
public class FixedBOMItemMapperTest {
    @Spy
    @InjectMocks
    private FixedBOMItemMapper fixedBOMItemMapper;
    @Mock
    private BOMItemManager bomItemManager;
    @Mock
    private BOMItemMappingSupport bomItemMappingSupport;

    @Test
    public void testMappingQty() {
        FixedBOMItem fixedBOMItem = new FixedBOMItem();
        fixedBOMItem.setQuantity(10);
        Material material = new Material();
        material.setId("3");
        fixedBOMItem.setItemMaterial(material);

        Mockito.when(bomItemManager.isBOMItemCompleted(fixedBOMItem))
                .thenReturn(true);

        Mockito.when(bomItemManager.isQuantityCompleted(fixedBOMItem))
                .thenReturn(false);

        Integer qty = fixedBOMItemMapper.mapQTY(fixedBOMItem, new FixedBOMItemMappingConfig(), "1,2", "3", null);
        Assertions.assertThat(qty).isEqualTo(10);
    }

    @Test
    public void testMappingQTY2() {
        FixedBOMItem fixedBOMItem = new FixedBOMItem();
        fixedBOMItem.setQuantity(10);
        Material material = new Material();
        material.setId("3");
        fixedBOMItem.setItemMaterial(material);

        Mockito.when(bomItemManager.isBOMItemCompleted(fixedBOMItem))
                .thenReturn(false);

        Mockito.when(bomItemManager.isQuantityCompleted(fixedBOMItem))
                .thenReturn(false);

        Mockito.when(bomItemMappingSupport.calcBIQuantity(any(), any(), any(), any()))
                .thenReturn(11);

        Integer qty = fixedBOMItemMapper.mapQTY(fixedBOMItem, new FixedBOMItemMappingConfig(), "1,2", "3", null);
        Assertions.assertThat(qty).isEqualTo(11);
    }



}