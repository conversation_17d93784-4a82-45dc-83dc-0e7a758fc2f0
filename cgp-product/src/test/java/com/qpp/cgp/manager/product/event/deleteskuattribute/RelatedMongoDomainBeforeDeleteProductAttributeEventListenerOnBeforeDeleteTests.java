package com.qpp.cgp.manager.product.event.deleteskuattribute;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @Date 2021/3/1 18:01
 * @Description
 * @Version 1.0
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
public class RelatedMongoDomainBeforeDeleteProductAttributeEventListenerOnBeforeDeleteTests {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Test
    public void testByNullConfigurableProductSkuAttribute(){
        RelatedMongoDomainBeforeDeleteProductAttributeEventListener listener = new PropertyModelBeforeDeleteProductAttributeEventListener(mongoTemplate);
        try {
            listener.onBeforeDelete(null);
        } catch (Exception e) {
            Assert.assertEquals("Parameter：skuAttribute can not be null!", e.getMessage());
        }
    }

}
