package com.qpp.cgp.manager.product.preprocess.service;

import com.qpp.cgp.config.mongo.MongoTemplateFactory;
import com.qpp.cgp.domain.preprocess.config.AbstractPreprocessConfig;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.reflect.Whitebox;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

public class PageContentPreprocessServiceTest {


    @Test
    public void isNeedPreprocessForDesignIdByResultTrue() throws InvocationTargetException, IllegalAccessException {
        //判断通过DesignId是否需要预处理
        Method method = PowerMockito.method(PageContentPreprocessService.class, "isNeedPreprocessForDesignId", long.class);
        //测试BCED原则
        //测试只有一个作用，就是查询预处理，
        MongoTemplateFactory mongoTemplateFactory = Mockito.mock(MongoTemplateFactory.class);
        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
        Mockito.when(mongoTemplateFactory.getMongoTemplate(AbstractPreprocessConfig.class)).thenReturn(mongoTemplate);
        long design = 123L;
        Mockito.when(mongoTemplate.exists(Query.query(Criteria.where("designId").is(String.valueOf(design))), AbstractPreprocessConfig.class)).thenReturn(true);
        PageContentPreprocessService pageContentPreprocessService = new PageContentPreprocessService();
        Whitebox.setInternalState(pageContentPreprocessService, "mongoTemplateFactory", mongoTemplateFactory);
        Object result = method.invoke(pageContentPreprocessService, design);
        Mockito.verify(mongoTemplateFactory, Mockito.times(1)).getMongoTemplate(AbstractPreprocessConfig.class);
        Mockito.verify(mongoTemplate, Mockito.times(1)).exists(Query.query(Criteria.where("designId").is(String.valueOf(design))), AbstractPreprocessConfig.class);
        Assert.assertNotNull(result);
        Assert.assertTrue(result instanceof Boolean);
        Assert.assertTrue((Boolean) result);
    }

    @Test
    public void isNeedPreprocessForDesignIdByResultFalse() throws InvocationTargetException, IllegalAccessException {
        //判断通过DesignId是否需要预处理
        Method method = PowerMockito.method(PageContentPreprocessService.class, "isNeedPreprocessForDesignId", long.class);
        //测试BCED原则
        //测试只有一个作用，就是查询预处理，
        MongoTemplateFactory mongoTemplateFactory = Mockito.mock(MongoTemplateFactory.class);
        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
        Mockito.when(mongoTemplateFactory.getMongoTemplate(AbstractPreprocessConfig.class)).thenReturn(mongoTemplate);
        long design = 123L;
        Mockito.when(mongoTemplate.exists(Query.query(Criteria.where("designId").is(String.valueOf(design))), AbstractPreprocessConfig.class)).thenReturn(false);
        PageContentPreprocessService pageContentPreprocessService = new PageContentPreprocessService();
        Whitebox.setInternalState(pageContentPreprocessService, "mongoTemplateFactory", mongoTemplateFactory);
        Object result = method.invoke(pageContentPreprocessService, design);
        Mockito.verify(mongoTemplateFactory, Mockito.times(1)).getMongoTemplate(AbstractPreprocessConfig.class);
        Mockito.verify(mongoTemplate, Mockito.times(1)).exists(Query.query(Criteria.where("designId").is(String.valueOf(design))), AbstractPreprocessConfig.class);
        Assert.assertNotNull(result);
        Assert.assertTrue(result instanceof Boolean);
        Assert.assertFalse((Boolean) result);
    }
}