//package com.qpp.cgp.manager.product.lock;
//
//import com.qpp.cgp.EmbeddedTestUtils;
//import com.qpp.cgp.domain.product.lock.ProductLockConfig;
//import com.qpp.cgp.util.WithCustomerUserDetails;
//import com.qpp.id.generator.IdGenerator;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.Mockito;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.test.util.ReflectionTestUtils;
//
///**
// * <AUTHOR> Chiu
// * @Date 2021/3/9 16:18
// * @Description
// * @Version 1.0
// */
//@WithCustomerUserDetails("1998508")
//@RunWith(SpringJUnit4ClassRunner.class)
//@SpringBootTest
//public class ProductLockConfigManagerUnlockTests {
//
//    private ProductLockConfigManager productCategoryManager = new ProductLockConfigManager();
//    IdGenerator idGenerator = Mockito.mock(IdGenerator.class);
//
//    @Before
//    public void init() throws Exception {
//        MongoTemplate mongoTemplate = EmbeddedTestUtils.startEmbeddedMongo("test", "productlockconfigs", "classpath:com/qpp/cgp/manager/product/lock/ProductLockConfigManagerUnlockTestsData.txt");
//        ReflectionTestUtils.setField(productCategoryManager, "idGenerator", idGenerator);
//        ReflectionTestUtils.setField(productCategoryManager, "mongoTemplate", mongoTemplate);
//    }
//
//    @Test
//    public void testByProductId(){
//
//        // 408880 当前产品处于加锁状态，加锁记录中只有一条数据，userId为0
//        try {
//            ProductLockConfig lockConfig = productCategoryManager.Unlock(408880L);
//        } catch (Exception e) {
//            Assert.assertEquals("No user has locked the productConfig with ID 408880!", e.getMessage());
//        }
//
//        // 7599480 处于未加锁状态
//        try {
//            ProductLockConfig lockConfig = productCategoryManager.Unlock(7599480L);
//        } catch (Exception e) {
//            Assert.assertEquals("The productConfig with ID 7599480 is not locked!", e.getMessage());
//        }
//
//        // 325761 加锁记录中userID都为0，处于加锁状态
//        try {
//            ProductLockConfig productLockConfig = productCategoryManager.Unlock(325761L);
//        } catch (Exception e) {
//            Assert.assertEquals("No user has locked the productConfig with ID 325761!", e.getMessage());
//        }
//
//        // 133829 处于加锁状态，最后一次记录的userId为0，其中包含有正常用户（userId为140800）的加锁记录
//        try {
//            ProductLockConfig productLockConfig = productCategoryManager.Unlock(133829L);
//        } catch (Exception e) {
//            Assert.assertEquals("Users do not match, need to unlock, please communicate with the user who ID 140800!", e.getMessage());
//        }
//
//        // 7718265 处于加锁状态，最后一次记录为系统加锁，前一次记录为用户解锁（userId为1998508）
//        ProductLockConfig productLockConfig = productCategoryManager.Unlock(7718265L);
//        Assert.assertFalse(productLockConfig.getIsLock());
//
//    }
//}
