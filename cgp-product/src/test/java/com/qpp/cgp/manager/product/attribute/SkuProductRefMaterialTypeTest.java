package com.qpp.cgp.manager.product.attribute;

import com.qpp.cgp.config.mongo.MongoTemplateBeanNames;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class SkuProductRefMaterialTypeTest {
    /**
     * 查找对应的Sku产品关联的是SMT的Sku产品
     */

    @Autowired
    private MongoTemplate mongoTemplate;

    @Qualifier(value = MongoTemplateBeanNames.CONFIG)
    @Autowired
    private MongoTemplate configMongoTemplate;

    @Test
    public void findSkuProductRefMaterialType(){
//        Query query = Query.query(Criteria.where("clazz").is("com.qpp.cgp.domain.product.SkuProduct"));
//        query.fields().include("_id");
//        List<SkuProduct> skuProducts = configMongoTemplate.find(query, SkuProduct.class);
//        List<Long> skuIds = skuProducts.stream().map(SkuProduct::getId).collect(Collectors.toList());
//        List<ProductConfig> productConfigs = configMongoTemplate.find(Query.query(Criteria.where("productId").in(skuIds)), ProductConfig.class);
//        List<Long> productConfigIds = productConfigs.stream().map(ProductConfig::getId).collect(Collectors.toList());
//        //查询对应的Bom配置
//        List<ProductConfigBom> productConfigBoms = configMongoTemplate.find(Query.query(Criteria.where("productConfigId").in(productConfigIds)), ProductConfigBom.class);
//        List<String> materialIds = productConfigBoms.stream().map(ProductConfigBom::getProductMaterialId).collect(Collectors.toList());
//        Query materialQuery = Query.query(Criteria.where("_id").in(materialIds).and("clazz").is("com.qpp.cgp.domain.bom.MaterialType"));
//        materialQuery.fields().include("_id").include("clazz");
//        List<Material> materials = mongoTemplate.find(materialQuery, Material.class);
//        List<String> collect = materials.stream().map(Material::getId).collect(Collectors.toList());
//        String sql = "[";
//        int i = 0;
//        for (String id : collect) {
//            if (i != 0 ) {
//                sql = sql + "," + "\"" + id + "\"";
//            } else if (i == 0) {
//                sql = sql + "\"" + id + "\"";
//            }
//            i++;
//        }
//        sql = sql + "]";
//        System.out.println(sql);
    }
}
