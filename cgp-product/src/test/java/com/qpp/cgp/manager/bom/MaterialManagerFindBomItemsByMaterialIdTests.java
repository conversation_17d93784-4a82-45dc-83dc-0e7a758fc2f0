//package com.qpp.cgp.manager.bom;
//
//import com.qpp.cgp.CgpProductTestApplication;
//import com.qpp.cgp.EmbeddedTestUtils;
//import com.qpp.cgp.domain.bom.Material;
//import com.qpp.cgp.domain.bom.bomitem.BOMItem;
//import com.qpp.cgp.domain.dto.bom.BOMItemDTO;
//import com.qpp.id.generator.IdGenerator;
//import com.qpp.mongo.driver.HybridMongoTemplate;
//import org.junit.After;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.Mockito;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @Date 2021/3/8 16:20
// * @Description
// * @Version 1.0
// */
//@RunWith(SpringRunner.class)
//@SpringBootTest
//public class MaterialManagerFindBomItemsByMaterialIdTests {
//
//    MaterialManager materialManager;
//    MongoTemplate mongoTemplate;
//
//    @Before
//    public void init() throws Exception {
//         mongoTemplate = EmbeddedTestUtils.startEmbeddedMongo("test", "materials", "classpath:com/qpp/cgp/manager/bom/MaterialManagerFindBomItemByMaterialIdTestData.txt");
//        IdGenerator idGenerator = Mockito.mock(IdGenerator.class);
//        materialManager = new MaterialManager((HybridMongoTemplate) mongoTemplate, idGenerator);
//    }
//
//    @Test
//    public void testByRealMaterialId(){
//        Assert.assertEquals(mongoTemplate.getDb().getName(), "test");
//        String materialId = "133861";
//        List<BOMItemDTO> bomItemsByMaterialId = materialManager.findBomItemsByMaterialId(materialId);
//        Material material = mongoTemplate.findById(materialId, Material.class);
//        List<BOMItem> childItems = material.getChildItems();
//        Assert.assertEquals(childItems.size(), bomItemsByMaterialId.size());
//    }
//
//    @After
//    public void destroyMongo(){
//        EmbeddedTestUtils.destroyMongo();
//    }
//
//}
