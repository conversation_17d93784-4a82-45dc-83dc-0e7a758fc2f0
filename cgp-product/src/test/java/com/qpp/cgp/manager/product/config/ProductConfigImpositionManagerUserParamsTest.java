package com.qpp.cgp.manager.product.config;

import com.qpp.cgp.domain.bom.attribute.RtType;
import com.qpp.cgp.domain.bom.runtime.RtObject;
import com.qpp.cgp.domain.product.config.ProductConfigImposition;
import com.qpp.cgp.dto.product.config.UserParamsDTO;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;


import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;

/**
 * <AUTHOR>
 * @date 2021/4/23
 */
@RunWith(MockitoJUnitRunner.class)
public class ProductConfigImpositionManagerUserParamsTest {

    @Spy
    @InjectMocks
    private ProductConfigImpositionManager productConfigImpositionManager;

    @Test
    public void testUpdateUserParams() {
        ProductConfigImposition productConfigImposition = new ProductConfigImposition();
        Mockito.doReturn(productConfigImposition).when(productConfigImpositionManager).findById(anyLong());
        Mockito.doAnswer(invocation ->  invocation.getArgument(0))
                .when(productConfigImpositionManager).saveUpdate(any(), any());

        RtType userParams = new RtType();
        userParams.setId("1");
        userParams.setClazz(RtType.class.getName());

        RtObject userParamDefaultValues = new RtObject();
        userParamDefaultValues.setId("1");
        userParamDefaultValues.setClazz(RtObject.class.getName());

        UserParamsDTO userParamsDTO = new UserParamsDTO();
        userParamsDTO.setUserParams(userParams);
        userParamsDTO.setUserParamDefaultValues(userParamDefaultValues);

        ProductConfigImposition result = productConfigImpositionManager.updateUserParams(1L, userParamsDTO);

        RtType resultUserParams = result.getUserParams();
        Assertions.assertThat(resultUserParams).isEqualTo(userParams);
        Assertions.assertThat(resultUserParams.getId()).isNotBlank();
        Assertions.assertThat(resultUserParams.getClazz()).isNotBlank();

        RtObject resultUserParamDefaultValues = result.getUserParamDefaultValues();
        Assertions.assertThat(resultUserParamDefaultValues).isEqualTo(userParamDefaultValues);
        Assertions.assertThat(resultUserParamDefaultValues.getId()).isNotBlank();
        Assertions.assertThat(resultUserParamDefaultValues.getClazz()).isNotBlank();
    }
}
