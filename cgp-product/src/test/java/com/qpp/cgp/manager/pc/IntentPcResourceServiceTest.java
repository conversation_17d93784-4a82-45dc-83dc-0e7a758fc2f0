package com.qpp.cgp.manager.pc;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jayway.jsonpath.JsonPath;
import com.qpp.cgp.domain.bom.runtime.PageContent;
import com.qpp.cgp.manager.pcresource.PCResourceService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({IntentPcResourceService.class})
@PowerMockIgnore({"javax.management.*", "org.w3c.dom.*", "org.apache.log4j.*", "org.xml.sax.*", "javax.xml.*", "javax.script.*"})
public class IntentPcResourceServiceTest {

    @InjectMocks
    private IntentPcResourceService intentPcResourceService;

    @Mock
    private PCResourceService pcResourceService;

    @Mock
    private IntentPcResourceActionStrategyFactory intentPcResourceActionStrategyFactory;

    @Test
    public void testApplyConvertedTargetJsonToPcGivenNull() throws Exception {

        // input
        String targetSelector = null;
        PageContent pageContent = null;
        Object targetJson = null;
        // mock
        intentPcResourceService = PowerMockito.spy(intentPcResourceService);

        // invoke
        String methodName = "applyConvertedTargetJsonToPc";
        Object retVal = ReflectionTestUtils.invokeMethod(intentPcResourceService, methodName, targetSelector, pageContent, targetJson);

        // verify
        PowerMockito.verifyPrivate(intentPcResourceService).invoke(methodName, targetSelector, pageContent, targetJson);
        assert retVal == pageContent;
    }

    @Test
    public void testApplyConvertedTargetJsonToPcGivenNotNull() throws Exception {

        // input
        String targetSelector = "$.layers[0]";
        PageContent pageContent = new PageContent();
        List<Map<String, Object>> layers = new ArrayList<>();
        Map<String, Object> layer = new HashMap<>();
        layer.put("clazz", "test");
        layers.add(layer);
        pageContent.setLayers(layers);

        PageContent pageContent1 = new PageContent();
        List<Map<String, Object>> layers1 = new ArrayList<>();
        Map<String, Object> layer1 = new HashMap<>();
        layer1.put("clazz", "test1");
        layers1.add(layer1);
        pageContent1.setLayers(layers1);
        ObjectMapper objectMapper = new ObjectMapper();
        Object targetJson = JsonPath.read(objectMapper.writeValueAsString(pageContent1), targetSelector);
        // mock
        intentPcResourceService = PowerMockito.spy(intentPcResourceService);
        ReflectionTestUtils.setField(intentPcResourceService, "objectMapper", objectMapper);

        // invoke
        String methodName = "applyConvertedTargetJsonToPc";
        Object retVal = ReflectionTestUtils.invokeMethod(intentPcResourceService, methodName, targetSelector, pageContent, targetJson);

        // verify
        PowerMockito.verifyPrivate(intentPcResourceService).invoke(methodName, targetSelector, pageContent, targetJson);
    }
}