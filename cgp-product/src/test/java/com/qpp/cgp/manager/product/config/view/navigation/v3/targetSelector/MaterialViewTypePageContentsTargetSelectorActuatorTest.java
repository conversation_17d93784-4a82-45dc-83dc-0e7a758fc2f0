package com.qpp.cgp.manager.product.config.view.navigation.v3.targetSelector;

import com.google.common.collect.Sets;
import com.jayway.jsonpath.DocumentContext;
import com.qpp.cgp.domain.bom.runtime.AbstractSource;
import com.qpp.cgp.domain.bom.runtime.PageContent;
import com.qpp.cgp.domain.product.config.view.navigation.config.v3.*;
import com.qpp.cgp.domain.simplifyBom.PCCollectionSource;
import com.qpp.cgp.domain.simplifyBom.SBNodeRuntime;
import com.qpp.cgp.domain.simplifyBom.SimplifyMaterialView;
import com.qpp.cgp.manager.product.config.view.navigation.NavigationConfigManager;
import com.qpp.cgp.manager.product.config.view.navigation.v3.targetSelector.filterSelector.SelectorFilterCalculateService;
import org.apache.commons.compress.utils.Lists;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.powermock.api.mockito.PowerMockito.mock;
import static org.powermock.api.mockito.PowerMockito.when;

@PowerMockIgnore({"javax.management.*", "org.w3c.dom.*", "org.apache.log4j.*", "org.xml.sax.*", "javax.xml.*", "javax.script.*"})
@RunWith(PowerMockRunner.class)
@PrepareForTest(NavigationConfigManager.class)
public class MaterialViewTypePageContentsTargetSelectorActuatorTest {
    @InjectMocks
    private MaterialViewTypePageContentsTargetSelectorActuator actuator= new MaterialViewTypePageContentsTargetSelectorActuator();

    @Mock
    private SelectorFilterCalculateService selectorFilterCalculateService;
    @Test
    public void testSelectInstance() {
        // Arrange
        MaterialViewTypePageContentsTargetSelector targetSelector = new MaterialViewTypePageContentsTargetSelector();
        targetSelector.setMaterialViewTypeCode("id");
        SBNodeRuntime node = mock(SBNodeRuntime.class);
        Map<String, AbstractSource> idAndAbstractSource = new HashMap<>();
        Map<String, Object> context = new HashMap<>();
        DocumentContext documentContext = mock(DocumentContext.class);
        AbstractNavItem abstractNavItem = new DynamicNavItemConfig();

        SimplifyMaterialView simplifyMaterialView = new SimplifyMaterialView();

        PageContent pageContent1 = new PageContent();
        pageContent1.setIndex("1");
        PageContent pageContent2 = new PageContent();
        pageContent2.setIndex("2");
        PageContent pageContent3 = new PageContent();
        pageContent3.setIndex("3");
        ArrayList<PageContent> pageContents = new ArrayList<>();
        pageContents.add(pageContent1);
        pageContents.add(pageContent2);
        pageContents.add(pageContent3);
        simplifyMaterialView.setPageContents(pageContents);

        idAndAbstractSource.put("id", simplifyMaterialView);



        PCIndexRangeSelectorFilter pcIndexRangeSelectorFilter = new PCIndexRangeSelectorFilter();
        targetSelector.setFilter(pcIndexRangeSelectorFilter);

        when(selectorFilterCalculateService.calculate(any(), any(), any(), anyInt(), anyInt())).thenReturn(Sets.newHashSet(1, 2, 3)); // Modify according to your needs
        // Use reflection to access the private method
        PCCollectionSource select = (PCCollectionSource) actuator.select(targetSelector, node, idAndAbstractSource, context, documentContext, abstractNavItem);

        Assertions.assertThat(select.getPcList().size()).isEqualTo(3);
        Assertions.assertThat(select.getPcList().stream()
                        .map(PageContent::getIndex)
                        .collect(Collectors.toSet()))
                .contains("1", "2", "3");
    }
}