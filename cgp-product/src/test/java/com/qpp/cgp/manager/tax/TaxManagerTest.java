package com.qpp.cgp.manager.tax;

import com.google.common.collect.ImmutableList;
import com.qpp.cgp.domain.common.Country;
import com.qpp.cgp.domain.tax.Area;
import com.qpp.cgp.domain.tax.Tax;
import com.qpp.mongo.driver.HybridMongoTemplate;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

public class TaxManagerTest {

    @Test
    public void getCountryIdByTax(){
        TaxManager taxManager = new TaxManager(null, null);
        Tax tax = new Tax();
        Area area = new Area();
        Country country = new Country();
        country.setId(11L);
        area.setCountry(country);
        tax.setArea(area);
        Long countryId = taxManager.getCountryIdByTax(tax);
        Assert.assertEquals(11L, countryId.longValue());
    }

    @Test
    public void checkExistsSameName(){
        HybridMongoTemplate mongoTemplate = Mockito.mock(HybridMongoTemplate.class);
        TaxManager taxManager = new TaxManager(mongoTemplate, null);
        String taxId = "11";
        String taxName = "关税";
        Exception exception2 = null;
        try {
            Query query = Query.query(Criteria.where("name").is(taxName).and("area.country._id").is(9L));
            Tax tax = new Tax();
            tax.setId("12");
            Mockito.when(mongoTemplate.find(query, Tax.class)).thenReturn(ImmutableList.of(tax));
            taxManager.checkExistsSameName(taxId, taxName,9L);
            Mockito.verify(mongoTemplate).find(query, Tax.class);
        } catch (Exception e) {
            exception2 = e;
        }
        Assert.assertNotNull(exception2);
        Exception exception = null;
        try {
            Query query = Query.query(Criteria.where("name").is("科技关税").and("area.country._id").is(9L));
            Tax tax = new Tax();
            tax.setId("12");
            Mockito.when(mongoTemplate.find(query, Tax.class)).thenReturn(ImmutableList.of(tax));
            taxManager.checkExistsSameName(null, "科技关税", 9L);
            Mockito.verify(mongoTemplate).find(query, Tax.class);
        } catch (Exception e) {
            exception = e;
        }
        Assert.assertNotNull(exception);
        Exception exception3 = null;
        try {
            Query query = Query.query(Criteria.where("name").is("非科技关税").and("area.country._id").is(9L));
            Tax tax = new Tax();
            tax.setId("12");
            Mockito.when(mongoTemplate.find(query, Tax.class)).thenReturn(ImmutableList.of());
            taxManager.checkExistsSameName(null, "非科技关税", 9L);
            Mockito.verify(mongoTemplate).find(query, Tax.class);
        } catch (Exception e) {
            exception3 = e;
        }
        Assert.assertNull(exception3);
    }
}
