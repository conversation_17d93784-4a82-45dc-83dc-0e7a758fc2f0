package com.qpp.cgp.manager.product.day;

import com.google.common.collect.Sets;
import com.qpp.cgp.domain.product.SkuProduct;
import com.qpp.cgp.domain.product.day.ProductionDayConfig;
import com.qpp.cgp.domain.product.day.SingleQtyTableProductionDaySetting;
import com.qpp.cgp.value.ExpressionValueEx;
import com.qpp.commons.json.JsonUtils;
import com.qpp.core.exception.BusinessException;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.driver.HybridMongoTemplate;
import org.junit.Before;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.testng.collections.Lists;


import java.util.ArrayList;
import java.util.HashSet;

import static com.qpp.cgp.enumeration.product.day.ProductionDayConfigManagerException.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * @className: ProductionDayConfigManagerTest
 * @description:
 * @author: TT-Berg
 * @date: 2022/10/13
 **/
@PowerMockIgnore({"javax.management.*", "org.w3c.dom.*", "org.apache.log4j.*", "org.xml.sax.*", "javax.xml.*", "javax.script.*"})
@RunWith(MockitoJUnitRunner.class)
@PrepareForTest(ProductionDayConfigManager.class)
public class ProductionDayConfigManagerTest {

    private ProductionDayConfigManager productionDayConfigManager;

    int state = 1;

    @Before
    public void setUp() {
        if (state == 0) {
            productionDayConfigManager = Mockito.mock(ProductionDayConfigManager.class);
        } else {
            HybridMongoTemplate mongoTemplate = Mockito.mock(HybridMongoTemplate.class);
            IdGenerator idGenerator = Mockito.mock(IdGenerator.class);
            productionDayConfigManager = Mockito.spy(new ProductionDayConfigManager(mongoTemplate, idGenerator));
        }
    }

    //region 值检查

    /**
     * 值检查失败测试1-产品为空
     */
    @Test
    public void testCheckFail1() {
        ProductionDayConfig config = new ProductionDayConfig();
        BusinessException exception = null;
        try {
            productionDayConfigManager.saveNew(config);
        } catch (BusinessException e) {
            exception = e;
        }
        assertNotNull(exception);
        assertTrue(exception.getCode() == PRODUCT_NOT_NULL.getCode());
    }

    /**
     * 值检查失败测试2-生产配置为空
     */
    @Test
    public void testCheckFail2() {
        ProductionDayConfig config = new ProductionDayConfig();
        SkuProduct product = new SkuProduct();
        config.setProduct(product);
        BusinessException exception = null;
        try {
            productionDayConfigManager.saveNew(config);
        } catch (BusinessException e) {
            exception = e;
        }
        assertNotNull(exception);
        assertTrue(exception.getCode() == SETTING_NOT_NULL.getCode());
    }

    /**
     * 值检查失败测试3-产品id为空
     */
    @Test
    public void testCheckFail3() {
        ProductionDayConfig config = new ProductionDayConfig();
        SkuProduct product = new SkuProduct();
        config.setProduct(product);
        SingleQtyTableProductionDaySetting setting = new SingleQtyTableProductionDaySetting();
        config.setSetting(setting);
        BusinessException exception = null;
        try {
            productionDayConfigManager.saveNew(config);
        } catch (BusinessException e) {
            exception = e;
        }
        assertNotNull(exception);
        assertTrue(exception.getCode() == PRODUCT_ID_NOT_NULL.getCode());
    }

    /**
     * 值检查失败测试4-产品不存在
     */
    @Test
    public void testCheckFail4() {
        ProductionDayConfig config = new ProductionDayConfig();
        SkuProduct product = new SkuProduct();
        product.setId(10l);
        config.setProduct(product);
        SingleQtyTableProductionDaySetting setting = new SingleQtyTableProductionDaySetting();
        config.setSetting(setting);
        Mockito.doReturn(false).when(productionDayConfigManager).findAvailableProductById(Mockito.anyLong());
        BusinessException exception = null;
        try {
            productionDayConfigManager.saveNew(config);
        } catch (BusinessException e) {
            exception = e;
        }
        assertNotNull(exception);
        assertTrue(exception.getCode() == PRODUCT_NOT_EXIST.getCode());
    }

    /**
     * 值检查成功
     */
    @Test
    public void testCheckTrue() {
        ProductionDayConfig config = new ProductionDayConfig();
        SkuProduct product = new SkuProduct();
        product.setId(10l);
        config.setProduct(product);
        SingleQtyTableProductionDaySetting setting = new SingleQtyTableProductionDaySetting();
        config.setSetting(setting);
        Mockito.doReturn(true).when(productionDayConfigManager).findAvailableProductById(Mockito.anyLong());
        Mockito.doNothing().when(productionDayConfigManager).checkSetting(Mockito.any());
        BusinessException exception = null;
        try {
            productionDayConfigManager.saveNew(config);
        } catch (BusinessException e) {
            exception = e;
        }
        assertNull(exception);
    }

    //endregion

    //region 新增

    /**
     * 添加成功测试1-当产品没有对应的天数计算配置时，直接添加
     */
    @Test
    public void testSaveTrue1() {
        ProductionDayConfig config = new ProductionDayConfig();
        SkuProduct product = new SkuProduct();
        product.setId(10l);
        config.setProduct(product);
        SingleQtyTableProductionDaySetting setting = new SingleQtyTableProductionDaySetting();
        config.setSetting(setting);
        Mockito.doReturn(true).when(productionDayConfigManager).findAvailableProductById(Mockito.anyLong());
        Mockito.doNothing().when(productionDayConfigManager).checkSetting(Mockito.any());
        Mockito.doReturn(null).when(productionDayConfigManager).findProductionDayConfigsByProductId(Mockito.anyLong());
        BusinessException exception = null;
        try {
            productionDayConfigManager.saveNew(config);
        } catch (BusinessException e) {
            exception = e;
        }
        assertNull(exception);
    }

    /**
     * 添加失败测试1-当产品有对应的天数计算配置时，当本身是默认配置，且文档中存在默认配置
     */
    @Test
    public void testSaveFalse1() {
        ProductionDayConfig config = new ProductionDayConfig();
        SkuProduct product = new SkuProduct();
        product.setId(10l);
        config.setProduct(product);
        SingleQtyTableProductionDaySetting setting = new SingleQtyTableProductionDaySetting();
        config.setSetting(setting);
        Mockito.doReturn(true).when(productionDayConfigManager).findAvailableProductById(Mockito.anyLong());
        Mockito.doNothing().when(productionDayConfigManager).checkSetting(Mockito.any());
        Mockito.doReturn(Lists.newArrayList(config)).when(productionDayConfigManager).findProductionDayConfigsByProductId(Mockito.anyLong());
        Mockito.doReturn(Lists.newArrayList(config)).when(productionDayConfigManager).trueConditionProductionDayConfigs(Mockito.anyList());
        BusinessException exception = null;
        try {
            productionDayConfigManager.saveNew(config);
        } catch (BusinessException e) {
            exception = e;
        }
        assertNotNull(exception);
        assertTrue(exception.getCode() == PRODUCT_HAS_DEFAULT_TRUE_CONFIG.getCode());
    }

    /**
     * 添加成功测试3-当产品有对应的天数计算配置时，当本身是默认配置，且文档中不存在默认配置
     */
    @Test
    public void testSaveTrue3() {
        ProductionDayConfig config = new ProductionDayConfig();
        SkuProduct product = new SkuProduct();
        product.setId(10l);
        config.setProduct(product);
        SingleQtyTableProductionDaySetting setting = new SingleQtyTableProductionDaySetting();
        config.setSetting(setting);
        Mockito.doReturn(true).when(productionDayConfigManager).findAvailableProductById(Mockito.anyLong());
        Mockito.doNothing().when(productionDayConfigManager).checkSetting(Mockito.any());
        Mockito.doReturn(Lists.newArrayList(config)).when(productionDayConfigManager).findProductionDayConfigsByProductId(Mockito.anyLong());
        Mockito.doReturn(null).when(productionDayConfigManager).trueConditionProductionDayConfigs(Mockito.anyList());
        BusinessException exception = null;
        try {
            productionDayConfigManager.saveNew(config);
        } catch (BusinessException e) {
            exception = e;
        }
        assertNull(exception);
    }

    /**
     * 添加成功测试2-当产品有对应的天数计算配置时，当本身不是默认配置
     */
    @Test
    public void testSaveFalse2() {
        ProductionDayConfig config = new ProductionDayConfig();
        config.setCondition(new ExpressionValueEx());
        SkuProduct product = new SkuProduct();
        product.setId(10l);
        config.setProduct(product);
        SingleQtyTableProductionDaySetting setting = new SingleQtyTableProductionDaySetting();
        config.setSetting(setting);
        Mockito.doReturn(true).when(productionDayConfigManager).findAvailableProductById(Mockito.anyLong());
        Mockito.doNothing().when(productionDayConfigManager).checkSetting(Mockito.any());
        Mockito.doReturn(Lists.newArrayList(config)).when(productionDayConfigManager).findProductionDayConfigsByProductId(Mockito.anyLong());
        BusinessException exception = null;
        try {
            productionDayConfigManager.saveNew(config);
        } catch (BusinessException e) {
            exception = e;
        }
        assertNull(exception);
    }
    //endregion

    //region 修改测试

    /**
     * 修改测试成功1-当该产品没有对应的生产天数计算配置时，直接修改
     */
    @Test
    public void testUpdateTrue1() {
        ProductionDayConfig config = new ProductionDayConfig();
        SkuProduct product = new SkuProduct();
        product.setId(10l);
        config.setProduct(product);
        SingleQtyTableProductionDaySetting setting = new SingleQtyTableProductionDaySetting();
        config.setSetting(setting);
        Mockito.doReturn(true).when(productionDayConfigManager).findAvailableProductById(Mockito.anyLong());
        Mockito.doNothing().when(productionDayConfigManager).checkSetting(Mockito.any());
        BusinessException exception = null;
        try {
            productionDayConfigManager.saveUpdate(config, 1l);
        } catch (BusinessException e) {
            exception = e;
        }
        assertNull(exception);
    }

    /**
     * 修改测试成功2-当该产品有对应的生产天数计算配置时，当本身不是默认配置，直接修改
     */
    @Test
    public void testUpdateTrue2() {
        ProductionDayConfig config = new ProductionDayConfig();
        SkuProduct product = new SkuProduct();
        config.setCondition(new ExpressionValueEx());
        product.setId(10l);
        config.setProduct(product);
        SingleQtyTableProductionDaySetting setting = new SingleQtyTableProductionDaySetting();
        config.setSetting(setting);
        Mockito.doReturn(true).when(productionDayConfigManager).findAvailableProductById(Mockito.anyLong());
        Mockito.doNothing().when(productionDayConfigManager).checkSetting(Mockito.any());
        BusinessException exception = null;
        try {
            productionDayConfigManager.saveUpdate(config, 1l);
        } catch (BusinessException e) {
            exception = e;
        }
        assertNull(exception);
    }

    /**
     * 修改测试成功3-当该产品有对应的生产天数计算配置时，当本身是默认配置，且文档中不存在默认配置
     */
    @Test
    public void testUpdateTrue3() {
        ProductionDayConfig config = new ProductionDayConfig();
        SkuProduct product = new SkuProduct();
        product.setId(10l);
        config.setProduct(product);
        SingleQtyTableProductionDaySetting setting = new SingleQtyTableProductionDaySetting();
        config.setSetting(setting);
        Mockito.doReturn(true).when(productionDayConfigManager).findAvailableProductById(Mockito.anyLong());
        Mockito.doNothing().when(productionDayConfigManager).checkSetting(Mockito.any());
        Mockito.doReturn(Lists.newArrayList(config)).when(productionDayConfigManager).findProductionDayConfigsByProductId(Mockito.anyLong());
        Mockito.doReturn(null).when(productionDayConfigManager).trueConditionProductionDayConfigs(Mockito.anyList());
        BusinessException exception = null;
        try {
            productionDayConfigManager.saveUpdate(config, 1l);
        } catch (BusinessException e) {
            exception = e;
        }
        assertNull(exception);
    }

    /**
     * 修改成功测试4-当该产品有对应的生产天数计算配置时，当本身是默认配置时，且存在默认配置，但修改时修改的是自身
     */
    @Test
    public void testUpdateTrue4() {
        ProductionDayConfig config = new ProductionDayConfig();
        SkuProduct product = new SkuProduct();
        product.setId(10l);
        config.setProduct(product);
        SingleQtyTableProductionDaySetting setting = new SingleQtyTableProductionDaySetting();
        config.setSetting(setting);
        Mockito.doReturn(true).when(productionDayConfigManager).findAvailableProductById(Mockito.anyLong());
        Mockito.doNothing().when(productionDayConfigManager).checkSetting(Mockito.any());
        Mockito.doReturn(Lists.newArrayList(config)).when(productionDayConfigManager).findProductionDayConfigsByProductId(Mockito.anyLong());
        Mockito.doReturn(Lists.newArrayList(config)).when(productionDayConfigManager).trueConditionProductionDayConfigs(Mockito.anyList());
        Mockito.doReturn(true).when(productionDayConfigManager).updateSelf(Mockito.anyLong(), Mockito.any());
        BusinessException exception = null;
        try {
            productionDayConfigManager.saveUpdate(config, 1l);
        } catch (BusinessException e) {
            exception = e;
        }
        assertNull(exception);
    }

    /**
     * 修改失败测试-当该产品有对应的生产天数计算配置时，当本身是默认配置时，且存在默认配置，但修改时修改的不是自身
     */
    @Test
    public void testUpdateFail() {
        ProductionDayConfig config = new ProductionDayConfig();
        SkuProduct product = new SkuProduct();
        product.setId(10l);
        config.setProduct(product);
        SingleQtyTableProductionDaySetting setting = new SingleQtyTableProductionDaySetting();
        config.setSetting(setting);
        Mockito.doReturn(true).when(productionDayConfigManager).findAvailableProductById(Mockito.anyLong());
        Mockito.doNothing().when(productionDayConfigManager).checkSetting(Mockito.any());
        Mockito.doReturn(Lists.newArrayList(config)).when(productionDayConfigManager).findProductionDayConfigsByProductId(Mockito.anyLong());
        Mockito.doReturn(Lists.newArrayList(config)).when(productionDayConfigManager).trueConditionProductionDayConfigs(Mockito.anyList());
        Mockito.doReturn(false).when(productionDayConfigManager).updateSelf(Mockito.anyLong(), Mockito.any());
        BusinessException exception = null;
        try {
            productionDayConfigManager.saveUpdate(config, 1l);
        } catch (BusinessException e) {
            exception = e;
        }
        assertNotNull(exception);
        assertTrue(exception.getCode() == PRODUCT_HAS_DEFAULT_TRUE_CONFIG.getCode());
    }
    //endRegion

    @Test
    public void testFindProductionDayConfigsByProductIdJustForId() {
        ProductionDayConfigManager configManager = null;
        HybridMongoTemplate mongoTemplate = Mockito.mock(HybridMongoTemplate.class);
        Mockito.doReturn(new ArrayList<>(1)).when(mongoTemplate).find(Mockito.any(), Mockito.any());
        IdGenerator idGenerator = Mockito.mock(IdGenerator.class);
        configManager = Mockito.spy(new ProductionDayConfigManager(mongoTemplate, idGenerator));
        configManager.findProductionDayConfigsByProductIdJustForId(new HashSet<>());
        Mockito.verify(mongoTemplate, Mockito.times(1)).find(Mockito.any(), Mockito.any());
    }
}