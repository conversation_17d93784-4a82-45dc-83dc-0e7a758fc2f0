package com.qpp.cgp.manager.pc;

import com.qpp.cgp.config.mongo.MongoTemplateFactory;
import com.qpp.cgp.domain.bom.AbstractMaterialViewType;
import com.qpp.cgp.domain.bom.ProductMaterialViewType;
import com.qpp.cgp.domain.simplifyBom.SimplifyMaterialViewType;
import com.qpp.cgp.expression.Expression;
import com.qpp.cgp.manager.pcresource.PCResourceService;
import com.qpp.cgp.manager.theme.ThemeGeneratorFactory;
import com.qpp.cgp.value.calculator.ValueExCalculateService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;

@RunWith(PowerMockRunner.class)
@PrepareForTest({PcMvtThemeProcessor.class})
@PowerMockIgnore({"javax.management.*", "org.w3c.dom.*", "org.apache.log4j.*", "org.xml.sax.*", "javax.xml.*", "javax.script.*"})
public class PcMvtThemeProcessorTest {

    @InjectMocks
    private PcMvtThemeProcessor pcMvtThemeProcessor;

    @Mock
    private MongoTemplateFactory mongoTemplateFactory;

    @Mock
    private ThemeGeneratorFactory themeGeneratorFactory;

    @Mock
    private ValueExCalculateService valueExCalculateService;

    @Before
    public void setUp() {
        pcMvtThemeProcessor = PowerMockito.spy(pcMvtThemeProcessor);
    }

    @Test
    public void testGetGivenNull() throws Exception {

        // input
        String input = null;

        // mock
        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
        Mockito.when(mongoTemplateFactory.getMongoTemplate(ProductMaterialViewType.class)).thenReturn(mongoTemplate);
        Mockito.when(mongoTemplateFactory.getMongoTemplate(SimplifyMaterialViewType.class)).thenReturn(mongoTemplate);

        // invoke
        Object retVal = ReflectionTestUtils.invokeMethod(pcMvtThemeProcessor, "get", input);

        // verify & assert
        PowerMockito.verifyPrivate(pcMvtThemeProcessor).invoke("get", input);
        Mockito.verify(mongoTemplateFactory).getMongoTemplate(ProductMaterialViewType.class);
        Mockito.verify(mongoTemplateFactory).getMongoTemplate(SimplifyMaterialViewType.class);
        Mockito.verify(mongoTemplate, Mockito.times(2)).findById(Mockito.any(), Mockito.any());

        assert retVal == null;
    }

    @Test
    public void testGetGivenNotNull() throws Exception {

        // input
        String input = "1";

        // mock
        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
        Mockito.when(mongoTemplateFactory.getMongoTemplate(ProductMaterialViewType.class)).thenReturn(mongoTemplate);
        Mockito.when(mongoTemplateFactory.getMongoTemplate(SimplifyMaterialViewType.class)).thenReturn(mongoTemplate);

        // invoke
        Object retVal = ReflectionTestUtils.invokeMethod(pcMvtThemeProcessor, "get", input);

        // verify & assert
        PowerMockito.verifyPrivate(pcMvtThemeProcessor).invoke("get", input);
        Mockito.verify(mongoTemplateFactory).getMongoTemplate(ProductMaterialViewType.class);
        Mockito.verify(mongoTemplateFactory).getMongoTemplate(SimplifyMaterialViewType.class);
        Mockito.verify(mongoTemplate, Mockito.times(2)).findById(Mockito.any(), Mockito.any());

        assert retVal == null;
    }

    @Test
    public void testGetThemeGivenNull() throws Exception {

        // input
        AbstractMaterialViewType amvt = null;
        Map<String, Object> context = null;

        // mock

        // invoke
        Object retVal = ReflectionTestUtils.invokeMethod(pcMvtThemeProcessor, "getTheme", amvt, context);

        // verify & assert
        PowerMockito.verifyPrivate(pcMvtThemeProcessor).invoke("get", amvt, context);
        Mockito.verifyNoMoreInteractions(pcMvtThemeProcessor);
        assert retVal == null;
    }

    @Test
    public void testGetThemeGivenDefaultExpressionNull() throws Exception {

        // input
        ProductMaterialViewType amvt = Mockito.mock(ProductMaterialViewType.class);
        Map<String, Object> context = null;

        // mock
        Mockito.when(amvt.getDefaultThemeExpression()).thenReturn(null);
        Mockito.when(amvt.getDefaultTheme()).thenReturn(null);

        // invoke
        Object retVal = ReflectionTestUtils.invokeMethod(pcMvtThemeProcessor, "getTheme", amvt, context);

        // verify & assert
        PowerMockito.verifyPrivate(pcMvtThemeProcessor).invoke("get", amvt, context);
        Mockito.verify(amvt).getDefaultThemeExpression();
        Mockito.verify(amvt).getDefaultTheme();
        Mockito.verifyNoInteractions(valueExCalculateService);

        assert retVal == null;
    }

    @Test
    public void testGetThemeGivenDefaultExpressionNotNull() throws Exception {

        // input
        ProductMaterialViewType amvt = Mockito.mock(ProductMaterialViewType.class);
        Map<String, Object> context = new HashMap<>();

        // mock
        Mockito.when(amvt.getDefaultThemeExpression()).thenReturn(new Expression());
        Mockito.when(amvt.getDefaultTheme()).thenReturn(null);

        // invoke
        Object retVal = ReflectionTestUtils.invokeMethod(pcMvtThemeProcessor, "getTheme", amvt, context);

        // verify & assert
        PowerMockito.verifyPrivate(pcMvtThemeProcessor).invoke("get", amvt, context);
        Mockito.verify(amvt).getDefaultThemeExpression();
        Mockito.verify(amvt, Mockito.times(0)).getDefaultTheme();
        Mockito.verify(valueExCalculateService).calculate(Mockito.any(), Mockito.any());

        assert retVal == null;
    }
}