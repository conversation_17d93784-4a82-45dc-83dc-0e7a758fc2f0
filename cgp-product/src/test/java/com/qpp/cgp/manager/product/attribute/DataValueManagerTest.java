package com.qpp.cgp.manager.product.attribute;

import com.qpp.cgp.domain.attribute.Attribute;
import com.qpp.cgp.domain.attribute.AttributeOption;
import com.qpp.cgp.domain.attribute.DataDefinition;
import com.qpp.cgp.domain.attribute.DataValue;
import com.qpp.core.exception.BusinessException;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.driver.HybridMongoTemplate;
import org.hibernate.validator.constraints.CreditCardNumber;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.io.DataOutput;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
public class DataValueManagerTest {

    private DataValueManager dataValueManager;

    @Mock
    private HybridMongoTemplate mongoTemplate = Mockito.mock(HybridMongoTemplate.class);

    @Mock
    private IdGenerator idGenerator = Mockito.mock(IdGenerator.class);

    @Before
    public void setUp() {
        Mockito.when(idGenerator.generateId()).thenReturn(0L);
        dataValueManager = new DataValueManager(mongoTemplate, idGenerator);
    }

    @Test
    public void testSaveNewByNullOption() {
        DataDefinition dataDefinition = new DataDefinition();
        dataDefinition.setId(123123L);

        DataValue dataValue = new DataValue();
        dataValue.setOption(null);
        dataValue.setDataDefinition(dataDefinition);
        dataValue.setValue("value");

        Mockito.when(mongoTemplate.exists(
                Query.query(Criteria.where("_id").is(123123L)),
                DataDefinition.class)).thenReturn(true);

        try {
            DataValue newDataValue = dataValueManager.saveNew(dataValue);
            Assertions.assertEquals(dataValue, newDataValue, "新增失败！");
            Mockito.verify(mongoTemplate).exists(
                    Query.query(Criteria.where("_id").is(123123L)),
                    DataDefinition.class);
        } catch (BusinessException e) {
            Assertions.assertEquals(108000021, e.getCode());
        }
    }

    @Test
    public void testSaveNewByNotValidOption() {
        AttributeOption option = new AttributeOption();
        option.setId(111111L);

        DataDefinition dataDefinition = new DataDefinition();
        dataDefinition.setId(123123L);

        DataValue dataValue = new DataValue();
        dataValue.setOption(option);
        dataValue.setDataDefinition(dataDefinition);
        dataValue.setValue("value");

        Mockito.when(mongoTemplate.exists(
                Query.query(Criteria.where("_id").is(123123L)),
                DataDefinition.class)).thenReturn(true);

        try {
            DataValue newDataValue = dataValueManager.saveNew(dataValue);
            Assertions.assertEquals(dataValue, newDataValue, "新增失败！");
            Mockito.verify(mongoTemplate).exists(
                    Query.query(Criteria.where("_id").is(123123L)),
                    DataDefinition.class);
        } catch (BusinessException e) {
            Assertions.assertEquals(108000022, e.getCode());
        }
    }

    @Test
    public void testSaveNewByNullDataDefinition() {
        AttributeOption option = new AttributeOption();
        option.setId(111111L);

        DataValue dataValue = new DataValue();
        dataValue.setOption(option);
        dataValue.setDataDefinition(null);
        dataValue.setValue("value");

        Mockito.when(mongoTemplate.exists(
                Query.query(Criteria.where("_id").is(111111L)),
                AttributeOption.class)).thenReturn(true);

        try {
            DataValue newDataValue = dataValueManager.saveNew(dataValue);
            Assertions.assertEquals(dataValue, newDataValue, "新增失败！");
            Mockito.verify(mongoTemplate).exists(
                    Query.query(Criteria.where("_id").is(111111L)),
                    AttributeOption.class);
        } catch (BusinessException e) {
            Assertions.assertEquals(108000023, e.getCode());
        }
    }

    @Test
    public void testSaveNewByNotValidDataDefinition() {
        AttributeOption option = new AttributeOption();
        option.setId(111111L);

        DataDefinition dataDefinition = new DataDefinition();
        dataDefinition.setId(123123L);

        DataValue dataValue = new DataValue();
        dataValue.setOption(option);
        dataValue.setDataDefinition(dataDefinition);
        dataValue.setValue("value");

        Mockito.when(mongoTemplate.exists(
                Query.query(Criteria.where("_id").is(111111L)),
                AttributeOption.class)).thenReturn(true);

        try {
            DataValue newDataValue = dataValueManager.saveNew(dataValue);
            Assertions.assertEquals(dataValue, newDataValue, "新增失败！");
            Mockito.verify(mongoTemplate).exists(
                    Query.query(Criteria.where("_id").is(111111L)),
                    AttributeOption.class);
        } catch (BusinessException e) {
            Assertions.assertEquals(108000024, e.getCode());
        }
    }

    @Test
    public void testSaveNewByNullValue() {
        Attribute attribute = new Attribute();
        attribute.setId(0L);

        AttributeOption option = new AttributeOption();
        option.setId(111111L);
        option.setAttribute(attribute);

        DataDefinition dataDefinition = new DataDefinition();
        dataDefinition.setId(123123L);
        dataDefinition.setAttribute(attribute);

        DataValue dataValue = new DataValue();
        dataValue.setOption(option);
        dataValue.setDataDefinition(dataDefinition);
        dataValue.setValue(null);

        Mockito.when(mongoTemplate.exists(
                Query.query(Criteria.where("_id").is(111111L)),
                AttributeOption.class)).thenReturn(true);
        Mockito.when(mongoTemplate.exists(
                Query.query(Criteria.where("_id").is(123123L)),
                DataDefinition.class)).thenReturn(true);

        Mockito.when(mongoTemplate.findById(111111L,AttributeOption.class)).thenReturn(option);
        Mockito.when(mongoTemplate.findById(123123L,DataDefinition.class)).thenReturn(dataDefinition);

        try {
            DataValue newDataValue = dataValueManager.saveNew(dataValue);
            Assertions.assertEquals(dataValue, newDataValue, "新增失败！");
            Mockito.verify(mongoTemplate).exists(
                    Query.query(Criteria.where("_id").is(111111L)),
                    AttributeOption.class);
            Mockito.verify(mongoTemplate).exists(
                    Query.query(Criteria.where("_id").is(123123L)),
                    DataDefinition.class);
            Mockito.verify(mongoTemplate).findById(111111L,AttributeOption.class);
            Mockito.verify(mongoTemplate).findById(123123L,DataDefinition.class);
        } catch (BusinessException e) {
            Assertions.assertEquals(108000025, e.getCode());
        }
    }

    @Test
    public void testSaveNewByNotSameAttributeForOptionAndDefinition() {
        Attribute attribute1 = new Attribute();
        attribute1.setId(0L);
        Attribute attribute2 = new Attribute();
        attribute2.setId(1L);

        AttributeOption option = new AttributeOption();
        option.setId(111111L);
        option.setAttribute(attribute1);

        DataDefinition dataDefinition = new DataDefinition();
        dataDefinition.setId(123123L);
        dataDefinition.setAttribute(attribute2);

        DataValue dataValue = new DataValue();
        dataValue.setOption(option);
        dataValue.setDataDefinition(dataDefinition);
        dataValue.setValue(dataValue);

        Mockito.when(mongoTemplate.exists(
                Query.query(Criteria.where("_id").is(111111L)),
                AttributeOption.class)).thenReturn(true);
        Mockito.when(mongoTemplate.exists(
                Query.query(Criteria.where("_id").is(123123L)),
                DataDefinition.class)).thenReturn(true);

        Mockito.when(mongoTemplate.findById(111111L,AttributeOption.class)).thenReturn(option);
        Mockito.when(mongoTemplate.findById(123123L,DataDefinition.class)).thenReturn(dataDefinition);

        try {
            DataValue newDataValue = dataValueManager.saveNew(dataValue);
            Assertions.assertEquals(dataValue, newDataValue, "新增失败！");
            Mockito.verify(mongoTemplate).exists(
                    Query.query(Criteria.where("_id").is(111111L)),
                    AttributeOption.class);
            Mockito.verify(mongoTemplate).exists(
                    Query.query(Criteria.where("_id").is(123123L)),
                    DataDefinition.class);
            Mockito.verify(mongoTemplate).findById(111111L,AttributeOption.class);
            Mockito.verify(mongoTemplate).findById(123123L,DataDefinition.class);
        } catch (BusinessException e) {
            Assertions.assertEquals(108000026, e.getCode());
        }
    }

    @Test
    public void testSaveNewByNormal() {
        Attribute attribute = new Attribute();
        attribute.setId(0L);

        AttributeOption option = new AttributeOption();
        option.setId(111111L);
        option.setAttribute(attribute);

        DataDefinition dataDefinition = new DataDefinition();
        dataDefinition.setId(123123L);
        dataDefinition.setAttribute(attribute);

        DataValue dataValue = new DataValue();
        dataValue.setOption(option);
        dataValue.setDataDefinition(dataDefinition);
        dataValue.setValue("value");

        Mockito.when(mongoTemplate.exists(
                Query.query(Criteria.where("_id").is(111111L)),
                AttributeOption.class)).thenReturn(true);
        Mockito.when(mongoTemplate.exists(
                Query.query(Criteria.where("_id").is(123123L)),
                DataDefinition.class)).thenReturn(true);

        Mockito.when(mongoTemplate.findById(111111L,AttributeOption.class)).thenReturn(option);
        Mockito.when(mongoTemplate.findById(123123L,DataDefinition.class)).thenReturn(dataDefinition);

        DataValue newDataValue = dataValueManager.saveNew(dataValue);
        Assertions.assertEquals(dataValue, newDataValue, "新增失败！");
        Mockito.verify(mongoTemplate).exists(
                Query.query(Criteria.where("_id").is(111111L)),
                AttributeOption.class);
        Mockito.verify(mongoTemplate).exists(
                Query.query(Criteria.where("_id").is(123123L)),
                DataDefinition.class);
        Mockito.verify(mongoTemplate).findById(111111L,AttributeOption.class);
        Mockito.verify(mongoTemplate).findById(123123L,DataDefinition.class);
    }

    @Test
    public void testSaveUpdateByNullOption() {
        DataDefinition dataDefinition = new DataDefinition();
        dataDefinition.setId(123123L);

        DataValue dataValue = new DataValue();
        dataValue.setOption(null);
        dataValue.setDataDefinition(dataDefinition);
        dataValue.setValue("value");

        Mockito.when(mongoTemplate.exists(
                Query.query(Criteria.where("_id").is(123123L)),
                DataDefinition.class)).thenReturn(true);

        try {
            DataValue newDataValue = dataValueManager.saveUpdate(dataValue,0L);
            Assertions.assertEquals(dataValue, newDataValue, "更新失败！");
            Mockito.verify(mongoTemplate).exists(
                    Query.query(Criteria.where("_id").is(123123L)),
                    DataDefinition.class);
        } catch (BusinessException e) {
            Assertions.assertEquals(108000021, e.getCode());
        }
    }

    @Test
    public void testSaveUpdateByNotValidOption() {
        AttributeOption option = new AttributeOption();
        option.setId(111111L);

        DataDefinition dataDefinition = new DataDefinition();
        dataDefinition.setId(123123L);

        DataValue dataValue = new DataValue();
        dataValue.setOption(option);
        dataValue.setDataDefinition(dataDefinition);
        dataValue.setValue("value");

        Mockito.when(mongoTemplate.exists(
                Query.query(Criteria.where("_id").is(123123L)),
                DataDefinition.class)).thenReturn(true);

        try {
            DataValue newDataValue = dataValueManager.saveUpdate(dataValue,0L);
            Assertions.assertEquals(dataValue, newDataValue, "更新失败！");
            Mockito.verify(mongoTemplate).exists(
                    Query.query(Criteria.where("_id").is(123123L)),
                    DataDefinition.class);
        } catch (BusinessException e) {
            Assertions.assertEquals(108000022, e.getCode());
        }
    }

    @Test
    public void testSaveUpdateByNullDataDefinition() {
        AttributeOption option = new AttributeOption();
        option.setId(111111L);

        DataValue dataValue = new DataValue();
        dataValue.setOption(option);
        dataValue.setDataDefinition(null);
        dataValue.setValue("value");

        Mockito.when(mongoTemplate.exists(
                Query.query(Criteria.where("_id").is(111111L)),
                AttributeOption.class)).thenReturn(true);

        try {
            DataValue newDataValue = dataValueManager.saveUpdate(dataValue,0L);
            Assertions.assertEquals(dataValue, newDataValue, "更新失败！");
            Mockito.verify(mongoTemplate).exists(
                    Query.query(Criteria.where("_id").is(111111L)),
                    AttributeOption.class);
        } catch (BusinessException e) {
            Assertions.assertEquals(108000023, e.getCode());
        }
    }

    @Test
    public void testSaveUpdateByNotValidDataDefinition() {
        AttributeOption option = new AttributeOption();
        option.setId(111111L);

        DataDefinition dataDefinition = new DataDefinition();
        dataDefinition.setId(123123L);

        DataValue dataValue = new DataValue();
        dataValue.setOption(option);
        dataValue.setDataDefinition(dataDefinition);
        dataValue.setValue("value");

        Mockito.when(mongoTemplate.exists(
                Query.query(Criteria.where("_id").is(111111L)),
                AttributeOption.class)).thenReturn(true);

        try {
            DataValue newDataValue = dataValueManager.saveUpdate(dataValue,0L);
            Assertions.assertEquals(dataValue, newDataValue, "更新失败！");
            Mockito.verify(mongoTemplate).exists(
                    Query.query(Criteria.where("_id").is(111111L)),
                    AttributeOption.class);
        } catch (BusinessException e) {
            Assertions.assertEquals(108000024, e.getCode());
        }
    }

    @Test
    public void testSaveUpdateByNullValue() {
        Attribute attribute = new Attribute();
        attribute.setId(0L);

        AttributeOption option = new AttributeOption();
        option.setId(111111L);
        option.setAttribute(attribute);

        DataDefinition dataDefinition = new DataDefinition();
        dataDefinition.setId(123123L);
        dataDefinition.setAttribute(attribute);

        DataValue dataValue = new DataValue();
        dataValue.setOption(option);
        dataValue.setDataDefinition(dataDefinition);
        dataValue.setValue(null);

        Mockito.when(mongoTemplate.exists(
                Query.query(Criteria.where("_id").is(111111L)),
                AttributeOption.class)).thenReturn(true);
        Mockito.when(mongoTemplate.exists(
                Query.query(Criteria.where("_id").is(123123L)),
                DataDefinition.class)).thenReturn(true);

        try {
            DataValue newDataValue = dataValueManager.saveUpdate(dataValue,0L);
            Assertions.assertEquals(dataValue, newDataValue, "更新失败！");
            Mockito.verify(mongoTemplate).exists(
                    Query.query(Criteria.where("_id").is(111111L)),
                    AttributeOption.class);
            Mockito.verify(mongoTemplate).exists(
                    Query.query(Criteria.where("_id").is(123123L)),
                    DataDefinition.class);
            Mockito.verify(mongoTemplate).findById(111111L,AttributeOption.class);
            Mockito.verify(mongoTemplate).findById(123123L,DataDefinition.class);
        } catch (BusinessException e) {
            Assertions.assertEquals(108000025, e.getCode());
        }
    }

    @Test
    public void testSaveUpdateByNotSameAttributeForOptionAndDefinition() {
        Attribute attribute1 = new Attribute();
        attribute1.setId(0L);
        Attribute attribute2 = new Attribute();
        attribute2.setId(1L);

        AttributeOption option = new AttributeOption();
        option.setId(111111L);
        option.setAttribute(attribute1);

        DataDefinition dataDefinition = new DataDefinition();
        dataDefinition.setId(123123L);
        dataDefinition.setAttribute(attribute2);

        DataValue dataValue = new DataValue();
        dataValue.setOption(option);
        dataValue.setDataDefinition(dataDefinition);
        dataValue.setValue(dataValue);

        Mockito.when(mongoTemplate.exists(
                Query.query(Criteria.where("_id").is(111111L)),
                AttributeOption.class)).thenReturn(true);
        Mockito.when(mongoTemplate.exists(
                Query.query(Criteria.where("_id").is(123123L)),
                DataDefinition.class)).thenReturn(true);

        Mockito.when(mongoTemplate.findById(111111L,AttributeOption.class)).thenReturn(option);
        Mockito.when(mongoTemplate.findById(123123L,DataDefinition.class)).thenReturn(dataDefinition);

        try {
            DataValue newDataValue = dataValueManager.saveUpdate(dataValue,0L);
            Assertions.assertEquals(dataValue, newDataValue, "更新失败！");
            Mockito.verify(mongoTemplate).exists(
                    Query.query(Criteria.where("_id").is(111111L)),
                    AttributeOption.class);
            Mockito.verify(mongoTemplate).exists(
                    Query.query(Criteria.where("_id").is(123123L)),
                    DataDefinition.class);
            Mockito.verify(mongoTemplate).findById(111111L,AttributeOption.class);
            Mockito.verify(mongoTemplate).findById(123123L,DataDefinition.class);
        } catch (BusinessException e) {
            Assertions.assertEquals(108000026, e.getCode());
        }
    }

    @Test
    public void testSaveUpdateByNormal() {
        Attribute attribute = new Attribute();
        attribute.setId(0L);

        AttributeOption option = new AttributeOption();
        option.setId(111111L);
        option.setAttribute(attribute);

        DataDefinition dataDefinition = new DataDefinition();
        dataDefinition.setId(123123L);
        dataDefinition.setAttribute(attribute);

        DataValue dataValue = new DataValue();
        dataValue.setOption(option);
        dataValue.setDataDefinition(dataDefinition);
        dataValue.setValue("value");

        Mockito.when(mongoTemplate.exists(
                Query.query(Criteria.where("_id").is(111111L)),
                AttributeOption.class)).thenReturn(true);
        Mockito.when(mongoTemplate.exists(
                Query.query(Criteria.where("_id").is(123123L)),
                DataDefinition.class)).thenReturn(true);

        Mockito.when(mongoTemplate.findById(111111L,AttributeOption.class)).thenReturn(option);
        Mockito.when(mongoTemplate.findById(123123L,DataDefinition.class)).thenReturn(dataDefinition);

        DataValue newDataValue = dataValueManager.saveUpdate(dataValue,0L);
        Assertions.assertEquals(dataValue, newDataValue, "更新失败！");
        Mockito.verify(mongoTemplate).exists(
                Query.query(Criteria.where("_id").is(111111L)),
                AttributeOption.class);
        Mockito.verify(mongoTemplate).exists(
                Query.query(Criteria.where("_id").is(123123L)),
                DataDefinition.class);
        Mockito.verify(mongoTemplate).findById(111111L,AttributeOption.class);
        Mockito.verify(mongoTemplate).findById(123123L,DataDefinition.class);

    }
}
