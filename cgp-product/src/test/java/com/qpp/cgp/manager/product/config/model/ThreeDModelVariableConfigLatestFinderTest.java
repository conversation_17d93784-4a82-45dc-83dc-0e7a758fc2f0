package com.qpp.cgp.manager.product.config.model;

import com.qpp.cgp.config.mongo.MongoTemplateFactory;
import com.qpp.cgp.domain.product.config.model.ThreeDModelVariableConfig;
import com.qpp.cgp.manager.application.ApplicationConfigService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.mongodb.core.MongoTemplate;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class ThreeDModelVariableConfigLatestFinderTest {

    @InjectMocks
    private ThreeDModelVariableConfigLatestFinder latestFinder;

    @Mock
    private ApplicationConfigService applicationConfigService;

    @Mock
    private MongoTemplateFactory mongoTemplateFactory;

    /**
     * 传入null情况
     */
    @Test
    public void testGetLatestGivenNull() {

        // mock
        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
        Mockito.when(mongoTemplateFactory.getMongoTemplate(ThreeDModelVariableConfig.class)).thenReturn(mongoTemplate);
        Mockito.when(applicationConfigService.isTestMode()).thenReturn(false);

        // invoke
        latestFinder.getLatest(null, false);

        // verify
        Mockito.verify(applicationConfigService).isTestMode();
        Mockito.verify(mongoTemplateFactory).getMongoTemplate(ThreeDModelVariableConfig.class);
        Mockito.verify(mongoTemplate).find(Mockito.any(), Mockito.any());
    }

    @Test
    public void testGetSpecifyVersionThreeDModelVariableConfig() {

        // mock
        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
        Mockito.when(mongoTemplateFactory.getMongoTemplate(ThreeDModelVariableConfig.class)).thenReturn(mongoTemplate);

        // invoke
        latestFinder.getSpecifyVersionThreeDModelVariableConfig(null, 1);

        // verify
        Mockito.verify(mongoTemplateFactory).getMongoTemplate(Mockito.eq(ThreeDModelVariableConfig.class));
        Mockito.verify(mongoTemplate).find(Mockito.any(), Mockito.eq(ThreeDModelVariableConfig.class));
    }
}