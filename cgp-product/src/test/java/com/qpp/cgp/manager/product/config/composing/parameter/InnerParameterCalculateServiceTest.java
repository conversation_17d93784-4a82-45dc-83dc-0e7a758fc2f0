package com.qpp.cgp.manager.product.config.composing.parameter;

import com.qpp.cgp.domain.product.config.composing.parameter.InnerParameter;
import com.qpp.cgp.value.calculator.ValueExCalculateService;
import com.qpp.core.exception.BusinessException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2021/6/21 15:30
 * @Version 1.0
 */
public class InnerParameterCalculateServiceTest {

    private InnerParameterCalculateService innerParameterCalculateService = new InnerParameterCalculateService();

    private boolean flag;

    @Before
    public void before() {
        flag = false;
    }

    @Test
    public void testCaculateByInnerParametersExistNull() {
        ValueExCalculateService valueExCalculateService = Mockito.mock(ValueExCalculateService.class);
        ReflectionTestUtils.setField(innerParameterCalculateService, "valueExCalculateService", valueExCalculateService);
        Mockito.when(valueExCalculateService.calculate(Mockito.any(), Mockito.anyMap())).thenReturn("sucess");
        // 初始化参数
        InnerParameter innerParameter01 = new InnerParameter();
        innerParameter01.setName("good");
        InnerParameter innerParameter02 = new InnerParameter();
        innerParameter02.setName("good01");
        List<InnerParameter> innerParameters = Arrays.asList(null, innerParameter01, innerParameter02);
        Map<String, Object> map = innerParameterCalculateService.calculate(innerParameters, new HashMap<>());
        Assert.assertEquals(3, innerParameters.size());
        Assert.assertEquals(2, map.size());

    }

    @Test
    public void testCaculateByInnerParametersExistRepalaceKeyOfMap() {
        ValueExCalculateService valueExCalculateService = Mockito.mock(ValueExCalculateService.class);
        ReflectionTestUtils.setField(innerParameterCalculateService, "valueExCalculateService", valueExCalculateService);
        Mockito.when(valueExCalculateService.calculate(Mockito.any(), Mockito.anyMap())).thenReturn("sucess");
        // 初始化参数
        InnerParameter innerParameter01 = new InnerParameter();
        innerParameter01.setName("good");
        InnerParameter innerParameter02 = new InnerParameter();
        innerParameter02.setName("good");
        innerParameter02.setValueMappings(new LinkedList<>());
        List<InnerParameter> innerParameters = Arrays.asList(innerParameter01, innerParameter02);
        Map<String, Object> map = innerParameterCalculateService.calculate(innerParameters, new HashMap<>());
        Assert.assertEquals(2, innerParameters.size());
        Assert.assertEquals(1, map.size());

    }

    @Test
    public void testCaculateByInnerParameterIsNull() {
        HashMap<String, Object> stringObjectHashMap = new HashMap<>();
        InnerParameter innerParameter = null;
        try {
            innerParameterCalculateService.calculate(innerParameter, stringObjectHashMap);
        } catch (BusinessException e) {
            flag = true;
            Assert.assertEquals(200044, e.getCode());
        }
        Assert.assertTrue(flag);
    }

    @Test
    public void testCaculateCorrect() {
        ValueExCalculateService valueExCalculateService = Mockito.mock(ValueExCalculateService.class);
        ReflectionTestUtils.setField(innerParameterCalculateService, "valueExCalculateService", valueExCalculateService);
        Mockito.when(valueExCalculateService.calculate(Mockito.any(), Mockito.anyMap())).thenReturn("sucess");

        Object objectStr = innerParameterCalculateService.calculate(new InnerParameter(), new HashMap<>());
        Assert.assertEquals("sucess", String.valueOf(objectStr));
    }
}
