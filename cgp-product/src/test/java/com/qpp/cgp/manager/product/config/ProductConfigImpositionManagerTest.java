package com.qpp.cgp.manager.product.config;

import com.qpp.cgp.domain.dto.product.ProductDTO;
import com.qpp.cgp.domain.product.config.ProductConfigImposition;
import com.qpp.cgp.domain.product.config.ProductConfigItem;
import com.qpp.cgp.manager.product.ProductManager;
import com.qpp.cgp.repository.product.config.ProductConfigImpositionRepository;
import org.assertj.core.api.Assertions;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

@RunWith(MockitoJUnitRunner.class)
public class ProductConfigImpositionManagerTest {

    @Spy
    @InjectMocks
    private ProductConfigImpositionManager productConfigImpositionManager;

    @Mock
    private ProductManager productManager;

    @Mock
    private ProductConfigManager productConfigManager;

    @Mock
    private ProductConfigImpositionRepository productConfigImpositionRepository;

    @Test
    public void testFindByProductIdSku() {
        ProductDTO productDTO = new ProductDTO();
        productDTO.setType("sku");
        productDTO.setConfigurableProductId(2L);
        Optional<Long> skuProductConfigIdByProductId = Optional.of(2L);
        ProductConfigImposition productConfigImposition1 = new ProductConfigImposition();
        ProductConfigImposition productConfigImposition2 = new ProductConfigImposition();
        ProductConfigImposition productConfigImposition3 = new ProductConfigImposition();
        productConfigImposition3.setId(3L);
        productConfigImposition1.setConfigVersion(1L);
        productConfigImposition2.setConfigVersion(2L);
        productConfigImposition3.setConfigVersion(2L);
        Optional<Long> productConfigIdOptional = Optional.of(4L);

        Mockito.when(productManager.findById(any()))
                .thenReturn(productDTO);
        Mockito.when(productConfigManager.getProductConfigIdByProductId(eq(1L)))
                .thenReturn(skuProductConfigIdByProductId);
        Mockito.when(productConfigManager.getProductConfigIdByProductId(eq(2L)))
                .thenReturn(productConfigIdOptional);
        Mockito.when(productConfigImpositionRepository.findByProductConfigIdOrderByConfigVersionAsc(any()))
                .thenReturn(Lists.newArrayList(productConfigImposition1, productConfigImposition2))
                .thenReturn(Lists.newArrayList(productConfigImposition3));
        Optional<ProductConfigImposition> productConfigImpositionOptional
                = productConfigImpositionManager.findByProductId(1L);

        Assertions.assertThat(productConfigImpositionOptional.get().getId()).isEqualTo(3L);
    }

    @Test
    public void testFindByProductIdConfig() {
        ProductDTO productDTO = new ProductDTO();
        productDTO.setType("CONFIGURABLE");
        productDTO.setConfigurableProductId(2L);
        Optional<Long> skuProductConfigIdByProductId = Optional.of(2L);
        ProductConfigImposition productConfigImposition1 = new ProductConfigImposition();
        ProductConfigImposition productConfigImposition2 = new ProductConfigImposition();
        ProductConfigImposition productConfigImposition3 = new ProductConfigImposition();
        productConfigImposition2.setId(3L);
        productConfigImposition1.setConfigVersion(1L);
        productConfigImposition2.setConfigVersion(2L);
        productConfigImposition3.setConfigVersion(3L);
        productConfigImposition1.setStatus(ProductConfigItem.STATUS_RELEASE);
        productConfigImposition2.setStatus(ProductConfigItem.STATUS_RELEASE);
        productConfigImposition3.setStatus(ProductConfigItem.STATUS_DRAFT);

        Mockito.when(productManager.findById(any()))
                .thenReturn(productDTO);
        Mockito.when(productConfigManager.getProductConfigIdByProductId(eq(1L)))
                .thenReturn(skuProductConfigIdByProductId);
        Mockito.when(productConfigImpositionRepository.findByProductConfigIdOrderByConfigVersionAsc(any()))
                .thenReturn(Lists.newArrayList(productConfigImposition1, productConfigImposition2,
                        productConfigImposition3));
        Optional<ProductConfigImposition> productConfigImpositionOptional
                = productConfigImpositionManager.findByProductId(1L);

        Assertions.assertThat(productConfigImpositionOptional.get()).isEqualTo(productConfigImposition2);
    }

}