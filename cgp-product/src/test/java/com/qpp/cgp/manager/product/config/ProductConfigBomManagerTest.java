//package com.qpp.cgp.manager.product.config;
//
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
///**
// * <AUTHOR> Lee 2018/10/25 18:48
// */
//@RunWith(SpringRunner.class)
//@SpringBootTest
//public class ProductConfigBomManagerTest {
//
//
//    @Autowired
//    private ProductConfigBomManager productConfigBomManager;
//
//    @Test
//    public void syncProduceComponentConfigs() {
//
//        Long bomId = 241177L;
//        productConfigBomManager.syncProduceComponentConfigs(bomId);
//
//    }
//}