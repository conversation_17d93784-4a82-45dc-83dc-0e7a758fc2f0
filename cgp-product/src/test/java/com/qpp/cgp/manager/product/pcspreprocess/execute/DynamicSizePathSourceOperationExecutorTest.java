package com.qpp.cgp.manager.product.pcspreprocess.execute;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.pcspreprocess.operatorconfig.OptionProjectionGroup;
import com.qpp.cgp.domain.pcspreprocess.opertion.DynamicSizePathSourceOperation;
import com.qpp.cgp.expression.Expression;
import com.qpp.cgp.service.dynamicsize.v2.QppDynamicSizeRemoteService;
import com.qpp.cgp.value.ExpressionValueEx;
import com.qpp.cgp.value.ValueEx;
import com.qpp.cgp.value.ValueType;
import com.qpp.cgp.value.calculator.ValueExCalculateService;
import com.qpp.service.script.NashornScriptService;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @date 2023/8/4
 */
@ExtendWith(MockitoExtension.class)
public class DynamicSizePathSourceOperationExecutorTest {

    @Spy
    @InjectMocks
    private DynamicSizePathSourceOperationExecutor dynamicSizePathSourceOperationExecutor;

    @Mock
    private ValueExCalculateService valueExCalculateService;

    @Mock
    private QppDynamicSizeRemoteService qppDynamicSizeRemoteService;

    private NashornScriptService nashornScriptService = new NashornScriptService();

    private ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    public void init() {
        Mockito.when(valueExCalculateService.calculate(any(), any()))
                .thenAnswer(invocation -> {
                    ExpressionValueEx valueEx = invocation.getArgument(0);
                    Expression expression = valueEx.getExpression();
                    Map<String, Object> context = invocation.getArgument(1);
                    Map<String, Object> allInputs = new HashMap<>();
                    allInputs.put("context", context);
                    return nashornScriptService.evalJavascriptFunction(expression.getExpression(),
                            "expression", Object.class, allInputs);
                });

        ReflectionTestUtils.setField(dynamicSizePathSourceOperationExecutor, "objectMapper", objectMapper);
    }

    @Test
    public void testExecute() {
        Expression expression1 = new Expression();
        expression1.setResultType(ValueType.String);
        expression1.setExpression("function expression(context) { return 'test'; }");
        Expression expression2 = new Expression();
        expression2.setResultType(ValueType.String);
        expression2.setExpression("function expression(context) { return 'Cut'; }");
        Expression expression3 = new Expression();
        expression3.setResultType(ValueType.Number);
        expression3.setExpression("function expression(context) { return 10; }");
        Expression expression4 = new Expression();
        expression4.setResultType(ValueType.Number);
        expression4.setExpression("function expression(context) { return 21; }");
        Expression expression5 = new Expression();
        expression5.setResultType(ValueType.Boolean);
        expression5.setExpression("function expression(context) { return true; }");

        ExpressionValueEx templateName = new ExpressionValueEx();
        templateName.setType(ValueType.String);
        templateName.setExpression(expression1);
        ExpressionValueEx layer = new ExpressionValueEx();
        layer.setType(ValueType.String);
        layer.setExpression(expression2);
        ExpressionValueEx valueEx3 = new ExpressionValueEx();
        valueEx3.setType(ValueType.Number);
        valueEx3.setExpression(expression3);
        ExpressionValueEx valueEx4 = new ExpressionValueEx();
        valueEx4.setType(ValueType.Number);
        valueEx4.setExpression(expression4);
        ExpressionValueEx valueEx5 = new ExpressionValueEx();
        valueEx5.setType(ValueType.Boolean);
        valueEx5.setExpression(expression5);

        Map<String, ValueEx> projection1 = new HashMap<>();
        projection1.put("test1", valueEx3);
        projection1.put("test2", valueEx4);
        Map<String, ValueEx> projection2 = new HashMap<>();
        projection2.put("test3", valueEx3);
        projection2.put("test4", valueEx4);

        OptionProjectionGroup group = new OptionProjectionGroup();
        group.setCondition(valueEx5);
        group.setProjection(projection2);

        DynamicSizePathSourceOperation sourceOperation = new DynamicSizePathSourceOperation();
        sourceOperation.setTemplateName(templateName);
        sourceOperation.setLayer(layer);
        sourceOperation.setDpi(96);
        sourceOperation.setVariables(projection1);
        sourceOperation.setOptionProjections(ImmutableList.of(group));

        Map<String, Object> context = new HashMap<>();

        ArgumentCaptor<String> templateNameCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> layerCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Integer> dpiCaptor = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Map<String, Object>> varialbesCaptor = ArgumentCaptor.forClass(Map.class);

        Mockito.when(qppDynamicSizeRemoteService.generatePath(templateNameCaptor.capture(),
                layerCaptor.capture(), dpiCaptor.capture(), varialbesCaptor.capture()))
                .thenReturn("M 0 0 h 100 v 100 h -100 Z");

        Object path = dynamicSizePathSourceOperationExecutor.execute(sourceOperation, context);

        Assertions.assertThat(templateNameCaptor.getValue()).isEqualTo("test");
        Assertions.assertThat(layerCaptor.getValue()).isEqualTo("Cut");
        Assertions.assertThat(dpiCaptor.getValue()).isEqualTo(96);
        Assertions.assertThat(varialbesCaptor.getValue())
                .isEqualTo(ImmutableMap.of("test1", 10, "test2", 21, "test3", 10, "test4", 21));
        Assertions.assertThat(path).isEqualTo("M 0 0 h 100 v 100 h -100 Z");
    }
}
