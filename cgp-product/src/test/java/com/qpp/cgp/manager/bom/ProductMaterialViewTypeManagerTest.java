package com.qpp.cgp.manager.bom;

import com.qpp.cgp.config.mongo.MongoTemplateFactory;
import com.qpp.cgp.domain.bom.ProductMaterialViewType;
import com.qpp.cgp.domain.simplifyBom.SimplifyBomConfig;
import com.qpp.core.exception.BusinessException;
import com.qpp.mongo.driver.HybridMongoTemplate;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;

public class ProductMaterialViewTypeManagerTest {

    @Test
    public void getProductMaterialViewTypeIdsTest(){
        ProductMaterialViewTypeManager productMaterialViewTypeManager = new ProductMaterialViewTypeManager(null, null);
        MongoTemplateFactory mongoTemplateFactory = Mockito.mock(MongoTemplateFactory.class);
        MongoTemplate mongoTemplate = Mockito.mock(MongoTemplate.class);
        Mockito.when(mongoTemplateFactory.getMongoTemplate(ProductMaterialViewType.class)).thenReturn(mongoTemplate);
        Whitebox.setInternalState(productMaterialViewTypeManager, "mongoTemplateFactory", mongoTemplateFactory);
        Long productConfigDesignId = 111L;
        List<ProductMaterialViewType> productMaterialViewTypes = new ArrayList<>();
        ProductMaterialViewType productMaterialViewType = new ProductMaterialViewType();
        productMaterialViewType.setId("123");
        productMaterialViewTypes.add(productMaterialViewType);
        Mockito.when(mongoTemplate.find(Query.query(Criteria.where("productConfigDesignId").is(productConfigDesignId)), ProductMaterialViewType.class)).thenReturn(productMaterialViewTypes);
        List<String> productMaterialViewTypeIds = productMaterialViewTypeManager.getProductMaterialViewTypeIds(productConfigDesignId);
        Mockito.verify(mongoTemplate, Mockito.times(1)).find(Query.query(Criteria.where("productConfigDesignId").is(productConfigDesignId)), ProductMaterialViewType.class);
        Mockito.verify(mongoTemplateFactory, Mockito.times(1)).getMongoTemplate(ProductMaterialViewType.class);
        Assert.assertEquals(1, productMaterialViewTypeIds.size());
        Assert.assertEquals(productMaterialViewType.getId(), productMaterialViewTypeIds.get(0));
    }

    @Test
    public void delCheckTest(){
        HybridMongoTemplate mongoTemplate = Mockito.mock(HybridMongoTemplate.class);
        ProductMaterialViewTypeManager productMaterialViewTypeManager = new ProductMaterialViewTypeManager(null, null);
        ReflectionTestUtils.setField(productMaterialViewTypeManager,"mongoTemplate",mongoTemplate);
        String id="123";
        Query query = Query.query(Criteria.where("viewDisplayConfig.nodeViewConfigs.displayProductMaterialTypes._id").is(id));

        try {
            productMaterialViewTypeManager.delCheck(id);
            Mockito.when(mongoTemplate.exists(query, SimplifyBomConfig.class)).thenReturn(true);
        }catch (Exception e){
            Assert.assertNotNull(e);
            BusinessException businessException=(BusinessException)e;
            Assert.assertEquals(1742251,businessException.getCode());
        }
    }

}