package com.qpp.cgp.manager.product.copier.product.config;

import com.qpp.cgp.domain.product.config.ProductConfig;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Optional;

/**
 * <AUTHOR> Chiu
 * @Date 2021/3/5 17:45
 * @Description
 * @Version 1.0
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
public class ProductConfigCopierCopyTests {

    @Autowired
    private ProductConfigCopier productConfigCopier;

    @Test
    public void testByNullProductConfig(){
        try {
            ProductConfig productConfig = productConfigCopier.copy(null, 123L, null,
                    null, null, null, null, false,
                    Optional.empty());
        } catch (Exception e) {
            Assert.assertEquals("Parameter：source can not be null!", e.getMessage());
        }
    }

}
