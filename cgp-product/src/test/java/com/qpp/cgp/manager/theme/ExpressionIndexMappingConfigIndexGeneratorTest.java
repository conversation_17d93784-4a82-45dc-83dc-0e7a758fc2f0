package com.qpp.cgp.manager.theme;

import com.qpp.cgp.domain.theme.ExpressionIndexMappingConfig;
import com.qpp.cgp.expression.Expression;
import com.qpp.cgp.expression.calculator.ExpressionCalculatorService;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;

import java.util.HashMap;
import java.util.Map;

public class ExpressionIndexMappingConfigIndexGeneratorTest {

    @Test
    public void getItemIndex(){
        ExpressionCalculatorService expressionCalculatorService = Mockito.mock(ExpressionCalculatorService.class);
        ExpressionIndexMappingConfigIndexGenerator expressionIndexMappingConfigIndexGenerator = new ExpressionIndexMappingConfigIndexGenerator();
        Whitebox.setInternalState(expressionIndexMappingConfigIndexGenerator, "expressionCalculatorService", expressionCalculatorService);
        ExpressionIndexMappingConfig expressionIndexMappingConfig = new ExpressionIndexMappingConfig();
        Map<String, Object> context = new HashMap<>();
        Expression expression = new Expression();
        expressionIndexMappingConfig.setExpression(expression);
        Mockito.when(expressionCalculatorService.calculate(expression, context)).thenReturn(10);
        int itemIndex = expressionIndexMappingConfigIndexGenerator.getItemIndex(expressionIndexMappingConfig, context);
        Mockito.verify(expressionCalculatorService).calculate(expression, context);
        Assert.assertEquals(10, itemIndex);
    }

}