package com.qpp.cgp.domain.order;

import com.qpp.cgp.domain.shipping.DeliveryRegistrationFee;
import com.qpp.cgp.dto.currency.CurrencyExchangeResult;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@NoArgsConstructor
@Data
public class ShippingQuote implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1L;


    private String code;

    private String title;

    private String description;

    private BigDecimal cost = new BigDecimal(0);

    private BigDecimal tax = new BigDecimal(0);

    private BigDecimal totalExTax = new BigDecimal(0);

    private BigDecimal totalIncTax = new BigDecimal(0);

    private int minDeliveryDays;

    private int maxDeliveryDays;

    private Integer minProduceDays;

    private Integer maxProduceDays;

    private boolean isFree = false;

    private DeliveryRegistrationFee deliveryRegistrationFee;


    private BigDecimal freeShippingOver = new BigDecimal(0);

    private CurrencyExchangeResult currencyExchangeResult;


}
