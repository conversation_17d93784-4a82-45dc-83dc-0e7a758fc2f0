package com.qpp.cgp.domain.dto.shipment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("產品組裝詳細")
public class SaleOrderProductItemDTO {
    @ApiModelProperty("产品项id")
    private String productItemId;

    @ApiModelProperty("产品图片")
    private String productImage;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("产品描述(含名稱，材質，尺寸)")
    private String productDesc;

    @ApiModelProperty("产品数量")
    private Integer qty;

    @ApiModelProperty("订单项序号")
    private Long sortNo;

    @ApiModelProperty("產品單價(新增)")
    private Number unitPrice;


}
