package com.qpp.cgp.domain.dto.component;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> Lee 2019/7/26 15:29
 */
@ApiModel(description = "組件生產信息")
@NoArgsConstructor
@Data
public class ComponentProductionDTO {

    @ApiModelProperty(value = "PS订单项id", required = true)
    private String orderItemId;

    @ApiModelProperty(value = "CGP生產物料路徑", required = true)
    private String materialPath;

    @ApiModelProperty(value = "生產數量", required = true)
    private Integer qty;

    @ApiModelProperty(value = "生產備註")
    private String comment;

    @ApiModelProperty(value = "組件倉存信息[組件生產完成，選擇的入倉]")
    private List<ComponentStoreDTO> storeItems = new ArrayList();

    @ApiModelProperty(value = "更新時間", required = true)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

}
