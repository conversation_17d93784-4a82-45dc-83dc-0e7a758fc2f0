package com.qpp.cgp.domain.postage;

public class WeightBasedPostageRule {

	private Double startWeight;
	
	private Double endWeight;

	private Double plusFee;
	
	private Double PlusWeight;
	
	private Double firstWeight;
	
	private Double firstFee;
	
	private Double amountPromotion;
	
	private Double promotion;
	
	//总重量额外费用比例
	private Double extraFeeRate;
	
	public Double getStartWeight() {
		return startWeight;
	}

	public void setStartWeight(Double startWeight) {
		this.startWeight = startWeight;
	}

	public Double getEndWeight() {
		return endWeight;
	}

	public void setEndWeight(Double endWeight) {
		this.endWeight = endWeight;
	}

	public Double getPlusFee() {
		return plusFee;
	}

	public void setPlusFee(Double plusFee) {
		this.plusFee = plusFee;
	}

	public Double getPlusWeight() {
		return PlusWeight;
	}

	public void setPlusWeight(Double plusWeight) {
		PlusWeight = plusWeight;
	}

	public Double getFirstWeight() {
		return firstWeight;
	}

	public void setFirstWeight(Double firstWeight) {
		this.firstWeight = firstWeight;
	}

	public Double getFirstFee() {
		return firstFee;
	}

	public void setFirstFee(Double firstFee) {
		this.firstFee = firstFee;
	}

	public Double getExtraFeeRate() {
		return extraFeeRate;
	}

	public void setExtraFeeRate(Double extraFeeRate) {
		this.extraFeeRate = extraFeeRate;
	}

	public Double getAmountPromotion() {
		return amountPromotion;
	}

	public void setAmountPromotion(Double amountPromotion) {
		this.amountPromotion = amountPromotion;
	}

	public Double getPromotion() {
		return promotion;
	}

	public void setPromotion(Double promotion) {
		this.promotion = promotion;
	}
	
}
