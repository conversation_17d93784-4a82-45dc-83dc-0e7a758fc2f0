package com.qpp.cgp.domain.dto.ordeitem;

import com.qpp.cgp.domain.order.AbstractOrderLineItem;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by smart on 12/16/2017.
 */
@NoArgsConstructor
@Data
public class OrderItemStatusIdOnly {

    public Long id;

    public Long statusId;

    public static OrderItemStatusIdOnly of(AbstractOrderLineItem orderLineItem) {
        OrderItemStatusIdOnly orderItemStatusIdOnly = new OrderItemStatusIdOnly();
        orderItemStatusIdOnly.setId(Long.valueOf(orderLineItem.getId()));
        orderItemStatusIdOnly.setStatusId(orderLineItem.getStatusId());
        return orderItemStatusIdOnly;
    }
}
