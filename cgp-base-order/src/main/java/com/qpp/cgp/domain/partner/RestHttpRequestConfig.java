package com.qpp.cgp.domain.partner;

import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * rest请求配置
 * Created by smart on 12/8/2017.
 */
@NoArgsConstructor
@Data
public class RestHttpRequestConfig extends MongoDomain {

    private String method;

    private String url;

    private Map<String, String> headers;

    private String body;

    private Map<String, String> queryParameters;

    private String successPath;

    private String successKey;

    private String errorMessagePath;

}
