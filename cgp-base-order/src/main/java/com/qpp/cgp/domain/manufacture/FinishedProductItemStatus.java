package com.qpp.cgp.domain.manufacture;

import com.qpp.cgp.config.domaintype.RuntimeDomain;
import com.qpp.cgp.migration.CgpMongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 成品项状态
 * Created by smart on 10/25/2017.
 */
@NoArgsConstructor
@Data
@RuntimeDomain
@Document(collection = "finishedproductitemstatuses")
public class FinishedProductItemStatus extends CgpMongoDomain {

    //等待生产
    public static final Long WAITING_PRODUCE = 156201L;

    //已备齐 外购产品
    public static final Long READY = 156202L;

    //已打印
    public static final Long PRINTED = 156203L;

    //已生产
    public static final Long PRODUCED = 156204L;

    //已完成
    public static final Long FINISHED = 241635L;

    private String name;

}
