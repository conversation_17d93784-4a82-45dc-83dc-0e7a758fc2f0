package com.qpp.cgp.domain.dto.shipment;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.checkerframework.checker.units.qual.A;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.Instant;

@Data
@ApiModel("发货单签收DTO")
public class ShipmentReceiveInfo {
    @ApiModelProperty("簽收人")
    @NotBlank(message = "receiver should not be empty!")
    private String receiver;

    @ApiModelProperty("簽收時間")
    @NotNull
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+08:00")
    private Instant receiveTime;

    @ApiModelProperty("备注")
    private String remark;
}
