package com.qpp.cgp.domain.partner;

import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * partner的订单状态变化通知配置
 * Created by smart on 12/8/2017.
 */
@NoArgsConstructor
@Data
@Document(collection = "partnerorderstatuschangenotifications")
public class PartnerOrderStatusChangeNotification extends MongoDomain {

    private Long partnerId;

    private List<OrderStatusChangeNotification> notifications;

}
