package com.qpp.cgp.domain.partner.report.settle.config;

import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class PartnerReportProductConfig extends MongoDomain {

    private List<Long> productId;

    private String displayName;

    private double price;

    {
        setClazz(this.getClass().getName());
    }

}
