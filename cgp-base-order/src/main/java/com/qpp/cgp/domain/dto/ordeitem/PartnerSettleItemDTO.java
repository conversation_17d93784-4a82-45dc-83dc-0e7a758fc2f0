package com.qpp.cgp.domain.dto.ordeitem;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qpp.cgp.domain.bom.runtime.RtObject;
import com.qpp.cgp.domain.order.ShipmentInfo;
import com.qpp.cgp.domain.partner.Partner;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Transient;

import java.util.Date;
import java.util.List;

/**
 * Created by smart on 12/26/2017.
 */
@NoArgsConstructor
@Data
public class PartnerSettleItemDTO {

    private Long id;

    private String productSku;

    private Long orderId;

    private Long productId;

    private Integer qty;

    @JsonProperty
    @Transient
    private RtObject extraParam;

    private String productDisplayName;

    private String price;

    private String amount;

    private String comment;

    private List<String> bindOrderNumbers;

    private ShipmentInfo shipmentInfo;

    private String orderNumber;

    private Boolean isTest;

    private Date datePurchased;

    private Date deliveryDate;

    private String deliveryNo;

    private Date receivedDate;

    @JsonProperty
    @Transient
    private Partner partner;

}
