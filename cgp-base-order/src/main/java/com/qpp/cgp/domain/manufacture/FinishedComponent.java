package com.qpp.cgp.domain.manufacture;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * PS的生产组件信息
 */
@ApiModel(description = "生产报表组件")
@NoArgsConstructor
@Data
public class FinishedComponent {

    //待打印状态
    public static final String STATUS_WAIT_PRINT = "D";
    //待生产状态
    public static final String STATUS_WAIT_PRODUCE = "F";

    @ApiModelProperty("单号")
    private String orderNo;

    @ApiModelProperty("销售订单号")
    private String saleOrderNo;

    @ApiModelProperty("订单类型")
    private String orderType;

    @ApiModelProperty("产品id")
    private String designID;

    @ApiModelProperty("产品项序号")
    private int lineNum;

    @ApiModelProperty("下单日期")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private Date orderDate;

    @ApiModelProperty("订单审核日期")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private Date approveDate;

    @ApiModelProperty("组件id")
    private String componentID;

    @ApiModelProperty("组件编号")
    private String componentCode;

    @ApiModelProperty("组件类型")
    private String componentType = "product";

    @ApiModelProperty("组件类型描述")
    private String componentTypeDesc = "product";

    @ApiModelProperty("产品组件描述")
    private String componentDesc;

    @ApiModelProperty("生产机器编号")
    private String machineNo;

    @ApiModelProperty("生产总数")
    private int qty;

    @ApiModelProperty("未生产数量")
    private int unfinishQty;

    @ApiModelProperty("生产状态")
    private String productionStatus;

    @ApiModelProperty("备注")
    private String remark;

}
