package com.qpp.cgp.domain.manufacture;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qpp.cgp.config.domaintype.RuntimeDomain;
import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * Created by smart on 10/25/2017.
 */
@RuntimeDomain
@NoArgsConstructor
@Data
@Document(collection = "finishedproductitemstatushistories")
public class FinishedProductItemStatusHistory extends MongoDomain {

    @JsonProperty
    @Transient
    private FinishedProductItemWorkAction action;

    private Long actionId;

    private int qty;

    private String comment;

    private String finishedProductItemId;

    private Long userId;

    public void setAction(FinishedProductItemWorkAction action) {
        this.action = action;
        this.actionId = action.getId();
    }
}
