package com.qpp.cgp.domain.partner.report.settle.config;

import com.qpp.mongo.domain.MongoDomain;
import com.qpp.cgp.domain.mail.MailTemplateConfig;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
@Document(collection = "partnerorderreportconfigs")
public class PartnerOrderReportConfig extends MongoDomain {

    private Long partnerId;

    /**
     * 指定查询的
     */
    private Long statusId;

    /**
     * 生成的文件的名称
     */
    private String fileName;

    /**
     * Excel模板文件的路径
     */
    private String filePath;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * quartz定时发送的cron表达式
     */
    private String expression;

    /**
     * Excel模板文件的第一个sheet模板的下标
     */
    private Integer sheetIndex = 0;

    /**
     * 指定生成的Excel模板文件的第一个sheet的名称
     */
    private String sheetName;

    /**
     * Excel图表的标题
     */
    private String title;

    /**
     * 定义title的在Excel表格中的位置
     */
    private PositionConfig titlePositions;

    /**
     * 间隔时间(指定状态的日期与指定系统时间之间的间隔)
     */
    private Integer dateInterval;

    /**
     * 是否启用该配置
     */
    private Boolean isEnable;

    /**
     * 产品信息配置(产品名称，名称对应的Product的id集合，产品价格)
     */
    private List<PartnerReportProductConfig> productConfigs;

    /**
     * partner下产品的信息总览(总数量、总价格、单价、年月)
     */
    private PartnerReportSummaryConfig partnerReportSummaryConfig;

    /**
     * partner下产品的详细信息(每一天的日期、每种产品单日销售额、数量总计)
     */
    private PartnerReportDetailsConfig partnerReportDetailsConfig;

    /**
     * 第二个表的配置
     */
    private PartnerReportOrderListConfig partnerReportOrderListConfig;

    /**
     * 邮件模板配置
     */
    private MailTemplateConfig mailTemplateConfig;

    {
        setClazz(this.getClass().getName());
    }

}



