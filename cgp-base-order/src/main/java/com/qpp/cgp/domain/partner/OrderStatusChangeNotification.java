package com.qpp.cgp.domain.partner;

import com.qpp.mongo.domain.MongoDomain;
import com.qpp.cgp.domain.dto.mail.MailItem;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 订单状态变化的通知配置
 * Created by smart on 12/8/2017.
 */
@NoArgsConstructor
@Data
public class OrderStatusChangeNotification extends MongoDomain {

    private Long statusId;

    private String statusName;

    //是否测试模式
    private boolean isTest;

    private List<RestHttpRequestConfig> restHttpRequestConfigs;

    private MailItem mailItem;

}
