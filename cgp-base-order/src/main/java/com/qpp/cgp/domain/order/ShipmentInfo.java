package com.qpp.cgp.domain.order;

import com.qpp.mongo.domain.MongoDomain;

import java.math.BigDecimal;
import java.util.Date;

//@Document(collection = "shipmentinfos")
public class ShipmentInfo extends MongoDomain {

    private static final long serialVersionUID = 1L;

    private String orderNumber;

    private Double weight;

    private String deliveryNo;

    private Date deliveryDate;

    private String shippingMethodCode;

    private String shippingMethodName;

    private String vendorCode;

    private BigDecimal cost;

//    @JsonIgnore
//    private Order order;

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getVendorCode() {
        return vendorCode;
    }

    public void setVendorCode(String vendorCode) {
        this.vendorCode = vendorCode;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public AbstractOrder getOrder() {
//        return order;
        return null;
    }

    public void setOrder(AbstractOrder order) {
//        Order orderReference = new Order(order.getId());
//        this.order = orderReference;
    }

    public String getDeliveryNo() {
        return deliveryNo;
    }

    public void setDeliveryNo(String deliveryNo) {
        this.deliveryNo = deliveryNo;
    }

    public Date getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public String getShippingMethodCode() {
        return shippingMethodCode;
    }

    public void setShippingMethodCode(String shippingMethodCode) {
        this.shippingMethodCode = shippingMethodCode;
    }

    public String getShippingMethodName() {
        return shippingMethodName;
    }

    public void setShippingMethodName(String shippingMethodName) {
        this.shippingMethodName = shippingMethodName;
    }

    public BigDecimal getCost() {
        return cost;
    }

    public void setCost(BigDecimal cost) {
        this.cost = cost;
        if (cost == null) {
            this.cost = new BigDecimal(0);
        }
    }

    @Override
    public String toString() {
        return "ShipmentInfo{" +
                "orderNumber='" + orderNumber + '\'' +
                ", weight=" + weight +
                ", deliveryNo='" + deliveryNo + '\'' +
                ", deliveryDate=" + deliveryDate +
                ", shippingMethodCode='" + shippingMethodCode + '\'' +
                ", shippingMethodName='" + shippingMethodName + '\'' +
                ", cost=" + cost +
//                ", order=" + order +
                '}';
    }
}
