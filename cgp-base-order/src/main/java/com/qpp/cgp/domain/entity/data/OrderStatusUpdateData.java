package com.qpp.cgp.domain.entity.data;

import com.qpp.cgp.config.domaintype.RuntimeDomain;
import com.qpp.cgp.domain.dto.order.SalesOrderItemImpressionInfoDTO;
import com.qpp.cgp.domain.dto.order.ShipmentBoxDTO;
import com.qpp.cgp.domain.order.ShipmentBox;
import com.qpp.cgp.domain.order.ShipmentInfo;
import com.qpp.mongo.domain.MongoDomain;
import com.qpp.qris.domain.dto.printing.PrintingDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Document(value = "orderstatusupdatedata")
@RuntimeDomain
@Data
public class OrderStatusUpdateData extends MongoDomain {

    private long statusId;

    private Date paidDate;

    private String paymentMethod;

    @ApiModelProperty(value = "物流信息")
    private ShipmentInfo shipmentInfo;

    private boolean customerNotify;

    private String comment;

    public List<ShipmentBox> getShipmentBoxes() {
        return shipmentBoxes;
    }

    public void setShipmentBoxes(List<ShipmentBox> shipmentBoxes) {
        this.shipmentBoxes = shipmentBoxes;
        List<ShipmentBoxDTO> shipmentBoxDTOS = new ArrayList<>();
        if (shipmentBoxes != null && !shipmentBoxes.isEmpty()) {
            for (ShipmentBox shipmentBox : shipmentBoxes) {
                ShipmentBoxDTO shipmentBoxDTO = new ShipmentBoxDTO();
                BeanUtils.copyProperties(shipmentBox, shipmentBoxDTO, "clazz", "order");
                shipmentBoxDTO.setClazz(ShipmentBoxDTO.class.getName());
                shipmentBoxDTOS.add(shipmentBoxDTO);
            }
        }
        this.shipmentBoxDTOS = shipmentBoxDTOS;
    }

    public List<ShipmentBoxDTO> getShipmentBoxDTOS() {
        return shipmentBoxDTOS;
    }

    public void setShipmentBoxDTOS(List<ShipmentBoxDTO> shipmentBoxDTOS) {
        this.shipmentBoxDTOS = shipmentBoxDTOS;
    }

    @ApiModelProperty(value = "装箱信息")
    private List<ShipmentBox> shipmentBoxes;
    /**
     * 由于ShipmentBox中的boxProductItems字段源代码中不允许序列化，所以用DTO同步存储
     */
    @ApiModelProperty(value = "装箱信息DTO")
    private List<ShipmentBoxDTO> shipmentBoxDTOS;

    @ApiModelProperty(value = "排版信息(该字段只用于测试使用)")
    private PrintingDTO printing;

    @ApiModelProperty(value = "排版信息(该字段只用于测试使用)")
    private List<SalesOrderItemImpressionInfoDTO> impressionInfos;


    /**
     * bulkOrder - 是否返回到预拼单项列表中
     */
    private Boolean isAddToPreBulkOrderItem;

    /**
     * 审核分类:审核不通过的时候才会有
     */
    private String reviewCategory;

    /**
     * 审核意见：审核不通过的时候才会有
     */
    private String reviewAdvise;


    /**
     * 出货日期
     */
    private Date shippedDate;

    /**
     * 签收时间
     */
    private Date signDate;

    /**
     * 签收备注
     */
    private String signRemark;
}
