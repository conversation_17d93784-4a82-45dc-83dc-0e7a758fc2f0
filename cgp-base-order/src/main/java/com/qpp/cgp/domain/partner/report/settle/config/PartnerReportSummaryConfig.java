package com.qpp.cgp.domain.partner.report.settle.config;

import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class PartnerReportSummaryConfig extends MongoDomain {

    private String dateFormat;

    private List<String> displayNames;

    private String priceFormat;

    {
        setClazz(this.getClass().getName());
    }

}
