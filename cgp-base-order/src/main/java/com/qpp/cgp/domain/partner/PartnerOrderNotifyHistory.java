package com.qpp.cgp.domain.partner;

import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.Instant;
import java.util.Map;

/**
 * Created by smart on 12/14/2017.
 */
@NoArgsConstructor
@Data
@Document(collection = "partnerordernotifyhistories")
public class PartnerOrderNotifyHistory extends MongoDomain {

    private Long partnerId;

    private String partnerName;

    private RestHttpRequestConfig config;

    private Map<String, Object> context;

    private boolean success;

    private String message;

    private Instant notifyDate = Instant.now();


}
