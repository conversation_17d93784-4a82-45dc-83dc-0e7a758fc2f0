package com.qpp.cgp.domain.manufacture;

import com.qpp.cgp.config.domaintype.RuntimeDomain;
import com.qpp.cgp.migration.CgpMongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 生产订单状态
 * Created by smart on 10/25/2017.
 */
@NoArgsConstructor
@Data
@RuntimeDomain
@Document(collection = "manufactureorderstatuses")
public class ManufactureOrderStatus extends CgpMongoDomain {

    //等待生产
    public static final Long WAITING_PRODUCE = 156207L;

    //已生产
    public static final Long PRODUCED = 156208L;

    //已打印
    public static final Long PRINTED = 236483L;

    //已完成
    public static final Long FINISHED = 241677L;

    private String name;

}
