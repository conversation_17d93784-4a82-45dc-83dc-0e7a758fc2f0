package com.qpp.cgp.domain.postage;

import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.cgp.domain.common.Website;
import com.qpp.cgp.migration.CgpMongoDomain;
import net.sf.json.JSONArray;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.PostLoad;
import java.util.ArrayList;
import java.util.List;

@ConfigDomain
@Document(collection = "postageexpress")
public class ExpressPostage extends CgpMongoDomain {

    private static final long serialVersionUID = 1L;

    @Transient
    private List<WeightBasedPostageRule> rules = new ArrayList<>();

    private String ruleString;

    private String address;

    private Website website;

    public void setRules(List<WeightBasedPostageRule> rules) {
        this.rules = rules;
        this.ruleString = JSONArray.fromObject(this.rules).toString();
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Website getWebsite() {
        return website;
    }

    public void setWebsite(Website website) {
        this.website = website;
    }


    public List<WeightBasedPostageRule> getRules() {
        JSONArray jsonArray = JSONArray.fromObject(this.ruleString);
        this.rules = (List<WeightBasedPostageRule>) JSONArray.toCollection(jsonArray, WeightBasedPostageRule.class);
        return rules;
    }

    @SuppressWarnings("unchecked")
    @PostLoad
    public void postLoad() {

        JSONArray jsonArray = JSONArray.fromObject(this.ruleString);
        this.rules = (List<WeightBasedPostageRule>) JSONArray.toCollection(jsonArray, WeightBasedPostageRule.class);
    }

    public void clearRules() {
        this.ruleString = null;
    }


}
