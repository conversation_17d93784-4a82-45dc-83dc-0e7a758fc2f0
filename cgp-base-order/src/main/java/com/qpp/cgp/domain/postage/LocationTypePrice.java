package com.qpp.cgp.domain.postage;


import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.cgp.migration.CgpMongoDomain;
import org.springframework.data.mongodb.core.mapping.Document;

import java.math.BigDecimal;

@ConfigDomain
@Document(collection = "postagelocationtypeprices")
public class LocationTypePrice extends CgpMongoDomain {

	private static final long serialVersionUID = 1L;

	private String locationTypeCode;
	
	private String shippingMethod;
	
	private BigDecimal startOrderAmount;
	
	private BigDecimal endOrderAmount;
	
	private BigDecimal price;

	public String getLocationTypeCode() {
		return locationTypeCode;
	}

	public void setLocationTypeCode(String locationTypeCode) {
		this.locationTypeCode = locationTypeCode;
	}

	public String getShippingMethod() {
		return shippingMethod;
	}

	public void setShippingMethod(String shippingMethod) {
		this.shippingMethod = shippingMethod;
	}

	public BigDecimal getStartOrderAmount() {
		return startOrderAmount;
	}

	public void setStartOrderAmount(BigDecimal startOrderAmount) {
		this.startOrderAmount = startOrderAmount;
	}

	public BigDecimal getEndOrderAmount() {
		return endOrderAmount;
	}

	public void setEndOrderAmount(BigDecimal endOrderAmount) {
		this.endOrderAmount = endOrderAmount;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}
}
