package com.qpp.cgp.domain.payment;


import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Column;

@NoArgsConstructor
@Data
@ConfigDomain
@Document(collection = "usercustomvalues")
public class UserCustomValue extends MongoDomain {

    protected static final long serialVersionUID = 1L;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "name")
    private String name;

    @Column(name = "value")
    private String value;


}
