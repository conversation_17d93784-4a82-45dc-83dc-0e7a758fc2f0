package com.qpp.cgp.domain.order;

import java.io.Serializable;
import java.util.Map;

public class PaymentDetails implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public static final int COD = 1;
	public static final int BROWSER_PAYMENT_GATEWAY = 2;
	public static final int SERVER_PAYMENT_GATEWAY = 3;
	public static final int BANK_TRANSFER = 4;

	public static final int BROWSER_PAYMENT_GATEWAY_BRAINTREE_CC = 5;
	
	public static final int BROWSER_PAYMENT_GATEWAY_WX_H5 = 6;
	
	private String code;
	
	private String title;
	
	private String description;
	
	private int sortOrder;
	
	private long orderStatusId;
	
	private int paymentType;
	
	private Map<String, String> parameters;
	
	private String requestUrl;
	
	private String referrer;
	
	private String postOrGet;
	
	private boolean showOwner = true;
	
	private boolean showCVV = true;
	
	private boolean showType = true;
	
	private boolean showAddr = true;
	
	private boolean showPostcode = true;

	
	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public int getSortOrder() {
		return sortOrder;
	}

	public void setSortOrder(int sortOrder) {
		this.sortOrder = sortOrder;
	}

	public long getOrderStatusId() {
		return orderStatusId;
	}

	public void setOrderStatusId(long orderStatusId) {
		this.orderStatusId = orderStatusId;
	}

	public int getPaymentType() {
		return paymentType;
	}

	public void setPaymentType(int paymentType) {
		this.paymentType = paymentType;
	}

	public Map<String, String> getParameters() {
		return parameters;
	}

	public void setParameters(Map<String, String> parameters) {
		this.parameters = parameters;
	}

	public String getRequestUrl() {
		return requestUrl;
	}

	public void setRequestUrl(String requestUrl) {
		this.requestUrl = requestUrl;
	}

	public String getReferrer() {
		return referrer;
	}

	public void setReferrer(String referrer) {
		this.referrer = referrer;
	}

	public String getPostOrGet() {
		return postOrGet;
	}

	public void setPostOrGet(String postOrGet) {
		this.postOrGet = postOrGet;
	}

	public boolean isShowOwner() {
		return showOwner;
	}

	public void setShowOwner(boolean showOwner) {
		this.showOwner = showOwner;
	}

	public boolean isShowCVV() {
		return showCVV;
	}

	public void setShowCVV(boolean showCVV) {
		this.showCVV = showCVV;
	}

	public boolean isShowType() {
		return showType;
	}

	public void setShowType(boolean showType) {
		this.showType = showType;
	}

	public boolean isShowAddr() {
		return showAddr;
	}

	public void setShowAddr(boolean showAddr) {
		this.showAddr = showAddr;
	}

	public boolean isShowPostcode() {
		return showPostcode;
	}

	public void setShowPostcode(boolean showPostcode) {
		this.showPostcode = showPostcode;
	}

}
