package com.qpp.cgp.domain.dto.customs;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.qpp.cgp.domain.user.AddressBook;
import jodd.util.StringUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2018/08/27
 */
@JsonInclude(JsonInclude.Include.ALWAYS)
@NoArgsConstructor
@Data
public class ShippingAddressInfoDTO {
    private String email;

    private String firstName;

    private String lastName;

    private String address1;

    private String address2;

    private String city;

    private String cityCode;

    private String state;

    private String stateCode;

    private String zip;

    private String countryCode;

    private String country;

    private String locationType;

    private String locationTypeCode;

    private String locationTypeValue;

    private String phone;

    private String areaCode;

    private String area;

    private String mobile;

    private String poBox;

    /**
     * 使用空字符串替代null值
     *
     * @param str
     * @return
     */
    private static String emptyReplaceNull(String str) {
        return Objects.isNull(str) ? "" : str;
    }

    public static ShippingAddressInfoDTO of(AddressBook addressBook, String stateCode) {
        ShippingAddressInfoDTO addressInfo = new ShippingAddressInfoDTO();
        addressInfo.email = addressBook.getEmailAddress();
        addressInfo.firstName = addressBook.getFirstName();
        addressInfo.lastName = addressBook.getLastName();
        addressInfo.address1 = addressBook.getStreetAddress1();
        addressInfo.address2 = emptyReplaceNull(addressBook.getStreetAddress2());
        addressInfo.city = addressBook.getCity();
        addressInfo.cityCode = "";
        addressInfo.state = addressBook.getState();
        addressInfo.stateCode = StringUtils.isBlank(stateCode) ? addressBook.getStateCode() : stateCode;
        addressInfo.zip = addressBook.getPostcode();
        addressInfo.countryCode = addressBook.getCountryCode2();
        addressInfo.country = addressBook.getCountryName();
        addressInfo.locationType = addressBook.getLocationType();
        if (StringUtil.isBlank(addressInfo.locationType)) {
            addressInfo.locationType = addressBook.getLocationTypeCode();
        }
        addressInfo.locationTypeValue = addressBook.getLocationTypeValue();
        addressInfo.locationTypeCode = addressBook.getLocationTypeCode();
        addressInfo.phone = addressBook.getTelephone();
        addressInfo.areaCode = "";
        addressInfo.area = "";
        addressInfo.mobile = addressBook.getMobile();
        addressInfo.poBox = "";
        return addressInfo;
    }
}
