package com.qpp.cgp.domain.shipping;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.cgp.domain.common.Website;
import com.qpp.cgp.migration.CgpMongoDomain;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * description:订单的实际运输方式：用户生产修改的，不是下单用户选择的运送方式，目前是通过数据库直接添加数据
 */
@ConfigDomain
@Document(collection = "shippingmethodactual")
public class ShippingMethod extends CgpMongoDomain {

    private static final long serialVersionUID = 1L;

    private String code;

    private String title;

    private String description;

    @JsonIgnore
    @Getter@Setter
    private Boolean available;

    @Getter@Setter
    private Integer sortOrder;

    @JsonIgnore
    private Website website;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Website getWebsite() {
        return website;
    }

    public void setWebsite(Website website) {
        this.website = website;
    }
}
