package com.qpp.cgp.domain.dto.ordeitem;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qpp.cgp.domain.bom.runtime.RtObject;
import com.qpp.cgp.domain.order.OrderStatus;
import com.qpp.cgp.domain.order.ShipmentInfo;
import com.qpp.cgp.domain.partner.Partner;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Transient;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2018/2/28
 */
@NoArgsConstructor
@Data
public class OrderSettleItemDTO {

    private Long id;

    private String orderNumber;

    private List<String> bindOrderNumbers;

    private ShipmentInfo shipmentInfo;

    private String deliveryNo;

    @JsonProperty
    @Transient
    private RtObject extraParam;

    @JsonProperty
    @Transient
    private Partner partner;

    @JsonProperty
    @Transient
    private Partner productPartner;

    private Date datePurchased;

    private Date deliveryDate;

    private Date receivedDate;

    @JsonProperty
    @Transient
    private OrderStatus status;

    private Boolean isTest;

    public String getDeliveryNo() {
        return Optional.ofNullable(shipmentInfo)
                .map(ShipmentInfo::getDeliveryNo)
                .orElse(null);
    }
}
