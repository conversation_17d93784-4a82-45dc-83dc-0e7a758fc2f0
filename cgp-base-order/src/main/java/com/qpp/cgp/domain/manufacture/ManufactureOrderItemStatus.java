package com.qpp.cgp.domain.manufacture;

import com.qpp.cgp.config.domaintype.RuntimeDomain;
import com.qpp.cgp.migration.CgpMongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 生产订单项状态
 * Created by smart on 10/25/2017.
 */
@NoArgsConstructor
@Data
@RuntimeDomain
@Document(collection = "manufactureorderitemstatuses")
public class ManufactureOrderItemStatus extends CgpMongoDomain {

    //等待生产
    public static final Long WAITING_PRODUCE = 156205L;

    //已生产
    public static final Long PRODUCED = 156206L;

    //已打印
    public static final Long PRINTED = 236484L;

    //已完成
    public static final Long FINISHED = 241676L;

    private String name;

}
