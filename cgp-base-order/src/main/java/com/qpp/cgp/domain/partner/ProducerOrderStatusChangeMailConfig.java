package com.qpp.cgp.domain.partner;

import com.qpp.mongo.domain.MongoDomain;
import com.qpp.cgp.domain.mail.MailTemplateConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date 2018/4/20
 */
@SuppressWarnings("SpellCheckingInspection")
@ApiModel(description = "供应商订单变化邮件通知配置", parent = MongoDomain.class)
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
@Document(collection = "producerorderstatuschangemailconfigs")
public class ProducerOrderStatusChangeMailConfig extends MongoDomain {

    /**
     * {@link Partner#id}
     */
    private Long partnerId;

    /**
     * 上一个状态
     */
    private Long preStatusId;

    /**
     * 改变后状态
     */
    private Long curStatusId;

    /**
     * 是发给供应商的还是QP网站的
     */
    @ApiModelProperty(value = "邮件接收方", allowableValues = "partner, qp")
    private String use;

    /**
     * 是供应商后台的配置还是cgp后台网站的配置
     */
    @ApiModelProperty(allowableValues = "backstage, customer")
    private String type;

    /**
     * 邮件发送模板配置
     */
    private MailTemplateConfig mailTemplateConfig;

}
