package com.qpp.cgp.domain.order.stateFlow;

import com.qpp.cgp.config.domaintype.ConfigDomain;
import com.qpp.cgp.domain.stateflow.config.StateFlow;
import com.qpp.cgp.manager.application.ApplicationMode;
import com.qpp.mongo.domain.LongMongoDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotNull;

/**
 * @Author: zark
 * @Date: 2024/8/22 10:50
 * @Description: 状态机和状态的配置
 */
@Data
@ConfigDomain
@Document("stateflowandstateconfig")
public class StateFlowAndStateConfig extends LongMongoDomain {

    @ApiModelProperty("对应的状态机")
    @NotNull
    private StateFlow flow;

    @ApiModelProperty("当前订单状态")
    @NotNull
    private String key;

    @ApiModelProperty("对应下一步的订单状态id")
    @NotNull
    private String nextOrderStateId;

    private ApplicationMode mode;
}
