package com.qpp.cgp.domain.shipment;

import com.qpp.cgp.migration.CgpMongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * todo 重量单位
 * 发货装箱信息
 *
 * <AUTHOR>
 * @since 2021/3/26
 */
@NoArgsConstructor
@Data
public class ShipmentBox extends CgpMongoDomain {

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     * 产品的总数量
     */
    private Integer productQty;

    /**
     * 箱子数量
     */
    private Integer boxQty;

    /**
     * 箱子长度
     */
    private Double boxLength;

    /**
     * 箱子宽度
     */
    private Double boxWidth;

    /**
     * 箱子高度
     */
    private Double boxHeight;

    /**
     * 箱子尺寸单位
     */
    private String boxSizeUnit;

    /**
     * 产品重量
     */
    private Double productWeight;

    /**
     * 总重量
     */
    private Double totalWeight;

    /**
     * 装箱类型
     */
    private String packageType;

    /**
     * 订单项产品发货装箱信息
     */
    private List<ShipmentBoxProductItem> productItems;

    /**
     * 是否单独装箱
     */
    private Boolean alonePacking = false;

    /**
     * 排序序号
     */
    private Integer sortNo = 0;

    /**
     * 重量单位
     */
    private String weightUnit;

    /**
     * 装箱操作人
     */
    private String acPerson;

    /**
     * 装箱时间
     */
    private String acData;

    /**
     * 包裹编号
     */
    private String packageNo;

}
