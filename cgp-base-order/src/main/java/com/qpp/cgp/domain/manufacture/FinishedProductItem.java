package com.qpp.cgp.domain.manufacture;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qpp.cgp.config.domaintype.RuntimeDomain;
import com.qpp.mongo.domain.MongoDomain;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

/**
 * 成品项
 * Created by smart on 10/25/2017.
 */
@RuntimeDomain
@NoArgsConstructor
@Data
@Document(collection = "finishedproductitems")
public class FinishedProductItem extends MongoDomain {


    private String materialId;

    private int qty;

    private Boolean isNeedPrint = false;

    private Boolean isOutsourcing = false;

    @JsonProperty
    @Transient
    private FinishedProductItemStatus status;

    private Long statusId;

    private String manufactureOrderItemId;

    @Transient
    private ManufactureOrderItem manufactureOrderItem;

    private List<FinishedProductItemCurrentInfo> currentItems = new ArrayList<>();

    //生产机器
    private String machine;

    //生产基地
    private String productionBaseCode;

    public FinishedProductItemStatus getStatus() {
        return status;
    }

    public void setStatus(FinishedProductItemStatus status) {
        this.statusId = status.getId();
        this.status = status;
    }
}
