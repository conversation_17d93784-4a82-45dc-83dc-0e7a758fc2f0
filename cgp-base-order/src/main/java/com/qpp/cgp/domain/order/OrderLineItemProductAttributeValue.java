package com.qpp.cgp.domain.order;

import com.qpp.mongo.domain.MongoDomain;

public class OrderLineItemProductAttributeValue extends MongoDomain {

    private static final long serialVersionUID = 1L;

    private Long attributeId;

    private String attributeInputType;

    private String attributeName;

    private String attributeValue;

    private String attributeOptionIds;

//    @JsonIgnore
//    @Transient
//    private OrderLineItem orderLineItem;


    public AbstractOrderLineItem getOrderLineItem() {
//        return orderLineItem;
        return null;
    }

    public void setOrderLineItem(AbstractOrderLineItem orderLineItem) {
//        this.orderLineItem = orderLineItem;
    }

    public Long getAttributeId() {
        return attributeId;
    }

    public void setAttributeId(Long attributeId) {
        this.attributeId = attributeId;
    }

    public String getAttributeInputType() {
        return attributeInputType;
    }

    public void setAttributeInputType(String attributeInputType) {
        this.attributeInputType = attributeInputType;
    }

    public String getAttributeName() {
        return attributeName;
    }

    public void setAttributeName(String attributeName) {
        this.attributeName = attributeName;
    }

    public String getAttributeValue() {
        return attributeValue;
    }

    public void setAttributeValue(String attributeValue) {
        this.attributeValue = attributeValue;
    }

    public String getAttributeOptionIds() {
        return attributeOptionIds;
    }

    public void setAttributeOptionIds(String attributeOptionIds) {
        this.attributeOptionIds = attributeOptionIds;
    }


}
