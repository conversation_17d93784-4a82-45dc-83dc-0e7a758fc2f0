package com.qpp.cgp.domain.partner.report.settle.config;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2018/1/27
 */
@NoArgsConstructor
@Data
public class PartnerReportOrderListConfig {

    /**
     * 日期格式化字符串
     */
    private String dateFormat;

    /**
     * 价格格式化字符串
     */
    private String priceFormat;

    /**
     * 指定生成的第二个sheet的名称
     */
    private String sheetName;

    /**
     * 指定Excel模板文件中第二个sheet模板的下标
     */
    private Integer sheetIndex = 1;

}
