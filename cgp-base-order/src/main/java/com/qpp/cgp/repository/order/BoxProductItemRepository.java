package com.qpp.cgp.repository.order;

import com.qpp.cgp.config.mongo.MongoTemplateFactory;
import com.qpp.cgp.domain.order.BoxProductItem;
import com.qpp.cgp.domain.order.ShipmentBox;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.driver.HybridMongoTemplate;
import com.qpp.web.business.manager.AbstractMongoCurdManager;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 * Description
 * @version 1.0
 * Date Create by 2018-09-05 18:22
 * Modified by
 */
@Service
public class BoxProductItemRepository extends AbstractMongoCurdManager<BoxProductItem, String> {

    @Autowired
    public BoxProductItemRepository(MongoTemplateFactory mongoTemplateFactory, IdGenerator idgenerator) {
        super((HybridMongoTemplate) mongoTemplateFactory.getMongoTemplate(BoxProductItem.class), idgenerator);
    }

    public List<BoxProductItem> findByShipmentBox(ShipmentBox shipmentBox) {
        return mongoTemplate.find(
                Query.query(Criteria.where("shipmentBox._id").is(shipmentBox.getId())), BoxProductItem.class);
    }

    public BoxProductItem save(BoxProductItem boxProductItem) {
        if (null == boxProductItem.getId()) {
            return saveNew(boxProductItem);
        } else {
            return saveUpdate(boxProductItem, boxProductItem.getId());
        }
    }

    public BoxProductItem save(BoxProductItem boxProductItem, ShipmentBox shipmentBox) {
        boxProductItem.setShipmentBox(shipmentBox);
        return save(boxProductItem);
    }

    public void deleteAllByShipmentBoxId(List<String> ids) {
        mongoTemplate.remove(Query.query(Criteria.where("shipmentBox._id").in(ids)), entityType);
    }
}
