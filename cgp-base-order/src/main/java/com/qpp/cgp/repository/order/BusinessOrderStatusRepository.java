package com.qpp.cgp.repository.order;

import com.qpp.cgp.domain.order.BusinessOrderStatus;
import com.qpp.cgp.migration.CgpMongoDomainRepository;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;


@Service
public class BusinessOrderStatusRepository extends CgpMongoDomainRepository<BusinessOrderStatus, Long> {

    /**
     *
     * @param originId
     * @return
     */
    public BusinessOrderStatus findByOriginId(Long originId) {
       return mongoTemplate.findOne(Query.query(Criteria.where("originId").is(originId)), BusinessOrderStatus.class);
    }
}
