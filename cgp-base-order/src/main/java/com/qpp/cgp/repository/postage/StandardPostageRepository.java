package com.qpp.cgp.repository.postage;

import com.qpp.cgp.domain.common.Website;
import com.qpp.cgp.domain.postage.StandardPostage;
import com.qpp.cgp.migration.CgpMongoDomainRepository;
import com.qpp.cgp.repository.common.WebsiteRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;


@Service
public class StandardPostageRepository extends CgpMongoDomainRepository<StandardPostage, Long> {

    public StandardPostage findByAddressAndWebsite(String address, Website website) {
        return mongoTemplate.findOne(Query.query(Criteria.where("address").is(address).and("website.id").is(website.getId())), StandardPostage.class);
    }

    @Autowired
    private WebsiteRepository websiteRepository;

    @Override
    public <S extends StandardPostage> S fillData(S entity) {
        Website website = entity.getWebsite();
        if (website != null) {
            websiteRepository.findById(website.getId()).ifPresent(entity::setWebsite);
        }
        return entity;
    }
}
