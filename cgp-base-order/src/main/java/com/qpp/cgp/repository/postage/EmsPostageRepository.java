package com.qpp.cgp.repository.postage;

import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.common.Website;
import com.qpp.cgp.domain.postage.EmsPostage;
import com.qpp.cgp.migration.CgpMongoDomainRepository;
import com.qpp.cgp.repository.common.WebsiteRepository;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;


@Service
public class EmsPostageRepository extends CgpMongoDomainRepository<EmsPostage, Long> {

    public EmsPostage findByAddressAndWebsite(String name, Website website) {

        if (null == website){
            throw BusinessExceptionBuilder.of(1600003, ImmutableMap.of("paramName", "website"));
        }

        return mongoTemplate.findOne(Query.query(Criteria.where("address").is(name).and("website.id").is(website.getId())), EmsPostage.class);
    }

    @Autowired
    private WebsiteRepository websiteRepository;

    @Override
    public <S extends EmsPostage> S fillData(S entity) {
        Website website = entity.getWebsite();
        if (website != null) {
            websiteRepository.findById(website.getId()).ifPresent(entity::setWebsite);
        }
        return entity;
    }
}
