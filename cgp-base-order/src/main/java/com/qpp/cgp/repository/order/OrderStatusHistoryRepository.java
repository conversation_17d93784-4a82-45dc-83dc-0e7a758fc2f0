package com.qpp.cgp.repository.order;

import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.order.AbstractOrder;
import com.qpp.cgp.domain.order.OrderStatus;
import com.qpp.cgp.domain.order.OrderStatusHistory;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.driver.HybridMongoTemplate;
import com.qpp.web.business.manager.AbstractMongoCurdManager;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class OrderStatusHistoryRepository extends AbstractMongoCurdManager<OrderStatusHistory, String> {

    @Autowired
    private OrderStatusRepository orderStatusRepository;

    @Autowired
    private BusinessOrderStatusRepository businessOrderStatusRepository;

    @Autowired
    public OrderStatusHistoryRepository(HybridMongoTemplate mongoTemplate, IdGenerator idgenerator) {
        super(mongoTemplate, idgenerator);
    }

    public OrderStatusHistory getFullOrderStatusHistory(OrderStatusHistory orderStatusHistory) {

        if (orderStatusHistory == null) {
            throw BusinessExceptionBuilder.of(12100003, ImmutableMap.of("paramName", "orderStatusHistory"));
        }

        Long orderStatusId = orderStatusHistory.getOrderStatusId();
        Optional<OrderStatus> optOrderStatus = orderStatusRepository.findById(orderStatusId);
        if (optOrderStatus.isPresent()) {
            orderStatusHistory.setStatus(optOrderStatus.get());
        } else {
            orderStatusHistory.setStatus(businessOrderStatusRepository.findById(orderStatusId).orElse(null));
        }
        // 当没有创建者时使用修改者代替
        String modifyBy = orderStatusHistory.getCreatedBy() != null ? orderStatusHistory.getCreatedBy() : orderStatusHistory.getModifiedBy();
        orderStatusHistory.setCreatedBy(modifyBy);
        return orderStatusHistory;
    }

    public List<OrderStatusHistory> findByOrderId(String orderId) {
        List<OrderStatusHistory> statusHistories = mongoTemplate.find(
                Query.query(Criteria.where("order._id").is(orderId)), entityType);
        return statusHistories.stream()
                .map(this::getFullOrderStatusHistory)
                .collect(Collectors.toList());
    }

    public List<OrderStatusHistory> findByOrderIds(Set<String> orderIds) {
        List<OrderStatusHistory> statusHistories = mongoTemplate.find(
                Query.query(Criteria.where("order._id").in(orderIds)), entityType);
        return statusHistories.stream()
                .map(this::getFullOrderStatusHistory)
                .collect(Collectors.toList());
    }

    public OrderStatusHistory save(OrderStatusHistory history, AbstractOrder order) {
        history.setOrder(order);
        return saveNew(history);
    }

    /**
     * 判断订单状态中是否存在某一状态
     */
    public boolean exists(String orderId, long orderStatusId) {
        return mongoTemplate.exists(Query.query(Criteria.where("order._id").is(orderId)
                .and("orderStatusId").is(orderStatusId)), entityType);
    }

    public OrderStatusHistory findLastByOrderId(String orderId) {
        Query query = Query.query(Criteria.where("order._id").is(orderId));
        query.with(Sort.by(Sort.Direction.DESC, "modifiedDate")).limit(1);
        return mongoTemplate.findOne(query, entityType);
    }

    public OrderStatus getPaymentState(String orderId) {
        Query query = Query.query(Criteria.where("order._id").is(orderId)
                .and("orderStatusId").in(40, 100, 101, 117));
        query.with(Sort.by(Sort.Direction.DESC, "modifiedDate")).limit(1);
        OrderStatusHistory paymentStatusHistory = mongoTemplate.findOne(query, entityType);
        if (null == paymentStatusHistory) {
            return null;
        }
        Optional<OrderStatus> paymentStatus = orderStatusRepository.findById(paymentStatusHistory.getOrderStatusId());
        return paymentStatus.orElse(null);
    }
}
