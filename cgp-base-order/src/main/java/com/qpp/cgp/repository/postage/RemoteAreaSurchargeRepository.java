package com.qpp.cgp.repository.postage;

import com.qpp.cgp.domain.postage.RemoteAreaSurcharge;
import com.qpp.cgp.migration.CgpMongoDomainRepository;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;


@Service
public class RemoteAreaSurchargeRepository extends CgpMongoDomainRepository<RemoteAreaSurcharge, Long> {

    public RemoteAreaSurcharge findByInfo(String countryCode, String shippingMethod, String postCode, double orderAmount) {
        RemoteAreaSurcharge result = mongoTemplate.findOne(
                Query.query(Criteria.where("startPostcode").lte(postCode).and("endPostcode").gte(postCode)
                        .and("startOrderAmount").lte(orderAmount).and("endOrderAmount").gte(orderAmount)
                        .and("countryCode").is(countryCode)
                        .and("shippingMethod").is(shippingMethod))
                , RemoteAreaSurcharge.class);
        return result;
    }
}
