package com.qpp.cgp.repository.shipping;

import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.domain.common.Website;
import com.qpp.cgp.domain.shipping.ShippingMethod;
import com.qpp.cgp.migration.CgpMongoDomainRepository;
import com.qpp.cgp.repository.common.WebsiteRepository;
import com.qpp.core.exception.BusinessException;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;


import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class ShippingMethodRepository extends CgpMongoDomainRepository<ShippingMethod, Long> {

    public List<ShippingMethod> findByAvailableTrueAndWebsiteOrderBySortOrderAsc(Website website) {

        if (null == website){
            throw new BusinessException("参数website不能为空");
        }

        List<ShippingMethod> shippingMethods = mongoTemplate.find(Query.query(Criteria.where("available").is(true)
                        .and("website.id").is(website.getId()))
                        .with(Sort.by(Sort.Direction.ASC, "sortOrder"))
                , ShippingMethod.class);
        return shippingMethods.stream().map(this::fillData).collect(Collectors.toList());
    }

    public List<ShippingMethod> findByAvailableTrueAndWebsiteIdsOrderBySortOrderAsc(List<Long> websiteIds) {
        List<ShippingMethod> shippingMethods = mongoTemplate.find(Query.query(Criteria.where("available").is(true)
                                .and("website.id").in(websiteIds))
                        .with(Sort.by(Sort.Direction.ASC, "sortOrder"))
                , ShippingMethod.class);

        return shippingMethods.stream().map(this::fillData).collect(Collectors.toList());
    }

    public ShippingMethod findByCodeAndWebsite(String shippingMethodCode, Website website) {

        if (null == website){
            throw BusinessExceptionBuilder.of(1600003, ImmutableMap.of("paramName", "website"));
        }

        ShippingMethod shippingMethod = mongoTemplate.findOne(Query.query(Criteria.where("code").is(shippingMethodCode)
                        .and("website.id").is(website.getId()))
                , ShippingMethod.class);
        return fillData(shippingMethod);
    }

    @Autowired
    private WebsiteRepository websiteRepository;

    @Override
    public <S extends ShippingMethod> S fillData(S shippingMethod) {
        if (shippingMethod != null) {
            Optional.ofNullable(shippingMethod.getWebsite()).flatMap(website -> websiteRepository.findById(website.getId())).ifPresent(shippingMethod::setWebsite);
        }
        return shippingMethod;
    }
}
