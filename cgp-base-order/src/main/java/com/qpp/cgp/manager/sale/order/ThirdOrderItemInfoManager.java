package com.qpp.cgp.manager.sale.order;

import com.qpp.cgp.config.mongo.MongoTemplateBeanNames;
import com.qpp.cgp.domain.order.ThirdOrderItemInfo;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.driver.HybridMongoTemplate;
import com.qpp.web.business.manager.AbstractMongoCurdManager;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

/**
 * @className: ThirdOrderItemInfoManager
 * @description:
 * @author: TT-Berg
 * @date: 2023/1/4
 **/
@Service
public class ThirdOrderItemInfoManager extends AbstractMongoCurdManager<ThirdOrderItemInfo, String> {

    public ThirdOrderItemInfoManager(@Qualifier(MongoTemplateBeanNames.RUNTIME) HybridMongoTemplate mongoTemplate
            , IdGenerator idgenerator) {
        super(mongoTemplate, idgenerator);
    }

    public ThirdOrderItemInfo findByOrderItemId(String orderItemId) {
        return mongoTemplate.
                findOne(Query.query(Criteria.where("orderItemId").is(Long.valueOf(orderItemId)))
                        , ThirdOrderItemInfo.class);
    }

}
