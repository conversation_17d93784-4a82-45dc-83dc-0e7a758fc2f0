package com.qpp.cgp.manager.order;

import com.qpp.cgp.domain.order.OrderStatusChangeMailConfig;
import com.qpp.cgp.manager.permission.NeedPermission;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.driver.HybridMongoTemplate;
import com.qpp.web.business.manager.AbstractMongoCurdManager;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@NeedPermission(resource = "OrderStatusChangeMailConfig")
public class OrderStatusChangeMailConfigManager extends AbstractMongoCurdManager<OrderStatusChangeMailConfig, String> {
    public OrderStatusChangeMailConfigManager(HybridMongoTemplate mongoTemplate, IdGenerator idgenerator) {
        super(mongoTemplate, idgenerator);
    }

    public OrderStatusChangeMailConfig deleteOne(String configId) {
        Query query = Query.query(Criteria.where(OrderStatusChangeMailConfig.idProperty).is(configId));
        return mongoTemplate.findAndRemove(query, OrderStatusChangeMailConfig.class);
    }

    public OrderStatusChangeMailConfig findOne(Long preStatusId, Long curStatusId, Long partnerId, Long websiteId, String use) {
        Query query = Query.query(Criteria.where("preStatusId").is(preStatusId)
                .and("curStatusId").is(curStatusId)
                // .and("websiteId").is(websiteId)
                .and("use").is(use));
        if (Objects.nonNull(partnerId)) {
            query.addCriteria(Criteria.where("partnerId").is(partnerId));
        } else if (Objects.nonNull(websiteId)) {
            query.addCriteria(Criteria.where("websiteId").is(websiteId));
        }
        return mongoTemplate.findOne(query, OrderStatusChangeMailConfig.class);
    }

}
