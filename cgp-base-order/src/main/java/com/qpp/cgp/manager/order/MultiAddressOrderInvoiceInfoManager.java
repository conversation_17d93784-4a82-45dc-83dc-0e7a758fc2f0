package com.qpp.cgp.manager.order;

import com.qpp.cgp.config.mongo.MongoTemplateBeanNames;
import com.qpp.cgp.domain.shipment.MultiAddressOrderInvoiceInfo;
import com.qpp.id.generator.IdGenerator;
import com.qpp.mongo.driver.HybridMongoTemplate;
import com.qpp.web.business.manager.AbstractLongMongoCurdManager;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;

/**
 * @className: MultiAddressOrderInvoiceInfoManager
 * @description:
 * @author: TT-Berg
 * @date: 2023/8/10
 **/
@Repository
public class MultiAddressOrderInvoiceInfoManager extends AbstractLongMongoCurdManager<MultiAddressOrderInvoiceInfo, Long> {

    public MultiAddressOrderInvoiceInfoManager(@Qualifier(MongoTemplateBeanNames.RUNTIME) HybridMongoTemplate mongoTemplate, IdGenerator idgenerator) {
        super(mongoTemplate, idgenerator);
    }

    public List<MultiAddressOrderInvoiceInfo> findByOrderId(String orderId) {
        return mongoTemplate.find(Query.query(Criteria.where("orderId").is(orderId)), MultiAddressOrderInvoiceInfo.class);
    }

    public List<MultiAddressOrderInvoiceInfo> batchInsert(List<MultiAddressOrderInvoiceInfo> multiAddressOrderInvoiceInfoList) {
        multiAddressOrderInvoiceInfoList.forEach(multiAddressOrderInvoiceInfo -> {
            if (Objects.isNull(multiAddressOrderInvoiceInfo.getId())) {
                multiAddressOrderInvoiceInfo.setId(idgenerator.generateId());
            }
        });
        return (List<MultiAddressOrderInvoiceInfo>) mongoTemplate.insert(multiAddressOrderInvoiceInfoList, MultiAddressOrderInvoiceInfo.class);
    }

}
