package com.qpp.cgp.manager.module;

import com.qpp.cgp.config.mongo.MongoTemplateFactory;
import com.qpp.cgp.core.module.ordertotal.IOrderTotalModule;
import com.qpp.cgp.domain.common.module.OrderTotalModuleConfig;
import com.qpp.cgp.domain.common.module.OrderTotalModuleConfigV2;
import com.qpp.cgp.domain.order.AbstractOrder;
import com.qpp.cgp.domain.order.OrderTotal;
import com.qpp.cgp.repository.common.module.OrderTotalModuleConfigRepository;
import com.qpp.core.exception.BusinessException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by smart on 10/11/2017.
 */
@Service
public class OrderTotalManager extends AbstractManager {

    @Autowired
    private OrderTotalModuleConfigRepository orderTotalModuleConfigRepository;

    @Autowired
    private MongoTemplateFactory mongoTemplateFactory;


    public List<OrderTotalModuleConfig> getOrderTotalModuleConfigListByWebsiteIdAndOrderBySortOrderAsc(Long websiteId) {

        List<OrderTotalModuleConfig> list = orderTotalModuleConfigRepository.findByWebsiteIdAndAvailableOrderBySortOrderAsc(websiteId, true);

        return list;
    }

    public List<OrderTotalModuleConfigV2> getOrderTotalModuleConfigListByWebsiteIdAndOrderBySortOrderAscV2(Long websiteId) {

        Criteria criteria = Criteria.where("website._id").is(websiteId)
                .and("available").is(true);
        Sort sort = Sort.by(Sort.Order.asc("sortOrder"));
        Query query = Query.query(criteria).with(sort);

        return mongoTemplateFactory.getMongoTemplate(OrderTotalModuleConfigV2.class).find(query, OrderTotalModuleConfigV2.class);
    }

    public AbstractOrder populateOrderWithOrderTotals(AbstractOrder order) {

        if (order == null) {
            throw new BusinessException("Cannot populate OrderTotal to null order");
        }
        if (order.getOrderTotals() == null) {
            order.setOrderTotals(new ArrayList<OrderTotal>());
        }
//		order.setOrderTotals(new ArrayList<OrderTotal>());

        order.calculateInitTotals();

        if ((order.getTotalIncTax() == null) || (order.getTotalExTax() == null)
                || (order.getTax() == null)) {

            throw new BusinessException(
                    "The total amounts for the order could not be successfully calculated.");
        }


        List<OrderTotalModuleConfig> configList = getOrderTotalModuleConfigListByWebsiteIdAndOrderBySortOrderAsc(order.getWebsiteId());

        List<OrderTotal> retList = new ArrayList<>();


        for (OrderTotalModuleConfig config : configList) {

            OrderTotal ot = getOrderTotalByConfig(config, order);

            if (ot != null) {
                ot.setOrder(order);
                retList.add(ot);
            }

        }

        List<OrderTotalModuleConfigV2> configListV2 = getOrderTotalModuleConfigListByWebsiteIdAndOrderBySortOrderAscV2(order.getWebsiteId());

        List<OrderTotal> retListV2 = new ArrayList<>();


        for (OrderTotalModuleConfigV2 config : configListV2) {

            OrderTotal ot = getOrderTotalByConfigV2(config, order);

            if (ot != null) {
                ot.setOrder(order);
                retListV2.add(ot);
            }

        }



        order.getOrderTotals().clear();
        order.getOrderTotals().addAll(retList);
        if (!retListV2.isEmpty()) {
            order.getOrderTotals().addAll(retListV2);
        }

        order.calculateShippingMethod();

        return order;

    }

    private OrderTotal getOrderTotalByConfig(OrderTotalModuleConfig config, AbstractOrder order) {

        String className = config.getClassName();

        checkRequired("className", className, String.class);

        try {

            Class<?> orderTotalModuleClass = Class.forName(className);

            IOrderTotalModule orderTotalModule = (IOrderTotalModule) orderTotalModuleClass.newInstance();

            OrderTotal ot = orderTotalModule.getOrderTotal(order, config);

            return ot;

        } catch (Exception e) {
            getLogger().error("new instance module class happened error", e);
            throw new BusinessException("new module instance happened error");
        }

    }

    private OrderTotal getOrderTotalByConfigV2(OrderTotalModuleConfigV2 config, AbstractOrder order) {

        String className = config.getClassName();

        checkRequired("className", className, String.class);

        try {

            Class<?> orderTotalModuleClass = Class.forName(className);

            IOrderTotalModule orderTotalModule = (IOrderTotalModule) orderTotalModuleClass.newInstance();
            OrderTotalModuleConfig orderTotalModuleConfig = new OrderTotalModuleConfig();
            BeanUtils.copyProperties(config, orderTotalModuleConfig);
            OrderTotal ot = orderTotalModule.getOrderTotal(order, orderTotalModuleConfig);

            return ot;

        } catch (Exception e) {
            getLogger().error("new instance module class happened error", e);
            return null;
        }

    }


}
