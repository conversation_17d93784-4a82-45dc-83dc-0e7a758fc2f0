package com.qpp.cgp.manager.module;

import com.qpp.core.exception.BusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public abstract class AbstractManager {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());


    public void checkRequired(String fieldName, String fieldValue, Object type) {

        if (type == String.class) {
            checkRequiredString(fieldName, fieldValue);
        }

    }


    public void checkRequiredString(String fieldName, String fieldValue) {

        if (fieldValue == null || fieldValue.trim().equals("")) {

            getLogger().error(fieldName + " cannot be null");
            throw new BusinessException(fieldName + "cannot be null");
        }

    }


    public Logger getLogger() {
        return logger;
    }

}
