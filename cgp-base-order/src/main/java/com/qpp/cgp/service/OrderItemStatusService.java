package com.qpp.cgp.service;

import com.google.common.collect.ImmutableMap;
import com.qpp.cgp.cache.RedisComponent;
import com.qpp.cgp.config.mongo.MongoTemplateBeanNames;
import com.qpp.cgp.domain.orderitem.OrderItemStatus;
import com.qpp.cgp.service.common.CommonPropertiesService;
import com.qpp.web.core.exception.BusinessExceptionBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Service
public class OrderItemStatusService {
    @Autowired
    private RedisComponent<String,Object> redisComponent;

    @Qualifier(MongoTemplateBeanNames.RUNTIME)
    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private CommonPropertiesService commonPropertiesService;

    private static String orderItemStatusPreStr = "OrderItemStatus:";

    public String getStatusNameByStatusId(Long statusId){
        String cacheKey = orderItemStatusPreStr + statusId.toString();
        Object cacheValue = redisComponent.get(cacheKey);
        if (cacheValue == null) {
            OrderItemStatus orderItemStatus = Optional.ofNullable(mongoTemplate.findById(statusId, OrderItemStatus.class)).orElseThrow(() -> BusinessExceptionBuilder.of(4000017, ImmutableMap.of("statusId", statusId)));
            redisComponent.set(cacheKey, orderItemStatus.getName());
            redisComponent.expire(cacheKey, getCacheDays(), TimeUnit.DAYS);
            return orderItemStatus.getName();
        }
        redisComponent.expire(cacheKey, getCacheDays(), TimeUnit.DAYS);
        return (String) cacheValue;
    }

    private Long getCacheDays(){
        return Long.parseLong(Optional.ofNullable(commonPropertiesService.getKeyValueAndApplicationModeForNoException("OrderItemCacheDays")).orElse("7"));
    }

    public void clearCacheOrderItemStatusName() {
        redisComponent.delete(orderItemStatusPreStr + "*");
    }
}
